//
//  UPResourceResult.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
typedef enum : NSUInteger {
    UPResourceErrorCodeSUCCESS = 0,
    UPResourceErrorCodeFAILURE,
    UPResourceErrorCodeINVALID,
    UPResourceErrorCodeTIMEOUT,
    UPResourceErrorCodeCANCEL
} UPResourceErrorCode;
NS_ASSUME_NONNULL_BEGIN

@interface UPResourceResult : NSObject
@property (nonatomic, assign) UPResourceErrorCode errorCode;
@property (nonatomic, copy) NSString *extraInfo;
@property (nonatomic, strong) id extraData;
- (instancetype)initResult:(UPResourceErrorCode)errorCode extraInfo:(NSString *)extraInfo extraData:(id)extraData;
+ (instancetype)invalidResult:(NSString *)extraInfo;
+ (instancetype)failureResult:(NSString *)extraInfo;
- (BOOL)isSuccessful;
@end

NS_ASSUME_NONNULL_END

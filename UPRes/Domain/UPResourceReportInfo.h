//
//  UPResourceReportInfo.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPResourceReportInfo : NSObject
@property (nonatomic, copy) NSString *user_id;
@property (nonatomic, copy) NSString *client_id;
@property (nonatomic, copy) NSString *app_version;
@property (nonatomic, copy) NSString *res_type;
@property (nonatomic, copy) NSString *res_name;
@property (nonatomic, copy) NSString *res_version;
@property (nonatomic, copy) NSString *is_preset;
@property (nonatomic, copy) NSString *installed_time;
@property (nonatomic, copy) NSString *is_gray;

@property (nonatomic, copy) NSString *time_length;
@property (nonatomic, copy) NSString *value;
@property (nonatomic, copy) NSString *reTryCount;
@property (nonatomic, copy) NSString *result;
@property (nonatomic, assign) BOOL showDialog;
@property (nonatomic, copy) NSString *resourceIP;
@property (nonatomic, copy) NSString *downloadBlockInfos;
@property (nonatomic, copy) NSString *downloadNetInfos;
@property (nonatomic, copy) NSString *downloadBackgroundRunInfos;
@end

NS_ASSUME_NONNULL_END

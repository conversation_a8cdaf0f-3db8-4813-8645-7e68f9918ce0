//
//  UPResourceInfo.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceItem.h"
#import "UPResourceType.h"

extern NSString *const UPRes_TypeString_HTML;
extern NSString *const UPRes_TypeString_DeviceConfig;
extern NSString *const UPRes_TypeString_APICloud;
extern NSString *const UPRes_TypeString_ReactNative;
extern NSString *const UPRes_TypeString_MPAAS;
extern NSString *const UPRes_TypeString_DEVICE_CUSTOM_INFO;
extern NSString *const THEMEPREFIX;
extern NSString *const UPRes_TypeString_Video;
extern NSString *const UPRes_typeString_Flutter;
extern NSString *const UPRes_typeString_Routes;
extern NSString *const UPRes_typeString_Picture;
extern NSString *const UPRes_typeString_Audio;
extern NSString *const UPRes_typeString_Other;
extern NSString *const UPRes_TypeString_ConfigFile;

typedef enum : NSUInteger {
    UPResourceDownLoadPriorityLow = 0,
    UPResourceDownLoadPriorityMid,
    UPResourceDownLoadPriorityHeight,
} UPResourceDownLoadPriorityType;

typedef NS_ENUM(NSUInteger, UPResourceStatus) {
    UPResourceStatusPublish = 0,
    UPResourceStatusOFF,
};

@protocol UPFileDelegate
, UPTimeDelegate, UPResourceReporter;
@interface UPResourceInfo : UPResourceItem <NSCopying>
@property (nonatomic, copy) NSString *path;
@property (nonatomic, assign) BOOL active;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, copy) NSString *version;
@property (nonatomic, copy) NSString *link;
@property (nonatomic, copy) NSString *hashStr;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *deviceTypeIndex;
@property (nonatomic, weak) id<UPFileDelegate> fileDelegate;
@property (nonatomic, weak) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, weak) id<UPResourceReporter> reporterDelegate;
/**
 是否预置资源
 **/
@property (nonatomic, assign) BOOL preset;
/**
 首页地址
 */
@property (nonatomic, copy) NSString *indexPath;

@property (nonatomic, assign) UPResourceDownLoadPriorityType priority;
/**
 是否最新资源标识，每次从Server请求到的资源信息更新到数据库时该字段默认为TRUE，同时修改其他资源信息的该字段为FALSE
 */
@property (nonatomic, assign) BOOL isServerLatest;
/**
 是否需要支持隐藏状态栏，YES 表示需要隐藏，NO 表示不隐藏
 */
@property (nonatomic, assign) BOOL hideStatusBar;


/// 资源包测试类型 （1:主线 2:ABTest）
@property (nonatomic, copy) NSString *resourceType;

/**
 * @brief 是否强制升级 1强制 0不强制优先使用已安装版本
 */
@property (nonatomic, assign) BOOL forceUpgrade;

/**
 * @brief 资源规则 json 字符串（仅资源是插件包时，才有loadMode、enableMode、blockingMode三个属性；其他资源，resRules为null）
 */
@property (nonatomic, strong) NSDictionary *resRules;

/**
  远端地址
 */
@property (nonatomic, copy) NSString *remoteUrl;

/// 资源包状态 0发布中  1下架
@property (nonatomic, assign) UPResourceStatus resStatus;

+ (NSString *)stringValueOfResourceType:(UPResourceType)type;
+ (UPResourceType)typeValueOfString:(NSString *)typeStr;

/// 是否主题资源
- (BOOL)isTheme;

@end

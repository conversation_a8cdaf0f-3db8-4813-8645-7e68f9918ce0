//
//  UPResourceResult.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/9.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceResult.h"

@implementation UPResourceResult
- (instancetype)initResult:(UPResourceErrorCode)errorCode extraInfo:(NSString *)extraInfo extraData:(id)extraData
{
    if (self = [super init]) {
        _errorCode = errorCode;
        _extraData = extraData;
        _extraInfo = extraInfo;
    }
    return self;
}
+ (instancetype)invalidResult:(NSString *)extraInfo
{
    return [[UPResourceResult alloc] initResult:UPResourceErrorCodeINVALID extraInfo:extraInfo extraData:nil];
}
+ (instancetype)failureResult:(NSString *)extraInfo
{
    return [[UPResourceResult alloc] initResult:UPResourceErrorCodeFAILURE extraInfo:extraInfo extraData:nil];
}
- (BOOL)isSuccessful
{
    return self.errorCode == UPResourceErrorCodeSUCCESS;
}
@end

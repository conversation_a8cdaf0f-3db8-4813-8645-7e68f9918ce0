//
//  UPResourceInfo.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import "UPFileDelegate.h"
#import "UPResourceTimeSystem.h"
#import "UPResourceReporter.h"
#import <uplog/UPLog.h>
NSString *const UPRes_TypeString_HTML = @"h5";
NSString *const UPRes_TypeString_DeviceConfig = @"config";
NSString *const UPRes_TypeString_APICloud = @"apicloud";
NSString *const UPRes_TypeString_ReactNative = @"react";
NSString *const UPRes_TypeString_MPAAS = @"mPaaS";
NSString *const UPRes_TypeString_DEVICE_CUSTOM_INFO = @"deviceCustomInfo";
NSString *const UPRes_TypeString_ConfigApp = @"configAPP";
NSString *const UPRes_TypeString_APPFuncModel = @"app-func-model";
NSString *const THEMEPREFIX = @"appComRes";
NSString *const UPRes_TypeString_Video = @"video";
NSString *const UPRes_typeString_Flutter = @"flutter";
NSString *const UPRes_typeString_Routes = @"routes";
NSString *const UPRes_typeString_Picture = @"picture";
NSString *const UPRes_typeString_Audio = @"audio";
NSString *const UPRes_typeString_Other = @"other";
NSString *const UPRes_TypeString_ConfigFile = @"configFile";
NSString *const UPRes_TypeString_LUA = @"lua";
@implementation UPResourceInfo

- (id)copyWithZone:(NSZone *)zone
{
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.itemId = self.itemId;
    info.createTime = self.createTime;
    info.updateTime = self.updateTime;
    info.path = _path;
    info.active = _active;
    info.name = _name;
    info.type = _type;
    info.version = _version;
    info.link = _link;
    info.hashStr = _hashStr;
    info.model = _model;
    info.typeId = _typeId;
    info.prodNo = _prodNo;
    info.deviceTypeIndex = _deviceTypeIndex;
    info.preset = _preset;
    info.indexPath = _indexPath;
    info.resourceType = _resourceType;
    info.hideStatusBar = _hideStatusBar;
    info.forceUpgrade = _forceUpgrade;
    info.resRules = _resRules;
    info.resStatus = _resStatus;
    info.remoteUrl = _remoteUrl;
    return info;
}

- (BOOL)active
{
    if (UPRes_validStringValue(_indexPath).length) {
        return [self.fileDelegate exists:[NSString stringWithFormat:@"%@/%@/index.html", _path, _name]];
    }
    return UPRes_validStringValue(_path).length && [self.fileDelegate exists:_path];
}

+ (NSString *)stringValueOfResourceType:(UPResourceType)type
{
    NSDictionary *types = @{ @(UPResourceTypeHTML) : UPRes_TypeString_HTML,
                             @(UPResourceTypeDeviceConfig) : UPRes_TypeString_DeviceConfig,
                             @(UPResourceTypeAPICloud) : UPRes_TypeString_APICloud,
                             @(UPResourceTypeReactNative) : UPRes_TypeString_ReactNative,
                             @(UPResourceTypeMPAAS) : UPRes_TypeString_MPAAS,
                             @(UPResourceTypeDEVICE_CUSTOM_INFO) : UPRes_TypeString_DEVICE_CUSTOM_INFO,
                             @(UPResourceTypeConfigApp) : UPRes_TypeString_ConfigApp,
                             @(UPResourceTypeVideo) : UPRes_TypeString_Video,
                             @(UPResourceTypeFlutter) : UPRes_typeString_Flutter,
                             @(UPResourceTypeAppFuncModel) : UPRes_TypeString_APPFuncModel,
                             @(UPResourceTypeRoutes) : UPRes_typeString_Routes,
                             @(UPResourceTypePicture) : UPRes_typeString_Picture,
                             @(UPResourceTypeAudio) : UPRes_typeString_Audio,
                             @(UPResourceTypeOther) : UPRes_typeString_Other,
                             @(UPResourceTypeConfigFile) : UPRes_TypeString_ConfigFile,
                             @(UPResourceTypeLua) : UPRes_TypeString_LUA,
                             @(UPResourceTypeAll) : @""
    };
    return types[@(type)];
}

+ (UPResourceType)typeValueOfString:(NSString *)typeStr
{
    if (UPRes_isEmptyString(typeStr)) {
        return UPResourceTypeAll;
    }
    typeStr = [typeStr lowercaseString];
    NSDictionary *types = @{ UPRes_TypeString_HTML.lowercaseString : @(UPResourceTypeHTML),
                                                   UPRes_TypeString_DeviceConfig.
                             lowercaseString : @(UPResourceTypeDeviceConfig),
                             UPRes_TypeString_APICloud.
                             lowercaseString : @(UPResourceTypeAPICloud),
                             UPRes_TypeString_ReactNative.
                             lowercaseString : @(UPResourceTypeReactNative),
                             UPRes_TypeString_MPAAS.
                             lowercaseString : @(UPResourceTypeMPAAS),
                             UPRes_TypeString_DEVICE_CUSTOM_INFO.
                             lowercaseString : @(UPResourceTypeDEVICE_CUSTOM_INFO),
                             UPRes_TypeString_ConfigApp.
                             lowercaseString : @(UPResourceTypeConfigApp),
                             UPRes_TypeString_Video.
                             lowercaseString : @(UPResourceTypeVideo),
                             UPRes_typeString_Flutter.
                             lowercaseString : @(UPResourceTypeFlutter),
                             UPRes_TypeString_APPFuncModel.
                             lowercaseString : @(UPResourceTypeAppFuncModel),
                             UPRes_typeString_Routes.
                             lowercaseString : @(UPResourceTypeRoutes),
                             UPRes_typeString_Picture.
                             lowercaseString : @(UPResourceTypePicture),
                             UPRes_typeString_Audio.
                             lowercaseString : @(UPResourceTypeAudio),
                             UPRes_typeString_Other.
                             lowercaseString : @(UPResourceTypeOther),
                             UPRes_TypeString_ConfigFile.
                             lowercaseString : @(UPResourceTypeConfigFile),
                             UPRes_TypeString_LUA.
                             lowercaseString : @(UPResourceTypeLua)

    };
    NSNumber *type = types[typeStr];
    if (![type isKindOfClass:[NSNumber class]]) {
        return UPResourceTypeAll;
    }
    return type.integerValue;
}

- (NSString *)path
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      if ((self->_type == UPResourceTypeVideo || self->_type == UPResourceTypeDeviceConfig || self->_type == UPResourceTypeConfigFile || self->_type == UPResourceTypeLua) && self.active) {
          if ([self.reporterDelegate respondsToSelector:@selector(track:type:version:)]) {
              [self.reporterDelegate track:self->_name type:self->_type version:self->_version];
          }
      }
    });
    return _path;
}

- (NSString *)description
{
    return [NSString stringWithFormat:@"%@@%@", self.name, self.version];
}

- (BOOL)isTheme
{
    return [_name hasPrefix:THEMEPREFIX];
}
@end

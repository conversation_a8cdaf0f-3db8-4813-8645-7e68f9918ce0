//
//  UPRelationDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class UPResourceQuery, UPResourceInfo;
NS_ASSUME_NONNULL_BEGIN

@protocol UPRelationDelegate <NSObject>

/// 关联资源列表与关联条件的关系,返回nil时关联失败，否则返回资源列表
/// @param query 关联条件
/// @param isPreset 是否预置资源
/// @param infoList 资源列表
/// @return 关联后的资源列表
- (nullable NSArray<UPResourceInfo *> *)relationResList:(UPResourceQuery *)query isPreset:(BOOL)isPreset infoList:(NSArray<UPResourceInfo *> *)infoList;
@end

NS_ASSUME_NONNULL_END

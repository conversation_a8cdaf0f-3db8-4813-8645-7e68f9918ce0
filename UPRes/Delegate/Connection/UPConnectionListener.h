//
//  UPConnectionListener.h
//  UPRes
//
//  Created by <PERSON> on 2019/4/29.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef enum : NSInteger {
    TYPE_NONE = -1, // It's same as ConnectivityManager.TYPE_NONE
    TYPE_MOBILE = 0, // ConnectivityManager.TYPE_MOBILE
    TYPE_WIFI = 1, // ConnectivityManager.TYPE_WIFI
} UPConnectionType;

@protocol UPConnectionListener <NSObject>

- (void)onConnectionChanged:(BOOL)isOnline type:(UPConnectionType)type;
@optional
@property (nonatomic, copy) void (^connectionChangedBlock)(BOOL isOnline, UPConnectionType type);
@end

NS_ASSUME_NONNULL_END

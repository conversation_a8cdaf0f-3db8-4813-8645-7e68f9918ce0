//
//  UPConnectionDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPConnectionListener.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPConnectionDelegate <NSObject>
/**
 * 添加网络变化监听者
 */
- (void)addConnectionListener:(id<UPConnectionListener>)listener;
/**
 * 移除网络变化监听者
 */
- (void)removeConnectionListener:(id<UPConnectionListener>)listener;
/**
 * 清理网络变化监听者
 */
- (void)clearConnectionListeners;

/**
 * 判断当前网络是否可用
 *
 * @return true - 可用，false - 不可用
 */
- (BOOL)isAvailable;

/**
 * 获取当前的连接类型
 *
 * @return 类型，-1 - 无连接，0 - 蜂窝网络， 1 - WIFI
 */
- (UPConnectionType)getConnectionType;

/**
 * 获取Device WAN IP
 */
+ (NSString *)getWANIPAddress;

@end

NS_ASSUME_NONNULL_END

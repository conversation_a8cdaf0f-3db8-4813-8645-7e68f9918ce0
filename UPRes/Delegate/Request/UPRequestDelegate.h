//
//  UPRequestDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPResourceInfo, UPResourceCondition;

NS_ASSUME_NONNULL_BEGIN

@protocol UPRequestDelegate <NSObject>

/// 根据请求条件，从服务器请求资源列表
/// @param condition 资源条件
/// @param immediate 是否立即
/// @param completion 完成回调
- (NSArray<UPResourceInfo *> *)requestResourceList:(UPResourceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion;


@end

NS_ASSUME_NONNULL_END

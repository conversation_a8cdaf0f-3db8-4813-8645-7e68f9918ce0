//
//  UPDownloadCallback.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@class UPDownloadHandle;
@protocol UPDownloadCallback <NSObject>

- (void)onTaskStart:(UPDownloadHandle *)handle;

- (void)onTaskCancel:(UPDownloadHandle *)handle;

- (void)onTaskSuccess:(UPDownloadHandle *)handle;

- (void)onTaskFailure:(UPDownloadHandle *)handle error:(NSError *)error;

- (void)onProgressChanged:(UPDownloadHandle *)handle progress:(CGFloat)progress;

@end

NS_ASSUME_NONNULL_END

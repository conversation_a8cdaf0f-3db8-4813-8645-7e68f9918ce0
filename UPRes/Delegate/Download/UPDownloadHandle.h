//
//  UPDownloadHandle.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UPDownloadCallback;
@interface UPDownloadHandle : NSObject

/**
 * @brief 下载连接。
 */
@property (nonatomic, copy) NSString *link;

/**
 * @brief 下载回调。
 */
@property (nonatomic, weak) id<UPDownloadCallback> callback;

- (instancetype)initHandle:(NSString *)link callback:(id<UPDownloadCallback>)callback;

@end

NS_ASSUME_NONNULL_END

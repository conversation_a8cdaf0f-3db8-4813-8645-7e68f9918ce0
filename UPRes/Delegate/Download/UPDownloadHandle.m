//
//  UPDownloadHandle.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDownloadHandle.h"
#import "UPDownloadCallback.h"

@interface UPDownloadHandle ()

@end

@implementation UPDownloadHandle
- (instancetype)initHandle:(NSString *)link callback:(nonnull id<UPDownloadCallback>)callback
{
    if (self = [super init]) {
        self.link = link;
        self.callback = callback;
    }

    return self;
}

@end

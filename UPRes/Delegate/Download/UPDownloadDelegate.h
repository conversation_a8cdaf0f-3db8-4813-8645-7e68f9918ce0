//
//  UPDownloadDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class UPDownloadHandle;
@protocol UPDownloadCallback;

NS_ASSUME_NONNULL_BEGIN
@protocol UPConnectionDelegate;
@protocol UPDownloadDelegate <NSObject>

/**
 * @brief 创建下载任务
 * @param link 待下载的文件远程链接
 * @param folder 文件本地存储目录
 * @param fileName 文件名
 * @param callback 下载回调
 * @return 下载任务手柄
 */
- (UPDownloadHandle *)createUPDownloadHandle:(NSString *)link folder:(NSString *)folder fileName:(NSString *)fileName callback:(id<UPDownloadCallback>)callback;

/**
 * @brief 开始指定下载任务
 * @param handle 下载任务手柄
 */
- (void)start:(UPDownloadHandle *)handle;

/**
 * @brief 取消指定下载任务。注：只会取消任务，并不会清理已下载的数据
 * @param handle 下载任务手柄
 */
- (void)cancel:(UPDownloadHandle *)handle;
/**
 * @brief 设置下载重试次数
 * @param retryCount 次数
*/
- (void)setDownloadRetryCount:(NSInteger)retryCount;
/**
 * @brief 设置下载重试间隔时间
 * @param retryDealy 时间间隔
*/
- (void)setDownloadRetryDealy:(NSInteger)retryDealy;

/**
 * @brief 设置网络变化监控
 * @param connectionMonitor 网络变化监控
*/
- (void)setConnectionMonitor:(id<UPConnectionDelegate>)connectionMonitor;
@end

NS_ASSUME_NONNULL_END

//
//  UPFileDelegate.h
//  UPRes
//
//  Created by <PERSON> on 2019/4/29.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

static NSString *const HASH_ALGORITHM_MD5 = @"MD5";
static NSString *const HASH_ALGORITHM_SHA1 = @"SHA-1";

@protocol UPFileDelegate <NSObject>

/**
 * @brief 判断指定路径的文件或目录是否存在
 * @param path 路径
 * @return YES - 存在，NO - 不存在
 */
- (BOOL)exists:(NSString *)path;

/**
 * @brief 按指定路径创建目录
 * @param path 路径
 * @return YES - 成功，NO - 失败。
 */
- (BOOL)mkdirs:(NSString *)path;

/**
 * @brief 删除指定路径的文件或目录
 * @param path 路径
 * @return YES - 成功，NO - 失败。
 */
- (BOOL)deletePath:(NSString *)path;

/**
 * @brief 重命名/移动源路径文件到目标路径
 * @param source 源路径
 * @param target 目标路径
 * @return YES - 成功，NO - 失败。
 */
- (BOOL)rename:(NSString *)source to:(NSString *)target;

/**
 * @brief 判断指定路径是否是文件
 * @param path 路径
 * @return YES - 是，NO - 否。
 */
- (BOOL)isFile:(NSString *)path;

/**
 * @brief 判断指定路径是否是目录
 * @param path 路径
 * @return YES - 是，NO - 否。
 */
- (BOOL)isDirectory:(NSString *)path;

/**
 * @brief 获取指定路径的父路径
 * @param path 路径
 * @return 父路径
 */
- (NSString *)getParentPath:(NSString *)path;

/**
 * @brief 获取指定路径的文件名
 * @param path 路径
 * @return 文件名
 */
- (NSString *)getFilename:(NSString *)path;

/**
 * @brief 列出指定路径下所有文件的名称
 * @param path 路径
 * @return 文件名列表
 */
- (NSArray<NSString *> *)listSubNames:(NSString *)path;

/**
 * @brief 列出指定路径下所有文件的路径
 * @param path 路径
 * @return 路径列表
 */
- (NSArray<NSString *> *)listSubPaths:(NSString *)path;

/**
 * @brief 解压指定路径的压缩包到目标目录
 * @param source 压缩文件路径
 * @param target 目标目录
 * @return YES - 成功，NO - 失败
 */
- (BOOL)unzip:(NSString *)source to:(NSString *)target;

/**
 * @brief 复制文件
 * @param source 原文件路径
 * @param target 目标文件路径
 * @return YES - 成功，NO - 失败
 */
- (BOOL)copy:(NSString *)source to:(NSString *)target;

/**
 * @brief 将可写入对象写入指定路径的文件
 * @param object 可进行文件写操作的对象
 * @param target 目标文件路径
 * @return YES - 成功，NO - 失败
 */
- (BOOL)writeObject:(NSObject *)object to:(NSString *)target;

/**
 * @brief 计算指定文件的摘要值
 * @param path      路径
 * @param algorithm 算法
 * @return 十六进制摘要字符串
 */
- (NSString *)calcHash:(NSString *)path algorithm:(NSString *)algorithm;

/**
 * @brief 使用MD5算法计算摘要值
 * @param path 路径
 * @return 十六进制摘要字符串
 */
- (NSString *)calcMD5:(NSString *)path;

/**
 * @brief 使用SHA-1算法计算摘要值
 * @param path 路径
 * @return 十六进制摘要字符串
 */
- (NSString *)calcSHA1:(NSString *)path;
/**
 * @brief 计算单个文件路径下的存储大小
 * @param path 文件路径
 * @return 文件大小，比特
 */
- (unsigned long long)getFileSize:(NSString *)path;

@end

NS_ASSUME_NONNULL_END

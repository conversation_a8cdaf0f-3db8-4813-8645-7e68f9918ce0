//
//  UPDatabaseDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceQuery.h"
#import "UPResourceInfo.h"
#import "UPResourceReporterSetter.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPDatabaseDelegate <UPResourceReporterSetter>

/**
 * @brief 插入查询条件
 * @param query 查询条件对象
 * @return 成功返回YES，否则返回NO
 */
- (BOOL)insertResourceQuery:(UPResourceQuery *)query;

/**
 * @brief 删除查询条件
 * @param query 查询条件对象
 * @return 成功返回YES，否则返回NO
 */
- (BOOL)deleteResourceQuery:(UPResourceQuery *)query;

/**
 * @brief 更新查询条件
 * @param query 查询条件对象
 * @return 成功返回YES，否则返回NO
 */
- (BOOL)updateResourceQuery:(UPResourceQuery *)query;

/**
 * @brief 按指定参数获取查询条件
 *
 * @param fromFunc   查询接口标识
 * @param appVersion APP版本号
 * @param conditionStr  查询条件字符串
 * @return 返回与参数对应的查询条件对象，若未找到则返回nil.
 */
- (UPResourceQuery *)searchResourceQuery:(NSString *)fromFunc appVersion:(NSString *)appVersion conditionStr:(NSString *)conditionStr;

/**
 根据资源数组反查查询相关的查询条件
 @param infoList 资源数组
 @return 返回与查询参数对应的查询条件对象数组，未找到返回nil
 */
- (NSArray<UPResourceQuery *> *)searchResourceQuery:(NSArray<UPResourceInfo *> *)infoList;
/**
 * @brief 插入资源信息
 * @param info 资源信息
 * @return YES - 成功，NO - 失败
 */
- (BOOL)insertResourceInfo:(UPResourceInfo *)info;

/**
 * 根据查询条件获取资源列表
 *
 * @param query 查询条件
 * @return 资源列表
 */
- (NSArray<UPResourceInfo *> *)searchResourceList:(UPResourceQuery *)query;

/**
 * 根据资源类型和名称查询资源列表
 *
 * @param type 类型，传UPResourceTypeAll表示查全部资源
 * @param name 名称，传nil表示查询指定类型的全部资源
 * @return 资源列表
 */

- (NSArray<UPResourceInfo *> *)searchResourceList:(UPResourceType)type name:(NSString *__nullable)name;

/**
 * @brief 查询指定资源信息
 * @param type    类型
 * @param name    名称
 * @param version 版本
 * @return 资源信息
 */
- (nullable UPResourceInfo *)searchResourceInfo:(UPResourceType)type name:(NSString *)name version:(NSString *)version;

/**
 * @brief 更新资源信息
 * @param info 资源信息
 * @return YES - 成功，NO - 失败
 */
- (BOOL)updateResourceInfo:(UPResourceInfo *)info;

/**
 * @brief 删除资源信息
 * @param info 资源信息
 * @return YES - 成功，NO - 失败
 */
- (BOOL)deleteResourceInfo:(UPResourceInfo *)info;

/// 删除资源列表
/// @param infoList 资源列表
- (BOOL)deleteResourceInfos:(NSArray<UPResourceInfo *> *)infoList;
/**
 * @brief 插入查询条件和资源信息的关系
 * @param query 查询条件
 * @param info  资源信息
 * @return YES - 成功，NO - 失败
 */
- (BOOL)insertRelationBetweenQuery:(UPResourceQuery *)query andResourceInfo:(UPResourceInfo *)info;

/**
 * @brief 按查询条件删除与资源信息的关系
 * @param query 查询条件
 * @return YES - 成功，NO - 失败
 */
- (BOOL)deleteRelation:(UPResourceQuery *)query;

/// 删除条件列表
/// @param queryList 条件列表
- (BOOL)deleteQuerys:(NSArray<UPResourceQuery *> *)queryList;
/**
 * @brief 清空数据库
 */
- (BOOL)emptyAllData;

/**
 * @brief 清理数据库时保护数据库中预置资源数据
 * @param infoList 预置资源列表
 * @return 清除结果
 */
- (BOOL)cleanDataKeepPresetRes:(NSArray<UPResourceInfo *> *)infoList;

/**
 * @brief 查询所有的查询条件
 * @return 查询条件数组
 * @since 2.15.10
 */
- (NSArray<UPResourceQuery *> *)searchAllQuerys;

/// 查询指定状态资源
/// @param status 状态，必填
/// @param name 名称，nil 代表名称不限
/// @param type 类型，传UPResourceTypeAll表示查全部资源
/// @param version 版本，nil 表示版本不限
- (NSArray<UPResourceInfo *> *)searchResourceInfoByStatus:(UPResourceStatus)status name:(NSString *_Nullable)name type:(UPResourceType)type version:(NSString *_Nullable)version;

@end

NS_ASSUME_NONNULL_END

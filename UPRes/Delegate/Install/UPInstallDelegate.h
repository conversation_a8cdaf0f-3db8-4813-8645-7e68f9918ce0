//
//  UPInstallDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPResourceInfo;
@protocol UPResourceCallback
, UPResourceListener, UPPresetFileLoader;

NS_ASSUME_NONNULL_BEGIN

@protocol UPInstallDelegate <NSObject>

@property (nonatomic, strong) id<UPPresetFileLoader> scanner;

/// 安装资源
/// @param info 待安装资源对象
/// @param callback 安装回调
/// @param listener 进度监听
- (NSString *)install:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener> _Nullable)listener;
@end

NS_ASSUME_NONNULL_END

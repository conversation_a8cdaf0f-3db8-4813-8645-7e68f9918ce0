//
//  UPResourceRelationDelegate.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/28.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRelationDelegate.h"
#import "UPDatabaseDelegate.h"
#import "UPTimeDelegate.h"
@interface UPResourceRelationDelegate ()
@property (nonatomic, strong) id<UPDatabaseDelegate> databaseDelegate;
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
@end

@implementation UPResourceRelationDelegate

- (instancetype)initRelationDelegate:(id<UPDatabaseDelegate>)databaseDelgate timeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    if (self = [super init]) {
        _databaseDelegate = databaseDelgate;
        _timeDelegate = timeDelegate;
    }
    return self;
}

- (nullable NSArray<UPResourceInfo *> *)relationResList:(UPResourceQuery *)query isPreset:(BOOL)isPreset infoList:(NSArray<UPResourceInfo *> *)infoList
{
    if (!query) {
        return nil;
    }
    if (!isPreset) {
        BOOL deletResult = [self.databaseDelegate deleteRelation:query];
        if (deletResult == NO) {
            return nil;
        }
    }
    return [self relation:query isPreset:isPreset infoList:infoList];
}
- (NSArray<UPResourceInfo *> *)relation:(UPResourceQuery *)query isPreset:(BOOL)isPreset infoList:(NSArray<UPResourceInfo *> *)infoList
{
    NSMutableArray *relationList = [NSMutableArray array];
    NSTimeInterval currentTimeMills = self.timeDelegate.currentTimeMillis / 1000.0;
    __block BOOL result = YES;
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      UPResourceInfo *oldInfo = [self findDataBaseResourceInfoWithType:obj.type name:obj.name version:obj.version];
      if (!oldInfo) {
          obj.createTime = currentTimeMills;
          obj.updateTime = currentTimeMills;
          result = [self.databaseDelegate insertResourceInfo:obj];
          if (result) {
              oldInfo = [self.databaseDelegate searchResourceInfo:obj.type name:obj.name version:obj.version];
              result = oldInfo ? YES : NO;
          }
      }
      else if ([self isUpdate:isPreset oldInfo:oldInfo]) {
          if (oldInfo.active == NO) {
              oldInfo.updateTime = currentTimeMills;
              oldInfo.path = nil;
              oldInfo.hashStr = obj.hashStr;
              oldInfo.link = obj.link;
              oldInfo.preset = obj.preset;
              oldInfo.indexPath = nil;
              oldInfo.resStatus = obj.resStatus;
              result = [self.databaseDelegate updateResourceInfo:oldInfo];
          }
          else {
              result = YES;
          }
      }
      else {
          oldInfo.updateTime = currentTimeMills;
          oldInfo.model = obj.model;
          oldInfo.deviceTypeIndex = obj.deviceTypeIndex;
          oldInfo.typeId = obj.typeId;
          oldInfo.prodNo = obj.prodNo;
          if (![self equalHashStr:obj.hashStr oldHashStr:oldInfo.hashStr]) {
              oldInfo.path = nil;
              oldInfo.active = NO;
              oldInfo.hashStr = obj.hashStr;
              oldInfo.link = obj.link;
              oldInfo.indexPath = nil;
          }
          oldInfo.link = obj.link;
          oldInfo.preset = obj.preset;
          oldInfo.resStatus = obj.resStatus;
          result = [self.databaseDelegate updateResourceInfo:oldInfo];
      }
      if (result == NO) {
          *stop = YES;
          return;
      }
      [relationList addObject:oldInfo];
      result = [self.databaseDelegate insertRelationBetweenQuery:query andResourceInfo:oldInfo];
      if (result == NO) {
          *stop = YES;
      }
    }];
    return result ? relationList : nil;
}
- (UPResourceInfo *_Nullable)findDataBaseResourceInfoWithType:(UPResourceType)type name:(NSString *)name version:(NSString *)version
{
    UPResourceInfo *oldInfo = [self.databaseDelegate searchResourceInfo:type name:name version:version];
    if (!oldInfo) {
        oldInfo = [self.databaseDelegate searchResourceInfoByStatus:UPResourceStatusOFF name:name type:type version:version].firstObject;
    }
    return oldInfo;
}
- (BOOL)isUpdate:(BOOL)isPreset oldInfo:(UPResourceInfo *)oldInfo
{
    return isPreset && oldInfo.preset != YES;
}
- (BOOL)equalHashStr:(NSString *)newHashStr oldHashStr:(NSString *)oldHashStr
{
    return newHashStr.length && [oldHashStr isEqualToString:newHashStr];
}
@end

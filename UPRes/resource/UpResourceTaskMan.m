//
//  UpResourceTaskMan.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceTaskMan.h"
#import "UPResourceInfo.h"
#import "UPResourceCallback.h"
#import "UPResourceListener.h"
#import "UPResourceOperator.h"
#import "UPResourceTask.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
static NSInteger const EXECUTETASKCOUNT = 5;
@interface UpResourceTaskMan ()

/**
 存储任务的字典
 **/
@property (nonatomic, strong) NSMutableDictionary *taskInfo;

/**
 执行任务的数组
 **/
@property (nonatomic, strong) NSMutableArray *excuteTasks;
/**
 等待任务的数组
 **/
@property (nonatomic, strong) NSMutableArray *waitTasks;

@end
@implementation UpResourceTaskMan

- (instancetype)init
{

    if (self = [super init]) {

        self.taskInfo = [NSMutableDictionary dictionary];
        self.excuteTasks = [NSMutableArray array];
        self.waitTasks = [NSMutableArray array];
    }
    return self;
}

- (NSString *_Nullable)createOrListen:(UPResourceInfo *)info operator:(id<UPResourceOperator>) operator callback:(id<UPResourceCallback>)callback listener:(nullable id<UPResourceListener>)listener
{

    UPResourceTask *task = [self getTask:info];

    if (task != nil) {

        UPResourceOperatorBase *existOp = task.resourceOperator;
        if (![existOp.name isEqualToString:operator.name]) {
            UPLogError(@"UPResource", @"资源忙，%@操作正在进行", existOp.name);
            [self message:[NSString stringWithFormat:@"资源忙，%@操作正在进行", existOp.name] callback:callback];
            return nil;
        }

        [existOp.callback add:callback];
        [existOp.listener add:listener];
    }
    else {
        task = [UPResourceTask resourceTask:info operator:operator];

        @synchronized(self.taskInfo)
        {
            [self.taskInfo setObject:task forKey:task.taskId];
            [self addExecuteTasks:task];
        }
    }

    return task.taskId;
}
- (void)message:(NSString *)message callback:(id<UPResourceCallback>)callback
{
    if ([callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
        [callback onResult:NO message:message resourceInfo:nil];
    }
}

- (void)addExecuteTasks:(UPResourceTask *)task
{
    if (self.excuteTasks.count < EXECUTETASKCOUNT) {
        [self.excuteTasks addObject:task];
        [self executeTask:task];
    }
    else {
        if (task.resourceInfo.priority == UPResourceDownLoadPriorityHeight) {
            [self.waitTasks insertObject:task atIndex:0];
        }
        else if (task.resourceInfo.priority == UPResourceDownLoadPriorityMid) {
            [self.waitTasks addObject:task];
            [self.waitTasks sortUsingComparator:^NSComparisonResult(UPResourceTask *obj1, UPResourceTask *obj2) {
              return obj1.resourceInfo.priority < obj2.resourceInfo.priority;
            }];
        }
        else {
            [self.waitTasks addObject:task];
        }
    }
}

- (NSString *_Nullable)getTaskId:(UPResourceInfo *)info
{

    UPResourceTask *task = [self getTask:info];

    return [task isKindOfClass:[UPResourceTask class]] ? task.taskId : nil;
}

- (BOOL)cancel:(NSString *)taskId
{

    if (UPRes_isEmptyString(taskId)) {
        return NO;
    }
    @synchronized(self.taskInfo)
    {
        UPResourceTask *task = self.taskInfo[taskId];
        if ([task isKindOfClass:[UPResourceTask class]]) {
            [task cancel];
            [self.taskInfo removeObjectForKey:taskId];
            [self.excuteTasks removeObject:task];
            [self.waitTasks removeObject:task];
            return YES;
        }
        return NO;
    }
}

- (UPResourceTask *_Nullable)getTask:(UPResourceInfo *)info
{

    if (!info) {

        return nil;
    }

    NSString *name = info.name;
    NSString *version = info.version;
    NSString *type = [UPResourceInfo stringValueOfResourceType:info.type];
    if (UPRes_isEmptyString(name) || UPRes_isEmptyString(version) || UPRes_isEmptyString(type)) {
        return nil;
    }
    @synchronized(self.taskInfo)
    {

        if (self.taskInfo.allValues.count == 0) {

            return nil;
        }

        return [self fetchTaskByName:name version:version type:info.type];
    }
}
- (UPResourceTask *)fetchTaskByName:(NSString *)name version:(NSString *)version type:(UPResourceType)type
{

    for (UPResourceTask *task in self.taskInfo.allValues) {
        UPResourceInfo *resInfo = task.resourceInfo;
        if (UPRes_isEqualString(name, resInfo.name) && resInfo.type == type && UPRes_isEqualString(version, resInfo.version)) {
            return task;
        }
    }
    return nil;
}
- (void)executeTask:(UPResourceTask *)task
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      [task execute];
      @synchronized(self.taskInfo)
      {
          [self.taskInfo removeObjectForKey:task.taskId];
          [self.excuteTasks removeObject:task];
          [self checkTasks];
      }
    });
}
- (void)checkTasks
{
    if (self.waitTasks.count) {
        UPResourceTask *waitTask = self.waitTasks.firstObject;
        [self.excuteTasks addObject:waitTask];
        [self.waitTasks removeObject:waitTask];
        dispatch_async(dispatch_get_main_queue(), ^{
          [self executeTask:waitTask];
        });
    }
}
- (void)clearTasks
{
    @synchronized(self.taskInfo)
    {

        for (UPResourceTask *task in self.taskInfo.allValues) {
            [task cancel];
        }
        [self.taskInfo removeAllObjects];
        [self.waitTasks removeAllObjects];
        [self.excuteTasks removeAllObjects];
    }
}
@end

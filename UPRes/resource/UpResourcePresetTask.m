//
//  UpResourcePresetTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourcePresetTask.h"
#import <UPLog/UPLog.h>
#import "UPResourcePresetHelper.h"
#import "UPResourceListCallback.h"
@interface UpResourcePresetTask ()

/**
 scanner
 **/
@property (nonatomic, strong) id<UPPresetFileLoader> scanner;
/**
 appVersion
 **/
@property (nonatomic, copy) NSString *appVersion;
/**
 repository
 **/
@property (nonatomic, strong) UPResourceRepository *repository;

/**
 fileDelegate
 **/
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
/**
 timeDelegate
 **/
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
/**
 reporterDelegate
 **/
@property (nonatomic, strong) id<UPResourceReporter> reporterDelegate;

@end

@implementation UpResourcePresetTask

- (instancetype)initUpResourcePresetTask:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener
{

    return [super initUpResourceBatchTask:creator taskMan:taskMan callback:callback listener:listener];
}

- (void)setScanner:(id<UPPresetFileLoader>)scanner
{

    _scanner = scanner;
}
- (void)setAppVersion:(NSString *)appVersion
{
    _appVersion = appVersion;
}
- (void)setRepository:(UPResourceRepository *)repository
{
    _repository = repository;
}
- (void)setFileDelegate:(id<UPFileDelegate>)fileDelegate
{
    _fileDelegate = fileDelegate;
}
- (void)setTimeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    _timeDelegate = timeDelegate;
}

- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate
{
    _reporterDelegate = reporterDelegate;
}
#pragma mark override
- (void)execute
{
    UPResourcePresetHelper *preHelper = [[UPResourcePresetHelper alloc] initScanner:self.scanner repository:self.repository fileDelegate:self.fileDelegate appVersion:self.appVersion timeDelegate:self.timeDelegate];
    [preHelper setReporterDelegate:self.reporterDelegate];
    if (![self.scanner conformsToProtocol:@protocol(UPPresetFileLoader)]) {
        NSString *message = @"预置资源文件扫描器对象不符合协议要求！";
        [self result:NO message:message callback:[self getCallback] infoList:nil];
        return;
    }
    @try {
        NSArray<UPResourceInfo *> *infoList = [preHelper getPresetInfos];
        if (infoList == nil) {
            NSString *message = @"扫描文件异常,安装失败";
            [self result:NO message:message callback:[self getCallback] infoList:nil];
            return;
        }
        if (infoList.count == 0) {
            NSString *message = @"没有需要安装的预置资源";
            [self result:YES message:message callback:[self getCallback] infoList:infoList];
            return;
        }
        BOOL relationResult = [preHelper presetInfoRelations:infoList];
        if (relationResult == NO) {
            NSString *meesage = @"关联资源关系失败";
            [self result:NO message:meesage callback:[self getCallback] infoList:nil];
            return;
        }
        NSArray<UPResourceInfo *> *needInstallList = [preHelper getNeedInstallResource:infoList];
        if (needInstallList.count == 0) {
            NSString *message = @"安装资源成功";
            [self result:YES message:message callback:[self getCallback] infoList:infoList];
            return;
        }
        [self setInfoList:needInstallList];
    }
    @catch (NSException *exception) {

        UPLogWarning(@"UPResource", @"scanPresetFileList Failed%@", exception.reason);
    }
    @finally {
    }
    [super execute];
}
#pragma mark privateMethods
- (void)result:(BOOL)result message:(NSString *)message callback:(id<UPResourceListCallback>)callback infoList:(NSArray<UPResourceInfo *> *)infoList
{
    if ([callback conformsToProtocol:@protocol(UPResourceListCallback)] && [callback respondsToSelector:@selector(onResult:message:infoList:)]) {
        [callback onResult:result message:message infoList:infoList];
    }
    if (result == NO) {
        UPLogError(@"UPResource", @"异步预置资源发生错误，错误结果为%@", message);
    }
    else {
        UPLogInfo(@"UPResource", @"异步预置资源过程信息为%@", message);
    }
}
@end

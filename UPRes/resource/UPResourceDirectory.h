//
//  UPResourceDirectory.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceType.h"
#import "UPFileDelegate.h"

@class UPResourceInfo;
@interface UPResourceDirectory : NSObject
@property (nonatomic, copy) NSString *resourceRootFolderPath;
@property (nonatomic, readonly, copy) NSString *resourceTempFolderPath;

+ (UPResourceDirectory *)resourceDirectoryWithPath:(NSString *)rootPath fileDelegate:(id<UPFileDelegate>)fileDelegate;
- (NSString *)resourceFolderPathOfType:(UPResourceType)type;
- (NSString *)folderPathOfResource:(UPResourceInfo *)info;
- (NSString *)downloadFolderPathOfResource:(nonnull UPResourceInfo *)info;
- (NSString *)copyPathForDirectoryResource:(UPResourceInfo *)info;
- (UPResourceType)getResourceTypeByFolderName:(NSString *)folderName;
@end

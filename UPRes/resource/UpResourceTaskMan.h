//
//  UpResourceTaskMan.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/11.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
@class UPResourceInfo, UPResourceTask;

@protocol UPResourceCallback
, UPResourceListener, UPResourceOperator;

@interface UpResourceTaskMan : NSObject


- (NSString *_Nullable)createOrListen:(UPResourceInfo *)info operator:(id<UPResourceOperator>) operator callback:(id<UPResourceCallback>)callback listener:(nullable id<UPResourceListener>)listener;

- (BOOL)cancel:(NSString *)taskId;

- (NSString *_Nullable)getTaskId:(UPResourceInfo *)info;

- (UPResourceTask *_Nullable)getTask:(UPResourceInfo *)info;

- (void)clearTasks;

@end

NS_ASSUME_NONNULL_END

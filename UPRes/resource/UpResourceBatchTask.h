//
//  UpResourceBatchTask.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UpResourceTaskMan, UPResourceInfo;
@protocol UPResourceListener
, UPResourceCallback, UPResourceOperator, UPResourceListCallback;
NS_ASSUME_NONNULL_BEGIN


typedef id<UPResourceOperator> (^OperatorCreatorBlock)(id<UPResourceCallback> callback, id<UPResourceListener> listener);

@interface UpResourceBatchTask : NSObject

/**
 批量任务结束后执行的block
 **/
@property (nonatomic, copy) dispatch_block_t compltionBlock;

- (instancetype)initUpResourceBatchTask:(nullable NSArray<UPResourceInfo *> *)infoList creator:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener;

- (instancetype)initUpResourceBatchTask:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener;

- (void)execute;

- (id<UPResourceListCallback>)getCallback;

- (void)setInfoList:(NSArray<UPResourceInfo *> *)infoList;

@end

NS_ASSUME_NONNULL_END

//
//  UPRLoadedReportInfo.m
//  UPRes
//
//  Created by 吴子航 on 2023/3/6.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPRLoadedReportInfo.h"
@interface UPRLoadedReportInfo ()

@property (nonatomic, copy) NSString *uuid;
@property (nonatomic, copy) NSString *timestamp;
@property (nonatomic, copy) NSString *resName;
@property (nonatomic, copy) NSString *resType;
@property (nonatomic, copy) NSString *resVersion;

@end
@implementation UPRLoadedReportInfo

- (instancetype)initWithName:(NSString *)resName type:(NSString *)resType version:(NSString *)resVersion timestamp:(NSString *)timestamp
{
    if (self = [super init]) {
        _uuid = [NSUUID UUID].UUIDString;
        _timestamp = timestamp;
        _resName = resName;
        _resType = resType;
        _resVersion = resVersion;
    }

    return self;
}
@end

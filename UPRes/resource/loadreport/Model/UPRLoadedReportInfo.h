//
//  UPRLoadedReportInfo.h
//  UPRes
//
//  Created by 吴子航 on 2023/3/6.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface UPRLoadedReportInfo : NSObject

/// 客户端产生的事件唯一id
@property (nonatomic, copy, readonly) NSString *uuid;
///事件产生时间戳，单位：毫秒
@property (nonatomic, copy, readonly) NSString *timestamp;
///资源名称
@property (nonatomic, copy, readonly) NSString *resName;
///资源类型
@property (nonatomic, copy, readonly) NSString *resType;
///资源版本
@property (nonatomic, copy, readonly) NSString *resVersion;

- (instancetype)initWithName:(NSString *)resName
                        type:(NSString *)resType
                     version:(NSString *)resVersion
                   timestamp:(NSString *)timestamp;
@end

NS_ASSUME_NONNULL_END

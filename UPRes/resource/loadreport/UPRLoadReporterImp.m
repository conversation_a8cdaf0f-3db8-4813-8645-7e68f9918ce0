//
//  UPRLoadReportManager.m
//  UPRes
//
//  Created by 吴子航 on 2023/3/6.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPRLoadReporterImp.h"
#import <MJExtension/MJExtension.h>
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"

static NSInteger const UPR_ReportLimitCount = 50;
static NSInteger const UPR_DebounceLimitTimeMillis = 3000;

#define UPRLoadReportKey(name, type, version) [NSString stringWithFormat:@"%@%@%@", name, type, version]

@interface UPRLoadReporterImp ()

@property (nonatomic, strong) id<UPResourceDataSource> dataSource;
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, strong) id<UPConnectionDelegate> connectionDelegate;
@property (nonatomic, assign) BOOL isReporting;
@property (nonatomic, strong) NSMutableArray<UPRLoadedReportInfo *> *cache;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSNumber *> *debounce;
@property (nonatomic, strong) dispatch_queue_t reportQueue;

@end

@implementation UPRLoadReporterImp

- (instancetype)initWithDataSource:(id<UPResourceDataSource>)dataSource timeDelegate:(id<UPTimeDelegate>)timeDelegate connectionDelegate:(id<UPConnectionDelegate>)connectionDelegate
{
    if (self = [super init]) {
        _dataSource = dataSource;
        _timeDelegate = timeDelegate;
        _connectionDelegate = connectionDelegate;
        _cache = [NSMutableArray array];
        _debounce = [NSMutableDictionary dictionary];
        _reportQueue = dispatch_queue_create("com.haier.uplus.resource.report", DISPATCH_QUEUE_SERIAL);
    }

    return self;
}

#pragma mark - UPResourceReporter
- (void)track:(NSString *)resName type:(UPResourceType)resType version:(NSString *)resVersion
{
    NSString *typeStr = [UPResourceInfo stringValueOfResourceType:resType];
    if (UPRes_isEmptyString(resName) || UPRes_isEmptyString(typeStr) || UPRes_isEmptyString(resVersion)) {
        return;
    }
    if ((resType == UPResourceTypeVideo || resType == UPResourceTypeDeviceConfig || resType == UPResourceTypeConfigFile || resType == UPResourceTypeLua) &&
        ![self debounceHandle:resName type:typeStr version:resVersion]) {
        return;
    }
    UPRLoadedReportInfo *reportInfo = [[UPRLoadedReportInfo alloc] initWithName:resName type:typeStr version:resVersion timestamp:self.timestampString];
    dispatch_async(self.reportQueue, ^{
      [self.cache addObject:reportInfo];
      [self reportIfNeed];
    });
}

#pragma mark - private method
- (void)reportIfNeed
{
    if (!self.connectionDelegate.isAvailable || self.isReporting || self.cache.count == 0) {
        return;
    }
    NSArray<UPRLoadedReportInfo *> *reportData = self.prepareReportData;
    if ([reportData isKindOfClass:NSArray.class] && reportData.count && [self.dataSource respondsToSelector:@selector(reportResLoadedInfo:completion:)]) {
        self.isReporting = YES;
        [self.dataSource reportResLoadedInfo:reportData
                                  completion:^(NSError *_Nonnull error) {
                                    dispatch_async(self.reportQueue, ^{
                                      [reportData enumerateObjectsUsingBlock:^(UPRLoadedReportInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
                                        [self.cache removeObject:obj];
                                      }];
                                      self.isReporting = NO;
                                      [self reportIfNeed];
                                    });
                                  }];
    }
}

- (NSArray<UPRLoadedReportInfo *> *)prepareReportData
{
    NSRange range = NSMakeRange(0, MIN(self.cache.count, UPR_ReportLimitCount));
    NSArray<UPRLoadedReportInfo *> *reportData = [self.cache subarrayWithRange:range];
    return reportData;
}

- (BOOL)debounceHandle:(NSString *)name type:(NSString *)type version:(NSString *)version
{
    @synchronized(self.debounce)
    {
        NSString *loadReportKey = UPRLoadReportKey(name, type, version);
        NSNumber *debounceValue = self.debounce[loadReportKey];
        long currentTimeMillis = self.timeDelegate.currentTimeMillis;
        if (!debounceValue || (currentTimeMillis - debounceValue.longValue > UPR_DebounceLimitTimeMillis)) {
            self.debounce[loadReportKey] = @(currentTimeMillis);
            return YES;
        }

        return NO;
    }
}

- (NSString *)timestampString
{
    return [NSString stringWithFormat:@"%ld", self.timeDelegate.currentTimeMillis];
}
@end

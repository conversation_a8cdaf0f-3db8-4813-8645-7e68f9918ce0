//
//  UPRLoadReportManager.h
//  UPRes
//
//  Created by 吴子航 on 2023/3/6.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceDataSource.h"
#import "UPTimeDelegate.h"
#import "UPConnectionDelegate.h"
#import "UPResourceType.h"
#import "UPResourceInfo.h"
#import "UPRLoadedReportInfo.h"
#import "UPResourceReporter.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPRLoadReporterImp : NSObject <UPResourceReporter>

- (instancetype)initWithDataSource:(id<UPResourceDataSource>)dataSource
                      timeDelegate:(id<UPTimeDelegate>)timeDelegate
                connectionDelegate:(id<UPConnectionDelegate>)connectionDelegate;

@end

NS_ASSUME_NONNULL_END

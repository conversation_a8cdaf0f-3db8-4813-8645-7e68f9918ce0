//
//  UpResourceListenerHolder.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceListenerHolder.h"

@interface UpResourceListenerHolder ()

@end
@implementation UpResourceListenerHolder


- (instancetype)initWith:(id<UPResourceListener>)listener
{

    if (self = [super init]) {

        [self add:listener];
    }
    return self;
}

- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{

    NSArray<id<UPResourceListener>> *listeners = self.getAll;

    for (id<UPResourceListener> listener in listeners) {

        if ([listener conformsToProtocol:@protocol(UPResourceListener)] && [listener respondsToSelector:@selector(onProgressChanged:progress:)]) {

            [listener onProgressChanged:processor progress:progress];
        }
    }
}

@end

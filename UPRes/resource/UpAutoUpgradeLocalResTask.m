//
//  UpAutoUpgradeLocalResTask.m
//  UPRes
//
//  Created by 冉东军 on 2021/4/30.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpAutoUpgradeLocalResTask.h"
#import "UPResourceManager.h"
#import "UPResourceRepository.h"
#import <UPLog/UPLog.h>
#import "UPRequestDelegate.h"
#import "UPResCommonFunctions.h"

@interface UpAutoUpgradeLocalResTask () <UPResourceFilter>
@property (nonatomic, strong) UPResourceManager *resManager;
@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) NSArray *shouldIgnoreTypeArray;
@end

@implementation UpAutoUpgradeLocalResTask
- (instancetype)initWithResourceManager:(UPResourceManager *)manager resourceRepository:(UPResourceRepository *)repository
{
    if (self = [super init]) {

        self.resManager = manager;
        self.repository = repository;
    }
    return self;
}

- (void)start
{
    NSArray<UPResourceInfo *> *resourceList = [self.resManager getEntireList:self];
    if (!resourceList.count) {
        UPLogError(@"UPResource", @"%s[%d]自动更新本地已安装资源失败！error:本地已安装资源列表为空！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    NSArray<UPResourceQuery *> *allQueryList = [self.repository.databaseDelegate searchResourceQuery:resourceList];
    NSMutableArray<UPResourceDeviceCondition *> *deviceConditionArr = [NSMutableArray array];
    NSMutableDictionary<NSString *, NSMutableArray<NSString *> *> *normalConditonDict = [NSMutableDictionary dictionary];
    NSMutableArray<NSString *> *jointConditonArray = [NSMutableArray array];
    [allQueryList enumerateObjectsUsingBlock:^(UPResourceQuery *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.fromFunc isEqualToString:FROM_FUNC_BATCH_NORMAL_RESOURCE]) {
          return;
      }
      NSString *conditionStr = obj.conditionStr;
      NSArray *lineCountArr = [conditionStr componentsSeparatedByString:@"|"];
      NSMutableDictionary *conditionDict = UPRes_convertStringObjectWithDict(conditionStr);
      if (lineCountArr.count > 2) { //设备资源条件
          UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:conditionDict[@"rt"]]];
          condition.typeId = conditionDict[@"ti"];
          condition.model = conditionDict[@"md"];
          condition.prodNo = conditionDict[@"pn"];
          condition.deviceNetType = conditionDict[@"dnt"];
          condition.deviceType = conditionDict[@"tc"];
          condition.fromFunc = FROM_FUNC_DEVICE_RESOURCE;
          if (![jointConditonArray containsObject:conditionStr]) {
              [jointConditonArray addObject:conditionStr];
              [deviceConditionArr addObject:condition];
          }
      }
      else { //普通资源条件
          NSString *name = conditionDict[@"name"];
          if (![name isKindOfClass:[NSString class]] || name.length == 0) {
              return;
          }
          NSString *type = conditionDict[@"rt"];
          if ([UPResourceInfo typeValueOfString:type] == UPResourceTypeFlutter) {
              [self addResource:name withType:type toAutoUpgradeaDictionary:normalConditonDict];
          }
          else {
              [self addResource:name withType:[UPResourceInfo stringValueOfResourceType:UPResourceTypeMPAAS] toAutoUpgradeaDictionary:normalConditonDict];
          }
      }
    }];
    [self requestNormalResourceListWithConditon:normalConditonDict.copy];
    [self requestDeviceResourceListWithConditon:deviceConditionArr];
}

- (void)addResource:(NSString *)name withType:(NSString *)type toAutoUpgradeaDictionary:(NSMutableDictionary *)dict
{
    NSMutableArray *resNames = dict[type];
    if (!resNames) {
        resNames = [NSMutableArray array];
        [dict setObject:resNames forKey:type];
    }
    if ([resNames containsObject:name]) {
        return;
    }
    [resNames addObject:name];
}

//更新普通资源
- (void)requestNormalResourceListWithConditon:(NSDictionary *)normalConditonDict
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      // 创建信号量 1
      dispatch_semaphore_t semaphore = dispatch_semaphore_create(1);
      [normalConditonDict enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
        // 信号量减 1 ，小于 0 时卡住遍历 normalConditonDict 的线程，等待解锁后继续
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        NSString *names = [obj componentsJoinedByString:@","];
        UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:key]];
        condition.resName = names;
        condition.fromFunc = FROM_FUNC_BATCH_NORMAL_RESOURCE;
        condition.appVersion = self.resManager.appVersion;
        // 异步方法
        [self.resManager.requestDelegate requestResourceList:condition
                                                   immediate:NO
                                                  completion:^(NSArray<UPResourceInfo *> *_Nonnull infoList, NSError *_Nonnull error) {
                                                    if (!error) {
                                                        UPLogDebug(@"UPResource", @"%s[%d]更新普通资源列表成功", __PRETTY_FUNCTION__, __LINE__);
                                                    }
                                                    // 信号量加 1
                                                    dispatch_semaphore_signal(semaphore);
                                                  }];
      }];
    });
}

//更新设备资源
- (void)requestDeviceResourceListWithConditon:(NSArray *)deviceConditionArr
{
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      if (deviceConditionArr.count > 0) {
          // 创建信号量 5
          dispatch_semaphore_t semaphore = dispatch_semaphore_create(5);
          [deviceConditionArr enumerateObjectsUsingBlock:^(UPResourceDeviceCondition *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
            // 信号量减 1 ，小于 0 时卡住遍历 deviceConditionArr 的线程，等待解锁后继续
            dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
            // 异步方法
            [self.resManager requestDeviceResList:obj
                                        immediate:NO
                                       completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
                                         if (!error) {
                                             UPLogDebug(@"UPResource", @"%s[%d]更新设备资源列表成功", __PRETTY_FUNCTION__, __LINE__);
                                         }
                                         // 信号量加 1
                                         dispatch_semaphore_signal(semaphore);
                                       }];
          }];
      }
    });
}

- (BOOL)accept:(UPResourceInfo *)info
{
    return info.active && ![info.name hasPrefix:THEMEPREFIX] && ![self.shouldIgnoreTypeArray containsObject:@(info.type)];
}

- (NSArray *)shouldIgnoreTypeArray
{
    if (!_shouldIgnoreTypeArray) {
        _shouldIgnoreTypeArray = [[NSArray alloc] initWithObjects:@(UPResourceTypeDeviceConfig), @(UPResourceTypeLua), @(UPResourceTypeConfigApp), @(UPResourceTypeVideo), @(UPResourceTypeAppFuncModel), @(UPResourceTypeRoutes), @(UPResourceTypePicture), @(UPResourceTypeAudio), @(UPResourceTypeOther), @(UPResourceTypeConfigFile), nil];
    }
    return _shouldIgnoreTypeArray;
}
@end

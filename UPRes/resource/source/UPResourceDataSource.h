//
//  UPResourceDataSource.h
//  UPRes
//
//  Created by <PERSON> on 2019/5/10.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceCondition.h"
#import "UPResourceInfo.h"
#import "UPRLoadedReportInfo.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UPResourceDataSource <NSObject>
- (void)searchDeviceResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)searchDeviceConfigResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)searchDeviceLuaResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)searchDeviceCustomList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)searchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)batchSearchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)checkDynamicResStatusWithCondition:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nullable, NSError *_Nullable))completion;

- (void)searchAppFuncModelConfig:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion;
- (void)reportResLoadedInfo:(NSArray<UPRLoadedReportInfo *> *)infos completion:(void (^)(NSError *error))completion;
@end

NS_ASSUME_NONNULL_END

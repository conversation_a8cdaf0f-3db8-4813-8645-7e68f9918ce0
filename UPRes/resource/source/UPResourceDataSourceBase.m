//
//  UPResourceDataSourceBase.m
//  UPRes
//
//  Created by <PERSON> on 2019/5/13.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDataSourceBase.h"
#import <UPLog/UPLog.h>

@implementation UPResourceDataSourceBase
#pragma mark - UPResourceDataSource
- (void)searchDeviceResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)searchDeviceConfigResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)searchDeviceLuaResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)searchDeviceCustomList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)searchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}
- (void)batchSearchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}
- (void)searchAppFuncModelConfig:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)checkDynamicResStatusWithCondition:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nullable, NSError *_Nullable))completion
{
    NSError *error = [NSError errorWithDomain:@"not implement method" code:-1 userInfo:nil];
    completion(@[], error);
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}

- (void)reportResLoadedInfo:(NSArray<UPRLoadedReportInfo *> *)infos completion:(void (^)(NSError *_Nonnull))completion
{
    UPLogError(@"UPResource", @"%s[%d]该方法为空法，不应该被调用！", __PRETTY_FUNCTION__, __LINE__);
}
#pragma mark - Public Methods
- (instancetype)initDataSourceWithTestDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _isTestDataEnabled = testDataEnabled;
    }
    return self;
}

@end

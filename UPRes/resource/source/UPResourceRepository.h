//
//  UPResourceRepository.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceType.h"
#import "UPResEnvironment.h"
#import "UPTimeDelegate.h"
#import "UPFileDelegate.h"
#import "UPResourceDataSource.h"
#import "UPResourceDirectory.h"

@protocol UPDatabaseDelegate
, UPResourceFilter, UPRelationDelegate;
@class UPResourceInfo, UPResourceCondition;
@interface UPResourceRepository : NSObject

/**
 * @brief 环境枚举值
 */
@property (nonatomic, assign) UPResEnvironment environment;

/**
 * @brief 缓存文件夹路径
 */
@property (nonatomic, readonly, copy) NSString *cacheFolderPath;

/**
 directory
 **/
@property (nonatomic, strong, readonly) UPResourceDirectory *directory;
/**
 autoUpdateInterval
 **/
@property (nonatomic, assign) NSTimeInterval autoUpdateInterval;
@property (nonatomic, strong, readonly) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) id<UPRelationDelegate> relationDelegate;
@property (nonatomic, strong, readonly) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, strong, readonly) id<UPDatabaseDelegate> databaseDelegate;

- (instancetype)initRepositoryWithTimeDelegate:(id<UPTimeDelegate>)timeDelegate
                                  fileDelegate:(id<UPFileDelegate>)fileDelegate
                                     directory:(UPResourceDirectory *)directory
                                    dataSource:(id<UPResourceDataSource>)dataSource
                              databaseDelegate:(id<UPDatabaseDelegate>)databaseDelegate
                            autoUpdateInterval:(NSTimeInterval)autoUpdateInterval;

- (BOOL)presetResouceList:(UPResourceCondition *)condition infoList:(NSArray<UPResourceInfo *> *)infoList;

- (NSArray<UPResourceInfo *> *)requestResourceList:(UPResourceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion;


- (BOOL)updateResourceInfo:(UPResourceInfo *)info;


- (BOOL)cleanLocalData;


- (NSArray<UPResourceInfo *> *)getEntireList:(id<UPResourceFilter>)filter;

- (NSArray<UPResourceInfo *> *)getLatestList:(id<UPResourceFilter>)filter;

- (UPResourceInfo *)getLatestInfoByName:(NSString *)name type:(UPResourceType)type;

- (UPResourceInfo *)getLatestInstalledInfo:(NSString *)name type:(UPResourceType)type;

- (NSArray<UPResourceInfo *> *)getLatestInstalledList:(BOOL (^)(UPResourceInfo *info))filter;

- (UPResourceInfo *)getResourcInfo:(NSString *)name type:(UPResourceType)type version:(NSString *)version;
- (void)setAppVersion:(NSString *)appVersion;
- (NSArray<UPResourceInfo *> *)relationInfoList:(UPResourceCondition *)condition infoList:(NSArray<UPResourceInfo *> *)infoList;
- (NSArray<UPResourceInfo *> *)searchResList:(UPResourceCondition *)condition;
/**
 * @brief 关系清理时添加保护预置关系不被清除。
 * @param keepResInfoList 需要保护的资源列表
 * @return 清除成功返回YES，否则返回NO
 * @since 2.12.0
 */
- (BOOL)cleanDataKeepPresetRes:(NSArray<UPResourceInfo *> *)keepResInfoList;

/**
 * @brief 同类资源中筛选出最新已安装资源
 * @param resourceList 相同name、type资源列表
 * @return 最新已安装资源
 */
- (UPResourceInfo *)filterLatestInstallResourceBySameNameType:(NSArray<UPResourceInfo *> *)resourceList;
@end

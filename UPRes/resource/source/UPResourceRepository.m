//
//  UPResourceRepository.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRepository.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "UPResourceCondition.h"
#import "UPResourceQuery.h"
#import "UPDatabaseDelegate.h"
#import "UPResourceHelper.h"
#import "UPResourceDataSourceBase.h"
#import "UPResourceInjection.h"
#import "UPRelationDelegate.h"

@interface UPResourceRepository ()
/**
 databaseDelegate
 **/
@property (nonatomic, strong) id<UPResourceDataSource> dataSource;

/**
 当前app版本
 **/
@property (nonatomic, copy) NSString *appVersion;

@end

@implementation UPResourceRepository
#pragma mark - Non-Public Methods

- (BOOL)isNeedUpdate:(UPResourceQuery *)query conditionStr:(NSString *)conditionStr
{

    if (self.environment == UPResEnvironmentTest) {

        return YES;
    }
    if (!query) {

        return YES;
    }
    NSTimeInterval timeInterval = [self.timeDelegate currentTimeMillis] / 1000.0;
    if ((timeInterval - query.updateTime) > self.autoUpdateInterval) {
        return YES;
    }
    UPLogInfo(@"UPResource", @"%s[%d]当前时间戳为%.f,条件更新的时间为%.f", __PRETTY_FUNCTION__, __LINE__, timeInterval, query.updateTime);
    return !UPRes_isEqualString(query.conditionStr, conditionStr);
}

#pragma mark - Override Methods
- (instancetype)initRepositoryWithTimeDelegate:(id<UPTimeDelegate>)timeDelegate
                                  fileDelegate:(id<UPFileDelegate>)fileDelegate
                                     directory:(UPResourceDirectory *)directory
                                    dataSource:(id<UPResourceDataSource>)dataSource
                              databaseDelegate:(id<UPDatabaseDelegate>)databaseDelegate
                            autoUpdateInterval:(NSTimeInterval)autoUpdateInterval
{
    if (self = [super init]) {
        _cacheFolderPath = directory.resourceRootFolderPath;
        _environment = UPResEnvironmentProduction;
        if ([dataSource isKindOfClass:[UPResourceDataSourceBase class]] && ((UPResourceDataSourceBase *)dataSource).isTestDataEnabled) {
            _environment = UPResEnvironmentTest;
        }
        _timeDelegate = timeDelegate;
        _fileDelegate = fileDelegate;
        _dataSource = dataSource;
        _databaseDelegate = databaseDelegate;
        _autoUpdateInterval = autoUpdateInterval;
        _directory = directory;
    }
    return self;
}

#pragma mark private Method
- (UPResourceQuery *)createQuery:(UPResourceCondition *)condition
{

    UPResourceQuery *query = [[UPResourceQuery alloc] init];
    query.fromFunc = condition.fromFunc;
    query.conditionStr = condition.combine;
    query.appVersion = condition.appVersion;
    return query;
}

- (BOOL)isKeepPresetResPath:(NSString *)path keepList:(NSArray<UPResourceInfo *> *)keepList
{
    if (!keepList.count) {
        return NO;
    }
    NSString *pathName = path.lastPathComponent;
    if (!pathName) {
        return NO;
    }
    for (UPResourceInfo *info in keepList) {
        if ([info.path containsString:pathName]) {
            return YES;
        }
    }

    return NO;
}

- (BOOL)deleteUnKeepPresetResPath:(NSString *)path keepList:(NSArray<UPResourceInfo *> *)keepList
{
    __block BOOL result = YES;
    id<UPFileDelegate> fileDelegate = self.fileDelegate;
    NSArray<NSString *> *pathList = [fileDelegate listSubPaths:path];
    [pathList enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      BOOL isDelete = YES;
      for (UPResourceInfo *info in keepList) {
          NSString *pathStr = info.path;
          if (pathStr && [obj containsString:pathStr]) {
              isDelete = NO;
              break;
          }
      }
      if (isDelete) {
          if (![obj.pathExtension isEqualToString:@"db"]) {
              result = [fileDelegate deletePath:obj];
          }
      }
    }];
    return result;
}

#pragma mark public Method

- (BOOL)presetResouceList:(UPResourceCondition *)condition infoList:(NSArray<UPResourceInfo *> *)infoList
{

    UPResourceQuery *query = [self createQuery:condition];
    query.updateTime = 0;
    query.createTime = 0;
    NSArray<UPResourceInfo *> *list = [self.relationDelegate relationResList:query isPreset:YES infoList:infoList];
    return list ? YES : NO;
}
- (NSArray<UPResourceInfo *> *)searchResList:(UPResourceCondition *)condition
{
    UPResourceQuery *query = [self createQuery:condition];
    UPResourceQuery *localQuery = [self.databaseDelegate searchResourceQuery:condition.fromFunc appVersion:condition.appVersion conditionStr:condition.combine];
    NSArray<UPResourceInfo *> *infoList;
    if (localQuery) {
        infoList = [self.databaseDelegate searchResourceList:query];
    }
    return infoList;
}

- (NSArray<UPResourceInfo *> *)requestResourceList:(UPResourceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPResourceQuery *query = [self createQuery:condition];
    query.updateTime = self.timeDelegate.currentTimeMillis / 1000.0;
    query.createTime = self.timeDelegate.currentTimeMillis / 1000.0;
    UPResourceQuery *localQuery = [self.databaseDelegate searchResourceQuery:condition.fromFunc appVersion:condition.appVersion conditionStr:condition.combine];
    NSArray<UPResourceInfo *> *infoList;
    if (localQuery) {
        infoList = [self.databaseDelegate searchResourceList:query];
    }
    NSError *error;
    if (!infoList) {
        error = [NSError errorWithDomain:@"查询本地infoList失败" code:-1 userInfo:nil];
    }
    else if (infoList.count > 1) {
        infoList = [infoList sortedArrayUsingComparator:^NSComparisonResult(UPResourceInfo *_Nonnull obj1, UPResourceInfo *_Nonnull obj2) {
          return obj1.isServerLatest < obj2.isServerLatest;
        }];
    }
    if (immediate || [self isNeedUpdate:localQuery conditionStr:query.conditionStr]) {
        [self updateInfoListInBackground:condition query:query completion:completion];
    }
    else {
        if (completion) {
            completion(infoList, error);
        }
    }
    if (self.environment == UPResEnvironmentTest) {
        return nil;
    }
    return infoList;
}

- (void)updateInfoListInBackground:(UPResourceCondition *)condition query:(UPResourceQuery *)query completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    void (^resCompletion)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull) = ^(NSArray<UPResourceInfo *> *_Nonnull resources, NSError *_Nonnull error) {
      [self.dataSource checkDynamicResStatusWithCondition:condition
                                               completion:^(NSArray<UPResourceInfo *> *resList, NSError *error) {
                                                 if (error) {
                                                     return;
                                                 }
                                                 [self updateLocalResStatus:resList];
                                               }];
      if (error) {
          if (completion) {
              completion(nil, error);
          }
          return;
      }
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSArray<UPResourceInfo *> *infoList = resources;
        [self updateResourceListIsServerLatestState:infoList];
        infoList = [self.relationDelegate relationResList:query isPreset:NO infoList:infoList];
        NSError *err;
        if (!infoList) {
            err = [NSError errorWithDomain:@"关联资源infoList失败" code:-1 userInfo:nil];
        }
        if (completion) {
            dispatch_async(dispatch_get_main_queue(), ^{
              completion(infoList, err);
            });
        }
      });
    };

    [self searchResListSwitchWith:condition resCompletion:resCompletion];
}

- (void)searchResListSwitchWith:(UPResourceCondition *)condition resCompletion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))resCompletion
{
    NSInteger func = [UPResourceCondition numberValueOfFromfunc:condition.fromFunc];
    switch (func) {
        case 0:
            [self.dataSource searchNormalResList:condition completion:resCompletion];
            break;
        case 1:
            [self.dataSource searchDeviceConfigResList:condition completion:resCompletion];
            break;
        case 2:
            [self.dataSource searchDeviceResList:condition completion:resCompletion];
            break;
        case 3:
            [self.dataSource searchDeviceCustomList:condition completion:resCompletion];
            break;
        case 4:
            [self.dataSource batchSearchNormalResList:condition completion:resCompletion];
            break;
        case 6:
            [self.dataSource searchAppFuncModelConfig:condition completion:resCompletion];
            break;
        case 7:
            [self.dataSource searchDeviceLuaResList:condition completion:resCompletion];
            break;
        default:
            break;
    }
}

#pragma mark - update local resource
- (void)updateLocalResStatus:(NSArray<UPResourceInfo *> *)resList
{
    [resList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      UPResourceInfo *oldInfo = [self.databaseDelegate searchResourceInfo:obj.type name:obj.name version:obj.version];
      if (!oldInfo || oldInfo.resStatus == obj.resStatus) {
          return;
      }
      oldInfo.resStatus = obj.resStatus;
      BOOL result = [self.databaseDelegate updateResourceInfo:oldInfo];
      if (!result) {
          UPLogError(@"UPResource", @"%s[%d]数据库更新resStatus信息失败！", __PRETTY_FUNCTION__, __LINE__);
      }
    }];
}

- (BOOL)updateResourceInfo:(UPResourceInfo *)info
{
    info.updateTime = self.timeDelegate.currentTimeMillis / 1000.0;
    return [self.databaseDelegate updateResourceInfo:info];
}

- (BOOL)cleanLocalData
{
    BOOL bDataBaseResult = [self.databaseDelegate emptyAllData];
    if (!bDataBaseResult) {
        UPLogError(@"UPResource", @"%s[%d] clean database failure！", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    id<UPFileDelegate> fileDelegate = self.fileDelegate;
    NSArray<NSString *> *pathList = [fileDelegate listSubPaths:self.cacheFolderPath];

    if (![pathList isKindOfClass:[NSArray class]] || pathList.count == 0) {
        UPLogInfo(@"UPResource", @"%s[%d] clean local data path list empty!", __PRETTY_FUNCTION__, __LINE__);
        return YES;
    }
    __block BOOL result = YES;
    [pathList enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {

      if (![obj.pathExtension isEqualToString:@"db"]) {
          result = [fileDelegate deletePath:obj];
      }
      if (!result) {
          UPLogError(@"UPResource", @"%s[%d] delete path failure！path:%@", __PRETTY_FUNCTION__, __LINE__, obj);
      }
    }];
    if (result) {
        UPLogInfo(@"UPResource", @"clean local data success");
    }
    return result;
}

- (BOOL)cleanDataKeepPresetRes:(NSArray<UPResourceInfo *> *)keepResInfoList
{
    if (!keepResInfoList.count) {
        return [self cleanLocalData];
    }

    BOOL bDataBaseResult = [self cleanDataBaseKeepPresetRes:keepResInfoList];
    if (!bDataBaseResult) {
        UPLogError(@"UPResource", @"%s[%d]clean database failure！", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }

    id<UPFileDelegate> fileDelegate = self.fileDelegate;
    NSArray<NSString *> *pathList = [fileDelegate listSubPaths:self.cacheFolderPath];
    if (![pathList isKindOfClass:[NSArray class]] || pathList.count == 0) {
        UPLogInfo(@"UPResource", @"%s[%d] clean local data path list empty!", __PRETTY_FUNCTION__, __LINE__);
        return YES;
    }

    __block BOOL result = YES;
    [pathList enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([self isKeepPresetResPath:obj keepList:keepResInfoList]) {
          result = [self deleteUnKeepPresetResPath:obj keepList:keepResInfoList];
      }
      else {
          if (![obj.pathExtension isEqualToString:@"db"]) {
              result = [fileDelegate deletePath:obj];
          }
          if (!result) {
              UPLogError(@"UPResource", @"%s[%d]delete path failure！path:%@", __PRETTY_FUNCTION__, __LINE__, obj);
          }
      }
    }];
    if (result) {
        UPLogInfo(@"UPResource", @"clean local data success");
    }
    return result;
}

- (BOOL)cleanDataBaseKeepPresetRes:(NSArray<UPResourceInfo *> *)infoList
{
    NSMutableArray<NSString *> *keepInfoIds = [NSMutableArray new];
    for (UPResourceInfo *info in infoList) {
        [keepInfoIds addObject:[NSString stringWithFormat:@"%ld", info.itemId]];
    }

    NSArray<UPResourceInfo *> *infos = [self.databaseDelegate searchResourceList:UPResourceTypeAll name:nil];
    NSMutableArray<UPResourceInfo *> *noKeepInfos = [NSMutableArray new];
    for (UPResourceInfo *info in infos) {
        NSString *infoId = [NSString stringWithFormat:@"%ld", info.itemId];
        if (![keepInfoIds containsObject:infoId]) {
            [noKeepInfos addObject:info];
        }
    }

    NSArray<UPResourceQuery *> *querys = [self.databaseDelegate searchResourceQuery:infos];
    BOOL bQueryResult = YES;
    NSMutableArray *deleteQuerys = [NSMutableArray new];
    for (UPResourceQuery *query in querys) {
        if (![keepInfoIds containsObject:query.correlationId]) {
            [deleteQuerys addObject:query];
        }
    }
    bQueryResult = [self.databaseDelegate deleteQuerys:deleteQuerys];
    BOOL bResult = [self.databaseDelegate deleteResourceInfos:noKeepInfos];

    return bResult && bQueryResult;
}

- (UPResourceInfo *)filterLatestInstallResourceBySameNameType:(NSArray<UPResourceInfo *> *)resourceList
{
    resourceList = [resourceList sortedArrayUsingComparator:^NSComparisonResult(UPResourceInfo *obj1, UPResourceInfo *obj2) {
      NSString *versionStr = obj1.version ?: @"";
      return [obj2.version compare:versionStr options:NSNumericSearch];
    }];
    __block UPResourceInfo *info;
    [resourceList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.active && obj.isServerLatest) {
          info = obj;
          *stop = YES;
      }
    }];

    if (nil == info) {
        //compiliant with old algorithom
        [resourceList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          if (obj.active) {
              info = obj;
              *stop = YES;
          }
        }];
    }

    return info;
}

- (NSArray<UPResourceInfo *> *)getEntireList:(id<UPResourceFilter>)filter
{

    NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:UPResourceTypeAll name:nil];
    return [UPResourceHelper filterInfoList:infoList byFilter:filter];
}

- (NSArray<UPResourceInfo *> *)getLatestList:(id<UPResourceFilter>)filter
{

    NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:UPResourceTypeAll name:nil];
    NSArray<UPResourceInfo *> *latestList = [UPResourceHelper filterByLatestVersionOrIsServerLatest:infoList];

    return [UPResourceHelper filterInfoList:latestList byFilter:filter];
}
- (UPResourceInfo *)getLatestInfoByName:(NSString *)name type:(UPResourceType)type
{
    if (type == UPResourceTypeAll) {
        UPLogError(@"UPResource", @"不能传入UPResourceTypeAll类型");
        return nil;
    }
    if (name == nil || [[UPResourceInfo stringValueOfResourceType:type] isEqualToString:@""]) {
        UPLogError(@"UPResource", @"name或者type不能为空");
        return nil;
    }
    NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:type name:name];
    NSArray<UPResourceInfo *> *latestList = [UPResourceHelper filterByLatestVersionOrIsServerLatest:infoList];
    if (latestList.count == 0) {
        return nil;
    }
    return latestList.firstObject;
}
- (UPResourceInfo *)getLatestInstalledInfo:(NSString *)name type:(UPResourceType)type
{
    if (![self checkLatestInstalledInfoParam:name type:type]) { //为了解决圈复杂度
        return nil;
    };
    NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:type name:name];
    UPLogInfo(@"UPResource", @"%s%d当前查询结果为%@", __func__, __LINE__, infoList);
    if (infoList.count == 0) {
        if (type != UPResourceTypeVideo) {
            UPLogError(@"UPResource", @"%s%d当前查询结果为空", __func__, __LINE__);
            return nil;
        }
        return [self getLatestInstalledVideoInfo:name type:type];
    }
    UPResourceInfo *packageInfo = [self filterLatestInstallResourceBySameNameType:infoList];
    if (packageInfo == nil) {
        UPLogError(@"UPResource", @"%s%d未查找到最新已安装资源", __func__, __LINE__);
        return packageInfo;
    }
    /**
     视频类型资源包允许视频源文件与资源包同名 如下：
     --video                        （视频资源根目录）
     ---xiaoyou@1.0.0               （视频资源包目录）
     ----xiaoyou.mp4                （视频源文件）
     ----...
     视频源文件查询
     type = UPResourceTypeVideo
     name = xiaoyou
     查询逻辑
     1. 根据 name 查找到资源包信息且资源类型为视频资源时，遍历资源包父级目录，查找是否有与资源包同名视频文件。
     2. 若查找到同名视频源文件，拼接源文件路径返回。
     3. 若未查找到同名视频源文件，返回资源包信息。
     */
    if (type == UPResourceTypeVideo) {
        UPResourceInfo *info = [self fetchSandBoxResourceFileWithName:name info:packageInfo];
        if (info) {
            return info;
        }
    }
    return packageInfo;
}

- (UPResourceInfo *)getLatestInstalledVideoInfo:(NSString *)name type:(UPResourceType)type
{
    /**
     视频独有逻辑，视频源文件查询 resName 规则为 资源包名_源文件编号 如下：
     -resource_cache_path_root      （资源根目录）
     --video                        （视频资源根目录）
     ---xiaoyou@1.0.0               （视频资源包目录）
     ----xiaoyou_01.mp4             （视频源文件）
     ----xiaoyou_02.mp4             （视频源文件）
     ----xiaoyou_03.mp4             （视频源文件）
     视频源文件查询
     type = UPResourceTypeVideo
     name = xiaoyou_01
     查询逻辑
     1. 根据 name 未查找到资源信息时，说明 name 可能为视频源文件名，此时将 name 以 "_" 分割。
     2. 若分割结果数组元素数量等于 2 则根据源文件命名规则可知数组中第一个元素为源文件所在资源包名。否则说明不是视频源文件查询，直接返回 nil。
     3. 取得资源包名，查询视频资源包信息。
     4. 查询到资源包信息拼接源文件路径返回，否则直接返回 nil
     */
    __block UPResourceInfo *info = nil;
    NSArray *resNameComponents = [name componentsSeparatedByString:@"_"];
    if (resNameComponents.count == 2) {
        // 查找父级视频资源包信息
        NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:type name:resNameComponents.firstObject];
        if (infoList.count == 0) {
            return info;
        }
        UPResourceInfo *packageInfo = [self filterLatestInstallResourceBySameNameType:infoList];
        if (packageInfo) {
            // 遍历本地父级视频资源文件夹查找视频源文件路径
            return [self fetchSandBoxResourceFileWithName:name info:packageInfo];
        }
    }
    return info;
}

- (NSArray<UPResourceInfo *> *)getLatestInstalledList:(BOOL (^)(UPResourceInfo *info))filter
{
    NSArray<UPResourceInfo *> *infoList = [self.databaseDelegate searchResourceList:UPResourceTypeAll name:nil];
    if (UPRes_isEmptyArray(infoList)) {
        return @[];
    }
    NSMutableDictionary *activeInfoList = [NSMutableDictionary dictionary];
    __block NSString *key = nil;
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (!obj.active) {
          return;
      }
      /**
         以 类型+@+名称 作为 key 防止出现同名不同类型资源
         */
      key = [NSString stringWithFormat:@"%@@%@", [UPResourceInfo stringValueOfResourceType:obj.type], obj.name];
      NSMutableArray *list = activeInfoList[key];
      if (!list) {
          list = [NSMutableArray array];
          [activeInfoList setObject:list forKey:key];
      }
      [list addObject:obj];
    }];
    NSMutableArray *latestInstalledList = [NSMutableArray array];
    [activeInfoList enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      UPResourceInfo *info = [self filterLatestInstallResourceBySameNameType:obj];
      if (info && filter(info)) {
          [latestInstalledList addObject:info];
      }
    }];
    return latestInstalledList.copy;
}

- (UPResourceInfo *)fetchSandBoxResourceFileWithName:(NSString *)name info:(UPResourceInfo *)packageInfo
{
    // 遍历本地文件查找名称为“name”的源文件
    __block UPResourceInfo *info = nil;
    NSArray *subFiles = [self.fileDelegate listSubPaths:packageInfo.path];
    NSString *filePath = [packageInfo.path stringByAppendingPathComponent:name];
    [subFiles enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([[obj stringByDeletingPathExtension] isEqualToString:filePath]) {
          // 修改 resourceInfo 中 name 为资源源文件名，拼接源文件路径
          info = packageInfo;
          info.name = name;
          info.path = obj;
          *stop = YES;
      }
    }];
    return info;
}

- (BOOL)checkLatestInstalledInfoParam:(NSString *)name type:(UPResourceType)type
{
    if (type == UPResourceTypeAll) {
        UPLogError(@"UPResource", @"不能传入UPResourceTypeAll类型");
        return NO;
    }
    if (name == nil || [[UPResourceInfo stringValueOfResourceType:type] isEqualToString:@""]) {
        UPLogError(@"UPResource", @"name或者type不能为空");
        return NO;
    }
    return YES;
}

- (UPResourceInfo *)getResourcInfo:(NSString *)name type:(UPResourceType)type version:(NSString *)version
{
    if (!name || !version || type == UPResourceTypeAll) {
        UPLogError(@"UPResource", @"name、version不为空，type不能为all");
        return nil;
    }
    UPResourceInfo *info = [self.databaseDelegate searchResourceInfo:type name:name version:version];
    return info;
}
- (void)setAppVersion:(NSString *)appVersion
{
    _appVersion = appVersion;
}
- (NSArray<UPResourceInfo *> *)relationInfoList:(UPResourceCondition *)condition infoList:(NSArray<UPResourceInfo *> *)infoList
{
    UPResourceQuery *query = [self createQuery:condition];
    return [self.relationDelegate relationResList:query isPreset:NO infoList:infoList];
}
- (void)updateResourceListIsServerLatestState:(NSArray<UPResourceInfo *> *)resourceList
{
    [resourceList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull newObj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSArray<UPResourceInfo *> *kTheSameTypeAndNameResourceList = [self.databaseDelegate searchResourceList:newObj.type name:newObj.name];
      for (UPResourceInfo *oldObj in kTheSameTypeAndNameResourceList) {
          NSString *newVersion = newObj.version;
          if (newVersion && ![oldObj.version isEqualToString:newVersion] && oldObj.isServerLatest) {
              oldObj.isServerLatest = NO;
              BOOL result = [self.databaseDelegate updateResourceInfo:oldObj];
              if (!result)
                  UPLogError(@"UPResource", @"%s[%d]更新资源%@的IsServerLatest信息失败！", __PRETTY_FUNCTION__, __LINE__, oldObj);
          }
          else if (newVersion && [oldObj.version isEqualToString:newVersion] && !oldObj.isServerLatest) {
              oldObj.isServerLatest = YES;
              BOOL result = [self.databaseDelegate updateResourceInfo:oldObj];
              if (!result)
                  UPLogError(@"UPResource", @"%s[%d]更新资源%@的IsServerLatest信息失败！", __PRETTY_FUNCTION__, __LINE__, oldObj);
          }
      }
    }];
}

@end

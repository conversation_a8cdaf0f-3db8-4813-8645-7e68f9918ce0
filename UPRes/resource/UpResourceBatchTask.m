//
//  UpResourceBatchTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceBatchTask.h"
#import "UpResourceTaskMan.h"
#import "UPResourceInfo.h"
#import "UPResourceCallback.h"
#import "UPResourceListener.h"
#import "UPResourceOperator.h"
#import "UPResourceTask.h"
#import <UPLog/UPLog.h>
#import "UPResourceListCallback.h"
@interface UpResourceBatchTask () <UPResourceCallback>

/**
 taskId
 **/
@property (nonatomic, copy) NSString *taskId;
/**
 block
 **/
@property (nonatomic, copy) OperatorCreatorBlock creator;

/**
 taskMan
 **/
@property (nonatomic, strong) UpResourceTaskMan *taskMan;

/**
 infoList
 **/
@property (nonatomic, strong) NSArray<UPResourceInfo *> *infoList;

/**
 callback
 **/
@property (nonatomic, weak) id<UPResourceListCallback> callback;

/**
 listener
 **/
@property (nonatomic, weak) id<UPResourceListener> listener;

/**
 results
 **/
@property (nonatomic, strong) NSMutableDictionary<UPResourceInfo *, NSNumber *> *results;

@end

@implementation UpResourceBatchTask

- (instancetype)initUpResourceBatchTask:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener
{

    return [self initUpResourceBatchTask:nil creator:creator taskMan:taskMan callback:callback listener:listener];
}

- (instancetype)initUpResourceBatchTask:(nullable NSArray<UPResourceInfo *> *)infoList creator:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener
{


    if (self = [super init]) {
        if (infoList) {
            self.infoList = infoList;
        }
        self.callback = callback;
        self.taskMan = taskMan;
        self.listener = listener;
        self.creator = creator;
        self.taskId = [NSUUID UUID].UUIDString;
        self.results = [NSMutableDictionary dictionary];
    }

    return self;
}

- (id<UPResourceListCallback>)getCallback
{

    return self.callback;
}
- (void)setInfoList:(NSArray<UPResourceInfo *> *)infoList
{
    _infoList = infoList;
}

- (void)execute
{
    if (![self.infoList isKindOfClass:[NSArray class]] || self.infoList.count == 0) {
        if ([self.callback conformsToProtocol:@protocol(UPResourceListCallback)] && [self.callback respondsToSelector:@selector(onResult:message:infoList:)]) {
            [self.callback onResult:YES message:@"没有需要处理的资源列表" infoList:nil];
        }
    }
    for (UPResourceInfo *info in self.infoList) {
        if (self.creator) {
            id<UPResourceOperator> operator= self.creator(self, self.listener);
            NSString *taskId = [self.taskMan createOrListen:info operator:operator callback:self listener:self.listener];
            UPLogInfo(@"UPResource", @"批处理--%@--子任务id--%@", info, taskId);
        }
    }
}

- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
    [task cancel];
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    @synchronized(self.results)
    {
        [self.results setObject:@(success) forKey:info];
        [self checkDone];
    }
}

- (void)checkDone
{
    if (self.infoList.count != self.results.count) {
        return;
    }
    __block BOOL success = NO;
    NSMutableArray<UPResourceInfo *> *infoList = [NSMutableArray array];
    [self.results enumerateKeysAndObjectsUsingBlock:^(UPResourceInfo *_Nonnull key, NSNumber *_Nonnull obj, BOOL *_Nonnull stop) {
      if (obj.boolValue == YES) {
          success = YES;
          [infoList addObject:key];
      }
    }];
    if ([self.callback conformsToProtocol:@protocol(UPResourceListCallback)] && [self.callback respondsToSelector:@selector(onResult:message:infoList:)]) {
        [self.callback onResult:success message:success ? @"安装成功" : @"安装失败" infoList:infoList];
    }
    if (self.compltionBlock) {
        self.compltionBlock();
    }
}

@end

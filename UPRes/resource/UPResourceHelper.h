//
//  UPResourceHelper.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/20.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceFilter.h"

@interface UPResNameAndTypeFilter : NSObject <UPResourceFilter>

@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) NSString *type;

- (instancetype)initFilterWithName:(NSString *)name type:(NSString *)type;

@end

@interface UPResourceHelper : NSObject

+ (id<UPResourceFilter>)createNameAndTypeFilterWithName:(NSString *)name type:(NSString *)type;

+ (NSArray<UPResourceInfo *> *)filterInfoList:(NSArray<UPResourceInfo *> *)infoList byFilter:(id<UPResourceFilter>)filter;

+ (NSArray<UPResourceInfo *> *)filterByL<PERSON>tVersionOrIsServerLatest:(NSArray<UPResourceInfo *> *)infoList;

+ (NSString *)formatTypeName:(UPResourceInfo *)info;
+ (NSString *)formatResStr:(UPResourceInfo *)info;
+ (BOOL)isBlank:(NSString *)string;
+ (BOOL)isNonNullObject:(NSObject *)object conformToProtocol:(Protocol *)pro;
+ (BOOL)commpareVersion:(NSString *)version1 lastInfo:(NSString *)version2;
@end

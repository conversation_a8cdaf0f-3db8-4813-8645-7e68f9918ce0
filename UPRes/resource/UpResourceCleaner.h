//
//  UpResourceCleaner.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/22.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UpPreloadResourceInfo.h"

NS_ASSUME_NONNULL_BEGIN

@protocol UpResourceCleaner <NSObject>

- (BOOL)isCleaning;

- (BOOL)cleanLocalData;

- (BOOL)cleanLocalDataKeepPresetRes:(NSArray<UpPreloadResourceInfo *> *)keepResNameList;

@end

NS_ASSUME_NONNULL_END

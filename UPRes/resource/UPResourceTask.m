//
//  UPResourceTask.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceTask.h"
#import "UPResourceOperator.h"
#import <UPLog/UPLog.h>
#import "UPResourceResult.h"
@implementation UPResourceTask
#pragma mark - Override
- (instancetype)initTaskWithResourceInfo:(UPResourceInfo *)resourceInfo operator:(id<UPResourceOperator>)resourceOperator
{
    if (self = [super init]) {
        _taskId = [NSUUID UUID].UUIDString;
        _resourceInfo = resourceInfo;
        _resourceOperator = resourceOperator;
    }
    return self;
}

#pragma mark - Public Methods
+ (UPResourceTask *)resourceTask:(UPResourceInfo *)resourceInfo operator:(id<UPResourceOperator>)resourceOperator
{
    UPResourceTask *task = [[UPResourceTask alloc] initTaskWithResourceInfo:resourceInfo operator:resourceOperator];
    return task;
}

- (UPResourceResult *)execute
{
    if (![_resourceOperator conformsToProtocol:@protocol(UPResourceOperator)]) {
        UPLogError(@"UPResource", @"%s[%d]执行失败！任务的操作对象不符合UPResourceOperator协议！", __PRETTY_FUNCTION__, __LINE__);
        return [UPResourceResult invalidResult:@"任务的操作对象不符合UPResourceOperator协议"];
    }
    return [_resourceOperator operate:self];
}

- (void)cancel
{
    if (![_resourceOperator conformsToProtocol:@protocol(UPResourceOperator)]) {
        UPLogError(@"UPResource", @"%s[%d]执行失败！任务的操作对象不符合UPResourceOperator协议！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [_resourceOperator cancel:self];
}

- (void)resume
{
    if (![_resourceOperator conformsToProtocol:@protocol(UPResourceOperator)]) {
        UPLogError(@"UPResource", @"%s[%d]执行失败！任务的操作对象不符合UPResourceOperator协议！", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    [_resourceOperator resume];
}

@end

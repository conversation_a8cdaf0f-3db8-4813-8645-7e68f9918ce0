//
//  UPResourcePresetHelper.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourcePresetHelper.h"
#import "UPPresetFileLoader.h"
#import "UPResourceHelper.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import "UPResourceCondition.h"
#import "UPResourceRepository.h"
#import <UPLog/UPLog.h>
#import "UPFileDelegate.h"
#import "UPTimeDelegate.h"
#import "UPResourceReporter.h"
@interface UPResourcePresetHelper ()
@property (nonatomic, strong) id<UPPresetFileLoader> scanner;
@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, strong) id<UPResourceReporter> reporterDelegate;
@property (nonatomic, copy) NSString *appVersion;
@end

@implementation UPResourcePresetHelper
- (instancetype)initScanner:(id<UPPresetFileLoader>)scanner repository:(UPResourceRepository *)repository fileDelegate:(id<UPFileDelegate>)fileDelegate appVersion:(NSString *)appVersion timeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    if (self = [super init]) {
        self.scanner = scanner;
        self.repository = repository;
        self.fileDelegate = fileDelegate;
        self.appVersion = appVersion;
        self.timeDelegate = timeDelegate;
    }
    return self;
}

#pragma mark - getter, settter
- (void)setReporterDelegate:(id)reporterDelegate
{
    _reporterDelegate = reporterDelegate;
}

- (nullable NSArray<UPResourceInfo *> *)getPresetInfos
{
    if (![self.scanner conformsToProtocol:@protocol(UPPresetFileLoader)]) {
        return nil;
    }
    NSArray<NSDictionary *> *preSetFileDicts = [self.scanner scanPresetFilenameList];
    if (![preSetFileDicts isKindOfClass:[NSArray class]]) {
        return nil;
    }
    NSMutableArray *infoList = [NSMutableArray array];
    for (NSDictionary *mode in preSetFileDicts) {
        if ([UPResourceInfo typeValueOfString:mode[@"resType"]] == UPResourceTypeDeviceConfig || [UPResourceInfo typeValueOfString:mode[@"resType"]] == UPResourceTypeLua || [UPResourceInfo typeValueOfString:mode[@"resType"]] == UPResourceTypeConfigApp || [UPResourceInfo typeValueOfString:mode[@"resType"]] == UPResourceTypeAppFuncModel ||
            [UPResourceInfo typeValueOfString:mode[@"resType"]] == UPResourceTypeRoutes) {
            continue;
        }
        UPResourceInfo *info = [[UPResourceInfo alloc] init];
        info.name = mode[@"resName"];
        info.type = [UPResourceInfo typeValueOfString:mode[@"resType"]];
        info.hashStr = mode[@"hashMD5"];
        info.version = mode[@"version"];
        NSString *filePath = [self.scanner openPresetFile:mode[@"filename"]];
        info.link = filePath;
        info.preset = YES;
        info.hideStatusBar = [mode[@"hideStatusBar"] isEqualToString:@"true"];
        if (![self.scanner existFile:mode[@"filename"]]) {
            continue;
        }
        UPResourceInfo *locaInfo = [self getLocalPresetedInfo:info];
        if (locaInfo) {
            info.hashStr = locaInfo.hashStr;
            info.indexPath = locaInfo.indexPath;
            info.path = locaInfo.path;
        }
        info.fileDelegate = self.fileDelegate;
        info.timeDelegate = self.timeDelegate;
        info.reporterDelegate = self.reporterDelegate;
        [infoList addObject:info];
    }
    return infoList;
}
- (BOOL)presetInfoRelations:(NSArray<UPResourceInfo *> *)infoList
{
    __block BOOL result = YES;
    for (UPResourceInfo *info in infoList) {
        UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:info.type];
        condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
        condition.appVersion = self.appVersion;
        condition.resName = info.name;
        result = [self.repository presetResouceList:condition infoList:@[ info ]];
        if (result == NO) {
            break;
        }
    }
    if (result == NO) {
        return result;
    }
    NSMutableDictionary<NSString *, NSMutableArray<UPResourceInfo *> *> *nameResMap = [NSMutableDictionary dictionary];
    for (UPResourceInfo *info in infoList) {
        NSMutableArray<UPResourceInfo *> *list = nameResMap[info.name];
        if (list.count == 0) {
            list = [NSMutableArray array];
            [nameResMap setObject:list forKey:info.name];
        }
        if (![list containsObject:info]) {
            [list addObject:info];
        }
    }
    [nameResMap enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSMutableArray<UPResourceInfo *> *_Nonnull obj, BOOL *_Nonnull stop) {
      UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:UPResourceTypeAll];
      condition.appVersion = self.appVersion;
      condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
      condition.resName = key;
      result = [self.repository presetResouceList:condition infoList:obj];
      if (result == NO) {
          *stop = YES;
      }
    }];
    return result;
}
- (NSArray<UPResourceInfo *> *)getNeedInstallResource:(NSArray<UPResourceInfo *> *)infoList
{
    NSMutableArray *needInstallList = [NSMutableArray array];
    for (UPResourceInfo *info in infoList) {
        UPLogDebug(@"UPResource", @"开始读取本地资源");
        UPResourceInfo *findInfo = [self getLocalPresetedInfo:info];
        UPLogDebug(@"UPResource", @"结束读取本地资源");
        if (findInfo && findInfo.active == NO && info.preset) {
            findInfo.isServerLatest = YES;
            [needInstallList addObject:findInfo];
        }
    }
    return needInstallList;
}
- (UPResourceInfo *)getLocalPresetedInfo:(UPResourceInfo *)presetInfo
{
    return [self.repository getResourcInfo:presetInfo.name type:presetInfo.type version:presetInfo.version];
}

@end

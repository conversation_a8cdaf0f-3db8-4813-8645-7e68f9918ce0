//
//  UpResourceCallbackHolder.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceCallbackHolder.h"

@interface UpResourceCallbackHolder ()

@end

@implementation UpResourceCallbackHolder


- (instancetype)initWith:(id<UPResourceCallback>)callback
{

    if (self = [super init]) {

        [self add:callback];
    }

    return self;
}

- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{

    NSArray<id<UPResourceCallback>> *callbacks = self.getAll;

    for (id<UPResourceCallback> callback in callbacks) {

        if ([callback conformsToProtocol:@protocol(UPResourceCallback)] && [callback respondsToSelector:@selector(onPrompt:action:processor:)]) {

            [callback onPrompt:task action:action processor:processor];
        }
    }
}


- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{

    NSArray<id<UPResourceCallback>> *callbacks = self.getAll;

    for (id<UPResourceCallback> callback in callbacks) {

        if ([callback conformsToProtocol:@protocol(UPResourceCallback)] && [callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {

            [callback onResult:success message:message resourceInfo:info];
        }
    }
}


@end

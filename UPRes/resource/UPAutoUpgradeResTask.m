//
//  UPAutoUpgradeResTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/20.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAutoUpgradeResTask.h"
#import "UPResourceCallback.h"
#import "UpPreloadResourceInfo.h"
#import "UPResourceManager.h"
#import "UPResourceListener.h"
#import <UPLog/UPLog.h>
@interface UPAutoUpgradeResTask () <UPResourceCallback, UPResourceListener, UPResourceFilter>
@property (nonatomic, strong) NSArray<UpPreloadResourceInfo *> *infoList;
@property (nonatomic, weak) id<UPResourceCallback> callback;
@property (nonatomic, strong) UPResourceManager *resManager;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *needInstallResList;
@end

@implementation UPAutoUpgradeResTask
- (instancetype)initResList:(NSArray<UpPreloadResourceInfo *> *)infoList manager:(UPResourceManager *)manager callback:(id<UPResourceCallback>)callback
{
    if (self = [super init]) {

        self.infoList = infoList;
        self.callback = callback;
        self.resManager = manager;
        self.needInstallResList = [NSMutableArray array];
    }
    return self;
}
- (void)start
{
    UPLogInfo(@"UPResource", @"开始自动升级任务");
    NSMutableArray<UPResourceInfo *> *infolist = [self.resManager getLatestList:self].mutableCopy;
    if (infolist.count == 0) {
        UPLogInfo(@"UPResource", @"%s[%d]没有需要升级的资源", __PRETTY_FUNCTION__, __LINE__);
        if ([self.callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
            [self.callback onResult:YES message:@"没有需要升级的资源" resourceInfo:nil];
        }
        return;
    }
    for (NSInteger i = 0; i < self.infoList.count; i++) {
        for (NSInteger j = 0; j < infolist.count; j++) {
            BOOL result = [self.infoList[i].name isEqualToString:infolist[j].name] && self.infoList[i].type == infolist[j].type;
            if (result)
                [self.needInstallResList addObject:infolist[j]];
        }
    }
    [infolist removeObjectsInArray:self.needInstallResList];
    [self.needInstallResList addObjectsFromArray:infolist];
    [self installResInfoFromInfoList:self.needInstallResList];
}
#pragma mark Resource Delegate
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    UPLogDebug(@"UPResource", @"自动升级资源%@安装结果为%@", info.name, message);
    if ([self.callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
        [self.callback onResult:success message:message resourceInfo:info];
    }
    [self.needInstallResList removeObjectAtIndex:0];
    if (self.needInstallResList.count) {
        [self installResInfoFromInfoList:self.needInstallResList];
        return;
    }
    UPLogInfo(@"UPResource", @"结束自动升级任务");
}
- (BOOL)accept:(UPResourceInfo *)info
{
    return !info.path.length;
}

#pragma mark private methods
- (void)installResInfoFromInfoList:(NSArray<UPResourceInfo *> *)infoList
{
    UPResourceInfo *info = infoList.firstObject;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      [self.resManager install:info callback:self listener:self];
    });
}

@end

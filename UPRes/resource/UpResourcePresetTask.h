//
//  UpResourcePresetTask.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceBatchTask.h"
#import "UPPresetFileLoader.h"
#import "UPFileDelegate.h"
#import "UPResourceRepository.h"
#import "UPTimeDelegate.h"
#import "UPResourceReporter.h"
NS_ASSUME_NONNULL_BEGIN

@interface UpResourcePresetTask : UpResourceBatchTask
- (instancetype)initUpResourcePresetTask:(OperatorCreatorBlock)creator taskMan:(UpResourceTaskMan *)taskMan callback:(id<UPResourceListCallback>)callback listener:(nullable id<UPResourceListener>)listener;
- (void)setScanner:(id<UPPresetFileLoader>)scanner;
- (void)setFileDelegate:(id<UPFileDelegate>)fileDelegate;
- (void)setRepository:(UPResourceRepository *)repository;
- (void)setAppVersion:(NSString *)appVersion;
- (void)setTimeDelegate:(id<UPTimeDelegate>)timeDelegate;
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate;
@end

NS_ASSUME_NONNULL_END

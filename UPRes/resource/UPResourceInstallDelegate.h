//
//  UPResourceInstallDelegate.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPInstallDelegate.h"
#import "UPConnectionDelegate.h"
@protocol UPConnectionDelegate
, UPDownloadDelegate, UPFileDelegate, UPResourceTrackerProvider, UPDownloadPolicyProvider, UPPresetFileLoader;
@class UpResourceTaskMan, UPResourceRepository, UPResourceDirectory;
NS_ASSUME_NONNULL_BEGIN

@interface UPResourceInstallDelegate : NSObject <UPInstallDelegate>
- (instancetype)initInstallDelegate:(id<UPConnectionDelegate>)connetionDelegate downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate fileDelegate:(id<UPFileDelegate>)fileDelegate downloadPolicyProvider:(id<UPDownloadPolicyProvider>)downloadPolicyProvider taskMan:(UpResourceTaskMan *)taskMan repository:(UPResourceRepository *)repository directory:(UPResourceDirectory *)directory trackerProvider:(id<UPResourceTrackerProvider>)trackerProvider;
@end

NS_ASSUME_NONNULL_END

//
//  UPResourceManager.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceManager.h"
#import "UPResourceRepository.h"
#import "UPConnectionMonitor.h"
#import "UPResourceDirectory.h"
#import <UPLog/UPLog.h>
#import "UPResourceTask.h"
#import "UPResCommonFunctions.h"
#import "UPResourceProcessPipeline.h"
#import "UPResourceDownloader.h"
#import "UPResourceValidator.h"
#import "UPResourceExtractor.h"
#import "UPResourceTransporter.h"
#import "UPResourceRemover.h"
#import "UPResourceInstaller.h"
#import "UPResourceUninstaller.h"
#import "UPResourceHelper.h"
#import "UPResourceCondition.h"
#import "UPDownloaderDelegate.h"
#import "UPConnectionDelegate.h"
#import "UpResourceTaskMan.h"
#import "UPDatabaseDelegate.h"
#import "UpResourceBatchTask.h"
#import "UpResourcePresetTask.h"
#import "UpResourceCleanerImpl.h"
#import "UpPreloadResourceInfo.h"
#import "UPResourcePreLoadTask.h"
#import "UpResourceSyncPresetTask.h"
#import "UPAutoUpgradeResTask.h"
#import "UPResourceRelationDelegate.h"
#import "UPResourceRequestDelegate.h"
#import "UPResourceInstallDelegate.h"
#import "UPResourceTracker.h"
#import "UPResourceTrackerProvider.h"
#import "UPDownloadPolicyProvider.h"
#import "UPResourceScanner.h"
#import "UPResourceResult.h"
#import "UPResourceCleanUnusefulResTask.h"
#import "UpAutoUpgradeLocalResTask.h"
#import "UpResourcePresetDownloader.h"
#import "UPAssetPresetFileLoader.h"
#import "UPResourceErrCodes.h"
#import "UPRLoadReporterImp.h"

NSString *const UP_RES_VERSION = @"2.8.4";


NSString *const UP_RES_ROOT_PATH = @"uplus-resource";
NSString *const UP_RES_ROOT_PATH_TEST = @"uplus-resource-test";

long DEFAULT_DEVICE_CONFIG_UPDATE_INTERVAL = 60 * 10 * 1000;

@implementation UPResourceSettings
#pragma mark - Property Methods
- (NSString *)appVersion
{
    return self.resourceManager.appVersion;
}

- (void)setAppVersion:(NSString *)appVersion
{
    self.resourceManager.appVersion = appVersion;
}

- (UPConnectionType)downloadPolicy
{
    return self.resourceManager.downloadPolicy;
}

- (void)setDownloadPolicy:(UPConnectionType)downloadPolicy
{
    if (downloadPolicy == TYPE_WIFI || downloadPolicy == TYPE_MOBILE) {
        self.resourceManager.downloadPolicy = downloadPolicy;
    }
    else {
        UPLogError(@"UPResource", @"%s[%d]不支持该网络连接类型:%ld", __PRETTY_FUNCTION__, __LINE__, (long)downloadPolicy);
    }
}

- (long)resourceUpdateInterval
{
    return self.resourceManager.deviceConfigUpdateInterval;
}

- (void)setResourceUpdateInterval:(long)resourceUpdateInterval
{
    self.resourceManager.deviceConfigUpdateInterval = resourceUpdateInterval;
}
- (void)setDownloadRetryCount:(NSInteger)downloadRetryCount
{
    if (downloadRetryCount < 0) {
        _downloadRetryCount = 0;
    }
    if (downloadRetryCount > 5) {
        _downloadRetryCount = 5;
    }
    _downloadRetryCount = downloadRetryCount;
    self.resourceManager.downloadRetryCount = downloadRetryCount;
}
- (void)setDownloadRetryDelay:(NSInteger)downloadRetryDelay
{
    if (downloadRetryDelay < 0) {
        _downloadRetryDelay = 0;
    }
    if (downloadRetryDelay > 60) {
        _downloadRetryDelay = 60;
    }
    _downloadRetryDelay = downloadRetryDelay;
    self.resourceManager.downloadRetryDelay = downloadRetryDelay;
}

- (void)setEnvironment:(UPResEnvironment)environment
{
    _environment = environment;
    self.resourceManager.environment = environment;
}

#pragma mark - Public Methods
- (instancetype)initSettingsWithResourceManager:(UPResourceManager *)resourceManager
{
    if (self = [super init]) {
        _resourceManager = resourceManager;
    }
    return self;
}

@end

@interface UPResourceManager () <UPResourceTrackerProvider, UPDownloadPolicyProvider>
@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) UPResourceDirectory *directory;
@property (nonatomic, strong) id<UPDownloadDelegate> downloaderDelegate;
@property (nonatomic, strong) id<UPDatabaseDelegate> databaseDelegate;
@property (nonatomic, strong) UpResourcePresetTask *presetTaskRef;
@property (nonatomic, strong) UpResourceTaskMan *taskMan;
@property (nonatomic, copy) NSString *resRootPath;
@property (nonatomic, strong) id<UpResourceCleaner> cleaner;
@property (nonatomic, strong) id<UPResourceDataSource> dataSource;

@end

@implementation UPResourceManager
#pragma mark private Methods
- (void)setDownloadRetryDelay:(NSInteger)downloadRetryDelay
{
    [_downloaderDelegate setDownloadRetryDealy:downloadRetryDelay];
}
- (void)setDownloadRetryCount:(NSInteger)downloadRetryCount
{
    [_downloaderDelegate setDownloadRetryCount:downloadRetryCount];
}
- (BOOL)checkState:(id)callback
{

    if ([self isCleaning]) {

        if ([callback conformsToProtocol:@protocol(UPResourceCallback)] && [callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {

            [callback onResult:NO message:@"不能在清空数据时执行其他操作" resourceInfo:nil];
        }
        if ([callback conformsToProtocol:@protocol(UPResourceListCallback)] && [callback respondsToSelector:@selector(onResult:message:infoList:)]) {

            [callback onResult:NO message:@"不能在清空数据时执行其他操作" infoList:nil];
        }

        return NO;
    }
    return YES;
}
- (NSArray<UPResourceInfo *> *)updateCommonResList:(UPResourceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (![self checkState:nil]) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"正在执行清空操作时无法返回数据" code:-1 userInfo:nil]);
        }
        return nil;
    }
    condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
    condition.appVersion = self.appVersion;
    return [self.requestDelegate requestResourceList:condition immediate:immediate completion:completion];
}
- (id<UPResourceTracker>)getResourceTracker
{
    return _tracker;
}
- (UPResourceSettings *)getSettings
{
    return _settings;
}
- (UPConnectionType)getDownloadPolicy
{
    return _downloadPolicy;
}

- (void)setEnvironment:(UPResEnvironment)environment
{
    _environment = environment;
    self.repository.environment = environment;
}

#pragma mark - Public Methods

- (void)setDeviceConfigUpdateInterval:(long)deviceConfigUpdateInterval
{
    _deviceConfigUpdateInterval = deviceConfigUpdateInterval;
    _repository.autoUpdateInterval = deviceConfigUpdateInterval;
}


- (instancetype)initResourceManagerWithAppVersion:(NSString *)appVersion
                                      resRootPath:(NSString *)resRootPath
                                       dataSource:(id<UPResourceDataSource>)dataSource
                                 databaseDelegate:(id<UPDatabaseDelegate>)databaseDelegate
                                     fileDelegate:(id<UPFileDelegate>)fileDelegate
                                     timeDelegate:(id<UPTimeDelegate>)timeDelegate
                               connectionDelegate:(id<UPConnectionDelegate>)connectionDelegate
                                 downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate
                                          cleaner:(id<UpResourceCleaner>)cleaner
{
    if ([UPResourceHelper isBlank:appVersion] ||
        [UPResourceHelper isBlank:resRootPath] ||
        ![UPResourceHelper isNonNullObject:dataSource
                         conformToProtocol:@protocol(UPResourceDataSource)] ||
        ![UPResourceHelper isNonNullObject:databaseDelegate
                         conformToProtocol:@protocol(UPDatabaseDelegate)] ||
        ![UPResourceHelper isNonNullObject:fileDelegate
                         conformToProtocol:@protocol(UPFileDelegate)] ||
        ![UPResourceHelper isNonNullObject:timeDelegate
                         conformToProtocol:@protocol(UPTimeDelegate)] ||
        ![UPResourceHelper isNonNullObject:connectionDelegate
                         conformToProtocol:@protocol(UPConnectionDelegate)] ||
        ![UPResourceHelper isNonNullObject:downloadDelegate
                         conformToProtocol:@protocol(UPDownloadDelegate)]) {
        UPLogError(@"UPResource", @"%s[%d]UPResourceManager初始化失败！error:输入参数为空或不符合协议要求！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (self = [super init]) {
        _settings = [[UPResourceSettings alloc] initSettingsWithResourceManager:self];
        _appVersion = appVersion;
        _resRootPath = resRootPath;
        _deviceConfigUpdateInterval = DEFAULT_DEVICE_CONFIG_UPDATE_INTERVAL;
        _fileDelegate = fileDelegate;
        _directory = [UPResourceDirectory resourceDirectoryWithPath:resRootPath fileDelegate:fileDelegate];
        _databaseDelegate = databaseDelegate;
        _timeDelegate = timeDelegate;
        _dataSource = dataSource;
        _repository = [[UPResourceRepository alloc] initRepositoryWithTimeDelegate:timeDelegate fileDelegate:fileDelegate directory:_directory dataSource:dataSource databaseDelegate:databaseDelegate autoUpdateInterval:_deviceConfigUpdateInterval / 1000.0];
        [_repository setAppVersion:appVersion];
        _connectionDelegate = connectionDelegate;
        _downloaderDelegate = downloadDelegate;
        _taskMan = [[UpResourceTaskMan alloc] init];
        _cleaner = [self getCleaner:cleaner taksMan:_taskMan repository:_repository];
        _downloadPolicy = TYPE_WIFI;
        _requestDelegate = [[UPResourceRequestDelegate alloc] initRequestDelegate:_repository];
        _installDelegate = [[UPResourceInstallDelegate alloc] initInstallDelegate:_connectionDelegate downloadDelegate:_downloaderDelegate fileDelegate:fileDelegate downloadPolicyProvider:self taskMan:_taskMan repository:_repository directory:_directory trackerProvider:self];
        _relationDelegate = [[UPResourceRelationDelegate alloc] initRelationDelegate:_databaseDelegate timeDelegate:_timeDelegate];
        _repository.relationDelegate = _relationDelegate;
        _reporterDelegate = [[UPRLoadReporterImp alloc] initWithDataSource:dataSource timeDelegate:timeDelegate connectionDelegate:connectionDelegate];
        [self setReporterDelegate];
        _settings.downloadRetryDelay = 5;
        _settings.downloadRetryCount = 0;
    }
    return self;
}

- (void)setReporterDelegate
{
    if ([_databaseDelegate respondsToSelector:@selector(setReporterDelegate:)]) {
        [_databaseDelegate setReporterDelegate:self.reporterDelegate];
    }
}

- (void)setRelationDelegate:(id<UPRelationDelegate>)relationDelegate
{
    _relationDelegate = relationDelegate;
    self.repository.relationDelegate = relationDelegate;
}
- (id<UpResourceCleaner>)getCleaner:(id<UpResourceCleaner>)cleaner taksMan:(UpResourceTaskMan *)taskMan repository:(UPResourceRepository *)repository
{
    return cleaner ? cleaner : [[UpResourceCleanerImpl alloc] initUpResourceCleanerImpl:taskMan repository:repository];
}

- (void)setScanner:(id<UPPresetFileLoader>)scanner
{
    if (scanner) {
        _scanner = scanner;
        self.installDelegate.scanner = scanner;
    }
}

#pragma mark load preResource
- (void)extractPresetResList:(id<UPPresetFileLoader>)loader callback:(id<UPResourceListCallback>)callback
{
    if (![self.cleaner isCleaning] && self.presetTaskRef == nil) {
        OperatorCreatorBlock creator = ^(id<UPResourceCallback> callback, id<UPResourceListener> listener) {
          UPResPipelineBuilder *builder = [[UPResPipelineBuilder alloc] init];
          UpResourcePresetDownloader *localDownloader = [[UpResourcePresetDownloader alloc] initWithFileDelegate:self.fileDelegate directory:self.directory scanner:loader];
          UPResourceExtractor *extractor = [[UPResourceExtractor alloc] initWithFileDelegate:self.fileDelegate];
          UPResourceTransporter *transporter = [[UPResourceTransporter alloc] initWithFileDelegate:self.fileDelegate];
          UPResourceScanner *sacnner = [[UPResourceScanner alloc] initWithFileDelegate:self.fileDelegate];
          [[[[builder add:localDownloader] add:extractor] add:transporter] add:sacnner];
          UPResourceProcessPipeline *pipeline = [builder build];
          UPResourceInstaller *installer = [[[UPResourceInstaller alloc] init] resourceOperator:self.repository directory:self.directory callback:callback listener:listener processor:pipeline];
          installer.trackProvider = self;

          return installer;

        };
        self.presetTaskRef = [[UpResourcePresetTask alloc] initUpResourcePresetTask:creator taskMan:self.taskMan callback:callback listener:nil];
        [self.presetTaskRef setFileDelegate:self.fileDelegate];
        [self.presetTaskRef setTimeDelegate:self.timeDelegate];
        [self.presetTaskRef setRepository:self.repository];
        [self.presetTaskRef setAppVersion:self.appVersion];
        [self.presetTaskRef setScanner:loader];
        [self.presetTaskRef setReporterDelegate:self.reporterDelegate];
        dispatch_async(dispatch_get_global_queue(0, 0), ^{
          [self.presetTaskRef execute];
        });
        __weak typeof(self) weakself = self;
        self.presetTaskRef.compltionBlock = ^{
          weakself.presetTaskRef = nil;
        };
    }
    else {
        NSString *message = @"当前正处于清空数据状态,不能进行预安装操作";
        if (self.presetTaskRef) {
            message = @"当前正在执行预安装资源操作，不能重复启动";
        }
        [self result:NO message:message callback:callback infoList:nil];
        UPLogInfo(@"UPResource", @"%s[%d] %@", __PRETTY_FUNCTION__, __LINE__, message);
    }
}
- (void)result:(BOOL)result message:(NSString *)message callback:(id<UPResourceListCallback>)callback infoList:(NSArray<UPResourceInfo *> *)infoList
{
    if ([callback conformsToProtocol:@protocol(UPResourceListCallback)] && [callback respondsToSelector:@selector(onResult:message:infoList:)]) {
        [callback onResult:result message:message infoList:infoList];
    }
}

- (UPResourceResult *)syncExtractPresetResInfo:(id<UPPresetFileLoader>)loader name:(NSString *)name type:(UPResourceType)type
{
    if (name.length == 0 || type == UPResourceTypeAll) {
        UPLogError(@"UPResource", @"%s[%d]同步预置资源失败！error:资源名称不能为空或者类型不能为all！", __PRETTY_FUNCTION__, __LINE__);
        return [UPResourceResult invalidResult:@"资源名称不能为空或者类型不能为all"];
    }
    if ([self isCleaning]) {
        UPLogInfo(@"UPResource", @"%s[%d]当前正处于清理数据状态,不能进行预置操作", __PRETTY_FUNCTION__, __LINE__);
        return [UPResourceResult failureResult:@"不能在清空数据时执行其他操作"];
    }
    UPResPipelineBuilder *builder = [[UPResPipelineBuilder alloc] init];
    UpResourcePresetDownloader *localDownloader = [[UpResourcePresetDownloader alloc] initWithFileDelegate:self.fileDelegate directory:self.directory scanner:loader];
    UPResourceExtractor *extractor = [[UPResourceExtractor alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceTransporter *transporter = [[UPResourceTransporter alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceScanner *sacnner = [[UPResourceScanner alloc] initWithFileDelegate:self.fileDelegate];
    [[[[builder add:localDownloader] add:extractor] add:transporter] add:sacnner];
    UPResourceProcessPipeline *pipeline = [builder build];
    UPResourceInstaller *installer = [[[UPResourceInstaller alloc] init] resourceOperator:self.repository directory:self.directory callback:nil listener:nil processor:pipeline];
    installer.trackProvider = self;

    UpResourceSyncPresetTask *syncPresetTask = [[UpResourceSyncPresetTask alloc] initSyncPresetTask:name type:type resourceOperator:installer];
    [syncPresetTask setFileDelegate:self.fileDelegate];
    [syncPresetTask setTimeDelegate:self.timeDelegate];
    [syncPresetTask setReporterDelegate:self.reporterDelegate];
    [syncPresetTask setRepository:self.repository];
    [syncPresetTask setAppVersion:self.appVersion];
    [syncPresetTask setScanner:loader];
    return [syncPresetTask execute];
}

#pragma mark fetch reourceData

- (NSArray<UPResourceInfo *> *)updateNormalResList:(NSString *)resName type:(UPResourceType)type completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    return [self updateNormalResList:resName type:type immediate:NO completion:completion];
}
- (NSArray<UPResourceInfo *> *)searchNormalResList:(NSString *)resName type:(UPResourceType)type
{
    if (![resName isKindOfClass:NSString.class] || !resName.length) {
        UPLogError(@"UPResource", @"%s[%d]查询普通资源失败！error:资源名称不能为空！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (type == UPResourceTypeDeviceConfig || type == UPResourceTypeLua) {
        UPLogError(@"UPResource", @"%s[%d]查询普通资源失败！error:资源类型不能为config！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (![self checkState:nil]) {
        UPLogError(@"UPResource", @"%s[%d]正在执行清空操作时无法返回数据", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:type];
    condition.resName = resName;
    condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
    condition.appVersion = self.appVersion;
    return [self.repository searchResList:condition];
}
- (NSArray<UPResourceInfo *> *)searchDeviceResList:(UPResourceDeviceCondition *)condition
{
    if (![condition isKindOfClass:[UPResourceDeviceCondition class]]) {
        UPLogError(@"UPResource", @"%s[%d]查询条件不符合要求", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (![self checkState:nil]) {
        UPLogError(@"UPResource", @"%s[%d]正在执行清空操作时无法返回数据", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    condition.fromFunc = [UPResourceDeviceCondition stringFromfuncOfResourceType:condition.resourceType];
    condition.appVersion = self.appVersion;
    return [self.repository searchResList:condition];
}
- (void)requestNormalResList:(NSString *)resName type:(UPResourceType)type immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (![self checkState:nil]) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"正在执行清空操作时无法返回数据" code:-1 userInfo:nil]);
        }
        return;
    }
    if (![resName isKindOfClass:NSString.class] || !resName.length) {
        UPLogError(@"UPResource", @"%s[%d]查询普通资源失败！error:资源名称不能为空！", __PRETTY_FUNCTION__, __LINE__);
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"查询普通资源失败！error:资源名称不能为空" code:-1 userInfo:nil]);
        }
        return;
    }
    UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:type];
    condition.resName = resName;
    condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
    condition.appVersion = self.appVersion;
    [self.requestDelegate requestResourceList:condition immediate:immediate completion:completion];
}
- (void)requestDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (![self checkState:nil]) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"正在执行清空操作时无法返回数据" code:-1 userInfo:nil]);
        }
        return;
    }
    if (![condition isKindOfClass:[UPResourceDeviceCondition class]]) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"查询条件不符合要求" code:-1 userInfo:nil]);
        }
        return;
    }
    condition.fromFunc = [UPResourceDeviceCondition stringFromfuncOfResourceType:condition.resourceType];
    condition.appVersion = self.appVersion;
    [self.requestDelegate requestResourceList:condition immediate:immediate completion:completion];
}
- (NSArray<UPResourceInfo *> *)updateNormalResList:(NSString *)resName type:(UPResourceType)type immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (![resName isKindOfClass:NSString.class] || !resName.length) {
        UPLogError(@"UPResource", @"%s[%d]更新普通资源失败！error:资源名称不能为空！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    [self requestNormalResList:resName type:type immediate:immediate completion:completion];
    return [self searchNormalResList:resName type:type];
}

- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    return [self updateDeviceResList:condition immediate:NO completion:completion];
}

- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (![self checkState:nil]) {
        if (completion) {
            completion(nil, [NSError errorWithDomain:@"正在执行清空操作时无法返回数据" code:-1 userInfo:nil]);
        }
        return nil;
    }
    [self requestDeviceResList:condition immediate:immediate completion:completion];
    return [self searchDeviceResList:condition];
}

#pragma mark resource operate

- (NSString *)install:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    if (![info isKindOfClass:[UPResourceInfo class]]) {
        if ([callback conformsToProtocol:@protocol(UPResourceCallback)] && [callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
            [callback onResult:NO message:@"安装对象不符合要求" resourceInfo:nil];
        }
        return nil;
    }
    if (![self checkState:callback]) {
        return nil;
    }
    return [self.installDelegate install:info callback:callback listener:listener];
}


- (void)preloadNormalResList:(NSArray<UpPreloadResourceInfo *> *)preloadInfoList callback:(id<UPResourceCallback>)callback
{
    if (![self checkState:callback]) {
        return;
    }
    if (!preloadInfoList.count) {
        UPLogError(@"UPResource", @"%s[%d]预加载安装普通资源！error:输入参数为空", __PRETTY_FUNCTION__, __LINE__);
        return;
    }
    UPResourcePreLoadTask *task = [[UPResourcePreLoadTask alloc] initResList:preloadInfoList callback:callback];
    task.resManager = self;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      [task start];
    });
}
- (NSString *)uninstall:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback
{
    if (![info isKindOfClass:[UPResourceInfo class]]) {
        if ([callback conformsToProtocol:@protocol(UPResourceCallback)] && [callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
            [callback onResult:NO message:@"卸载对象不符合要求" resourceInfo:nil];
        }
        return nil;
    }
    if (![self checkState:callback]) {

        return nil;
    }
    UPResPipelineBuilder *builder = [[UPResPipelineBuilder alloc] init];
    UPResourceRemover *remover = [[UPResourceRemover alloc] initWithFileDelegate:self.fileDelegate];
    [builder add:remover];
    UPResourceProcessPipeline *pipeline = [builder build];
    UPResourceUninstaller *uninstaller = [[[UPResourceUninstaller alloc] init] resourceOperator:self.repository directory:self.directory callback:callback listener:nil processor:pipeline];
    uninstaller.fileDelegate = self.fileDelegate;
    return [self.taskMan createOrListen:info operator:uninstaller callback:callback listener:nil];
}
#pragma mark task operate
- (BOOL)cancel:(NSString *)taskID
{
    return [self.taskMan cancel:taskID];
}

- (NSString *)getTaskIdByInfo:(UPResourceInfo *)info
{
    return [self.taskMan getTaskId:info];
}

#pragma mark cleanLocalData
- (void)cleanLocalResourceCache:(id<UPResCacheCleanCallback>)callback
{
    [self cleanLocalDataKeepPresetRes:nil callback:callback];
}

- (void)cleanLocalDataKeepPresetRes:(NSArray<UpPreloadResourceInfo *> *)keepResNameList callback:(id<UPResCacheCleanCallback>)callback
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      BOOL bResult = [self.cleaner cleanLocalDataKeepPresetRes:keepResNameList];
      if (![UPResourceHelper isNonNullObject:callback conformToProtocol:@protocol(UPResCacheCleanCallback)]) {
          return;
      }
      if (bResult) {
          if ([callback respondsToSelector:@selector(didFinishCacheCleaningWithOptions:)]) {
              dispatch_async(dispatch_get_main_queue(), ^{
                [callback didFinishCacheCleaningWithOptions:@{}];
              });
          }
          return;
      }
      NSString *errMsg = [NSString stringWithFormat:@"缓存清理失败！"];
      UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
      if ([callback respondsToSelector:@selector(resourceCacheDidFailCleanWithError:)]) {
          dispatch_async(dispatch_get_main_queue(), ^{
            [callback resourceCacheDidFailCleanWithError:[NSError errorWithDomain:errMsg code:0 userInfo:nil]];
          });
      }
    });
}

#pragma mark - 自动更新本地已安装资源
- (void)autoUpgradeLocalResouces
{
    if (![self checkState:nil]) {
        return;
    }
    UpAutoUpgradeLocalResTask *autoUpgradeRes = [[UpAutoUpgradeLocalResTask alloc] initWithResourceManager:self resourceRepository:self.repository];
    [autoUpgradeRes start];
}

- (void)reportResourceLoaded:(NSString *)resName type:(UPResourceType)resType version:(NSString *)resVersion
{
    [self.reporterDelegate track:resName type:resType version:resVersion];
}
#pragma mark 获取单个文件时，没有安装时进行安装
- (UPResourceInfo *)getCommonResource:(NSString *)resName type:(UPResourceType)type selector:(id<UpResourceSelector>)selector callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{

    if (![self checkState:callback]) {

        return nil;
    }
    if (selector == nil) {
        UPLogError(@"UPResource", @"Missing UpResourceSelector");

        [self callbackInfo:callback success:NO message:@"Missing UpResourceSelector" info:nil];
        return nil;
    }
    void (^completion)(NSArray<UPResourceInfo *> *infoList, NSError *error) = ^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
      if (error) {
          if (error.code == UPResErrorCodeServerResponseEmptyList) {
              UPLogError(@"UPResource", @"resourceInfoList is empty");
              [self callbackInfo:callback success:NO message:[NSString stringWithFormat:@"%ld", UPResErrorCodeServerResponseEmptyList] info:nil];
              return;
          }
          UPLogError(@"UPResource", @"requestResourceList failure. Error:%@", error);
          [self callbackInfo:callback success:NO message:@"更新资源信息错误" info:nil];
          return;
      }
      UPResourceInfo *info = [selector selectFrom:infoList];
      if (info == nil) {
          [self callbackInfo:callback success:NO message:@"从列表中筛选信息失败" info:nil];
      }
      else if (!info.active) {

          NSString *taskId = [self.installDelegate install:info callback:callback listener:listener];
          UPLogInfo(@"UPResource", @"自动更新资源 %@-%@-%@ 开始，任务id = %@,info = %@", [UPResourceInfo stringValueOfResourceType:info.type], info.name, info.version, taskId, info);
      }
      else {
          [self callbackInfo:callback success:YES message:@"资源已安装" info:info];
      }
    };
    UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:type];
    condition.resName = resName;
    condition.appVersion = self.appVersion;
    condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
    NSArray<UPResourceInfo *> *infoList = [self.requestDelegate requestResourceList:condition immediate:NO completion:completion];
    UPResourceInfo *info = [selector selectFrom:infoList];

    return info && info.active ? info : nil;
}
- (void)callbackInfo:(id<UPResourceCallback>)callback success:(BOOL)success message:(NSString *)message info:(UPResourceInfo *)info
{
    BOOL result = [callback respondsToSelector:@selector(onResult:message:resourceInfo:)];
    if (result) {
        [callback onResult:success message:message resourceInfo:info];
    }
}


- (UPResourceInfo *)getDeviceResource:(UPResourceDeviceCondition *)condition selector:(id<UpResourceSelector>)selector callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{

    if (![self checkState:callback]) {

        return nil;
    }
    if (selector == nil) {
        UPLogError(@"UPResource", @"Missing UpResourceSelector");
        [self callbackInfo:callback success:NO message:@"Missing UpResourceSelector" info:nil];
        return nil;
    }

    void (^completion)(NSArray<UPResourceInfo *> *infoList, NSError *error) = ^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
      if (error) {
          UPLogError(@"UPResource", @"更新资源信息错误");
          [self callbackInfo:callback success:NO message:@"更新资源信息错误" info:nil];
          return;
      }
      UPResourceInfo *info = [selector selectFrom:infoList];
      if (info == nil) {
          [self callbackInfo:callback success:NO message:@"从列表中筛选信息失败" info:nil];
      }
      else if (!info.active) {
          NSString *taskId = [self.installDelegate install:info callback:callback listener:listener];
          UPLogInfo(@"UPResource", @"自动更新资源 %@-%@-%@ 开始，任务id = %@,info = %@", [UPResourceInfo stringValueOfResourceType:info.type], info.name, info.version, taskId, info);
      }
      else {
          [self callbackInfo:callback success:YES message:@"资源已安装" info:info];
      }

    };
    condition.fromFunc = [UPResourceDeviceCondition stringFromfuncOfResourceType:condition.resourceType];
    condition.appVersion = self.appVersion;
    NSArray<UPResourceInfo *> *infoList = [self.requestDelegate requestResourceList:condition immediate:NO completion:completion];
    UPResourceInfo *info = [selector selectFrom:infoList];
    return info && info.active ? info : nil;
}

- (NSArray<UPResourceInfo *> *)getEntireList:(id<UPResourceFilter>)filter
{
    if (![self checkState:nil]) {

        return @[];
    }
    return [self.repository getEntireList:filter];
}

- (NSArray<UPResourceInfo *> *)getLatestList:(id<UPResourceFilter>)filter
{
    if (![self checkState:nil]) {

        return @[];
    }
    return [self.repository getLatestList:filter];
}

- (UPResourceInfo *)getLatestInfoByName:(NSString *)name type:(UPResourceType)type
{
    if (![self checkState:nil]) {

        return nil;
    }
    return [self.repository getLatestInfoByName:name type:type];
}
- (UPResourceInfo *)getLatestInstalledInfo:(NSString *)name type:(UPResourceType)type
{
    if (![self checkState:nil]) {

        return nil;
    }
    return [self.repository getLatestInstalledInfo:name type:type];
}

- (NSArray<UPResourceInfo *> *)getLatestInstalledList:(BOOL (^)(UPResourceInfo *info))filter
{
    if (![self checkState:nil]) {

        return nil;
    }
    return [self.repository getLatestInstalledList:filter];
}

- (UPResourceInfo *)getResourceInfo:(NSString *)name type:(UPResourceType)type version:(NSString *)version
{

    if (![self checkState:nil]) {

        return nil;
    }
    return [self.repository getResourcInfo:name type:type version:version];
}

- (NSString *)getPathByType:(UPResourceType)type
{
    return [self.directory resourceFolderPathOfType:type];
}

- (BOOL)isCleaning
{

    return [self.cleaner isCleaning];
}
- (void)autoUpgradeCurrentResources:(NSArray<UpPreloadResourceInfo *> *)infoList callback:(id<UPResourceCallback>)callback
{
    if (![self checkState:callback]) {
        return;
    }
    UPAutoUpgradeResTask *autoTask = [[UPAutoUpgradeResTask alloc] initResList:infoList manager:self callback:callback];
    [autoTask start];
}
- (void)asyncInsertAndInstallResInfo:(UPResourceInfo *)info isDevRes:(BOOL)isDevRes deviceNetType:(NSString *)deviceNetType callback:(id<UPResourceCallback>)callback
{
}

- (void)cleanUselessResource:(NSTimeInterval)delayTime
{
    if (![self checkState:nil]) {
        return;
    }
    if (delayTime < 0) {
        delayTime = 0;
    }
    UPResourceCleanUnusefulResTask *task = [[UPResourceCleanUnusefulResTask alloc] initCleanUnusefulResTask:self.repository];
    [task queryResourceAndQuerys];
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(delayTime * NSEC_PER_SEC)), dispatch_get_global_queue(0, 0), ^{
      [task cleanUnusefulRes];
    });
}
- (unsigned long long)getLocalDataSize
{
    if (![self checkState:nil]) {
        UPLogError(@"UPResource", @"%s[%d]正在执行清空操作时无法返回数据", __PRETTY_FUNCTION__, __LINE__);
        return -1;
    }
    UPLogInfo(@"UPResource", @"%s[%d]开始计算资源包大小", __PRETTY_FUNCTION__, __LINE__);
    unsigned long long folderSize = [self.fileDelegate getFileSize:self.resRootPath];
    UPLogInfo(@"UPResource", @"%s[%d]结束计算资源包大小", __PRETTY_FUNCTION__, __LINE__);
    return folderSize;
}
- (void)getLocalDataSizeAsync:(void (^)(unsigned long long, NSError *))callback
{
    if (![self checkState:nil]) {
        UPLogError(@"UPResource", @"%s[%d]正在执行清空操作时无法返回数据", __PRETTY_FUNCTION__, __LINE__);
        if (callback) {
            callback(-1, [NSError errorWithDomain:@"正在执行清空操作时无法返回数据" code:-1 userInfo:nil]);
        }
        return;
    }
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      unsigned long long folderSize = [self.fileDelegate getFileSize:self.resRootPath];
      if (callback) {
          callback(folderSize, nil);
      }
    });
}


@end

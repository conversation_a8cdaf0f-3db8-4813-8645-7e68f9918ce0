//
//  UPResourcePreLoadTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/6.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourcePreLoadTask.h"
#import "UPResourceCallback.h"
#import "UpPreloadResourceInfo.h"
#import "UPResourceListener.h"
#import <UPLog/UPLog.h>

@interface UPResourcePreLoadTask () <UPResourceCallback, UPResourceListener>
@property (nonatomic, strong) NSArray<UpPreloadResourceInfo *> *preloadInfoList;
@property (nonatomic, weak) id<UPResourceCallback> callback;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *needInstallResList;
@end

@implementation UPResourcePreLoadTask
- (instancetype)initResList:(NSArray<UpPreloadResourceInfo *> *)preloadInfoList callback:(id<UPResourceCallback>)callback
{
    if (self = [super init]) {

        self.preloadInfoList = preloadInfoList;
        self.callback = callback;
    }
    return self;
}
- (void)start
{
    if (self.preloadInfoList.count == 0) {
        UPLogError(@"UPResource", @"预加载任务列表不能为空");
        return;
    }
    UPLogInfo(@"UPResource", @"开始预加载任务");
    [self fetchNormalResList:self.preloadInfoList.mutableCopy
             needInstallList:@[].mutableCopy
                  completion:^(NSArray<UPResourceInfo *> *infoList) {
                    self.needInstallResList = infoList.mutableCopy;
                    [self installResInfoFromInfoList:self.needInstallResList];
                  }];
}
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
}
- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    UPLogInfo(@"UPResource", @"预加载资源%@安装结果为%@", info.name, message);
    if ([self.callback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
        [self.callback onResult:success message:message resourceInfo:info];
    }
    [self.needInstallResList removeObject:info];
    if (self.needInstallResList.count) {
        [self installResInfoFromInfoList:self.needInstallResList];
        return;
    }
    UPLogInfo(@"UPResource", @"结束预加载任务");
}

#pragma mark private methods
- (void)installResInfoFromInfoList:(NSArray<UPResourceInfo *> *)infoList
{
    UPResourceInfo *info = infoList.firstObject;
    info.priority = UPResourceDownLoadPriorityMid;
    dispatch_async(dispatch_get_global_queue(0, 0), ^{
      [self.resManager install:info callback:self listener:self];
    });
}
- (void)fetchNormalResList:(NSMutableArray<UpPreloadResourceInfo *> *)preResList needInstallList:(NSMutableArray<UPResourceInfo *> *)needInstallList completion:(void (^)(NSArray<UPResourceInfo *> *infoList))completion
{
    UpPreloadResourceInfo *preResInfo = preResList.firstObject;
    [self.resManager updateNormalResList:preResInfo.name
                                    type:preResInfo.type
                              completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
                                if (error) {
                                    NSString *message = [NSString stringWithFormat:@"查找资源%@失败", preResInfo.name];
                                    [self.callback onResult:NO message:message resourceInfo:nil];
                                }
                                else {
                                    if ([infoList isKindOfClass:[NSArray class]]) {
                                        [needInstallList addObjectsFromArray:infoList];
                                    }
                                }
                                if (preResInfo) {
                                    [preResList removeObject:preResInfo];
                                }
                                if (!preResList.count) {
                                    if (completion) {
                                        completion(needInstallList);
                                    }
                                }
                                else {
                                    [self fetchNormalResList:preResList needInstallList:needInstallList completion:completion];
                                }

                              }];
}


@end

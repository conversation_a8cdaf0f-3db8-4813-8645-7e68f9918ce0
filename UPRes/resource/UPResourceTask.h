//
//  UPResourceTask.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPFileDelegate.h"

@class UPResourceInfo, UPResourceResult;
@protocol UPResourceOperator;

@interface UPResourceTask : NSObject
@property (nonatomic, readonly, copy) NSString *taskId;
@property (nonatomic, strong) UPResourceInfo *resourceInfo;
@property (nonatomic, readonly, strong) id<UPResourceOperator> resourceOperator;
@property (nonatomic, copy) NSString *downloadFile;
@property (nonatomic, copy) NSString *resourceFile;

+ (UPResourceTask *)resourceTask:(UPResourceInfo *)resourceInfo operator:(id<UPResourceOperator>)resourceOperator;
- (UPResourceResult *)execute;
- (void)cancel;
- (void)resume;

@end

//
//
//  UPResourcePresetHelper.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPResourceRepository, UPResourceInfo;
@protocol UPPresetFileLoader
, UPFileDelegate, UPTimeDelegate, UPResourceReporter;
NS_ASSUME_NONNULL_BEGIN

@interface UPResourcePresetHelper : NSObject
- (instancetype)initScanner:(id<UPPresetFileLoader>)scanner repository:(UPResourceRepository *)repository fileDelegate:(id<UPFileDelegate>)fileDelegate appVersion:(NSString *)appVersion timeDelegate:(id<UPTimeDelegate>)timeDelegate;
- (nullable NSArray<UPResourceInfo *> *)getPresetInfos;
- (BOOL)presetInfoRelations:(NSArray<UPResourceInfo *> *)infoList;
- (NSArray<UPResourceInfo *> *)getNeedInstallResource:(NSArray<UPResourceInfo *> *)infoList;
- (UPResourceInfo *)getLocalPresetedInfo:(UPResourceInfo *)presetInfo;
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate;
@end

NS_ASSUME_NONNULL_END

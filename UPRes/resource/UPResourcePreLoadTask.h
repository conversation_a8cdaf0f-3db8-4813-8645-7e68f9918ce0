//
//  UPResourcePreLoadTask.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/6.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceManager.h"

@class UpPreloadResourceInfo;
@protocol UPResourceCallback;
NS_ASSUME_NONNULL_BEGIN

@interface UPResourcePreLoadTask : NSObject
@property (nonatomic, strong) UPResourceManager *resManager;

- (instancetype)initResList:(NSArray<UpPreloadResourceInfo *> *)preloadInfoList callback:(id<UPResourceCallback>)callback;
- (void)start;
@end

NS_ASSUME_NONNULL_END

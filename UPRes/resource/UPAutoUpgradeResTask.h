//
//  UPAutoUpgradeResTask.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/20.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class UpPreloadResourceInfo;
@class UPResourceManager;
@protocol UPResourceCallback;
NS_ASSUME_NONNULL_BEGIN

@interface UPAutoUpgradeResTask : NSObject
- (instancetype)initResList:(NSArray<UpPreloadResourceInfo *> *)infoList manager:(UPResourceManager *)manager callback:(id<UPResourceCallback>)callback;
- (void)start;
@end

NS_ASSUME_NONNULL_END

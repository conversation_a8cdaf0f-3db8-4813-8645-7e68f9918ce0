//
//  UPResourceUninstaller.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/17.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceUninstaller.h"
#import "UPResourceInfo.h"
#import "UPResourceTask.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPResourceDirectory.h"
#import "UPResourceRepository.h"
#import "UPResourceResult.h"
@implementation UPResourceUninstaller
#pragma mark - Override Methods
- (UPResourceResult *)operate:(UPResourceTask *)task
{
    UPResourceInfo *info = task.resourceInfo;
    NSString *path = info.path;
    NSString *indexPath = info.indexPath;
    if (![self.fileDelegate exists:path]) {
        NSString *errMsg = [NSString stringWithFormat:@"资源(%@)未安装！", info.description];
        UPLogInfo(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        [self notifyResult:YES message:errMsg info:info];
        return [[UPResourceResult alloc] initResult:UPResourceErrorCodeSUCCESS extraInfo:errMsg extraData:info];
    }
    info.active = NO;
    info.path = nil;
    info.indexPath = nil;
    BOOL result = [self.repository updateResourceInfo:info];
    if (result) {
        info.path = path;
        info.indexPath = indexPath;
        result = [self.processor process:task callback:self.callback listener:self.listener];
    }
    else {
        [self.processor setResult:@"更新本地数据库失败"];
    }
    if (result) {
        info.path = nil;
        info.indexPath = nil;
    }
    NSString *message = result ? @"卸载成功!" : [self.processor result];
    [self notifyResult:result message:message info:info];
    if (!result) {
        return [UPResourceResult failureResult:message];
    }
    return [[UPResourceResult alloc] initResult:UPResourceErrorCodeSUCCESS extraInfo:message extraData:info];
}

- (NSString *)name
{

    return NSStringFromClass(self.class);
}
@end

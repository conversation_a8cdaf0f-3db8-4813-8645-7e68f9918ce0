//
//  UPResourceInstaller.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceInstaller.h"
#import "UPResourceInfo.h"
#import "UPResourceTask.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPResourceDirectory.h"
#import "UPResourceRepository.h"
#import "UPResourceTracker.h"
#import "UPResourceTrackerProvider.h"
#import "UPResourceConfig.h"
#import "UPResourceManager.h"
#import "UPResourceResult.h"
@interface UPResourceInstaller ()
@property (nonatomic, strong) UPResourceInfo *resourceInfo;
@end

@implementation UPResourceInstaller
#pragma mark - Override Methods
- (UPResourceResult *)operate:(UPResourceTask *_Nonnull)task
{
    self.resourceInfo = task.resourceInfo;
    UPResourceInfo *info = task.resourceInfo;
    if (info.active) {
        NSString *errMsg = [NSString stringWithFormat:@"资源(%@)已安装！", info.description];
        UPLogInfo(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        [self notifyResult:YES message:errMsg info:info];
        return [[UPResourceResult alloc] initResult:UPResourceErrorCodeSUCCESS extraInfo:errMsg extraData:info];
    }

    NSString *downloadPath = [self.directory downloadFolderPathOfResource:info];
    task.downloadFile = downloadPath;
    NSString *resourcePath = [self.directory folderPathOfResource:info];
    task.resourceFile = resourcePath;
    BOOL result = NO;
    if ([self.processor process:task callback:self.callback listener:self.listener]) {
        info.path = resourcePath;
        info.active = YES;
        result = [self.repository updateResourceInfo:info];
        if (!result) {
            [self.processor setResult:@"更新本地数据库失败"];
            info.path = nil;
            info.indexPath = nil;
        }
    }
    NSString *message = result ? @"安装成功!" : [self.processor result];
    [self notifyResult:result message:message info:info];
    if (result && [self.trackProvider.getResourceTracker respondsToSelector:@selector(reportInstallResult:)]) {
        dispatch_async(dispatch_get_main_queue(), ^{
          UPResourceReportInfo *reportInfo = [self getResourceReportInfoWith:info];
          [self.trackProvider.getResourceTracker reportInstallResult:reportInfo];
        });
    }
    return [self result:result message:message info:info];
}
- (UPResourceResult *)result:(BOOL)result message:(NSString *)message info:(UPResourceInfo *)info
{
    if (!result) {
        return [UPResourceResult failureResult:message];
    }
    return [[UPResourceResult alloc] initResult:UPResourceErrorCodeSUCCESS extraInfo:message extraData:info];
}

- (void)cancel:(UPResourceTask *)task
{

    if ([task.resourceInfo isKindOfClass:[UPResourceInfo class]] && [self.processor cancel] && !self.resourceInfo) {
        NSString *message = [NSString stringWithFormat:@"资源(%@)已取消安装！", task.resourceInfo.description];
        [self notifyResult:NO message:message info:task.resourceInfo];
    }
}

- (void)resume
{
    if ([self.resourceInfo isKindOfClass:[UPResourceInfo class]]) {
        [self.processor resume];
    }
}
- (NSString *)name
{

    return NSStringFromClass(self.class);
}

- (UPResourceReportInfo *)getResourceReportInfoWith:(UPResourceInfo *)info
{
    UPResourceReportInfo *reportInfo = [[UPResourceReportInfo alloc] init];
    reportInfo.res_name = info.name;
    reportInfo.res_type = [UPResourceInfo stringValueOfResourceType:info.type];
    reportInfo.res_version = info.version;
    reportInfo.installed_time = @(self.repository.timeDelegate.currentTimeMillis).stringValue;
    reportInfo.client_id = [UPResourceConfig shareInstance].clientID;
    reportInfo.user_id = [UPResourceConfig shareInstance].user_id;
    reportInfo.app_version = [UPResourceConfig shareInstance].appVersion;
    reportInfo.is_preset = info.preset ? @"true" : @"false";
    reportInfo.is_gray = self.trackProvider.getSettings.environment == UPResEnvironmentProduction ? @"false" : @"true";
    return reportInfo;
}

@end

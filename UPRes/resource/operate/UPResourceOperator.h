//
//  UPResourceOperator.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceCallback.h"
#import "UPResourceListener.h"
#import "UPResourceProcessor.h"
#import "UpResourceCallbackHolder.h"
#import "UpResourceListenerHolder.h"
@class UPResourceRepository;
@class UPResourceTask;
@class UPResourceDirectory, UPResourceResult;

@protocol UPResourceOperator <NSObject>
@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) UPResourceDirectory *directory;
@property (nonatomic, strong) UpResourceCallbackHolder *callback;
@property (nonatomic, strong) UpResourceListenerHolder *listener;
@property (nonatomic, weak) id<UPResourceProcessor> processor;

- (id<UPResourceOperator>)resourceOperator:(UPResourceRepository *)repository directory:(UPResourceDirectory *)directory callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener processor:(id<UPResourceProcessor>)processor;

- (UPResourceResult *)operate:(UPResourceTask *_Nonnull)task;
- (void)cancel:(UPResourceTask *)task;
- (void)resume;
@optional
- (NSString *)name;

@end

@class UPResourceInfo;
@interface UPResourceOperatorBase : NSObject <UPResourceOperator>
- (void)notifyResult:(BOOL)success message:(NSString *)message info:(UPResourceInfo *)info;
@end

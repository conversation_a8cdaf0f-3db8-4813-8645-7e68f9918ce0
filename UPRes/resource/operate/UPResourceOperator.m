//
//  UPResourceOperator.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/18.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceOperator.h"
#import "UPResourceDirectory.h"
#import "UPResourceResult.h"
@interface UPResourceOperatorBase ()
@property (nonatomic, strong) UPResourceRepository *resRepository;
@property (nonatomic, strong) UPResourceDirectory *resDirectory;
@property (nonatomic, strong) UpResourceCallbackHolder *resCallback;
@property (nonatomic, strong) UpResourceListenerHolder *resListener;
@property (nonatomic, strong) NSObject<UPResourceProcessor> *resProcessor;

@end

@implementation UPResourceOperatorBase
#pragma mark - UPResourceOperator
- (UPResourceRepository *)repository
{
    return self.resRepository;
}

- (void)setRepository:(UPResourceRepository *)repository
{
    self.resRepository = repository;
}

- (UPResourceDirectory *)directory
{
    return self.resDirectory;
}

- (void)setDirectory:(UPResourceDirectory *)directory
{
    self.resDirectory = directory;
}

- (UpResourceCallbackHolder *)callback
{
    return self.resCallback;
}

- (void)setCallback:(UpResourceCallbackHolder *)callback
{
    self.resCallback = callback;
}

- (UpResourceListenerHolder *)listener
{
    return self.resListener;
}

- (void)setListener:(UpResourceListenerHolder *)listener
{
    self.resListener = listener;
}

- (id<UPResourceProcessor>)processor
{
    return self.resProcessor;
}

- (void)setProcessor:(id<UPResourceProcessor>)processor
{
    self.resProcessor = processor;
}

- (id<UPResourceOperator>)resourceOperator:(UPResourceRepository *)repository directory:(UPResourceDirectory *)directory callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener processor:(id<UPResourceProcessor>)processor
{
    self.resRepository = repository;
    self.resDirectory = directory;
    self.resCallback = [[UpResourceCallbackHolder alloc] initWith:callback];
    self.resListener = [[UpResourceListenerHolder alloc] initWith:listener];
    self.resProcessor = processor;
    return self;
}

- (UPResourceResult *)operate:(UPResourceTask *)task
{
    return nil;
}

- (void)cancel:(UPResourceTask *)task
{
}

- (void)resume
{
}

#pragma mark - Public Methods
- (void)notifyResult:(BOOL)success message:(NSString *)message info:(UPResourceInfo *)info
{
    if (![self.resCallback conformsToProtocol:@protocol(UPResourceCallback)] || ![self.resCallback respondsToSelector:@selector(onResult:message:resourceInfo:)]) {
        return;
    }
    if ([NSThread isMainThread]) {
        [self.resCallback onResult:success message:message resourceInfo:info];
    }
    else {
        dispatch_async(dispatch_get_main_queue(), ^{
          [self.resCallback onResult:success message:message resourceInfo:info];
        });
    }
}
@end

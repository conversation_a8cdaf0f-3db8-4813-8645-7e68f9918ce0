//
//  UPResourceCondition.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/16.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceCondition.h"
#import "UPResCommonFunctions.h"
#import "UPResourceConfig.h"
NSString *const UPQuery_TypeString_HTML = @"h5";
NSString *const UPQuery_TypeString_DeviceConfig = @"config";
NSString *const UPQuery_TypeString_APICloud = @"apicloud";
NSString *const UPQuery_TypeString_ReactNative = @"react";
NSString *const UPQuery_TypeString_MPAAS = @"mPaaS";
NSString *const UPQuery_TypeString_DEVICE_CUSTOM_INFO = @"deviceCustomInfo";
NSString *const UPQuery_TypeString_All = @"";
NSString *const UPQuery_TypeString_ConfigApp = @"configAPP";
NSString *const UPQuery_TypeString_Video = @"video";
NSString *const UPQuery_TypeString_Flutter = @"flutter";
NSString *const UPQuery_TypeString_APPFuncModel = @"app-func-model";
NSString *const UPQuery_TypeString_Routes = @"routes";
NSString *const UPQuery_TypeString_Picture = @"picture";
NSString *const UPQuery_TypeString_Audio = @"audio";
NSString *const UPQuery_TypeString_Other = @"other";
NSString *const UPQuery_TypeString_ConfigFile = @"configFile";
NSString *const UPQuery_TypeString_Lua = @"lua";

NSString *const FROM_FUNC_COMMON_RESOURCE = @"0";
NSString *const FROM_FUNC_DEVICE_RESOURCE = @"2";
NSString *const FROM_FUNC_DEVICE_CONFIG = @"1";
NSString *const FROM_FUNC_DEVICE_CUSTOM_INFO = @"3";
NSString *const FROM_FUNC_BATCH_NORMAL_RESOURCE = @"4";
NSString *const FROM_FUNC_APP_FUNC_MODEL = @"6";
NSString *const FROM_FUNC_DEVICE_LUA = @"7";
@implementation UPResourceCondition

- (instancetype)initResourceType:(UPResourceType)resourceType
{

    if (self = [super init]) {

        _resourceType = resourceType;
    }

    return self;
}

- (NSString *)appVersion
{
    if (_appVersion == nil) {
        return [UPResourceConfig shareInstance].appVersion;
    }
    return _appVersion;
}

+ (NSInteger)numberValueOfFromfunc:(NSString *)fromfunc
{
    NSDictionary *dict = @{ FROM_FUNC_COMMON_RESOURCE : @(0),
                            FROM_FUNC_DEVICE_CONFIG : @(1),
                            FROM_FUNC_DEVICE_RESOURCE : @(2),
                            FROM_FUNC_DEVICE_CUSTOM_INFO : @(3),
                            FROM_FUNC_BATCH_NORMAL_RESOURCE : @(4),
                            FROM_FUNC_APP_FUNC_MODEL : @(6),
                            FROM_FUNC_DEVICE_LUA : @(7)
    };
    return [dict[fromfunc] integerValue];
}

+ (NSString *)stringValueOfResourceType:(UPResourceType)type
{

    switch (type) {
        case UPResourceTypeHTML:
            return UPQuery_TypeString_HTML;
            break;
        case UPResourceTypeReactNative:
            return UPQuery_TypeString_ReactNative;
            break;
        case UPResourceTypeAPICloud:
            return UPQuery_TypeString_APICloud;
            break;
        case UPResourceTypeDeviceConfig:
            return UPQuery_TypeString_DeviceConfig;
            break;
        case UPResourceTypeLua:
            return UPQuery_TypeString_Lua;
            break;
        case UPResourceTypeMPAAS:
            return UPQuery_TypeString_MPAAS;
            break;
        case UPResourceTypeDEVICE_CUSTOM_INFO:
            return UPQuery_TypeString_DEVICE_CUSTOM_INFO;
            break;
        case UPResourceTypeConfigApp:
            return UPQuery_TypeString_ConfigApp;
            break;
        case UPResourceTypeVideo:
            return UPQuery_TypeString_Video;
            break;
        default:
            return [self stringValueOfResourceType2:type];
            break;
    }
}

+ (NSString *)stringValueOfResourceType2:(UPResourceType)type
{
    switch (type) {
        case UPResourceTypeFlutter:
            return UPQuery_TypeString_Flutter;
            break;
        case UPResourceTypeAppFuncModel:
            return UPQuery_TypeString_APPFuncModel;
            break;
        case UPResourceTypeRoutes:
            return UPQuery_TypeString_Routes;
            break;
        case UPResourceTypePicture:
            return UPQuery_TypeString_Picture;
            break;
        case UPResourceTypeAudio:
            return UPQuery_TypeString_Audio;
            break;
        case UPResourceTypeOther:
            return UPQuery_TypeString_Other;
            break;
        case UPResourceTypeConfigFile:
            return UPQuery_TypeString_ConfigFile;
            break;
        case UPResourceTypeAll:
            return UPQuery_TypeString_All;
            break;
        default:
            return UPQuery_TypeString_All;
            break;
    }
}

- (NSString *)combine
{

    return [NSString stringWithFormat:@"rt=%@|name=%@", [UPResourceCondition stringValueOfResourceType:self.resourceType], UPRes_validStringValue(self.resName)];
}

@end

@implementation UPResourceDeviceCondition

- (NSString *)combine
{

    return [NSString stringWithFormat:@"%@|md=%@|ti=%@|pn=%@|tc=%@|dnt=%@", [super combine], UPRes_validStringValue(self.model), UPRes_validStringValue(self.typeId), UPRes_validStringValue(self.prodNo), UPRes_validStringValue(self.deviceType), UPRes_validStringValue(self.deviceNetType)];
}

+ (NSString *)stringFromfuncOfResourceType:(UPResourceType)type
{
    NSDictionary *fromFuncs = @{ @(UPResourceTypeHTML) : FROM_FUNC_DEVICE_RESOURCE,
                                 @(UPResourceTypeDeviceConfig) : FROM_FUNC_DEVICE_CONFIG,
                                 @(UPResourceTypeLua) : FROM_FUNC_DEVICE_LUA,
                                 @(UPResourceTypeAPICloud) : FROM_FUNC_DEVICE_RESOURCE,
                                 @(UPResourceTypeReactNative) : FROM_FUNC_DEVICE_RESOURCE,
                                 @(UPResourceTypeMPAAS) : FROM_FUNC_DEVICE_RESOURCE,
                                 @(UPResourceTypeDEVICE_CUSTOM_INFO) : FROM_FUNC_DEVICE_CUSTOM_INFO,
                                 @(UPResourceTypeConfigApp) : FROM_FUNC_DEVICE_RESOURCE,
                                 @(UPResourceTypeAppFuncModel) : FROM_FUNC_APP_FUNC_MODEL,
                                 @(UPResourceTypeAll) : FROM_FUNC_DEVICE_RESOURCE
    };
    return fromFuncs[@(type)];
}

@end

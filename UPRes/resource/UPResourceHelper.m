//
//  UPResourceHelper.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/20.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceHelper.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>

typedef enum : NSInteger {
    Version_Type_Undefined = 0, //未定义
    Version_Type_Three = 1, // 三段式版本号1.2.1
    Version_Type_Four = 2, // 四段式版本号7.4.2_2022022502
    Version_Type_Five = 3, // 五段式版本号7.4.2.1.2022022501
} UPResourceVersionType;

@implementation UPResNameAndTypeFilter
#pragma mark - UPResourceFilter
- (BOOL)accept:(UPResourceInfo *)info
{
    if (![info isKindOfClass:[UPResourceInfo class]]) {
        return NO;
    }
    NSString *typeString = [UPResourceInfo stringValueOfResourceType:info.type];
    return UPRes_isEqualString(info.name, self.name) && UPRes_isEqualString(typeString, self.type);
}

#pragma mark - Public Methods
- (instancetype)initFilterWithName:(NSString *)name type:(NSString *)type
{
    if (self = [super init]) {
        _name = name;
        _type = type;
    }
    return self;
}

@end

@implementation UPResourceHelper
+ (id<UPResourceFilter>)createNameAndTypeFilterWithName:(NSString *)name type:(NSString *)type
{
    return [[UPResNameAndTypeFilter alloc] initFilterWithName:name type:type];
}

+ (NSArray<UPResourceInfo *> *)filterInfoList:(NSArray<UPResourceInfo *> *)infoList byFilter:(id<UPResourceFilter>)filter
{
    if (UPRes_isEmptyArray(infoList)) {
        return @[];
    }
    if (![filter conformsToProtocol:@protocol(UPResourceFilter)] || ![filter respondsToSelector:@selector(accept:)]) {
        return [NSArray arrayWithArray:infoList];
    }
    NSMutableArray *arr = [NSMutableArray array];
    for (UPResourceInfo *info in infoList) {
        if ([filter accept:info]) {
            [arr addObject:info];
        }
    }
    NSComparator comparator = ^NSComparisonResult(UPResourceInfo *obj1, UPResourceInfo *obj2) {
      NSString *iName = obj2.name;
      if (iName) {
          return [obj1.name compare:iName];
      }
      else {
          return NSOrderedAscending;
      }
    };
    [arr sortUsingComparator:comparator];
    return arr;
}
+ (NSArray<UPResourceInfo *> *)filterByLatestVersionOrIsServerLatest:(NSArray<UPResourceInfo *> *)infoList
{

    if (infoList.count == 0) {

        return nil;
    }
    NSMutableDictionary<NSString *, UPResourceInfo *> *lastDict = [NSMutableDictionary dictionary];

    //first search latest resource by isServerLatest item, if not found resource then use latest version as search condition
    NSArray *kLatestList = [self filterByIsServerLatest:infoList];
    if (kLatestList.count > 0) {
        return kLatestList;
    }

    for (UPResourceInfo *info in infoList) {

        if (![info isKindOfClass:[UPResourceInfo class]]) {
            continue;
        }
        NSString *key = [NSString stringWithFormat:@"%@%@", [UPResourceInfo stringValueOfResourceType:info.type], info.name];
        UPResourceInfo *lastInfo = lastDict[key];
        if (!lastInfo) {
            lastDict[key] = info;
        }
        else if ([self commpareVersion:info.version lastInfo:lastInfo.version]) {

            lastDict[key] = info;
        }
    }
    return lastDict.allValues;
}
+ (NSArray<UPResourceInfo *> *)filterByIsServerLatest:(NSArray<UPResourceInfo *> *)infoList
{
    if (infoList.count == 0) {
        return nil;
    }
    NSMutableDictionary<NSString *, UPResourceInfo *> *lastDict = [NSMutableDictionary dictionary];
    for (UPResourceInfo *info in infoList) {
        if (![info isKindOfClass:[UPResourceInfo class]]) {
            continue;
        }
        NSString *key = [NSString stringWithFormat:@"%@%@", [UPResourceInfo stringValueOfResourceType:info.type], info.name];
        UPResourceInfo *lastInfo = lastDict[key];
        if (!lastInfo) {
            if (info.isServerLatest)
                lastDict[key] = info;
        }
        else {
            if (info.isServerLatest && [self commpareVersion:info.version lastInfo:lastInfo.version])
                lastDict[key] = info;
        }
    }
    return lastDict.allValues;
}
/*
 版本号比较规则：
 https://uh.haier.net:8444/confluence/pages/viewpage.action?pageId=186684311
 比较规则如下：
 比较前三位X.X.X按照高位大小依次比较，相同位数的情况下，高位越大，版本越大。
 若前三位相同，三段式版本>五段式版本>四段式版本
 X.X.X ->1.2.1
 X.X.X_yyyyMMddNN ->7.4.2_2022022502
 X.X.X.E.yyyyMMddNN ->7.4.2.1.2022022501
 */
+ (BOOL)commpareVersion:(NSString *)version1 lastInfo:(NSString *)version2
{
    UPLogInfo(@"UPResource", @"%s[%d]coming to compare version1 %@, version2 %@", __PRETTY_FUNCTION__, __LINE__, version1, version2);
    if (![UPResourceHelper versionIsValid:version1] || ![UPResourceHelper versionIsValid:version2] || [version2 isEqualToString:version1]) {

        return NO;
    }
    NSString *kThreeSegmentVersion1 = [UPResourceHelper getThreeSegmentVersion:version1];
    NSString *kThreeSegmentVersion2 = [UPResourceHelper getThreeSegmentVersion:version2];
    NSArray *array1 = [kThreeSegmentVersion1 componentsSeparatedByString:@"."];
    NSArray *array2 = [kThreeSegmentVersion2 componentsSeparatedByString:@"."];
    if (array1.count < 3 || array2.count < 3) {
        return NO;
    }
    for (NSInteger i = 0; i < 3; i++) {

        if ([array1[i] isEqualToString:array2[i]]) {

            continue;
        }
        return ([array1[i] integerValue] - [array2[i] integerValue]) > 0;
    }
    //前三位一样，则继续比较版本号日期、分支号、自增量
    NSComparisonResult kComparisonResult = [UPResourceHelper compareDifferentSegmentVersion:version1 lastInfo:version2];
    if (NSOrderedDescending == kComparisonResult) {
        return YES;
    }
    else if (NSOrderedAscending == kComparisonResult) {
        return NO;
    }

    return [UPResourceHelper commpareTheSameSegmentVersion:version1 lastInfo:version2];
}
+ (BOOL)isBlank:(NSString *)string
{
    if (![string isKindOfClass:[NSString class]] || string.length == 0) {
        return YES;
    }
    NSString *str = [string stringByTrimmingCharactersInSet:[NSCharacterSet whitespaceAndNewlineCharacterSet]];
    return str.length == 0;
}

+ (BOOL)isNonNullObject:(NSObject *)object conformToProtocol:(Protocol *)pro
{
    return [object isKindOfClass:[NSObject class]] && [object conformsToProtocol:pro];
}

#pragma mark-----------Private Functions-----------

+ (NSComparisonResult)compareDifferentSegmentVersion:(NSString *)version1 lastInfo:(NSString *)version2
{
    NSComparisonResult kComparisonResult = NSOrderedSame;
    //前三位一样，则继续比较版本号日期、分支号、自增量
    if (Version_Type_Three == [UPResourceHelper versionType:version1] || (Version_Type_Five == [UPResourceHelper versionType:version1] && Version_Type_Four == [UPResourceHelper versionType:version2])) {
        return NSOrderedDescending;
    }
    if (Version_Type_Three == [UPResourceHelper versionType:version2] || (Version_Type_Four == [UPResourceHelper versionType:version1] && Version_Type_Five == [UPResourceHelper versionType:version2])) {
        return NSOrderedAscending;
    }
    return kComparisonResult;
}

//compare two four or five segment versions 7.4.2_2022022502
+ (BOOL)commpareTheSameSegmentVersion:(NSString *)version1 lastInfo:(NSString *)version2
{
    NSComparisonResult kComparisonResult = [UPResourceHelper compareBranchNumberForFiveSegmentVersion:version1 lastInfo:version2];
    if (NSOrderedSame != kComparisonResult) {
        return (kComparisonResult == NSOrderedDescending) ? YES : NO;
    }
    NSString *kDateWithIncreasedNumber1 = [UPResourceHelper getDateWithIncreasedNumber:version1];
    NSString *kDateWithIncreasedNumber2 = [UPResourceHelper getDateWithIncreasedNumber:version2];
    return [UPResourceHelper compareDateWithIncreasedNumber:kDateWithIncreasedNumber1 lastInfo:kDateWithIncreasedNumber2];
}

//branch number is bigger ,version is lower
+ (NSComparisonResult)compareBranchNumberForFiveSegmentVersion:(NSString *)version1 lastInfo:(NSString *)version2
{
    NSComparisonResult kComparisonResult = NSOrderedSame;
    if (UPRes_isEmptyString(version1) || UPRes_isEmptyString(version2)) {
        return kComparisonResult;
    }
    if (Version_Type_Five == [UPResourceHelper versionType:version1] && Version_Type_Five == [UPResourceHelper versionType:version2]) {
        NSArray *kVersionComponentsList1 = [version1 componentsSeparatedByString:@"."];
        NSArray *kVersionComponentsList2 = [version2 componentsSeparatedByString:@"."];
        long kMinusResult = [kVersionComponentsList1[3] integerValue] - [kVersionComponentsList2[3] integerValue];
        if (kMinusResult > 0)
            kComparisonResult = NSOrderedAscending;
        else if (kMinusResult < 0)
            kComparisonResult = NSOrderedDescending;
    }
    return kComparisonResult;
}
//yyyyMMddNN,7.4.2_2022022301
+ (NSString *)getDateWithIncreasedNumber:(NSString *)version
{
    NSString *kDateWithIncreasedNumber = @"";
    if (UPRes_isEmptyString(version)) {
        return kDateWithIncreasedNumber;
    }
    if (Version_Type_Five == [UPResourceHelper versionType:version]) {
        NSArray *kVersionComponentsList = [version componentsSeparatedByString:@"."];
        kDateWithIncreasedNumber = kVersionComponentsList[4];
    }
    if (Version_Type_Four == [UPResourceHelper versionType:version]) {
        NSArray *kVersionComponentsList = [version componentsSeparatedByString:@"_"];
        kDateWithIncreasedNumber = kVersionComponentsList[1];
    }
    return kDateWithIncreasedNumber;
}


+ (UPResourceVersionType)versionType:(NSString *)version
{
    UPResourceVersionType kVersionType = Version_Type_Undefined;
    if (UPRes_isEmptyString(version)) {
        return kVersionType;
    }
    NSArray *kVersionComponentsList = [version componentsSeparatedByString:@"."];
    NSUInteger kCount = kVersionComponentsList.count;
    if (5 == kCount) {
        kVersionType = Version_Type_Five;
    }
    else if (3 == kCount) {
        kVersionType = Version_Type_Three;
        if (NSNotFound != [kVersionComponentsList[2] rangeOfString:@"_"].location) {
            kVersionType = Version_Type_Four;
        }
    }
    return kVersionType;
}

+ (NSString *)getThreeSegmentVersion:(NSString *)version
{
    NSString *kThreeSegmentVersion = @"";
    if (UPRes_isEmptyString(version)) {
        return kThreeSegmentVersion;
    }
    UPResourceVersionType kVersionType = [UPResourceHelper versionType:version];
    if (Version_Type_Four == kVersionType) {
        kThreeSegmentVersion = [UPResourceHelper getThreeSegmentVersionForFourSegmentVersion:version];
    }
    else {
        kThreeSegmentVersion = version;
    }

    return kThreeSegmentVersion;
}

+ (NSString *)getThreeSegmentVersionForFourSegmentVersion:(NSString *)version
{
    NSString *kThreeSegmentVersion = @"";
    if (UPRes_isEmptyString(version)) {
        return kThreeSegmentVersion;
    }
    NSArray *kVersionComponentsList = [version componentsSeparatedByString:@"_"];
    if (kVersionComponentsList.count > 0) {
        kThreeSegmentVersion = kVersionComponentsList[0];
    }
    return kThreeSegmentVersion;
}

/*
 parameter format:yyyyMMddNN
 yyyyMMdd 代表 年（四位年）月（两位月）日 （两位日）
 N代表自然数
 N的范围是0~9
 NN 代表 2位自增量 ，从01开始
 */
+ (BOOL)compareDateWithIncreasedNumber:(NSString *)firstDateWithIncreasedNumber lastInfo:(NSString *)secondDateWithIncreasedNumber
{
    UPLogInfo(@"UPResource", @"%s[%d]increased number and date firstDateWithIncreasedNumber %@, secondDateWithIncreasedNumber %@", __PRETTY_FUNCTION__, __LINE__, firstDateWithIncreasedNumber, secondDateWithIncreasedNumber);
    BOOL kCompareResult = NO;
    if (UPRes_isEmptyString(firstDateWithIncreasedNumber) || UPRes_isEmptyString(secondDateWithIncreasedNumber)) {
        return kCompareResult;
    }
    return (firstDateWithIncreasedNumber.integerValue - secondDateWithIncreasedNumber.integerValue) > 0;
}

//version includes three type characters:number . _
+ (BOOL)versionIsValid:(NSString *)version
{
    if (UPRes_isEmptyString(version)) {
        return NO;
    }
    NSString *regex = @"(^[\\d]+(\\.[\\d]+){2})($|(_[\\d]+|((\\.[\\d]+){2}))$)";
    NSPredicate *pred = [NSPredicate predicateWithFormat:@"SELF MATCHES %@", regex];
    if ([pred evaluateWithObject:version]) {
        return YES;
    }
    UPLogInfo(@"UPResource", @"%s[%d] version %@ is not valid", __PRETTY_FUNCTION__, __LINE__, version);
    return NO;
}
@end

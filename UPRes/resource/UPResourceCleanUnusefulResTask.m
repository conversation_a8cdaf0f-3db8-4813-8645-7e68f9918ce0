//
//  UPResourceCleanUnusefulResTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/8/7.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceCleanUnusefulResTask.h"
#import <UPLog/UPLog.h>
#import "UPResourceRepository.h"
#import "UPResourceQuery.h"
#import "UPDatabaseDelegate.h"
#import "UPResourceHelper.h"
#import "UPResCommonFunctions.h"

@interface UPResourceCleanUnusefulResTask () <UPResourceFilter>

@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *allResourceInfos;
@property (nonatomic, strong) NSArray<UPResourceQuery *> *allQuerys;
@property (nonatomic, strong) NSArray *shouldIgnoreCleanArray;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *installResourceList;
@end

@implementation UPResourceCleanUnusefulResTask

- (instancetype)initCleanUnusefulResTask:(UPResourceRepository *)repository
{
    if (self = [super init]) {
        _repository = repository;
    }
    return self;
}

- (void)queryResourceAndQuerys
{
    id<UPDatabaseDelegate> datebaseDelegate = self.repository.databaseDelegate;
    _allResourceInfos = [datebaseDelegate searchResourceList:UPResourceTypeAll name:nil];
    _allQuerys = [datebaseDelegate searchAllQuerys];
    _installResourceList = [UPResourceHelper filterInfoList:_allResourceInfos byFilter:self];
}
#pragma mark - public method
- (void)cleanUnusefulRes
{
    UPLogInfo(@"UPResource", @"%s%d--开始清除无用资源信息", __func__, __LINE__);
    if (_installResourceList.count == 0) {
        [self queryResourceAndQuerys];
    }
    //    [self cleanDetachedResource:_installResourceList];
    if (_installResourceList.count == 0) {
        UPLogInfo(@"UPResource", @"%s%d无需要清理的资源包", __func__, __LINE__);
        [self deleteUselessQuerys:[self searchDetachedQuerys:_allQuerys by:_allResourceInfos]];
    }
    else {
        [self cleanAttachedResource:_installResourceList];
    }
    UPLogInfo(@"UPResource", @"%s%d--完成清除无用资源信息", __func__, __LINE__);
}

#pragma mark - UPResourceFilter
- (BOOL)accept:(UPResourceInfo *)info
{
    if (info.type == UPResourceTypeConfigFile) {
        return info.active && !info.preset;
    }
    return info.active;
}

#pragma mark - private method
/**
 * 清理资源表中无记录的资源文件
 */
- (void)cleanDetachedResource:(NSArray<UPResourceInfo *> *)installResourceList
{
    id<UPFileDelegate> fileDelegate = self.repository.fileDelegate;
    UPResourceDirectory *directory = self.repository.directory;
    NSString *rootPath = directory.resourceRootFolderPath;
    NSArray<NSString *> *subPath = [fileDelegate listSubPaths:rootPath];
    NSMutableArray *needDeletePaths = [NSMutableArray array];
    for (NSString *obj in subPath) {
        UPResourceType type = [directory getResourceTypeByFolderName:obj.lastPathComponent];
        if ([self.shouldIgnoreCleanArray containsObject:@(type)]) {
            continue;
        }
        NSArray *resPaths = [fileDelegate listSubPaths:obj];
        if (resPaths.count == 0) {
            [needDeletePaths addObject:obj];
        }
        else {
            [needDeletePaths addObjectsFromArray:[self findNeedDeleteSubPaths:resPaths infoList:installResourceList type:type]];
        }
    }
    for (NSString *path in needDeletePaths) {
        [fileDelegate deletePath:path];
    }
}

/**
 * 清理资源表中有记录的资源以及关联关系
 */
- (void)cleanAttachedResource:(NSArray<UPResourceInfo *> *)installResourceList
{
    NSDictionary<NSString *, NSArray<UPResourceInfo *> *> *installResourceMap = [self integrateInstallResource:installResourceList];
    NSArray<UPResourceQuery *> *allInstallResourceQuerys = [self searchAttachedQuerys:_allQuerys by:installResourceList];
    [self findNeedDeleteResourceAndQuery:installResourceMap
                                  querys:allInstallResourceQuerys
                                complete:^(NSArray<UPResourceInfo *> *needDeleteResources, NSArray<UPResourceQuery *> *needDeleteQuerys) {
                                  [self deleteResource:needDeleteResources query:needDeleteQuerys];
                                }];
}

- (void)findNeedDeleteResourceAndQuery:(NSDictionary<NSString *, NSArray<UPResourceInfo *> *> *)installResourceMap querys:(NSArray<UPResourceQuery *> *)allInstallResourceQuerys complete:(void (^)(NSArray<UPResourceInfo *> *, NSArray<UPResourceQuery *> *))complete
{
    NSMutableArray<UPResourceInfo *> *needDeleteResources = [NSMutableArray array];
    NSMutableArray<UPResourceQuery *> *needDeleteQuerys = [NSMutableArray array];
    [installResourceMap.allValues enumerateObjectsUsingBlock:^(NSArray<UPResourceInfo *> *_Nonnull resourceList, NSUInteger idx, BOOL *_Nonnull stop) {
      UPResourceInfo *latestInstallResource = [self.repository filterLatestInstallResourceBySameNameType:resourceList];
      [resourceList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull resourceInfo, NSUInteger idx, BOOL *_Nonnull stop) {
        NSArray<UPResourceQuery *> *queryList = [self searchAttachedQuerys:allInstallResourceQuerys by:@[ resourceInfo ]];
        if ((UPRes_isEmptyArray(queryList) && latestInstallResource.itemId != resourceInfo.itemId) || (!resourceInfo.isTheme && resourceInfo.type != UPResourceTypeOther && !resourceInfo.isServerLatest && latestInstallResource.itemId != resourceInfo.itemId)) {
            [needDeleteResources addObject:resourceInfo];
            [needDeleteQuerys addObjectsFromArray:[self searchAttachedQuerys:self->_allQuerys by:@[ resourceInfo ]]];
        }
        else {
            [needDeleteQuerys addObjectsFromArray:[self findSameQuery:queryList]];
        }
      }];
    }];

    NSMutableSet<UPResourceQuery *> *needDeleteQuerySet = [NSMutableSet setWithArray:needDeleteQuerys];
    [needDeleteQuerySet addObjectsFromArray:[self searchDetachedQuerys:_allQuerys by:_allResourceInfos]];
    [needDeleteQuerys setArray:needDeleteQuerySet.allObjects];
    NSArray<UPResourceInfo *> *invalidateResList = [self.repository.databaseDelegate searchResourceInfoByStatus:UPResourceStatusOFF name:nil type:UPResourceTypeAll version:nil];
    [needDeleteResources addObjectsFromArray:invalidateResList];
    complete ? complete(needDeleteResources.copy, needDeleteQuerys.copy) : nil;
}

- (NSDictionary<NSString *, NSArray<UPResourceInfo *> *> *)integrateInstallResource:(NSArray<UPResourceInfo *> *)installResourceList
{
    NSMutableDictionary<NSString *, NSMutableArray<UPResourceInfo *> *> *resourceMap = [NSMutableDictionary dictionary];
    [installResourceList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSString *infoKey = [NSString stringWithFormat:@"%@%@", obj.name, [UPResourceInfo stringValueOfResourceType:obj.type]];
      NSMutableArray<UPResourceInfo *> *infosArray = resourceMap[infoKey];
      if (!infosArray) {
          infosArray = [NSMutableArray array];
      }
      [infosArray addObject:obj];
      [resourceMap setValue:infosArray forKey:infoKey];
    }];

    return resourceMap.copy;
}

- (NSArray<UPResourceQuery *> *)findSameQuery:(NSArray<UPResourceQuery *> *)queryList
{
    queryList = [queryList sortedArrayUsingComparator:^NSComparisonResult(UPResourceQuery *obj1, UPResourceQuery *obj2) {
      return obj1.updateTime < obj2.updateTime;
    }];
    NSMutableArray<NSString *> *keys = [NSMutableArray array];
    NSMutableArray<UPResourceQuery *> *sameQueryList = [NSMutableArray array];
    for (UPResourceQuery *query in queryList) {
        NSString *key = [NSString stringWithFormat:@"%@|fromFunc=%@", query.conditionStr, query.fromFunc];
        if (![keys containsObject:key]) {
            [keys addObject:key];
        }
        else {
            [sameQueryList addObject:query];
        }
    }

    return sameQueryList.copy;
}

- (UPResourceInfo *)findResourceInfo:(NSArray<UPResourceInfo *> *)infoList name:(NSString *)name type:(UPResourceType)type version:(NSString *)version
{
    UPResourceInfo *res = nil;
    for (UPResourceInfo *info in infoList) {
        if (info.type == type && [info.version isEqualToString:version] && [info.name isEqualToString:name]) {
            res = info;
            break;
        }
    }
    return res;
}

- (NSArray<UPResourceQuery *> *)searchDetachedQuerys:(NSArray<UPResourceQuery *> *)querys by:(NSArray<UPResourceInfo *> *)resources
{
    NSMutableArray<UPResourceQuery *> *detachedQuerys = [NSMutableArray array];
    NSMutableArray<NSString *> *attachedQueryIds = [NSMutableArray array];
    for (UPResourceQuery *query in querys) {
        if ([attachedQueryIds containsObject:query.correlationId]) {
            continue;
        }
        __block BOOL isAttached = NO;
        [resources enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull resourceInfo, NSUInteger idx, BOOL *_Nonnull stop) {
          if (query.correlationId.intValue == resourceInfo.itemId) {
              [attachedQueryIds addObject:query.correlationId];
              isAttached = YES;
              *stop = YES;
          }
        }];
        if (!isAttached) {
            [detachedQuerys addObject:query];
        }
    }

    return detachedQuerys.copy;
}

- (NSArray<UPResourceQuery *> *)searchAttachedQuerys:(NSArray<UPResourceQuery *> *)querys by:(NSArray<UPResourceInfo *> *)resources
{
    NSMutableArray<UPResourceQuery *> *attachedQuerys = [NSMutableArray array];
    NSMutableArray<NSString *> *attachedQueryIds = [NSMutableArray array];
    for (UPResourceQuery *query in querys) {
        if ([attachedQueryIds containsObject:query.correlationId]) {
            [attachedQuerys addObject:query];
            continue;
        }
        [resources enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull resourceInfo, NSUInteger idx, BOOL *_Nonnull stop) {
          if (query.correlationId.intValue == resourceInfo.itemId) {
              [attachedQueryIds addObject:query.correlationId];
              [attachedQuerys addObject:query];
              *stop = YES;
          }
        }];
    }

    return attachedQuerys.copy;
}

- (void)deleteResource:(NSArray<UPResourceInfo *> *)needDeleteResList query:(NSArray<UPResourceQuery *> *)needDeleteQueryList
{
    [self deleteUselessQuerys:needDeleteQueryList];
    [self deleteUselessResources:needDeleteResList];
}

- (void)deleteUselessQuerys:(NSArray<UPResourceQuery *> *)querys
{
    id<UPDatabaseDelegate> datebaseDelegate = self.repository.databaseDelegate;
    BOOL deleteQueryResult = [datebaseDelegate deleteQuerys:querys];
    if (!deleteQueryResult) {
        UPLogError(@"UPResource", @"%s%d--删除条件列表失败", __func__, __LINE__);
    }
}

- (void)deleteUselessResources:(NSArray<UPResourceInfo *> *)resources
{
    id<UPDatabaseDelegate> datebaseDelegate = self.repository.databaseDelegate;
    BOOL deleteResResult = [datebaseDelegate deleteResourceInfos:resources];
    if (!deleteResResult) {
        UPLogError(@"UPResource", @"%s%d--删除无用资源列表失败", __func__, __LINE__);
    }

    //删除文件
    id<UPFileDelegate> fileDelegate = self.repository.fileDelegate;
    [resources enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (![fileDelegate exists:obj.path]) {
          return;
      }
      [fileDelegate deletePath:obj.path];
    }];
}

- (NSArray<NSString *> *)findNeedDeleteSubPaths:(NSArray *)resPaths infoList:(NSArray<UPResourceInfo *> *)infoList type:(UPResourceType)type
{
    NSMutableArray<NSString *> *needDeletePath = [NSMutableArray array];
    for (NSString *path in resPaths) {
        NSArray *array = [path.lastPathComponent componentsSeparatedByString:@"@"];
        NSString *name = array.firstObject;
        NSString *version = array.lastObject;
        if ([name hasPrefix:THEMEPREFIX])
            continue;
        UPResourceInfo *res = [self findResourceInfo:infoList name:name type:type version:version];
        if (!res) {
            [needDeletePath addObject:path];
        }
    }
    return needDeletePath.copy;
}

- (NSArray *)shouldIgnoreCleanArray
{
    if (!_shouldIgnoreCleanArray) {
        _shouldIgnoreCleanArray = [[NSArray alloc] initWithObjects:@(UPResourceTypeAll), @(UPResourceTypeDeviceConfig), @(UPResourceTypeLua), @(UPResourceTypeConfigApp), @(UPResourceTypeAppFuncModel), @(UPResourceTypeRoutes), @(UPResourceTypeOther), @(UPResourceTypeConfigFile), nil];
    }
    return _shouldIgnoreCleanArray;
}
@end

//
//  UpResourceCleanerImpl.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/22.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceCleanerImpl.h"
#import "UPResCommonFunctions.h"

static NSInteger const STATE_RUNNING = 0;
static NSInteger const STATE_CLEAN_DATA = 10;

@interface UpResourceCleanerImpl () <UPResourceFilter>

/**
 taskMan
 **/
@property (nonatomic, strong) UpResourceTaskMan *taskMan;

/**
 repository
 **/
@property (nonatomic, strong) UPResourceRepository *repository;

@property (nonatomic, assign) NSInteger stateRef;

@property (nonatomic, strong) NSArray *shouldKeepArray;

@end

@implementation UpResourceCleanerImpl

- (instancetype)initUpResourceCleanerImpl:(UpResourceTaskMan *)taskMan repository:(UPResourceRepository *)repository
{

    if (self = [super init]) {

        _taskMan = taskMan;
        _repository = repository;
        _stateRef = STATE_RUNNING;
    }
    return self;
}

- (BOOL)cleanLocalData
{
    if (self.stateRef == STATE_RUNNING) {
        self.stateRef = STATE_CLEAN_DATA;
        [self.taskMan clearTasks];
        BOOL result = [self.repository cleanLocalData];
        self.stateRef = STATE_RUNNING;
        return result;
    }
    return NO;
}

- (BOOL)cleanLocalDataKeepPresetRes:(NSArray<UpPreloadResourceInfo *> *)keepResNameList
{
    if (self.stateRef == STATE_RUNNING) {
        self.stateRef = STATE_CLEAN_DATA;
        NSArray<UPResourceInfo *> *filterList = [self filterKeepPresetResList:keepResNameList];
        [self.taskMan clearTasks];

        BOOL result = [self.repository cleanDataKeepPresetRes:filterList];
        self.stateRef = STATE_RUNNING;
        return result;
    }
    return NO;
}

- (BOOL)isCleaning
{
    return self.stateRef == STATE_CLEAN_DATA;
}

#pragma mark - private

- (NSArray<UPResourceInfo *> *)filterKeepPresetResList:(NSArray<UpPreloadResourceInfo *> *)keepResNameList
{
    NSMutableArray *keepList = [NSMutableArray new];
    NSArray<UPResourceInfo *> *infos = [self.repository getEntireList:self];
    for (UpPreloadResourceInfo *info in keepResNameList) {
        if (info.name.length == 0) {
            continue;
        }
        UPResourceInfo *latestInfo;
        if (!UPRes_isEmptyString(info.version)) {
            latestInfo = [self.repository getResourcInfo:info.name type:info.type version:info.version];
        }
        else {
            latestInfo = [self.repository getLatestInstalledInfo:info.name type:info.type];
        }
        BOOL isPreset = [self isInfoPreset:latestInfo infos:infos];
        if (latestInfo && (isPreset || [latestInfo.name hasPrefix:THEMEPREFIX] || latestInfo.type == UPResourceTypeConfigFile)) {
            [keepList addObject:latestInfo];
        }
    }
    // 清理本地缓存不清理小优视频类型资源
    [infos enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([self.shouldKeepArray containsObject:@(obj.type)] && ![keepList containsObject:obj]) {
          [keepList addObject:obj];
      }
    }];
    return keepList;
}

- (BOOL)isInfoPreset:(UPResourceInfo *)info infos:(NSArray<UPResourceInfo *> *)infos
{
    for (UPResourceInfo *judgeInfo in infos) {
        if ([judgeInfo.name isEqualToString:info.name] && judgeInfo.type == info.type) {
            return YES;
        }
    }

    return info.preset;
}

- (NSArray *)shouldKeepArray
{
    if (!_shouldKeepArray) {
        _shouldKeepArray = [[NSArray alloc] initWithObjects:@(UPResourceTypeVideo), @(UPResourceTypeRoutes), @(UPResourceTypeOther), @(UPResourceTypeConfigApp), @(UPResourceTypeLua), nil];
    }
    return _shouldKeepArray;
}
#pragma - UPResourceFilter
- (BOOL)accept:(UPResourceInfo *)info
{
    return info.preset || [self.shouldKeepArray containsObject:@(info.type)];
}

@end

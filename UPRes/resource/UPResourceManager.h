//
//  UPResourceManager.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceInfo.h"
#import "UPResourceCallback.h"
#import "UPResourceListener.h"
#import "UPResourceFilter.h"
#import "UPResourceType.h"
#import "UPConnectionDelegate.h"
#import "UPResEnvironment.h"
#import "UPResourceCondition.h"
#import "UpResourceSelector.h"
#import "UPDatabaseDelegate.h"
#import "UPFileDelegate.h"
#import "UPTimeDelegate.h"
#import "UPConnectionDelegate.h"
#import "UPDownloadDelegate.h"
#import "UPResourceDataSource.h"
#import "UPResourceListCallback.h"
#import "UpResourceCleaner.h"
#import "UPResCacheCleanCallback.h"
#import "UPPresetFileLoader.h"
#import "UPResourceDownloadGifType.h"

extern NSString *const UP_RES_ACTION_NO_CONNECTION;
extern NSString *const UP_RES_ACTION_NETWORK_TYPE_MISMATCH;
extern NSString *const UP_RES_ROOT_PATH;
extern NSString *const UP_RES_ROOT_PATH_TEST;
@protocol UPRelationDelegate
, UPRequestDelegate, UPInstallDelegate, UPResourceTracker, UPConnectionDelegate;
@class UPResourceManager, UpPreloadResourceInfo, UPResourceResult;
@interface UPResourceSettings : NSObject
@property (nonatomic, weak, readonly) UPResourceManager *resourceManager;
@property (nonatomic, assign) UPResEnvironment environment;
@property (nonatomic, copy) NSString *appVersion;
@property (nonatomic, assign) UPConnectionType downloadPolicy;
@property (nonatomic, assign) long resourceUpdateInterval;
@property (nonatomic, assign) NSInteger downloadRetryCount;
@property (nonatomic, assign) NSInteger downloadRetryDelay;
- (instancetype)initSettingsWithResourceManager:(UPResourceManager *)resourceManager;

@end

@interface UPResourceManager : NSObject
@property (nonatomic, readonly, strong) UPResourceSettings *settings;
@property (nonatomic, readonly, copy) NSString *version;
@property (nonatomic, assign) UPConnectionType downloadPolicy;
@property (nonatomic, assign) UPResEnvironment environment;
@property (nonatomic, assign) long deviceConfigUpdateInterval;
@property (nonatomic, copy) NSString *appVersion;
@property (nonatomic, copy) UPResourceDownloadGifType (^gifTypeCallback)(void);
@property (nonatomic, strong, readonly) id<UPRelationDelegate> relationDelegate;
@property (nonatomic, strong, readonly) id<UPRequestDelegate> requestDelegate;
@property (nonatomic, strong, readonly) id<UPInstallDelegate> installDelegate;
@property (nonatomic, strong, readonly) id<UPResourceReporter> reporterDelegate;
@property (nonatomic, strong) id<UPResourceTracker> tracker;
@property (nonatomic, strong, readonly) id<UPConnectionDelegate> connectionDelegate;
@property (nonatomic, strong, readonly) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong, readonly) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, assign) NSInteger downloadRetryCount;
@property (nonatomic, assign) NSInteger downloadRetryDelay;
@property (nonatomic, strong) id<UPPresetFileLoader> scanner;

- (instancetype)initResourceManagerWithAppVersion:(NSString *)appVersion
                                      resRootPath:(NSString *)resRootPath
                                       dataSource:(id<UPResourceDataSource>)dataSource
                                 databaseDelegate:(id<UPDatabaseDelegate>)databaseDelegate
                                     fileDelegate:(id<UPFileDelegate>)fileDelegate
                                     timeDelegate:(id<UPTimeDelegate>)timeDelegate
                               connectionDelegate:(id<UPConnectionDelegate>)connectionDelegate
                                 downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate
                                          cleaner:(id<UpResourceCleaner>)cleaner;


/**
 * 安装预置资源（只支持普通资源）
 * 资源命名格式：Type@<EMAIL>
 *
 * @param loader 预置资源扫描器
 * @param callback     安装结果回调
 */
- (void)extractPresetResList:(id<UPPresetFileLoader>)loader callback:(id<UPResourceListCallback>)callback;

/**
 * 同步安装预置资源（只支持普通资源)，指定资源
 * 资源命名格式：Type@<EMAIL>
 *
 * @param loader 预置资源扫描器
 * @param name 资源名
 * @param type 资源类型
 */
- (UPResourceResult *)syncExtractPresetResInfo:(id<UPPresetFileLoader>)loader name:(NSString *)name type:(UPResourceType)type;


///  查询本地普通资源，灰度下返回为nil
/// @param resName 资源名称
/// @param type 资源类型，不支持config类型
- (NSArray<UPResourceInfo *> *)searchNormalResList:(NSString *)resName type:(UPResourceType)type;

/// 查询本地设备资源，灰度下返回为nil
/// @param condition 资源条件
- (NSArray<UPResourceInfo *> *)searchDeviceResList:(UPResourceDeviceCondition *)condition;

/// 查询服务器普通资源列表
/// @param resName 资源名称
/// @param type 资源类型
/// @param immediate 是否立即从服务器请求
/// @param completion 更新结果回调
- (void)requestNormalResList:(NSString *)resName type:(UPResourceType)type immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion;

/// 查询服务器设备资源列表
/// @param condition 资源条件
/// @param immediate 是否立即从服务器请求
/// @param completion 结果回调
- (void)requestDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion;
/**
 * 更新普通资源列表
 *
 * @param resName 普通资源名字
 * @param type  资源类型
 * @param completion  更新结果回调
 * @return 当前资源列表，可能为空列表
 */
- (NSArray<UPResourceInfo *> *)updateNormalResList:(NSString *)resName type:(UPResourceType)type completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion NS_DEPRECATED(2.9.5, 2.9.5, 2.9.5, 2.9.5, "请使用requestNormalResList");

/**
 更新普通资源列表,传入立即更新标识immediate为YES时，在缓存时间周期内，强制从服务器获取数据

 @param resName 普通资源名字
 @param type  资源类型
 @param immediate 立即更新标识
 @param completion 更新结果回调
 @return 当前资源列表，可能为空列表
 */
- (NSArray<UPResourceInfo *> *)updateNormalResList:(NSString *)resName type:(UPResourceType)type immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion NS_DEPRECATED(2.9.5, 2.9.5, 2.9.5, 2.9.5, "请使用requestNormalResList");


/**
 * 更新设备资源列表
 *
 * @param condition 设备资源查询条件
 * @param completion  更新结果回调
 * @return 当前资源列表，可能为空列表
 */
- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion NS_DEPRECATED(2.9.5, 2.9.5, 2.9.5, 2.9.5, "请使用requestDeviceResList");

/**
 * 更新设备资源列表，传入立即更新标识immediate为YES时，在缓存时间周期内，强制从服务器获取数据
 *
 * @param condition 设备资源查询条件
 *  @param immediate 立即更新标识
 * @param completion  更新结果回调
 * @return 当前资源列表，可能为空列表
 */
- (NSArray<UPResourceInfo *> *)updateDeviceResList:(UPResourceDeviceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))completion NS_DEPRECATED(2.9.5, 2.9.5, 2.9.5, 2.9.5, "请使用requestDeviceResList");


/**
 * @brief 安装指定资源<br/>
 * 通过资源信息里的链接下载，并调用相关的处理器（验证，解压）
 *
 * @param info     资源信息
 * @param callback 回调接口，提示、成功、失败等
 * @param listener 监听接口，当前步骤，进度
 * @return 任务ID
 */
- (NSString *)install:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener;

/**
 * 批量预加载资源信息，传入参数为UpPreloadResourceInfo列表，每一个的安装都会有回调
 *
 * @param preloadInfoList 资源名称列表
 * @param callback 结果回调,
 */
- (void)preloadNormalResList:(NSArray<UpPreloadResourceInfo *> *)preloadInfoList callback:(id<UPResourceCallback>)callback;
/**
 * @brief 卸载已安装的资源
 *
 * @param info     资源信息
 * @param callback 回调接口，提示、成功、失败等
 * @return 任务ID
 */
- (NSString *)uninstall:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback;


/**
 * @brief 取消正在进行的安装/更新任务<br/>
 *
 * @param taskID 任务ID
 * @return 取消结果，取消失败时返回NO。
 */
- (BOOL)cancel:(NSString *)taskID;

/**
 * @brief 通过资源信息获取正在进行的任务ID
 *
 * @param info 资源信息
 * @return 任务ID，没有正在进行的任务时返回nil。
 */
- (NSString *)getTaskIdByInfo:(UPResourceInfo *)info;

/**
 * @brief 清空本地的资源缓存方法。
 * @param callback 清空结果回调对象
 * @discussion 清空缓存会清除所有已安装的资源包文件和数据库信息。
 */
- (void)cleanLocalResourceCache:(id<UPResCacheCleanCallback>)callback;

/**
 * 根据资源类型和名称获取当前的资源信息，并自动升级到服务器提供的最新版<br/>
 * 如果当前不存在，则从服务器查询并安装，最终通过回调返回
 *
 * @param resName 普通资源名称
 * @param type 资源类型
 * @param selector  资源选择器
 * @param callback  提示和结果回调
 * @param listener  进度监听
 * @return 当前安装的资源信息
 */
- (UPResourceInfo *)getCommonResource:(NSString *)resName type:(UPResourceType)type selector:(id<UpResourceSelector>)selector callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener;


/**
 * 根据资源类型和名称获取当前的设备资源信息，并自动升级到服务器提供的最新版<br/>
 * 如果当前不存在，则从服务器查询并安装，最终通过回调返回
 *
 * @param condition 普通资源查询条件
 * @param selector  资源选择器
 * @param callback  提示和结果回调
 * @param listener  进度监听
 * @return 当前安装的资源信息
 */

- (UPResourceInfo *)getDeviceResource:(UPResourceDeviceCondition *)condition selector:(id<UpResourceSelector>)selector callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener;

/**
 根据过滤条件获取本地全部资源列表

 @param filter 过滤条件
 @return 资源列表
 */
- (NSArray<UPResourceInfo *> *)getEntireList:(id<UPResourceFilter>)filter;

/**
 根据过滤条件 获取本地最新的资源列表，同类型和名称的资源在列表中只存在一个

 @param filter 过滤条件
 @return 资源列表
 */
- (NSArray<UPResourceInfo *> *)getLatestList:(id<UPResourceFilter>)filter;

/**
 * @brief 根据名称获取最新的资源信息
 *
 * @param name 资源名称
 * @param type 资源类型
 * @return 资源信息
 */
- (UPResourceInfo *)getLatestInfoByName:(NSString *)name type:(UPResourceType)type;

/**
 * @brief 根据名称获取最新的已安装过的资源信息
 *
 * @param name 资源名称
 * @param type 资源类型
 * @return 资源信息
 */
- (UPResourceInfo *)getLatestInstalledInfo:(NSString *)name type:(UPResourceType)type;

/**
 * @brief 批量获取最新的已安装过的资源信息
 *
 * @param filter 资源类型
 * @return 资源信息
 */
- (NSArray<UPResourceInfo *> *)getLatestInstalledList:(BOOL (^)(UPResourceInfo *info))filter;

/**
 * @brief 获取指定名称、类型和版本号的资源信息对象方法。
 * @param name 资源名称
 * @param type 资源类型
 * @param version 资源的版本号
 * @return 成功返回对应的资源信息对象，失败返回nil。
 */
- (UPResourceInfo *)getResourceInfo:(NSString *)name type:(UPResourceType)type version:(NSString *)version;


/**
 * @brief 获取指定类型的资源存储路径
 *
 * @param type 资源类型
 * @return 资源存储路径
 */
- (NSString *)getPathByType:(UPResourceType)type;

/**
 * @brief 是否正在清除缓存
 *
 * @return 是否正在清除
 */
- (BOOL)isCleaning;

/// 自动安装最新资源，每个安装都会有回调
/// @param infoList 资源列表
/// @param callback 结果回调
- (void)autoUpgradeCurrentResources:(NSArray<UpPreloadResourceInfo *> *)infoList callback:(id<UPResourceCallback>)callback;

/// 异步插入资源且安装资源
/// @param info 资源信息
/// @param isDevRes 是否是设备资源
/// @param deviceNetType 是否是网器设备{ 网器：netDevice  非网器：notNetDevice}
/// @param callback 安装结果回调

- (void)asyncInsertAndInstallResInfo:(UPResourceInfo *)info isDevRes:(BOOL)isDevRes deviceNetType:(NSString *)deviceNetType callback:(id<UPResourceCallback>)callback NS_DEPRECATED(2.21.1, 2.21.1, 2.21.1, 2.21.1, "接口已废弃,空实现,后续可删除");

/// 清理无用资源
/// @param delayTime 延迟多长时间调用
- (void)cleanUselessResource:(NSTimeInterval)delayTime;

/// 同步获取资源包所占空间大小，返回单位为字节，当出错时返回-1
- (unsigned long long)getLocalDataSize;

/// 异步获取资源包所占空间大小
/// @param callback 回调size为字节，error中为错误信息
- (void)getLocalDataSizeAsync:(void (^)(unsigned long long size, NSError *error))callback;

/**
 * @brief 资源清理时添加保护特定资源不被清除。
 * @param keepResNameList 需要保护的资源列表
 * @param callback 清空结果回调对象
 * @discussion 保护指定资源名称类型的最新版本，其他资源数据正常清理。
 * @since 2.12.0
 */
- (void)cleanLocalDataKeepPresetRes:(NSArray<UpPreloadResourceInfo *> *)keepResNameList callback:(id<UPResCacheCleanCallback>)callback;

/// 自动更新本地已安装资源
- (void)autoUpgradeLocalResouces;

/// 上报所使用资源信息
- (void)reportResourceLoaded:(NSString *)resName
                        type:(UPResourceType)resType
                     version:(NSString *)resVersion;
@end

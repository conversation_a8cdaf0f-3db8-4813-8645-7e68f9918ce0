//
//  UPResourceCondition.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/16.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceType.h"

NS_ASSUME_NONNULL_BEGIN

extern NSString *const FROM_FUNC_COMMON_RESOURCE;
extern NSString *const FROM_FUNC_DEVICE_RESOURCE;
extern NSString *const FROM_FUNC_DEVICE_CONFIG;
extern NSString *const FROM_FUNC_DEVICE_CUSTOM_INFO;
extern NSString *const FROM_FUNC_BATCH_NORMAL_RESOURCE;
extern NSString *const FROM_FUNC_APP_FUNC_MODEL;
extern NSString *const FROM_FUNC_DEVICE_LUA;
@interface UPResourceCondition : NSObject


/**
 fromFunc
 **/
@property (nonatomic, copy) NSString *fromF<PERSON>c;

/**
 resourceType
 **/
@property (nonatomic, assign) UPResourceType resourceType;

/**
 appVersion
 **/
@property (nonatomic, copy) NSString *appVersion;
/**
 资源名称
 */
@property (nonatomic, copy) NSString *resName;


- (instancetype)initResourceType:(UPResourceType)resourceType;

/**
 根据资源类型返回对应字符串

 @param type 资源类型
 @return 对应字符串
 */
+ (NSString *)stringValueOfResourceType:(UPResourceType)type;

- (NSString *)combine;


/// 根据字符串fromFunc转number类型
/// @param fromfunc fromFunc
+ (NSInteger)numberValueOfFromfunc:(NSString *)fromfunc;

@end


@interface UPResourceDeviceCondition : UPResourceCondition

/**
 model
 **/
@property (nonatomic, copy) NSString *model;
/**
 typeId
 **/
@property (nonatomic, copy) NSString *typeId;
/**
 prodNo
 **/
@property (nonatomic, copy) NSString *prodNo;
/**
 deviceType
 **/
@property (nonatomic, copy) NSString *deviceType;

/**
 网器：netDevice  非网器：notNetDevice
 **/
@property (nonatomic, copy) NSString *deviceNetType;


/// 根据资源类型转fromFunc
/// @param type 资源类型
+ (NSString *)stringFromfuncOfResourceType:(UPResourceType)type;

@end


NS_ASSUME_NONNULL_END

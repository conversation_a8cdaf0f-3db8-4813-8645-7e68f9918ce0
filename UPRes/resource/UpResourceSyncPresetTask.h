//
//  UpResourceSyncPresetTask.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPPresetFileLoader.h"
#import "UPFileDelegate.h"
#import "UPResourceRepository.h"
#import "UPTimeDelegate.h"
#import "UPResourceReporter.h"
@protocol UPResourceOperator;
@class UPResourceResult;
NS_ASSUME_NONNULL_BEGIN

@interface UpResourceSyncPresetTask : NSObject
- (instancetype)initSyncPresetTask:(NSString *)name type:(UPResourceType)type resourceOperator:(id<UPResourceOperator>)resourceOperator;
- (void)setScanner:(id<UPPresetFileLoader>)scanner;
- (void)setFileDelegate:(id<UPFileDelegate>)fileDelegate;
- (void)setTimeDelegate:(id<UPTimeDelegate>)timeDelegate;
- (void)setRepository:(UPResourceRepository *)repository;
- (void)setAppVersion:(NSString *)appVersion;
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate;
- (UPResourceResult *)execute;
@end

NS_ASSUME_NONNULL_END

//
//  UPResourceDirectory.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDirectory.h"
#import <UPLog/UPLog.h>
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"

NSString *const UPResourceDirectory_HTML = @"H5ResPkg";
NSString *const UPResourceDirectory_ReactNative = @"ReactNative";
NSString *const UPResourceDirectory_DeviceConfig = @"DeviceConfig";
NSString *const UPResourceDirectory_APICloud = @"APICloud";
NSString *const UPResourceDirectory_MPAAS = @"mPaaS";
NSString *const UPResourceDirectory_Temp = @"Temp";
NSString *const UPResourceDirectory_DEVICE_CUSTOM_INFO = @"DeviceCustomInfo";
NSString *const UPResourceDirectory_ConfigFile_Suffix = @".signed.json";
NSString *const UPResourceDirectory_ConfigApp = @"configAPP";
NSString *const UPResourceDirectory_Video = @"video";
NSString *const UPResourceDirectory_Flutter = @"flutter";
NSString *const UPResourceDirectory_APPFuncModel = @"appFuncModel";
NSString *const UPResourceDirectory_Routes = @"routes";
NSString *const UPResourceDirectory_Picture = @"picture";
NSString *const UPResourceDirectory_Audio = @"audio";
NSString *const UPResourceDirectory_Other = @"other";
NSString *const UPResourceDirectory_ConfigFile = @"configFile";
NSString *const UPResourceDirectory_Lua = @"Lua";

@interface UPResourceDirectory ()
@property (nonatomic, strong) NSDictionary<NSNumber *, NSString *> *typeToName;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;

- (NSString *)folderNameOfResource:(nonnull UPResourceInfo *)info;
- (NSString *)folderNameOfConfigResource:(nonnull UPResourceInfo *)info;
- (NSString *)folderNameOfHTMLResource:(nonnull UPResourceInfo *)info;

@end

@implementation UPResourceDirectory
#pragma mark - Property Methods
- (NSString *)resourceTempFolderPath
{
    NSString *tempFolderPath = [_resourceRootFolderPath stringByAppendingPathComponent:UPResourceDirectory_Temp];
    id<UPFileDelegate> fileSystem = self.fileDelegate;
    BOOL bFolderExists = [fileSystem exists:tempFolderPath];
    BOOL bIsDirectory = [fileSystem isDirectory:tempFolderPath];

    if (bFolderExists && !bIsDirectory) {
        [fileSystem deletePath:tempFolderPath];
    }
    if (!bFolderExists || !bIsDirectory) {

        [fileSystem mkdirs:tempFolderPath];
    }
    return tempFolderPath;
}

#pragma mark - Non-Public Methods
- (NSString *)folderNameOfResource:(nonnull UPResourceInfo *)info
{
    NSString *folderName = nil;
    switch (info.type) {
        case UPResourceTypeDeviceConfig:
        case UPResourceTypeAppFuncModel:
            folderName = [self folderNameOfConfigResource:info];
            break;
        case UPResourceTypeConfigApp:
        case UPResourceTypeRoutes:
        case UPResourceTypeConfigFile:
        case UPResourceTypeLua:
            folderName = [self folderNameOfRouteResource:info];
            break;
        case UPResourceTypeHTML:
        case UPResourceTypeReactNative:
        case UPResourceTypeMPAAS:
        case UPResourceTypeAPICloud:
            folderName = [self folderNameOfHTMLResource:info];
            break;
        default:
            folderName = [self folderNameOfHTMLResource:info];
            break;
    }
    return folderName;
}

- (NSString *)folderNameOfConfigResource:(UPResourceInfo *)info
{
    NSString *folderName = [NSString stringWithFormat:@"%@@%@", info.name, info.version];
    if (!UPRes_isEmptyString(folderName)) {

        folderName = [folderName stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    }
    return [folderName stringByAppendingString:UPResourceDirectory_ConfigFile_Suffix];
}

- (NSString *)folderNameOfHTMLResource:(UPResourceInfo *)info
{
    return [info.name stringByAppendingFormat:@"@%@", info.version];
}

- (NSString *)folderNameOfRouteResource:(UPResourceInfo *)info
{
    NSString *folderName = [NSString stringWithFormat:@"%@@%@", info.name, info.version];
    if (!UPRes_isEmptyString(folderName)) {

        folderName = [folderName stringByReplacingOccurrencesOfString:@"/" withString:@"_"];
    }
    NSString *fileType = [info.link pathExtension];
    return fileType.length ? [folderName stringByAppendingFormat:@".%@", fileType] : [folderName stringByAppendingString:UPResourceDirectory_ConfigFile_Suffix];
}

#pragma mark - Override
- (instancetype)initWithRootPath:(NSString *)path fileDelegate:(id<UPFileDelegate>)fileDelegate
{
    if (self = [super init]) {
        _fileDelegate = fileDelegate;
        _resourceRootFolderPath = path;
        _typeToName = @{ @(UPResourceTypeHTML) : UPResourceDirectory_HTML,
                         @(UPResourceTypeReactNative) : UPResourceDirectory_ReactNative,
                         @(UPResourceTypeDeviceConfig) : UPResourceDirectory_DeviceConfig,
                         @(UPResourceTypeLua) : UPResourceDirectory_Lua,
                         @(UPResourceTypeAPICloud) : UPResourceDirectory_APICloud,
                         @(UPResourceTypeMPAAS) : UPResourceDirectory_MPAAS,
                         @(UPResourceTypeConfigApp) : UPResourceDirectory_ConfigApp,
                         @(UPResourceTypeDEVICE_CUSTOM_INFO) : UPResourceDirectory_DEVICE_CUSTOM_INFO,
                         @(UPResourceTypeVideo) : UPResourceDirectory_Video,
                         @(UPResourceTypeFlutter) : UPResourceDirectory_Flutter,
                         @(UPResourceTypeAppFuncModel) : UPResourceDirectory_APPFuncModel,
                         @(UPResourceTypeRoutes) : UPResourceDirectory_Routes,
                         @(UPResourceTypePicture) : UPResourceDirectory_Picture,
                         @(UPResourceTypeAudio) : UPResourceDirectory_Audio,
                         @(UPResourceTypeOther) : UPResourceDirectory_Other,
                         @(UPResourceTypeConfigFile) : UPResourceDirectory_ConfigFile
        };
    }
    return self;
}

#pragma mark - Public Methods
+ (UPResourceDirectory *)resourceDirectoryWithPath:(NSString *)rootPath fileDelegate:(id<UPFileDelegate>)fileDelegate
{
    UPResourceDirectory *directory = [[UPResourceDirectory alloc] initWithRootPath:rootPath fileDelegate:fileDelegate];
    return directory;
}

- (NSString *)resourceFolderPathOfType:(UPResourceType)type
{
    NSNumber *resType = @(type);
    NSString *dirName = _typeToName[resType];
    if (![dirName isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]该类型的资源文件夹不存在！type:%@", __PRETTY_FUNCTION__, __LINE__, resType.description);
        return nil;
    }
    NSString *folderPath = [_resourceRootFolderPath stringByAppendingPathComponent:dirName];

    id<UPFileDelegate> fileSystem = self.fileDelegate;
    BOOL bFolderExists = [fileSystem exists:folderPath];
    BOOL bIsDirectory = [fileSystem isDirectory:folderPath];

    if (bFolderExists && !bIsDirectory) {
        [fileSystem deletePath:folderPath];
    }
    if (!bFolderExists || !bIsDirectory) {

        if (![fileSystem mkdirs:folderPath]) {
            UPLogError(@"UPResource", @"%s[%d]该类型资源文件夹创建失败！type:%@", __PRETTY_FUNCTION__, __LINE__, resType.description);
            return nil;
        }
    }
    return folderPath;
}

- (NSString *)folderPathOfResource:(UPResourceInfo *)info
{
    NSString *rootPath = [self resourceFolderPathOfType:info.type];
    NSString *resourceFolderName = [self folderNameOfResource:info];
    return [rootPath stringByAppendingPathComponent:resourceFolderName];
}

- (NSString *)downloadFolderPathOfResource:(UPResourceInfo *)info
{
    NSString *temPath = self.resourceTempFolderPath;
    NSString *folderName = [self folderNameOfResource:info];
    NSString *fullFolderName = [folderName stringByAppendingString:@".tmp"];
    return [temPath stringByAppendingPathComponent:fullFolderName];
}

- (NSString *)copyPathForDirectoryResource:(UPResourceInfo *)info
{
    NSString *temPath = self.resourceTempFolderPath;
    NSString *folderPath = [temPath stringByAppendingPathComponent:[self folderNameOfResource:info]];
    id<UPFileDelegate> fileSystem = self.fileDelegate;
    BOOL bFolderExists = [fileSystem exists:folderPath];
    BOOL bIsDirectory = [fileSystem isDirectory:folderPath];

    if (bFolderExists && !bIsDirectory) {
        [fileSystem deletePath:folderPath];
    }
    if (!bFolderExists || !bIsDirectory) {
        [fileSystem mkdirs:folderPath];
    }

    return [folderPath stringByAppendingPathComponent:info.name];
}

- (UPResourceType)getResourceTypeByFolderName:(NSString *)folderName
{
    NSDictionary *dict = @{ UPResourceDirectory_HTML : @(UPResourceTypeHTML),
                            UPResourceDirectory_ReactNative : @(UPResourceTypeReactNative),
                            UPResourceDirectory_DeviceConfig : @(UPResourceTypeDeviceConfig),
                            UPResourceDirectory_Lua : @(UPResourceTypeLua),
                            UPResourceDirectory_APICloud : @(UPResourceTypeAPICloud),
                            UPResourceDirectory_MPAAS : @(UPResourceTypeMPAAS),
                            UPResourceDirectory_ConfigApp : @(UPResourceTypeConfigApp),
                            UPResourceDirectory_DEVICE_CUSTOM_INFO : @(UPResourceTypeDEVICE_CUSTOM_INFO),
                            UPResourceDirectory_Video : @(UPResourceTypeVideo),
                            UPResourceDirectory_Flutter : @(UPResourceTypeFlutter),
                            UPResourceDirectory_APPFuncModel : @(UPResourceTypeAppFuncModel),
                            UPResourceDirectory_Routes : @(UPResourceTypeRoutes),
                            UPResourceDirectory_Picture : @(UPResourceTypePicture),
                            UPResourceDirectory_Audio : @(UPResourceTypeAudio),
                            UPResourceDirectory_Other : @(UPResourceTypeOther),
                            UPResourceDirectory_ConfigFile : @(UPResourceTypeConfigFile)
    };
    NSNumber *num = dict[folderName];
    if (num == nil) {
        return UPResourceTypeAll;
    }
    return num.integerValue;
}
@end

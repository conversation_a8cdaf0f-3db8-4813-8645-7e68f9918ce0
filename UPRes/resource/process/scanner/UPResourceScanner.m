//
//  UPResourceScanner.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/19.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceScanner.h"
#import "UPResourceTask.h"
#import "UPResourceInfo.h"
#import <UPLog/UPLog.h>
#import "UPResourceRepository.h"

@interface UPResourceScanner ()
@property (nonatomic, strong) NSArray *shouldCheckArray;
@end
@implementation UPResourceScanner
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    return !self.isFinished;
}

- (BOOL)checkConfigAndCustomDeviceResourceType:(UPResourceType)type
{
    if ([self.shouldCheckArray containsObject:@(type)]) {
        return YES;
    }
    return NO;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    if ([self checkConfigAndCustomDeviceResourceType:task.resourceInfo.type] || [task.resourceInfo.name hasPrefix:THEMEPREFIX]) {
        UPLogInfo(@"UPResource", @"%s[%d]配置文件无index地址，默认扫描成功", __PRETTY_FUNCTION__, __LINE__);
        self.state = UPResProcessorStateSuccess;
        return YES;
    }
    NSString *tgtFilePath = task.resourceFile;
    if (![self.fileDelegate exists:tgtFilePath]) {
        self.state = UPResProcessorStateFailure;
        UPLogError(@"UPResource", @"%s[%d]扫描资源首页地址失败,资源路径%@不存在", __PRETTY_FUNCTION__, __LINE__, tgtFilePath);
        return NO;
    }
    NSArray *subPaths = [self.fileDelegate listSubPaths:tgtFilePath];
    if (subPaths.count == 0) {
        self.state = UPResProcessorStateFailure;
        UPLogError(@"UPResource", @"%s[%d]扫描资源首页地址失败,资源路径%@下无子路径", __PRETTY_FUNCTION__, __LINE__, tgtFilePath);
        return NO;
    }
    NSMutableArray<NSString *> *subLists = [self sortPathList:subPaths];
    NSString *indexPath = nil;
    while (subLists.count) {
        NSString *subPath = subLists.firstObject;
        [subLists removeObjectAtIndex:0];
        if (subPath == nil)
            continue;
        if (![self.fileDelegate isFile:subPath])
            [subLists addObjectsFromArray:[self sortPathList:[self.fileDelegate listSubPaths:subPath]]];
        else if ([subPath hasSuffix:@"index.html"]) {
            indexPath = [NSString stringWithFormat:@"file://%@", subPath];
            break;
        }
    }
    if (indexPath == nil) {
        self.state = UPResProcessorStateFailure;
        UPLogError(@"UPResource", @"%s[%d]该资源路径%@下不存在首页地址", __PRETTY_FUNCTION__, __LINE__, tgtFilePath);
        return NO;
    }
    task.resourceInfo.indexPath = indexPath;
    self.state = UPResProcessorStateSuccess;
    UPLogInfo(@"UPResource", @"首页地址扫描成功，indexPath地址为%@", indexPath);
    return YES;
}

- (NSMutableArray<NSString *> *)sortPathList:(NSArray<NSString *> *)listSubPaths
{
    NSMutableArray *fileList = [NSMutableArray array];
    NSMutableArray *dirList = [NSMutableArray array];
    if (listSubPaths.count == 0) {
        return fileList;
    }
    for (NSString *subPath in listSubPaths) {
        if ([self.fileDelegate isFile:subPath]) {
            [fileList addObject:subPath];
        }
        else {
            [dirList addObject:subPath];
        }
    }
    [fileList addObjectsFromArray:dirList];
    return fileList;
}

- (void)recycle
{
}

- (void)resume
{
}

- (instancetype)initWithFileDelegate:(id<UPFileDelegate>)fileDelegate
{
    if (self = [super init]) {
        self.fileDelegate = fileDelegate;
    }
    return self;
}

- (NSArray *)shouldCheckArray
{
    if (!_shouldCheckArray) {
        _shouldCheckArray = [[NSArray alloc] initWithObjects:@(UPResourceTypeDeviceConfig), @(UPResourceTypeLua), @(UPResourceTypeDEVICE_CUSTOM_INFO), @(UPResourceTypeConfigApp), @(UPResourceTypeVideo), @(UPResourceTypeFlutter), @(UPResourceTypeAppFuncModel), @(UPResourceTypeRoutes), @(UPResourceTypePicture), @(UPResourceTypeAudio), @(UPResourceTypeOther), @(UPResourceTypeConfigFile), nil];
    }
    return _shouldCheckArray;
}
@end

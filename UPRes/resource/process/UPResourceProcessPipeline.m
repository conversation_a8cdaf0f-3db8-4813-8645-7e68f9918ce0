//
//  UPResourceProcessPipeline.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceProcessPipeline.h"
#import <UPLog/UPLog.h>
#import "UPResCommonFunctions.h"
#import "UPResourceTask.h"

@interface UPResourceProcessPipeline ()
@property (nonatomic, strong) NSMutableArray<id<UPResourceProcessor>> *processors;
@property (nonatomic, strong) NSMutableArray<id<UPResourceProcessor>> *stack;
@property (atomic, assign) BOOL cancelled;
/**
 安装过程结果描述
 */
@property (nonatomic, copy) NSString *installDesc;
- (instancetype)initWithProcessors:(NSMutableArray<id<UPResourceProcessor>> *)processors;
+ (UPResourceProcessPipeline *)resourceProcessPipeline:(NSMutableArray<id<UPResourceProcessor>> *)processors;
@end

@implementation UPResourceProcessPipeline
#pragma mark - Non-Public Methods
- (instancetype)initWithProcessors:(NSMutableArray<id<UPResourceProcessor>> *)processors
{
    if (self = [super init]) {
        _processors = processors;
        _stack = [NSMutableArray array];
    }
    return self;
}

+ (UPResourceProcessPipeline *)resourceProcessPipeline:(NSMutableArray<id<UPResourceProcessor>> *)processors;
{
    UPResourceProcessPipeline *pipeline = [[UPResourceProcessPipeline alloc] initWithProcessors:processors];
    return pipeline;
}

#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    NSMutableArray<id<UPResourceProcessor>> *list = [NSMutableArray arrayWithArray:self.processors];
    BOOL success = NO;
    id<UPResourceProcessor> processor = nil;
    NSEnumerator *emunerator = [list reverseObjectEnumerator];
    while ((processor = [emunerator nextObject])) {
        success = [processor cancel];
        if (!success) {
            break;
        }
    }
    self.cancelled = success;
    return success;
}

- (void)resume
{
    if (!UPRes_isEmptyArray(self.stack)) {
        id<UPResourceProcessor> processor = [self.stack lastObject];
        [processor resume];
    }
}
- (NSString *)result
{

    return self.installDesc;
}
- (void)setResult:(NSString *)result
{
    self.installDesc = result;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    BOOL success = NO;
    for (id<UPResourceProcessor> processor in self.processors) {
        NSString *taskId = task.taskId;
        NSString *name = processor.name;
        UPLogInfo(@"UPResource", @"%s[%d]call %@ for task[%@] start", __PRETTY_FUNCTION__, __LINE__, name, taskId);
        [self.stack addObject:processor];
        success = [processor process:task callback:callback listener:listener];
        if (!success || self.cancelled) {
            self.installDesc = [self judgeAndSendFailureMessage:processor];
            break;
        }
        UPLogInfo(@"UPResource", @"%s[%d]call %@ for task[%@] stop -> %@", __PRETTY_FUNCTION__, __LINE__, name, taskId, @(success).description);
    }
    [self recycle];
    return success;
}

- (void)recycle
{
    id<UPResourceProcessor> processor = [self.stack lastObject];
    while (processor != nil) {
        [self.stack removeLastObject];
        [processor recycle];
        processor = [self.stack lastObject];
    }
}

#pragma mark privateMethod
- (NSString *)judgeAndSendFailureMessage:(id<UPResourceProcessor>)processor
{
    NSString *message = @"取消安装";
    if ([processor.name isEqualToString:@"UPResourceDownloader"]) {
        message = @"下载资源失败";
    }
    else if ([processor.name isEqualToString:@"UPResourceExtractor"]) {
        message = @"解压资源失败";
    }
    else if ([processor.name isEqualToString:@"UPResourceRemover"]) {
        message = @"删除资源失败";
    }
    else if ([processor.name isEqualToString:@"UPResourceTransporter"]) {
        message = @"移动资源失败";
    }
    else if ([processor.name isEqualToString:@"UPResourceValidator"]) {
        message = @"检验资源失败";
    }
    else if ([processor.name isEqualToString:@"UPResourceScanner"]) {
        message = @"扫描首页失败";
    }
    return message;
}

@end

@interface UPResPipelineBuilder ()
@property (nonatomic, strong) NSMutableArray<id<UPResourceProcessor>> *processors;
@end

@implementation UPResPipelineBuilder
- (instancetype)init
{
    if (self = [super init]) {
        _processors = [NSMutableArray array];
    }
    return self;
}

- (instancetype)add:(id<UPResourceProcessor>)processor
{
    if ([processor conformsToProtocol:@protocol(UPResourceProcessor)]) {
        [self.processors addObject:processor];
    }
    return self;
}

- (UPResourceProcessPipeline *)build
{
    if (UPRes_isEmptyArray(self.processors)) {
        NSString *errMsg = @"Processor List is EMPTY.";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        @throw [NSException exceptionWithName:@"UP_RES_EXE_ERR" reason:errMsg userInfo:nil];
    }
    return [UPResourceProcessPipeline resourceProcessPipeline:self.processors];
}

@end

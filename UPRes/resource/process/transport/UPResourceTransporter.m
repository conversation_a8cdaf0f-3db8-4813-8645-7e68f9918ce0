//
//  UPResourceTransporter.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceTransporter.h"
#import "UPResourceTask.h"
#import <UPLog/UPLog.h>

@implementation UPResourceTransporter
#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    return !self.isFinished;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    NSString *srcFilePath = task.downloadFile;
    NSString *tgtFilePath = task.resourceFile;
    id<UPFileDelegate> fileSystem = self.fileDelegate;
    if ([fileSystem exists:tgtFilePath]) {
        [fileSystem deletePath:tgtFilePath];
    }
    self.temporaryFilePath = tgtFilePath;
    BOOL success = [fileSystem rename:srcFilePath to:tgtFilePath];
    self.state = success ? UPResProcessorStateSuccess : UPResProcessorStateFailure;
    return success;
}

- (instancetype)initWithFileDelegate:(id<UPFileDelegate>)fileDelegate
{

    if (self = [super init]) {

        self.fileDelegate = fileDelegate;
    }
    return self;
}
- (void)recycle
{
    if (self.isSuccessful) {
        return;
    }
    if (![self cleanTemporaryFile]) {
        UPLogError(@"UPResource", @"%s[%d]资源移动临时文件(%@)清理失败", __PRETTY_FUNCTION__, __LINE__, self.temporaryFilePath);
    }
}

- (void)resume
{
}

@end

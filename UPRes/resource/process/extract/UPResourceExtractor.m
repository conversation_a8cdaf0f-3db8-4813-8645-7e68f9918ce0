//
//  UPResourceExtractor.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceExtractor.h"
#import "UPResourceTask.h"
#import "UPResourceInfo.h"
#import <UPLog/UPLog.h>
#import "UPResCommonFunctions.h"

@interface UPResourceExtractor ()
- (BOOL)doExtract:(UPResourceTask *)task;
@end

@implementation UPResourceExtractor
#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    return !self.isFinished;
}

- (void)resume
{
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    UPResourceInfo *info = task.resourceInfo;
    BOOL isExistsDirectory = [self.fileDelegate isDirectory:task.downloadFile];
    if (info.type == UPResourceTypeDeviceConfig || info.type == UPResourceTypeLua || info.type == UPResourceTypeConfigApp || isExistsDirectory || info.type == UPResourceTypeAppFuncModel || info.type == UPResourceTypeRoutes || info.type == UPResourceTypeConfigFile) {
        self.state = UPResProcessorStateSuccess;
        return YES;
    }
    else {
        BOOL success = [self doExtract:task];
        self.state = success ? UPResProcessorStateSuccess : UPResProcessorStateFailure;
        return success;
    }
}

- (void)recycle
{
    if (![self cleanTemporaryFile]) {
        UPLogError(@"UPResource", @"%s[%d]资源解压临时文件(%@)清理失败", __PRETTY_FUNCTION__, __LINE__, self.temporaryFilePath);
    }
}
- (instancetype)initWithFileDelegate:(id<UPFileDelegate>)fileDelegate
{

    if (self = [super init]) {

        self.fileDelegate = fileDelegate;
    }
    return self;
}

#pragma mark - Non-Public Methods
- (BOOL)doExtract:(UPResourceTask *)task
{
    NSString *sourceFile = task.downloadFile;


    id<UPFileDelegate> fileSystem = self.fileDelegate;

    BOOL exists = [fileSystem exists:sourceFile];
    BOOL isFile = [fileSystem isFile:sourceFile];

    if (!exists || !isFile) {
        NSString *errMsg = [NSString stringWithFormat:@"unzip file fialure. exists:%d, isFile:%d, path:%@", exists, isFile, sourceFile];
        UPLogInfo(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        return NO;
    }
    NSString *name = task.resourceInfo.name;
    NSString *targetFilePath = [sourceFile stringByDeletingPathExtension];
    self.temporaryFilePath = targetFilePath;

    BOOL ret = [fileSystem unzip:sourceFile to:targetFilePath];
    if (ret) {
        UPLogDebug(@"UPResource", @"%s[%d]资源包(%@)的压缩文件解压缩成功!", __PRETTY_FUNCTION__, __LINE__, name);
        if (![fileSystem deletePath:sourceFile]) {
            UPLogError(@"UPResource", @"%s[%d]delete zip failure! resource name:%@ resource path:%@", __PRETTY_FUNCTION__, __LINE__, name, sourceFile);
        }
        task.downloadFile = targetFilePath;
        return YES;
    }
    NSString *errMsg = [NSString stringWithFormat:@"unzip resource(%@) failure! source path:%@,target path:%@", name, sourceFile, targetFilePath];
    UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
    return NO;
}
@end

//
//  UPResourceProcessorBase.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceProcessorBase.h"
#import <UPLog/UPLog.h>

@implementation UPResourceProcessorBase
- (instancetype)init
{
    if (self = [super init]) {
        _state = UPResProcessorStateCreate;
    }
    return self;
}

- (BOOL)isSuccessful
{
    return self.state == UPResProcessorStateSuccess;
}

- (BOOL)isFinished
{
    UPResProcessorState state = self.state;
    return state == UPResProcessorStateSuccess || state == UPResProcessorStateFailure || state == UPResProcessorStateCancel;
}

- (BOOL)cleanTemporaryFile
{
    NSString *filePath = self.temporaryFilePath;
    if (![filePath isKindOfClass:[NSString class]] || filePath.length == 0) {
        return YES;
    }
    id<UPFileDelegate> fileSystem = self.fileDelegate;
    if (![fileSystem exists:filePath]) {
        return YES;
    }
    BOOL cleanSuccess = [fileSystem deletePath:filePath];
    return cleanSuccess;
}

@end

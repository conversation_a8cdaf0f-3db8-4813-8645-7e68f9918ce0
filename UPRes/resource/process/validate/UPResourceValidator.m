//
//  UPResourceValidator.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceValidator.h"
#import "UPResourceTask.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"

@interface UPResourceValidator ()
@property (nonatomic, assign) UPValidationAlgorithm algorithm;

- (instancetype)initWithAlgorithm:(UPValidationAlgorithm)algorithm;
- (NSString *)calculateHashCode:(NSString *)filePath;
@end

@implementation UPResourceValidator
#pragma mark - Non-Public Methods
- (instancetype)initWithAlgorithm:(UPValidationAlgorithm)algorithm fileDelegate:(id<UPFileDelegate>)fileDelegate
{
    if (self = [super init]) {
        _algorithm = algorithm;
        self.fileDelegate = fileDelegate;
    }
    return self;
}

- (NSString *)calculateHashCode:(NSString *)filePath
{
    NSString *hashCode = nil;
    switch (self.algorithm) {
        case UPValidationAlgorithmMD5:
            hashCode = [self.fileDelegate calcMD5:filePath];
            break;
        case UPValidationAlgorithmSHA:
            hashCode = [self.fileDelegate calcSHA1:filePath];
            break;
        default:
            break;
    }
    return hashCode;
}

#pragma mark - Public Methods
+ (UPResourceValidator *)resourceValidatorWithAlgorithm:(UPValidationAlgorithm)algorithm fileDelegate:(id<UPFileDelegate>)fileDelegate
{
    return [[UPResourceValidator alloc] initWithAlgorithm:algorithm fileDelegate:fileDelegate];
}

#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    return !self.isFinished;
}

- (void)resume
{
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    UPResourceInfo *info = task.resourceInfo;
    NSString *downloadFilePath = task.downloadFile;
    NSString *hashStr = [self calculateHashCode:downloadFilePath];
    BOOL success = UPRes_isEqualStringIgnoringCase(hashStr, info.hashStr, YES);
    self.state = success ? UPResProcessorStateSuccess : UPResProcessorStateFailure;
    return success;
}

- (void)recycle
{
}


@end

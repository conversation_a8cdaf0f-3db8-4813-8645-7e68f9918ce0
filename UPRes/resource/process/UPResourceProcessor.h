//
//  UPResourceProcessor.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceCallback.h"
#import "UPResourceListener.h"

typedef NS_ENUM(NSUInteger, UPResProcessorState) {
    UPResProcessorStateCreate = 0,
    UPResProcessorStateWaiting,
    UPResProcessorStateRunning,
    UPResProcessorStateSuccess,
    UPResProcessorStateFailure,
    UPResProcessorStateCancel
};

@class UPResourceTask;
@protocol UPResourceProcessor <NSObject>
- (NSString *)name;
- (BOOL)cancel;
- (void)resume;
- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener;
- (void)recycle;

@optional
- (NSString *)result;
- (void)setResult:(NSString *)result;
@end

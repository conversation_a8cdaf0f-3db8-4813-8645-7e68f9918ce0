//
//  UPResourceDownloader.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceProcessorBase.h"
#import "UPConnectionDelegate.h"
#import "UPResourceTrackerProvider.h"
#import "UPResourceTracker.h"
#import "UPResourceConfig.h"
#import "UPResourceManager.h"
@protocol UPDownloadDelegate;
@interface UPResourceDownloader : UPResourceProcessorBase <UPResourceProcessor>

- (instancetype)initWithConnectionDelegate:(id<UPConnectionDelegate>)connectionDelegate downloadPolicy:(UPConnectionType)downloadPolicy downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate promptTimeout:(NSInteger)promptTimeout fileDelegate:(id<UPFileDelegate>)fileDelegate trackProvider:(id<UPResourceTrackerProvider>)trackP<PERSON>ider;

+ (UPResourceDownloader *)resourceDownloader:(id<UPConnectionDelegate>)connectionDelegate downloadPolicy:(UPConnectionType)downloadPolicy downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate fileDelegate:(id<UPFileDelegate>)fileDelegate trackProvider:(id<UPResourceTrackerProvider>)trackProvider;
@end

//
//  UPResourceDownloader.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDownloader.h"
#import <UPLog/UPLog.h>
#import <MJExtension/MJExtension.h>
#import "UPResourceTask.h"
#import "UPResourceInfo.h"
#import "UPConnectionDelegate.h"
#import "UPDownloadDelegate.h"
#import "UPDownloadHandle.h"
#import "UPDownloadCallback.h"
#import "UPDownloaderHandle.h"

NSInteger const DEFAULT_PROMPT_TIMEOUT = 30;
NSString *const UP_RES_ACTION_NO_CONNECTION = @"no_connection";
NSString *const UP_RES_ACTION_NETWORK_TYPE_MISMATCH = @"network_type_mismatch";


@interface ResourceDownloadListener : NSObject <UPDownloadCallback>

/**
 info
 **/
@property (nonatomic, strong) UPResourceInfo *info;

/**
 downloader
 **/
@property (nonatomic, strong) UPResourceDownloader *downloader;

/**
 listener
 **/
@property (nonatomic, weak) id<UPResourceListener> listener;

- (instancetype)initDownloadListener:(UPResourceInfo *)info downloader:(UPResourceDownloader *)downloader listener:(id<UPResourceListener>)listener;

@end

@implementation ResourceDownloadListener


- (instancetype)initDownloadListener:(UPResourceInfo *)info downloader:(UPResourceDownloader *)downloader listener:(id<UPResourceListener>)listener
{

    if (self = [super init]) {
        _info = info;
        _downloader = downloader;
        _listener = listener;
    }

    return self;
}

- (void)onTaskStart:(UPDownloadHandle *)handle
{

    self.downloader.state = UPResProcessorStateRunning;
}

- (void)onProgressChanged:(UPDownloadHandle *)handle progress:(CGFloat)progress
{

    if (self.listener) {

        [self.listener onProgressChanged:self.downloader.name progress:progress];
    }
}
- (void)onTaskSuccess:(UPDownloadHandle *)handle
{
    UPDownloaderHandle *downloaderHandle = (UPDownloaderHandle *)handle;
    downloaderHandle.record.endDownloadTime = [NSDate date];
    downloaderHandle.record.downloadResult = YES;
    if (self.listener) {

        [self.listener onProgressChanged:self.downloader.name progress:100];
    }
    self.downloader.state = UPResProcessorStateSuccess;
}

- (void)onTaskFailure:(UPDownloadHandle *)handle error:(NSError *)error
{
    UPDownloaderHandle *downloaderHandle = (UPDownloaderHandle *)handle;
    downloaderHandle.record.endDownloadTime = [NSDate date];
    downloaderHandle.record.downloadResult = NO;
    self.downloader.state = UPResProcessorStateFailure;
}

- (void)onTaskCancel:(UPDownloadHandle *)handle
{
    UPDownloaderHandle *downloaderHandle = (UPDownloaderHandle *)handle;
    downloaderHandle.record.endDownloadTime = [NSDate date];
    downloaderHandle.record.downloadResult = NO;
    self.downloader.state = UPResProcessorStateCancel;
}


@end

@interface UPResourceDownloader ()
@property (nonatomic, weak) id<UPConnectionDelegate> connectionDelegate;
/**
 downloadDelegate
 **/
@property (nonatomic, weak) id<UPDownloadDelegate> downloadDelegate;
/**
 handle
 **/
@property (nonatomic, strong) UPDownloadHandle *handle;

@property (nonatomic, assign) UPConnectionType downloadPolicy;
/**
 promptTimeout
 **/
@property (nonatomic, assign) NSInteger promptTimeout;

/**
 downLoadListener
 **/
@property (nonatomic, strong) ResourceDownloadListener *downLoadListener;

/**
 trackProvider
 **/
@property (nonatomic, weak) id<UPResourceTrackerProvider> trackProvider;
@end

@implementation UPResourceDownloader


#pragma mark - Public Methods
- (instancetype)initWithConnectionDelegate:(id<UPConnectionDelegate>)connectionDelegate downloadPolicy:(UPConnectionType)downloadPolicy downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate promptTimeout:(NSInteger)promptTimeout fileDelegate:(id<UPFileDelegate>)fileDelegate trackProvider:(id<UPResourceTrackerProvider>)trackProvider
{
    if (self = [super init]) {
        _connectionDelegate = connectionDelegate;
        _downloadPolicy = downloadPolicy;
        _downloadDelegate = downloadDelegate;
        _promptTimeout = promptTimeout;
        self.fileDelegate = fileDelegate;
        self.trackProvider = trackProvider;
    }
    return self;
}

+ (UPResourceDownloader *)resourceDownloader:(id<UPConnectionDelegate>)connectionDelegate downloadPolicy:(UPConnectionType)downloadPolicy downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate fileDelegate:(id<UPFileDelegate>)fileDelegate trackProvider:(id<UPResourceTrackerProvider>)trackProvider
{

    return [[UPResourceDownloader alloc] initWithConnectionDelegate:connectionDelegate downloadPolicy:downloadPolicy downloadDelegate:downloadDelegate promptTimeout:DEFAULT_PROMPT_TIMEOUT fileDelegate:fileDelegate trackProvider:trackProvider];
}

#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    if (!self.isFinished) {
        if (self.handle != nil) {
            [self.downloadDelegate cancel:self.handle];
        }
        self.state = UPResProcessorStateCancel;
        return YES;
    }
    return NO;
}

- (void)resume
{
    self.state = UPResProcessorStateRunning;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    if (self.connectionDelegate.isAvailable == NO) {
        self.state = UPResProcessorStateWaiting;
        [callback onPrompt:task action:UP_RES_ACTION_NO_CONNECTION processor:self];
    }
    while (!self.isFinished) {
        if ([self checkConnectionAndTimeOut]) {
            [NSThread sleepForTimeInterval:1.0];
            continue;
        }
        if (self.isFinished || self.handle != nil) {
            [NSThread sleepForTimeInterval:1.0];
            continue;
        }
        if (self.downloadPolicy > self.connectionDelegate.getConnectionType) {
            self.state = UPResProcessorStateWaiting;
            [callback onPrompt:task action:UP_RES_ACTION_NETWORK_TYPE_MISMATCH processor:self];

            continue;
        }

        UPResourceInfo *info = task.resourceInfo;
        NSString *name = info.name;
        NSString *resUrl = info.link;
        NSString *zipFileDownloadFilePath = task.downloadFile;
        self.temporaryFilePath = zipFileDownloadFilePath;
        self.downLoadListener = [[ResourceDownloadListener alloc] initDownloadListener:info downloader:self listener:listener];
        self.handle = [self.downloadDelegate createUPDownloadHandle:resUrl folder:zipFileDownloadFilePath fileName:name callback:self.downLoadListener];
        [self.downloadDelegate setConnectionMonitor:self.connectionDelegate];
        [self.downloadDelegate start:self.handle];

        //ABTest上报
        if ([info.resourceType isEqualToString:@"2"] && [self.trackProvider.getResourceTracker respondsToSelector:@selector(reportABTestTrack:)]) {
            dispatch_async(dispatch_get_main_queue(), ^{
              UPResourceReportInfo *reportInfo = [self getResourceReportInfoWith:info];
              [self.trackProvider.getResourceTracker reportABTestTrack:reportInfo];
            });
        }
    }

    if ([self.trackProvider.getResourceTracker respondsToSelector:@selector(reporResDownloadTrack:)]) {
        [self getResourceDownloadReportInfoWith:task.resourceInfo
                                         handle:self.handle
                                         result:^(UPResourceReportInfo *reportInfo) {
                                           dispatch_async(dispatch_get_main_queue(), ^{
                                             [self.trackProvider.getResourceTracker reporResDownloadTrack:reportInfo];
                                           });
                                         }];
    }

    return self.isSuccessful;
}
- (BOOL)checkConnectionAndTimeOut
{
    if (self.connectionDelegate.isAvailable) {
        self.state = UPResProcessorStateRunning;
        return NO;
    }
    self.promptTimeout--;
    if (self.promptTimeout > 0) {
        return YES;
    }
    self.state = UPResProcessorStateFailure;
    return NO;
}

- (void)recycle
{
    if (!self.isSuccessful) {
        return;
    }
    if (![self cleanTemporaryFile]) {
        UPLogError(@"UPResource", @"%s[%d]资源下载临时文件(%@)清理失败", __PRETTY_FUNCTION__, __LINE__, self.temporaryFilePath);
    }
}

- (UPResourceReportInfo *)getResourceReportInfoWith:(UPResourceInfo *)info
{
    UPResourceReportInfo *reportInfo = [[UPResourceReportInfo alloc] init];
    reportInfo.res_name = info.name;
    reportInfo.res_type = [UPResourceInfo stringValueOfResourceType:info.type];
    reportInfo.res_version = info.version;
    reportInfo.client_id = [UPResourceConfig shareInstance].clientID;
    reportInfo.user_id = [UPResourceConfig shareInstance].user_id;
    reportInfo.app_version = [UPResourceConfig shareInstance].appVersion;
    reportInfo.is_preset = info.preset ? @"true" : @"false";
    reportInfo.is_gray = self.trackProvider.getSettings.environment == UPResEnvironmentProduction ? @"false" : @"true";
    return reportInfo;
}

- (void)getResourceDownloadReportInfoWith:(UPResourceInfo *)info handle:(UPDownloadHandle *)handle result:(void (^)(UPResourceReportInfo *))result
{
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      UPDownloaderHandle *downloadHandle = (UPDownloaderHandle *)handle;
      UPResourceReportInfo *reportInfo = [self getResourceReportInfoWith:info];
      reportInfo.time_length = [NSString stringWithFormat:@"%ld", downloadHandle.record.totalDownloadTimes];
      reportInfo.value = downloadHandle.record.fileSize;
      reportInfo.reTryCount = [NSString stringWithFormat:@"%ld", (long)downloadHandle.record.reTryCount];
      reportInfo.result = downloadHandle.record.downloadResult ? @"成功" : @"失败";
      // ⚠️ 复用优先级字段，目前仅有弹窗下载设置为高优先级
      reportInfo.showDialog = info.priority == UPResourceDownLoadPriorityHeight;
      reportInfo.resourceIP = downloadHandle.record.resourceIP;
      reportInfo.downloadBlockInfos = [UpDownloadBlockInfo mj_keyValuesArrayWithObjectArray:downloadHandle.record.downloadBlockInfos].mj_JSONString;
      reportInfo.downloadNetInfos = [UpDownloadNetInfo mj_keyValuesArrayWithObjectArray:downloadHandle.record.downloadNetInfos].mj_JSONString;
      reportInfo.downloadBackgroundRunInfos = [UpDownloadBackgroundRunInfo mj_keyValuesArrayWithObjectArray:downloadHandle.record.downloadBackgroundRunInfos].mj_JSONString;
      result ? result(reportInfo) : nil;
    });
}

@end

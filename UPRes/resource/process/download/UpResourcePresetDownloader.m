//
//  UpResourcePresetDownloader.m
//  UPRes
//
//  Created by 韩波标 on 2021/6/7.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourcePresetDownloader.h"
#import <UPLog/UPLog.h>
#import "UPResourceTask.h"
#import "UPResourceInfo.h"
#import "UPResourceDirectory.h"
#import "UPAssetPresetFileLoader.h"

@interface UpResourcePresetDownloader ()

@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) UPResourceDirectory *directory;
@property (nonatomic, strong) id<UPPresetFileLoader> scanner;
@end

@implementation UpResourcePresetDownloader
@synthesize fileDelegate = _fileDelegate;
@synthesize directory = _directory;

- (instancetype)initWithFileDelegate:(id<UPFileDelegate>)fileDelegate directory:(UPResourceDirectory *)directory scanner:(id<UPPresetFileLoader>)scanner
{
    if (self = [super init]) {
        self.fileDelegate = fileDelegate;
        self.directory = directory;
        self.scanner = scanner;
    }
    return self;
}

+ (UpResourcePresetDownloader *)resourcePresetDownloader:(id<UPFileDelegate>)fileDelegate directory:(UPResourceDirectory *)directory scanner:(id<UPPresetFileLoader>)scanner
{
    return [[self alloc] initWithFileDelegate:fileDelegate directory:directory scanner:scanner];
}

- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    if (!self.isFinished) {
        self.state = UPResProcessorStateCancel;
        return YES;
    }
    return NO;
}

- (void)resume
{
    self.state = UPResProcessorStateRunning;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    UPResourceInfo *info = task.resourceInfo;
    NSString *path = [self.scanner openPresetFile:info.link.lastPathComponent];
    //should check sandbox whether is consistent in case the info is saved to databse which app run last time
    if (![info.link isEqualToString:path]) {
        info.link = path;
    }
    NSString *targetPath = @"";
    if ([info.link hasSuffix:@".zip"] || ![self.fileDelegate isDirectory:info.link]) {
        targetPath = [self.directory downloadFolderPathOfResource:info];
    }
    else {
        targetPath = [self.directory copyPathForDirectoryResource:info];
        task.downloadFile = [targetPath stringByDeletingLastPathComponent];
    }
    UPLogDebug(@"UPResource", @"开始Copy资源 %@", info.link);
    BOOL kResult = [self.fileDelegate copy:info.link to:targetPath];
    UPLogDebug(@"UPResource", @"结束Copy资源%@", info.link);

    if (kResult) {
        self.state = UPResProcessorStateSuccess;
    }
    else {
        self.state = UPResProcessorStateFailure;
    }
    return self.isSuccessful;
}

- (void)recycle
{
}

@end

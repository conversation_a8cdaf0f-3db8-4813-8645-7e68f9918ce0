//
//  UPResourceRemover.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/17.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRemover.h"
#import "UPResourceTask.h"
#import <UPLog/UPLog.h>
#import "UPResCommonFunctions.h"
#import "UPResourceInfo.h"
#import "NSString+Paths.h"

@implementation UPResourceRemover
#pragma mark - UPResourceProcessor
- (NSString *)name
{
    return NSStringFromClass([self class]);
}

- (BOOL)cancel
{
    return !self.isFinished;
}

- (BOOL)process:(UPResourceTask *)task callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    self.state = UPResProcessorStateRunning;
    UPResourceInfo *info = task.resourceInfo;
    NSString *path = info.path;
    self.temporaryFilePath = path;
    if (UPRes_isEmptyString(path)) {
        self.state = UPResProcessorStateFailure;
        return false;
    }
    id<UPFileDelegate> fileSystem = self.fileDelegate;
    BOOL success = [fileSystem deletePath:path];
    self.state = success ? UPResProcessorStateSuccess : UPResProcessorStateFailure;
    return success;
}

- (instancetype)initWithFileDelegate:(id<UPFileDelegate>)fileDelegate
{

    if (self = [super init]) {

        self.fileDelegate = fileDelegate;
    }
    return self;
}
- (void)recycle
{
    //    if (![self cleanTemporaryFile]) {
    //        UPLogError(@"UPResource", @"%s[%d]资源删除临时文件(%@)清理失败", __PRETTY_FUNCTION__, __LINE__, self.temporaryFilePath);
    //    }
}

- (void)resume
{
}
@end

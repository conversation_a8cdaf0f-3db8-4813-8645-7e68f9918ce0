//
//  UPResourceProcessorBase.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceProcessor.h"
#import "UPFileDelegate.h"

@interface UPResourceProcessorBase : NSObject
@property (atomic, assign) UPResProcessorState state;
@property (atomic, readonly, assign) BOOL isSuccessful;
@property (atomic, readonly, assign) BOOL isFinished;
@property (nonatomic, copy) NSString *temporaryFilePath;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;

- (BOOL)cleanTemporaryFile;

@end

//
//  UPResourceInstallDelegate.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceInstallDelegate.h"
#import "UPResourceProcessPipeline.h"
#import "UPResourceDownloader.h"
#import "UPResourceValidator.h"
#import "UPResourceExtractor.h"
#import "UPResourceTransporter.h"
#import "UPResourceInstaller.h"
#import "UpResourceTaskMan.h"
#import "UPResourceTrackerProvider.h"
#import "UPDownloadPolicyProvider.h"
#import "UPResourceScanner.h"
#import "UPResourceInfo.h"
#import "UPResourceDirectory.h"
#import "UpResourcePresetDownloader.h"
@interface UPResourceInstallDelegate ()

@property (nonatomic, strong) id<UPDownloadDelegate> downloaderDelegate;
@property (nonatomic, weak) id<UPConnectionDelegate> connectionDelegate;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, assign) id<UPDownloadPolicyProvider> downloadPolicyProvider;
@property (nonatomic, strong) UpResourceTaskMan *taskMan;
@property (nonatomic, strong) UPResourceRepository *repository;
@property (nonatomic, strong) UPResourceDirectory *directory;
@property (nonatomic, weak) id<UPResourceTrackerProvider> trackerProvider;
@end

@implementation UPResourceInstallDelegate
@synthesize scanner = _scanner;

- (instancetype)initInstallDelegate:(id<UPConnectionDelegate>)connetionDelegate downloadDelegate:(id<UPDownloadDelegate>)downloadDelegate fileDelegate:(id<UPFileDelegate>)fileDelegate downloadPolicyProvider:(id<UPDownloadPolicyProvider>)downloadPolicyProvider taskMan:(UpResourceTaskMan *)taskMan repository:(UPResourceRepository *)repository directory:(UPResourceDirectory *)directory trackerProvider:(id<UPResourceTrackerProvider>)trackerProvider
{
    if (self = [super init]) {
        self.connectionDelegate = connetionDelegate;
        self.downloaderDelegate = downloadDelegate;
        self.fileDelegate = fileDelegate;
        self.downloadPolicyProvider = downloadPolicyProvider;
        self.taskMan = taskMan;
        self.repository = repository;
        self.directory = directory;
        self.trackerProvider = trackerProvider;
    }
    return self;
}
- (NSString *)install:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    UPResourceInstaller *installer = nil;
    if (![info.link hasPrefix:@"http"]) {
        installer = [self installPresetResource:info callback:callback listener:listener];
    }
    else {
        installer = [self installNormalResource:callback listener:listener];
    }
    return [self.taskMan createOrListen:info operator:installer callback:callback listener:listener];
}

- (UPResourceInstaller *)installNormalResource:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    UPResPipelineBuilder *builder = [[UPResPipelineBuilder alloc] init];
    UPResourceDownloader *downloader = [UPResourceDownloader resourceDownloader:self.connectionDelegate downloadPolicy:self.downloadPolicyProvider.getDownloadPolicy downloadDelegate:self.downloaderDelegate fileDelegate:self.fileDelegate trackProvider:self.trackerProvider];
    UPResourceValidator *validator = [UPResourceValidator resourceValidatorWithAlgorithm:UPValidationAlgorithmMD5 fileDelegate:self.fileDelegate];
    UPResourceExtractor *extractor = [[UPResourceExtractor alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceTransporter *transporter = [[UPResourceTransporter alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceScanner *scanner = [[UPResourceScanner alloc] initWithFileDelegate:self.fileDelegate];
    [[[[[builder add:downloader] add:validator] add:extractor] add:transporter] add:scanner];
    UPResourceProcessPipeline *pipeline = [builder build];
    UPResourceInstaller *installer = [[[UPResourceInstaller alloc] init] resourceOperator:self.repository directory:self.directory callback:callback listener:listener processor:pipeline];
    installer.trackProvider = self.trackerProvider;
    return installer;
}

- (UPResourceInstaller *)installPresetResource:(UPResourceInfo *)info callback:(id<UPResourceCallback>)callback listener:(id<UPResourceListener>)listener
{
    UPResPipelineBuilder *builder = [[UPResPipelineBuilder alloc] init];
    UpResourcePresetDownloader *localDownloader = [[UpResourcePresetDownloader alloc] initWithFileDelegate:self.fileDelegate directory:self.directory scanner:self.scanner];
    UPResourceExtractor *extractor = [[UPResourceExtractor alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceTransporter *transporter = [[UPResourceTransporter alloc] initWithFileDelegate:self.fileDelegate];
    UPResourceScanner *scanner = [[UPResourceScanner alloc] initWithFileDelegate:self.fileDelegate];
    [[[[builder add:localDownloader] add:extractor] add:transporter] add:scanner];
    UPResourceProcessPipeline *pipeline = [builder build];
    UPResourceInstaller *installer = [[[UPResourceInstaller alloc] init] resourceOperator:self.repository directory:self.directory callback:callback listener:listener processor:pipeline];
    installer.trackProvider = self.trackerProvider;
    return installer;
}

@end

//
//  UPResourceRequestDelegate.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/2/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRequestDelegate.h"
#import "UPResourceRepository.h"
@interface UPResourceRequestDelegate ()
@property (nonatomic, strong) UPResourceRepository *repository;
@end

@implementation UPResourceRequestDelegate

- (instancetype)initRequestDelegate:(UPResourceRepository *)repository
{
    if (self = [super init]) {
        self.repository = repository;
    }
    return self;
}
- (NSArray<UPResourceInfo *> *)requestResourceList:(UPResourceCondition *)condition immediate:(BOOL)immediate completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{

    return [self.repository requestResourceList:condition immediate:immediate completion:completion];
}
@end

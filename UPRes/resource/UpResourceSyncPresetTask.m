//
//  UpResourceSyncPresetTask.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceSyncPresetTask.h"
#import <UPLog/UPLog.h>
#import "UPResourceTask.h"
#import "UPResourcePresetHelper.h"
#import "UPResourceResult.h"
@interface UpResourceSyncPresetTask ()

/**
 scanner
 **/
@property (nonatomic, strong) id<UPPresetFileLoader> scanner;
/**
 appVersion
 **/
@property (nonatomic, copy) NSString *appVersion;
/**
 repository
 **/
@property (nonatomic, strong) UPResourceRepository *repository;

/**
 fileDelegate
 **/
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
/**
 timeDelegate
 **/
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
/**
 reporterDelegate
 **/
@property (nonatomic, strong) id<UPResourceReporter> reporterDelegate;
@property (nonatomic, copy) NSString *name;
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, strong) id<UPResourceOperator> resourceOperator;
@end

@implementation UpResourceSyncPresetTask

- (instancetype)initSyncPresetTask:(NSString *)name type:(UPResourceType)type resourceOperator:(id<UPResourceOperator>)resourceOperator
{

    if (self = [super init]) {
        self.name = name;
        self.type = type;
        self.resourceOperator = resourceOperator;
    }
    return self;
}
- (void)setScanner:(id<UPPresetFileLoader>)scanner
{
    _scanner = scanner;
}
- (void)setAppVersion:(NSString *)appVersion
{
    _appVersion = appVersion;
}
- (void)setRepository:(UPResourceRepository *)repository
{
    _repository = repository;
}
- (void)setFileDelegate:(id<UPFileDelegate>)fileDelegate
{
    _fileDelegate = fileDelegate;
}
- (void)setTimeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    _timeDelegate = timeDelegate;
}
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate
{
    _reporterDelegate = reporterDelegate;
}
#pragma mark override
- (UPResourceResult *)execute
{
    UPResourcePresetHelper *preHelper = [[UPResourcePresetHelper alloc] initScanner:self.scanner repository:self.repository fileDelegate:self.fileDelegate appVersion:self.appVersion timeDelegate:self.timeDelegate];
    [preHelper setReporterDelegate:self.reporterDelegate];
    if (![self.scanner conformsToProtocol:@protocol(UPPresetFileLoader)]) {
        NSString *message = @"预置资源文件扫描器对象不符合协议要求！";
        return [self result:NO message:message info:nil];
    }
    @try {
        UPLogDebug(@"UPResource", @"开始扫描文件");
        NSArray<UPResourceInfo *> *infoList = [preHelper getPresetInfos];
        UPLogDebug(@"UPResource", @"结束扫描文件");
        if (infoList == nil) {
            NSString *message = @"扫描文件异常,安装失败";
            UPLogError(@"UPResource", message);
            return [self result:NO message:message info:nil];
        }

        __block UPResourceInfo *info;
        [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *iName = obj.name;
          if (iName && [self.name isEqualToString:iName] && self.type == obj.type) {
              info = obj;
              *stop = YES;
          }
        }];
        if (info == nil) {
            NSString *message = [NSString stringWithFormat:@"没有需要安装的预置资源name = %@,type= %ld", self.name, self.type];
            return [self result:NO message:message info:nil];
        }
        UPLogDebug(@"UPResource", @"开始关联资源关系");
        BOOL relationResult = [preHelper presetInfoRelations:@[ info ]];
        UPLogDebug(@"UPResource", @"结束关联资源关系");
        if (relationResult == NO) {
            NSString *message = @"关联资源关系失败";
            UPLogError(@"UPResource", @"%s[%d] %@", __PRETTY_FUNCTION__, __LINE__, message);
            return [self result:NO message:message info:nil];
        }
        UPLogDebug(@"UPResource", @"开始检查是否需要安装资源");
        NSArray<UPResourceInfo *> *needInstallList = [preHelper getNeedInstallResource:@[ info ]];
        UPLogDebug(@"UPResource", @"结束检查是否需要安装资源");
        if (needInstallList.count == 0) {
            NSString *message = @"安装资源成功";
            UPResourceInfo *localPresetedInfo = [preHelper getLocalPresetedInfo:info];
            UPLogInfo(@"UPResource", @"%s[%d] needInstallList is empty.", __PRETTY_FUNCTION__, __LINE__);
            return [self result:YES message:message info:localPresetedInfo];
        }
        UPResourceTask *task = [UPResourceTask resourceTask:needInstallList.firstObject operator:self.resourceOperator];
        UPLogDebug(@"UPResource", @"开始安装资源");
        UPResourceResult *result = [task execute];
        UPLogDebug(@"UPResource", @"结束安装资源");
        return result;
    }
    @catch (NSException *exception) {

        UPLogWarning(@"UPResource", @"scanPresetFileList Failed%@", exception.reason);
    }
    @finally {
    }
}
#pragma mark privateMethods

- (UPResourceResult *)result:(BOOL)result message:(NSString *)message info:(UPResourceInfo *)info
{
    if (result == NO) {
        UPLogError(@"UPResource", @"同步预置资源发生错误，错误结果为%@", message);
        return [UPResourceResult failureResult:message];
    }
    UPLogInfo(@"UPResource", @"同步预置资源过程信息为%@", message);
    return [[UPResourceResult alloc] initResult:UPResourceErrorCodeSUCCESS extraInfo:message extraData:info];
}

@end

//
//  UPResourceCallback.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
@class UPResourceTask;
@class UPResourceInfo;
@protocol UPResourceProcessor;
@protocol UPResourceCallback <NSObject>
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info;
@optional
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor;

@end

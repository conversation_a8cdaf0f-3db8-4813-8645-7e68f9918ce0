//
//  UpResourceHolder.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/8.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpResourceHolder.h"

@interface UpResourceHolder ()

@property (nonatomic, strong) NSMutableSet<id> *targets;

@end
@implementation UpResourceHolder

- (instancetype)init
{

    if (self = [super init]) {

        self.targets = [NSMutableSet set];
    }

    return self;
}

- (void)add:(id)target
{

    if (target) {

        [self.targets addObject:target];
    }
}

- (void)remove:(id)target
{

    if (target) {

        [self.targets removeObject:target];
    }
}

- (void)clear
{

    [self.targets removeAllObjects];
}

- (NSArray *)getAll
{


    return self.targets.allObjects;
}


@end

//
//  UPAssetPresetFileLoader.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@protocol UPPresetFileLoader <NSObject>

/**
 * 获取预置资源目录下的文件名列表，文件命名Type@<EMAIL>
 *
 * @return 文件名列表
 */
- (nullable NSArray<NSDictionary *> *)scanPresetFilenameList;

/**
 * @brief 预置文件名的路径。
 * @param fileName 预置的文件名称
 * @return 返回文件名称的绝对路径。
 * @discussion 该方法名称看起来别扭，但是没办法，因为要与Android的接口名称保持一致。
 */
- (nullable NSString *)openPresetFile:(NSString *)fileName;


/// 通过文件名称判断预置路径下是否存在资源包
/// @param fileName 文件名称
- (BOOL)existFile:(NSString *)fileName;

@end

NS_ASSUME_NONNULL_END

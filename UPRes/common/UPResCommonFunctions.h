//
//  UPResCommonFunctions.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

BOOL UPRes_isEmptyString(NSString *str);
BOOL UPRes_isEqualString(NSString *str1, NSString *str2);
BOOL UPRes_isEqualStringIgnoringCase(NSString *str1, NSString *str2, BOOL ignoring);
BOOL UPRes_isEmptyArray(NSArray *arr);
NSString *UPRes_jsonStringValueOfJsonObject(NSObject *jsonObject, NSError **error);
NSDictionary *UPRes_jsonObjectValueOfJsonString(NSString *jsonString, NSError **error);
NSString *UPRes_validStringValue(NSString *value);
NSString *UPRes_stringValueOfDictionaryForKey(NSDictionary *dict, NSString *key);
NSComparisonResult UPRes_versionStringCompare(NSString *version, NSString *anotherVersion);
BOOL UPRes_isValidVersionString(NSString *version);
NSMutableDictionary *UPRes_convertStringObjectWithDict(NSString *string);
NSDictionary *UPRes_dictionaryValueOfDictionaryForKey(NSDictionary *dict, NSString *key);
NSDictionary *UPRes_validDictionaryValue(NSDictionary *value);

//
//  UPResCommonFunctions.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/15.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>

BOOL UPRes_isEmptyString(NSString *str)
{
    if (![str isKindOfClass:[NSString class]]) {
        return YES;
    }
    return str.length == 0;
}

BOOL UPRes_isEqualString(NSString *str1, NSString *str2)
{
    if (![str1 isKindOfClass:[NSString class]] || ![str2 isKindOfClass:[NSString class]]) {
        return NO;
    }
    return [str1 isEqualToString:str2];
}

BOOL UPRes_isEqualStringIgnoringCase(NSString *str1, NSString *str2, BOOL ignoring)
{
    if (!ignoring) {
        return UPRes_isEqualString(str1, str2);
    }

    if (![str1 isKindOfClass:[NSString class]] || ![str2 isKindOfClass:[NSString class]]) {
        return NO;
    }
    return [[str1 lowercaseString] isEqualToString:[str2 lowercaseString]];
}

BOOL UPRes_isEmptyArray(NSArray *arr)
{
    if (![arr isKindOfClass:[NSArray class]]) {
        return YES;
    }
    return arr.count == 0;
}

NSString *UPRes_jsonStringValueOfJsonObject(NSObject *jsonObject, NSError **error)
{
    if (![NSJSONSerialization isValidJSONObject:jsonObject]) {
        NSString *errMsg = [NSString stringWithFormat:@"该对象不是有效的JSON对象，无法完成转换！Object:%@", jsonObject];
        UPLogError(@"UPResource", @"%s[%d]JSON对象转JSON字符串失败！%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        if (error) {
            *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        }
        return nil;
    }
    NSError *err = NULL;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:jsonObject options:0 error:&err];
    if (err) {
        NSString *errMsg = [NSString stringWithFormat:@"该对象数据格式有误！Object:%@,error:%@", jsonObject, err];
        UPLogError(@"UPResource", @"%s[%d]JSON对象转JSON字符串失败！%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        if (error) {
            *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        }
        return nil;
    }
    NSString *jsonString = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    return jsonString;
}

NSDictionary *UPRes_jsonObjectValueOfJsonString(NSString *jsonString, NSError **error)
{
    if (!jsonString.length) {
        return nil;
    }
    NSError *err = nil;
    NSData *jsonData = [UPRes_validStringValue(jsonString) dataUsingEncoding:NSUTF8StringEncoding];
    NSDictionary *jsonObject = [NSJSONSerialization JSONObjectWithData:jsonData options:0 error:&err];
    if (err) {
        NSString *errMsg = [NSString stringWithFormat:@"该json字符串数据格式有误！String:%@,error:%@", jsonString, err];
        UPLogError(@"UPResource", @"%s[%d]JSON字符串转JSON对象失败！%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        if (error) {
            *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        }
        return nil;
    }
    return jsonObject;
}

NSString *UPRes_validStringValue(NSString *value)
{
    if (![value isKindOfClass:[NSString class]] && ![value isKindOfClass:[NSNumber class]]) {
        value = @"";
    }
    return [NSString stringWithFormat:@"%@", value];
}

NSString *UPRes_stringValueOfDictionaryForKey(NSDictionary *dict, NSString *key)
{
    NSString *value = dict[key];
    return UPRes_validStringValue(value);
}

NSDictionary *UPRes_dictionaryValueOfDictionaryForKey(NSDictionary *dict, NSString *key)
{
    NSDictionary *value = dict[key];
    return UPRes_validDictionaryValue(value);
}

NSDictionary *UPRes_validDictionaryValue(NSDictionary *value)
{
    if (![value isKindOfClass:[NSDictionary class]]) {
        return nil;
    }
    return value;
}

NSComparisonResult UPRes_versionStringCompare(NSString *version, NSString *anotherVersion)
{
    if (UPRes_isEqualStringIgnoringCase(version, anotherVersion, YES)) {
        return NSOrderedSame;
    }
    NSArray *versionElements = [version componentsSeparatedByString:@"."];
    NSArray *anotherVersionElements = [anotherVersion componentsSeparatedByString:@"."];
    NSUInteger iCount = MIN(versionElements.count, anotherVersionElements.count);
    for (int i = 0; i < iCount; i++) {
        NSString *versionElement = versionElements[i];
        NSString *anotherVersionElement = anotherVersionElements[i];
        NSInteger iVersionElementNumber = versionElement.integerValue;
        NSInteger iAnotherVersonElementNumber = anotherVersionElement.integerValue;
        if (UPRes_isEqualString(versionElement, anotherVersionElement) || iVersionElementNumber == iAnotherVersonElementNumber) {
            continue;
        }
        return iVersionElementNumber > iAnotherVersonElementNumber ? NSOrderedDescending : NSOrderedAscending;
    }
    if (versionElements.count == anotherVersionElements.count) {
        return NSOrderedSame;
    }
    return versionElements.count > anotherVersionElements.count ? NSOrderedDescending : NSOrderedAscending;
}

BOOL UPRes_isValidVersionString(NSString *version)
{
    if (UPRes_isEmptyString(version)) {
        return NO;
    }
    NSCharacterSet *characterSet = [NSCharacterSet characterSetWithCharactersInString:@"0123456789."];
    NSString *trimedString = [version stringByTrimmingCharactersInSet:characterSet];
    return UPRes_isEmptyString(trimedString);
}

NSMutableDictionary *UPRes_convertStringObjectWithDict(NSString *string)
{
    NSMutableDictionary *conditionDict = [NSMutableDictionary dictionary];
    if (string == nil && [string isEqualToString:@""]) {
        return nil;
    }
    NSArray *componentArr = [string componentsSeparatedByString:@"|"];
    for (NSString *str in componentArr) {
        NSArray *arr = [str componentsSeparatedByString:@"="];
        NSString *key = [NSString stringWithFormat:@"%@", arr[0]];
        NSString *value = [NSString stringWithFormat:@"%@", arr[1]];
        [conditionDict setObject:value forKey:key];
    }
    return conditionDict;
}

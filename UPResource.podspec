Pod::Spec.new do |s|
  s.name            = "UPResource"
  s.version         = "2.25.3.**********"
  s.author          = { "Zhukai<PERSON>" => "zhu<PERSON><PERSON>@haier.com" }
  s.license         = 'MIT'
  s.homepage        = 'www.example.com'
  s.source          = { :git => "https://git.haier.net/uplus/ios/resource.git", :tag => s.version.to_s}
  s.summary         = "U+App Resource component library for iOS platfrom"
  s.platform        = :ios, '8.0'
  s.requires_arc    = true
  s.frameworks      = 'Foundation'
  s.module_name     = 'UPResource'
  s.resources       = ['UPResDelegateIMP/*.xib','UPResDelegateIMP/**/*.xib','UPResDelegateIMP/UPResourceRes.bundle']
  s.pod_target_xcconfig = {     
    'DEFINES_MODULE' => 'YES'  
  } 
  s.subspec 'UPRes' do |ss|
  ss.source_files = 'UPRes/*.{h,m}','UPRes/**/*.{h,m}'
  end
  s.subspec 'UPResDelegateIMP' do |ss|
  ss.source_files = 'UPResDelegateIMP/*.{h,m,xib}','UPResDelegateIMP/**/*.{h,m,xib}'
  end
  s.dependency "upnetwork",">= 4.0.0"
  s.dependency "AWFileHash", ">=0.1.0"
  s.dependency "FMDB",">=2.7.5"
  s.dependency "ZipArchive", ">=1.4.0"
  s.dependency 'uplog','>=1.1.8'
  s.dependency 'UPTools/ModuleLanguage'
  s.dependency 'UPTools/Others'
  s.dependency 'UHWebImage'
  s.dependency 'UPCore/toggles','>=3.5.12.2024032301'
end

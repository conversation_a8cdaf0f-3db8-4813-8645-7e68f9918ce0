# 2.23.0
## TASK:
> -x_haieruplus-10733 iOS_iOS基础库日志完善<br /> 
> -x_haieruplus-10686 iOS_名称类H5资源请求接口切换<br /> 
> -x_haieruplus-10848 iOS_H5容器自研-iOS版本<br /> 
> -x_haieruplus-10695 iOS_动态资源触达率埋点及数据上报<br /> 
## BUG:
> -x_haieruplus-11886 【概率iOS】点击跳转H5资源包，加载动画结束后提示“下载资源失败”，未跳转<br /> 

# 2.23.1
## TASK:
> -hdszjes-1518 iOS_多语言增加阿拉伯语、 泰米尔语<br /> 

# 2.21.0
## TASK:
> -x_haieruplus-5548 iOS_YH_220922_000832_智家APP中增加：游客模式<br /> 
> -x_haieruplus-7104 iOS_资源管理单元测试重构<br /> 
> -x_haieruplus-7316 iOS_小优在线动态化试点<br /> 
## BUG:
> -x_haieruplus-9121 【iOS】偶现加载不出资源包<br /> 
> -x_haieruplus-9841 iOS的Flutter动态包按照用户圈选拉取不到资源包<br /> 

# 2.21.1
## TASK:
> -hdszjes-961 iOS_多语言增加中文<br /> 
> -hdszjes-1041 iOS_状态栏、标题栏优化<br /> 

# 2.19.8
## TASK:
> -x_haieruplus-4718 iOS_资源预置支持文件夹<br /> 
> -x_haieruplus-4971 iOS_设备支持获取应用侧属性描述文件<br /> 

# 2.19.7
## TASK:
> -hdszjes-592 iOS_东南亚server验收域名切换（技术演进）<br /> 

# 2.19.6
## TASK:
> -hdszjes-231 iOS_多语言：支持越南语<br /> 
> -hdszjes-219 iOS_loading弹框美化<br /> 
## BUG:
> -hdszjes-485 [验收]iPhone6s，加载动效不显示进度值<br /> 

# 2.19.5
## TASK:
> -x_haieruplus-1090 iOS_资源包加载逻辑优化<br /> 

# 2.19.4
## TASK:
> -ZHIJIAAPP-46128 iOS_资源下载埋点数据优化<br /> 

# 2.19.3
## BUG:
> -ZHIJIAAPP-45392 iOS启动调用配置文件接口频繁<br /> 

# 2.19.2
## BUG:
> -ZHIJIAAPP-43782 弱网下“我的”页若干按钮点击后无响应页面卡住无法操作，约1min提示“网络开小差了，稍后再试吧”<br /> 
> -ZHIJIAAPP-43752 弱网下登陆后点击首页的客服按钮页面卡住无法操作，超过1min后提示“网络开小差了，请稍后再试吧”<br /> 

# 2.19.1
## TASK:
> -ZHIJIAAPP-38219 iOS_iOS 空安全隐患的修改(二期)<br /> 
> -ZHIJIAAPP-38758 iOS_US-效能平台H5版本号规则需求_V1.0.0<br /> 
> -ZHIJIAAPP-39334 iOS_US-个人中心体验优化<br /> 
> -ZHIJIAAPP-38229 iOS_US-H5-设备管理页切换H5页面<br /> 

# 2.19.0
## TASK:
> -UAPPAC2-2507 iOS_印尼语适配<br /> 
> -UAPPAC2-2539 iOS_马来语适配<br /> 

# 2.18.0
## BUG:
> -ZHIJIAAPP-37208 【验收】iOS端启用生日场景后，点击查看场景未跳转至我的场景tab<br /> 

# 2.15.20
## TASK:
> -UAPPAC2-2264 iOS_印地语适配<br /> 

# 2.17.0
## TASK:
> -ZHIJIAAPP-32105 iOS_新增统计资源包下载时长和大小GIO点位<br /> 
> -ZHIJIAAPP-33557 iOS_iOS 空安全隐患的修改<br /> 
> -ZHIJIAAPP-32106 iOS_资源包打开支持全屏<br /> 

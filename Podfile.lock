PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - Aspects (1.4.1)
  - AWFileHash (0.1.0)
  - Cucumberish (1.4.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/SQLCipher (2.7.5):
    - SQLCipher
  - FMDB/standard (2.7.5)
  - Godzip (1.0.0)
  - MJExtension (3.4.1)
  - OCMock (3.8.1)
  - Protobuf (3.27.3)
  - Realm (10.28.3):
    - Realm/Headers (= 10.28.3)
  - Realm/Headers (10.28.3)
  - SQLCipher (4.5.3):
    - SQLCipher/standard (= 4.5.3)
  - SQLCipher/common (4.5.3)
  - SQLCipher/standard (4.5.3):
    - SQLCipher/common
  - SVProgressHUD (2.2.5)
  - uAnalytics (3.8.2)
  - UHMasonry (1.1.2.2023060801)
  - UHWebImage (3.8.6.2023060801):
    - UHWebImage/Core (= 3.8.6.2023060801)
  - UHWebImage/Core (3.8.6.2023060801)
  - UPCore/toggles (3.5.12.2024032301)
  - UPCore/UPContext (3.5.12.2024032301):
    - UPStorage (>= 1.4.13)
    - YYModel
  - uplog (1.7.6.2024091201):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.27.3)
    - ZipArchive (>= 1.4.0)
  - upnetwork (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= 4.0.6)
    - upnetwork/Headers (= 4.0.6)
    - upnetwork/HTTPDns (= 4.0.6)
    - upnetwork/Manager (= 4.0.6)
    - upnetwork/Request (= 4.0.6)
    - upnetwork/Settings (= 4.0.6)
    - upnetwork/Utils (= 4.0.6)
  - upnetwork/DynamicSign (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= 4.0.6)
  - upnetwork/DynamicSign/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= 4.0.6)
  - upnetwork/Headers/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= 4.0.6)
  - upnetwork/Manager/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= 4.0.6)
  - upnetwork/Request/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= 4.0.6)
  - upnetwork/Settings/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= 4.0.6)
  - upnetwork/Utils/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPStorage (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
    - UPStorage/Common (= 999.999.999.2024091001)
    - UPStorage/DataChange (= 999.999.999.2024091001)
    - UPStorage/Manager (= 999.999.999.2024091001)
    - UPStorage/Private (= 999.999.999.2024091001)
    - UPStorage/Public (= 999.999.999.2024091001)
    - UPStorage/Storage (= 999.999.999.2024091001)
    - UPStorage/UPStorageUtil (= 999.999.999.2024091001)
  - UPStorage/Common (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/DataChange (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Manager (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Private (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Public (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Storage (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/UPStorageUtil (999.999.999.2024091001):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPTools/ModuleLanguage (0.2.3):
    - AFNetworking (>= 3.1.0)
    - UHMasonry (>= 1.1.1)
  - UPTools/Others (0.2.3):
    - AFNetworking (>= 3.1.0)
    - Aspects (>= 1.0.0)
    - uAnalytics (>= 3.2.0)
    - UHMasonry (>= 1.1.1)
  - UpTrace (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
    - UpTrace/UpTrace (= 1.3.5.2025032901)
    - UpTrace/UpTraceCore (= 1.3.5.2025032901)
  - UpTrace/UpTrace (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - UpTrace/UpTraceCore (1.3.5.2025032901):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - UPVDN (2.7.1):
    - uplog (>= 1.1.2)
    - UPVDN/Back (= 2.7.1)
    - UPVDN/Categorys (= 2.7.1)
    - UPVDN/Launcher (= 2.7.1)
    - UPVDN/Page (= 2.7.1)
    - UPVDN/Patch (= 2.7.1)
    - UPVDN/ResultListener (= 2.7.1)
    - UPVDN/Utils (= 2.7.1)
    - UPVDN/VDNManager (= 2.7.1)
    - UPVDN/Vdns (= 2.7.1)
    - UPVDN/VirtualDomain (= 2.7.1)
  - UPVDN/Back (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Categorys (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Launcher (2.7.1):
    - uplog (>= 1.1.2)
    - UPVDN/Launcher/Native (= 2.7.1)
  - UPVDN/Launcher/Native (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Page (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Patch (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/ResultListener (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Utils (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/VDNManager (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/Vdns (2.7.1):
    - uplog (>= 1.1.2)
  - UPVDN/VirtualDomain (2.7.1):
    - uplog (>= 1.1.2)
  - YYModel (1.0.4)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - Aspects (= 1.4.1)
  - AWFileHash (= 0.1.0)
  - Cucumberish (= 1.4.0)
  - FMDB (= 2.7.5)
  - OCMock (= 3.8.1)
  - SVProgressHUD
  - UHWebImage (= 3.8.6.2023060801)
  - UPCore/toggles (= 3.5.12.2024032301)
  - UPCore/UPContext (= 3.5.12.2024032301)
  - uplog (= 1.7.6.2024091201)
  - upnetwork (= 4.0.6)
  - UPTools/ModuleLanguage (= 0.2.3)
  - UPTools/Others (= 0.2.3)
  - UpTrace (= 1.3.5.2025032901)
  - UPVDN (= 2.7.1)
  - YYModel (= 1.0.4)
  - ZipArchive (= 1.4.0)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - uAnalytics
    - UHMasonry
    - UHWebImage
    - UPCore
    - uplog
    - upnetwork
    - UPStorage
    - UPTools
    - UpTrace
    - UPVDN
  https://github.com/CocoaPods/Specs.git:
    - Aspects
    - Cucumberish
    - Godzip
    - Protobuf
    - Realm
    - SQLCipher
    - YYModel
  https://mirrors.tuna.tsinghua.edu.cn/git/CocoaPods/Specs.git:
    - AFNetworking
    - AWFileHash
    - FMDB
    - MJExtension
    - OCMock
    - SVProgressHUD
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  Aspects: 7595ba96a6727a58ebcbfc954497fc5d2fdde546
  AWFileHash: e9acc164dd6813cfd32df276e53bcda27a095100
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  Godzip: 4ee041500d1b0d56aa2415af3d99b932bfdec007
  MJExtension: 21c5f6f8c4d5d8844b7ae8fbae08fed0b501f961
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: c1cbc880ea7c4e9b157e113515c720a22ee7cf50
  Realm: 64e66568d981de2496f81ecab2e1372b27dc7d58
  SQLCipher: 57fa9f863fa4a3ed9dd3c90ace52315db8c0fdca
  SVProgressHUD: 1428aafac632c1f86f62aa4243ec12008d7a51d6
  uAnalytics: 5e5ec2958ebd8d1e253cd8b54d5ee76285cc6eab
  UHMasonry: 3be12fd6fbbb52fad07a26b4e0a8c667c9fd24f4
  UHWebImage: 1f6ca42ba00f9db3ab38144bba27e13a827ca91d
  UPCore: 7415967abe7d4c4961d6c97ecae7d76c62288b44
  uplog: a792f3d67a0bd700027053df84b5cf9e8e5e27a0
  upnetwork: e93d47c7d1acb9906832a0a0571c7e13c04c26df
  UPStorage: fd30d276903d6c14ed26af28915e9c6b91e9f4b2
  UPTools: 7a15c46b95bdd5ff983d8e3df7b3f6d283518faf
  UpTrace: de04e8399ec34f4b80b970ec5596cf0cb9c2b12e
  UPVDN: cd99e39dad127db018ff5c97f07a9c7e89dc8f0c
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: ea324fd650263a0e4cde55770aac78abaa81c777

COCOAPODS: 1.12.0

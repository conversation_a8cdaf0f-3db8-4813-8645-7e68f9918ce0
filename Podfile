source 'https://git.haier.net/uplus/shell/cocoapods/Specs.git'
source 'https://github.com/CocoaPods/Specs.git'

platform :ios, '12.0'

target 'UPRes' do
    use_modular_headers!

    pod 'uplog','1.7.6.2024091201'
end


target 'UPResDelegateIMP' do
  use_modular_headers!

  pod 'UPCore/UPContext', '3.5.12.2024032301'
  pod 'upnetwork','4.0.6'
  pod 'ZipArchive', '1.4.0'
  pod 'FMDB','2.7.5'
  pod 'AWFileHash', '0.1.0'
  pod 'UPVDN','2.7.1'
  pod 'uplog','1.7.6.2024091201'
  pod 'SVProgressHUD'
  pod 'UPTools/ModuleLanguage','0.2.3'
  pod 'UPTools/Others','0.2.3'
  pod 'UHWebImage','3.8.6.2023060801'
  pod 'UPCore/toggles','3.5.12.2024032301'
  pod 'YYModel','1.0.4'
  pod 'Aspects', '1.4.1'
  pod 'UpTrace','1.3.5.2025032901'
    
end

target 'ResDebugger' do
  use_modular_headers!

  pod 'UPCore/UPContext', '3.5.12.2024032301'
  pod 'upnetwork','4.0.6'
  pod 'ZipArchive', '1.4.0'
  pod 'FMDB','2.7.5'
  pod 'AWFileHash', '0.1.0'
  pod 'UPVDN','2.7.1'
  pod 'uplog','1.7.6.2024091201'
  pod 'SVProgressHUD'
  pod 'UPTools/ModuleLanguage','0.2.3'
  pod 'UPTools/Others','0.2.3'
  pod 'UPCore/toggles','3.5.12.2024032301'
  pod 'YYModel','1.0.4'
  pod 'Aspects', '1.4.1'
  pod 'UpTrace','1.3.5.2025032901'
end

target 'ResDebuggerTests' do
    use_modular_headers!
    
    pod 'UPCore/UPContext', '3.5.12.2024032301'
    pod 'Cucumberish','1.4.0'
    pod 'OCMock','3.8.1'
    pod 'upnetwork','4.0.6'
    pod 'ZipArchive', '1.4.0'
    pod 'FMDB','2.7.5'
    pod 'AWFileHash', '0.1.0'
    pod 'UPVDN','2.7.1'
    pod 'UPTools/ModuleLanguage','0.2.3'
    pod 'UPTools/Others','0.2.3'
    pod 'UPCore/toggles','3.5.12.2024032301'
    pod 'YYModel','1.0.4'
    pod 'Aspects', '1.4.1'
    pod 'UpTrace','1.3.5.2025032901'
end

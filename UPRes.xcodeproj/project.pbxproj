// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		225FE6F729CA8DBA00A8CEC1 /* UPOmsNormalRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 225FE6F329CA8DB900A8CEC1 /* UPOmsNormalRequest.m */; };
		225FE6F929CA8DBA00A8CEC1 /* UPOmsNormalRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 225FE6F529CA8DB900A8CEC1 /* UPOmsNormalRequest.h */; };
		22A252E3292DFBD700A46C48 /* DataBaseSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22A252E1292DFBD600A46C48 /* DataBaseSteps.m */; };
		22C485E9293DF928007032AC /* CacheCleanCallback.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C485E7293DF928007032AC /* CacheCleanCallback.m */; };
		22C485EC293DFA01007032AC /* ResourceSelectorDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 22C485EA293DFA01007032AC /* ResourceSelectorDelegate.m */; };
		22E3E5AC29481F0C00F10806 /* UPOmsAppFuncModelReques.h in Headers */ = {isa = PBXBuildFile; fileRef = 22E3E5AA29481F0C00F10806 /* UPOmsAppFuncModelReques.h */; };
		22E3E5AD29481F0C00F10806 /* UPOmsAppFuncModelReques.m in Sources */ = {isa = PBXBuildFile; fileRef = 22E3E5AB29481F0C00F10806 /* UPOmsAppFuncModelReques.m */; };
		22F421952669C24C005C2F43 /* features in Resources */ = {isa = PBXBuildFile; fileRef = 22F421942669C24C005C2F43 /* features */; };
		22FDE05A26560A01003001F1 /* AutoUpdateLocalResSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = 22FDE05926560A00003001F1 /* AutoUpdateLocalResSteps.m */; };
		2C0489A5298A8571001D4C3F /* UPResourceStatusTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C0489A3298A8571001D4C3F /* UPResourceStatusTransformer.h */; };
		2C0489A6298A8571001D4C3F /* UPResourceStatusTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C0489A4298A8571001D4C3F /* UPResourceStatusTransformer.m */; };
		2C0489A9298A96A8001D4C3F /* UPResourceStatusConvertResult.h in Headers */ = {isa = PBXBuildFile; fileRef = 2C0489A7298A96A8001D4C3F /* UPResourceStatusConvertResult.h */; };
		2C0489AA298A96A8001D4C3F /* UPResourceStatusConvertResult.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C0489A8298A96A8001D4C3F /* UPResourceStatusConvertResult.m */; };
		2CAF316529406E9A009DB26B /* UPOmsResStatusRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CAF316329406E9A009DB26B /* UPOmsResStatusRequest.h */; };
		2CAF316629406E9A009DB26B /* UPOmsResStatusRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CAF316429406E9A009DB26B /* UPOmsResStatusRequest.m */; };
		2CC23B1E28E17FC6004DB57E /* UPOmsResListRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = 2CC23B1C28E17FC6004DB57E /* UPOmsResListRequest.h */; };
		2CC23B1F28E17FC6004DB57E /* UPOmsResListRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CC23B1D28E17FC6004DB57E /* UPOmsResListRequest.m */; };
		2CC23B2228E42189004DB57E /* UPRequestNormalViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 2CC23B2128E42188004DB57E /* UPRequestNormalViewController.m */; };
		4093262C26B3A70C0067EA80 /* SpecifyResourceVersionController.h in Headers */ = {isa = PBXBuildFile; fileRef = 4093262026B3A70B0067EA80 /* SpecifyResourceVersionController.h */; };
		4093262D26B3A70C0067EA80 /* SpecifyResourceVersionAlertAnimator.h in Headers */ = {isa = PBXBuildFile; fileRef = 4093262126B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.h */; };
		4093262E26B3A70C0067EA80 /* SpecifyResourceVersionView.m in Sources */ = {isa = PBXBuildFile; fileRef = 4093262326B3A70B0067EA80 /* SpecifyResourceVersionView.m */; };
		4093262F26B3A70C0067EA80 /* SpecifyResourceVersionView.h in Headers */ = {isa = PBXBuildFile; fileRef = 4093262426B3A70B0067EA80 /* SpecifyResourceVersionView.h */; };
		4093263026B3A70C0067EA80 /* SpecifyResourceVersionAlertAnimator.m in Sources */ = {isa = PBXBuildFile; fileRef = 4093262526B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.m */; };
		4093263126B3A70C0067EA80 /* SpecifyResourceVersionController.m in Sources */ = {isa = PBXBuildFile; fileRef = 4093262626B3A70B0067EA80 /* SpecifyResourceVersionController.m */; };
		4093263226B3A70C0067EA80 /* SpecifyResourceVersionPatcher.m in Sources */ = {isa = PBXBuildFile; fileRef = 4093262726B3A70B0067EA80 /* SpecifyResourceVersionPatcher.m */; };
		4093263326B3A70C0067EA80 /* SpecifyResourceVersionPatcher.h in Headers */ = {isa = PBXBuildFile; fileRef = 4093262826B3A70B0067EA80 /* SpecifyResourceVersionPatcher.h */; };
		4093263426B3A70C0067EA80 /* SpecifyResourceVersionModel.h in Headers */ = {isa = PBXBuildFile; fileRef = 4093262A26B3A70B0067EA80 /* SpecifyResourceVersionModel.h */; };
		4093263526B3A70C0067EA80 /* SpecifyResourceVersionModel.m in Sources */ = {isa = PBXBuildFile; fileRef = 4093262B26B3A70B0067EA80 /* SpecifyResourceVersionModel.m */; };
		5D644F11266DBC1500F64F59 /* UpResourcePresetDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = 5D644F0F266DBC1500F64F59 /* UpResourcePresetDownloader.h */; };
		5D644F12266DBC1500F64F59 /* UpResourcePresetDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = 5D644F10266DBC1500F64F59 /* UpResourcePresetDownloader.m */; };
		83285ED6D1DF2A9BCEFEF69F /* libPods-ResDebuggerTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8A9E000D106A158CC3CB32E9 /* libPods-ResDebuggerTests.a */; };
		88B5D2FC49B5A38A0E472DA7 /* libPods-ResDebugger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 997ADF4FD9600234F13C92FE /* libPods-ResDebugger.a */; };
		952FEA772C85C080006061B2 /* UPResourceDownloadGifBar.xib in Resources */ = {isa = PBXBuildFile; fileRef = 952FEA762C85C080006061B2 /* UPResourceDownloadGifBar.xib */; };
		AD4A190F265355DF006D87CD /* UPResCacheCleanCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A60248888BC00414384 /* UPResCacheCleanCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1910265355DF006D87CD /* UPResCommonFunctions.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A62248888BC00414384 /* UPResCommonFunctions.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1911265355DF006D87CD /* UPDatabaseDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ABE248888BD00414384 /* UPDatabaseDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1912265355DF006D87CD /* UPRelationDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC0248888BD00414384 /* UPRelationDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1913265355DF006D87CD /* UPInstallDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC2248888BD00414384 /* UPInstallDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1914265355DF006D87CD /* UPConnectionDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC4248888BD00414384 /* UPConnectionDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1915265355DF006D87CD /* UPConnectionListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC5248888BD00414384 /* UPConnectionListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1916265355DF006D87CD /* UPResourceTracker.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC7248888BD00414384 /* UPResourceTracker.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1917265355DF006D87CD /* UPResourceTrackerProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AC8248888BD00414384 /* UPResourceTrackerProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1918265355DF006D87CD /* UPTimeDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ACA248888BD00414384 /* UPTimeDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1919265355DF006D87CD /* UPFileDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ACB248888BD00414384 /* UPFileDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191A265355DF006D87CD /* UPDownloadHandle.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ACD248888BD00414384 /* UPDownloadHandle.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191B265355DF006D87CD /* UPDownloadCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ACE248888BD00414384 /* UPDownloadCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191C265355DF006D87CD /* UPDownloadPolicyProvider.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ACF248888BD00414384 /* UPDownloadPolicyProvider.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191D265355DF006D87CD /* UPDownloadDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AD1248888BD00414384 /* UPDownloadDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191E265355DF006D87CD /* UPRequestDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AD3248888BD00414384 /* UPRequestDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A191F265355DF006D87CD /* UpPreloadResourceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ADC248888BD00414384 /* UpPreloadResourceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1920265355DF006D87CD /* UPResourceInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AD8248888BD00414384 /* UPResourceInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1921265355DF006D87CD /* UPResourceItem.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ADD248888BD00414384 /* UPResourceItem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1922265355DF006D87CD /* UPResourceQuery.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AD6248888BD00414384 /* UPResourceQuery.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1923265355DF006D87CD /* UPResourceReportInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ADE248888BD00414384 /* UPResourceReportInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1924265355DF006D87CD /* UPResourceType.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ADF248888BD00414384 /* UPResourceType.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1925265355DF006D87CD /* UPResourceResult.h in Headers */ = {isa = PBXBuildFile; fileRef = F81445A524B6A56D0011C07D /* UPResourceResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1926265355DF006D87CD /* UPPresetFileLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A5E248888BC00414384 /* UPPresetFileLoader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1927265355DF006D87CD /* UPResourceInstaller.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A86248888BC00414384 /* UPResourceInstaller.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1928265355DF006D87CD /* UPResourceUninstaller.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A8A248888BC00414384 /* UPResourceUninstaller.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1929265355DF006D87CD /* UPResourceOperator.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A8B248888BC00414384 /* UPResourceOperator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192A265355DF006D87CD /* UPResourceDownloader.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AB2248888BD00414384 /* UPResourceDownloader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192B265355DF006D87CD /* UPResourceExtractor.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AB6248888BD00414384 /* UPResourceExtractor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192C265355DF006D87CD /* UPResourceRemover.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AA9248888BD00414384 /* UPResourceRemover.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192D265355DF006D87CD /* UPResourceScanner.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ABA248888BD00414384 /* UPResourceScanner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192E265355DF006D87CD /* UPResourceTransporter.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AAC248888BD00414384 /* UPResourceTransporter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A192F265355DF006D87CD /* UPResourceProcessor.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AAF248888BD00414384 /* UPResourceProcessor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1930265355DF006D87CD /* UPResourceProcessorBase.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AB3248888BD00414384 /* UPResourceProcessorBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1931265355DF006D87CD /* UPResourceProcessPipeline.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AB7248888BD00414384 /* UPResourceProcessPipeline.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1932265355DF006D87CD /* UPResourceValidator.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AA4248888BD00414384 /* UPResourceValidator.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1933265355DF006D87CD /* UPValidationAlgorithm.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AA6248888BD00414384 /* UPValidationAlgorithm.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1934265355DF006D87CD /* UPResourceDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A7A248888BC00414384 /* UPResourceDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1935265355DF006D87CD /* UPResourceDataSourceBase.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A78248888BC00414384 /* UPResourceDataSourceBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1936265355DF006D87CD /* UPResourceRepository.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A77248888BC00414384 /* UPResourceRepository.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1937265355DF006D87CD /* UPAutoUpgradeResTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A93248888BD00414384 /* UPAutoUpgradeResTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1938265355DF006D87CD /* UpAutoUpgradeLocalResTask.h in Headers */ = {isa = PBXBuildFile; fileRef = 2220FBDA263BF895008E1F29 /* UpAutoUpgradeLocalResTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1939265355DF006D87CD /* UPResEnvironment.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A6E248888BC00414384 /* UPResEnvironment.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193A265355DF006D87CD /* UpResourceBatchTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A96248888BD00414384 /* UpResourceBatchTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193B265355DF006D87CD /* UPResourceCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A66248888BC00414384 /* UPResourceCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193C265355DF006D87CD /* UpResourceCallbackHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A8D248888BD00414384 /* UpResourceCallbackHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193D265355DF006D87CD /* UpResourceCleaner.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A67248888BC00414384 /* UpResourceCleaner.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193E265355DF006D87CD /* UpResourceCleanerImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A9D248888BD00414384 /* UpResourceCleanerImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A193F265355DF006D87CD /* UPResourceCondition.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A7E248888BC00414384 /* UPResourceCondition.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1940265355DF006D87CD /* UPResourceDirectory.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A98248888BD00414384 /* UPResourceDirectory.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1941265355DF006D87CD /* UPResourceErrCodes.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A99248888BD00414384 /* UPResourceErrCodes.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1942265355DF006D87CD /* UPResourceFilter.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A94248888BD00414384 /* UPResourceFilter.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1943265355DF006D87CD /* UPResourceHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A6B248888BC00414384 /* UPResourceHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1944265355DF006D87CD /* UpResourceHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12ABB248888BD00414384 /* UpResourceHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1945265355DF006D87CD /* UPResourceInstallDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A97248888BD00414384 /* UPResourceInstallDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1946265355DF006D87CD /* UPResourceListCallback.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A92248888BD00414384 /* UPResourceListCallback.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1947265355E0006D87CD /* UPResourceListener.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A95248888BD00414384 /* UPResourceListener.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1948265355E0006D87CD /* UpResourceListenerHolder.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A8C248888BD00414384 /* UpResourceListenerHolder.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1949265355E0006D87CD /* UPResourceManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A70248888BC00414384 /* UPResourceManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194A265355E0006D87CD /* UPResourcePreLoadTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A82248888BC00414384 /* UPResourcePreLoadTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194B265355E0006D87CD /* UPResourcePresetHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A9C248888BD00414384 /* UPResourcePresetHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194C265355E0006D87CD /* UpResourcePresetTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A6F248888BC00414384 /* UpResourcePresetTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194D265355E0006D87CD /* UPResourceRelationDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A6C248888BC00414384 /* UPResourceRelationDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194E265355E0006D87CD /* UPResourceRequestDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A69248888BC00414384 /* UPResourceRequestDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A194F265355E0006D87CD /* UpResourceSelector.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A9E248888BD00414384 /* UpResourceSelector.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1950265355E0006D87CD /* UpResourceSyncPresetTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12AA1248888BD00414384 /* UpResourceSyncPresetTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1951265355E0006D87CD /* UPResourceTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A9F248888BD00414384 /* UPResourceTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1952265355E0006D87CD /* UpResourceTaskMan.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A6A248888BC00414384 /* UpResourceTaskMan.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A1953265355E0006D87CD /* UPResourceCleanUnusefulResTask.h in Headers */ = {isa = PBXBuildFile; fileRef = F87B8DAA24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A195526535630006D87CD /* UPResCommonFunctions.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A63248888BC00414384 /* UPResCommonFunctions.m */; };
		AD4A195626535630006D87CD /* UPDownloadHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AD0248888BD00414384 /* UPDownloadHandle.m */; };
		AD4A195726535630006D87CD /* UpPreloadResourceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AD7248888BD00414384 /* UpPreloadResourceInfo.m */; };
		AD4A195826535630006D87CD /* UPResourceInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12ADA248888BD00414384 /* UPResourceInfo.m */; };
		AD4A195926535630006D87CD /* UPResourceItem.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AD5248888BD00414384 /* UPResourceItem.m */; };
		AD4A195A26535630006D87CD /* UPResourceQuery.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12ADB248888BD00414384 /* UPResourceQuery.m */; };
		AD4A195B26535630006D87CD /* UPResourceReportInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AD9248888BD00414384 /* UPResourceReportInfo.m */; };
		AD4A195C26535630006D87CD /* UPResourceResult.m in Sources */ = {isa = PBXBuildFile; fileRef = F81445A624B6A56D0011C07D /* UPResourceResult.m */; };
		AD4A195D26535630006D87CD /* UPResourceInstaller.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A85248888BC00414384 /* UPResourceInstaller.m */; };
		AD4A195E26535630006D87CD /* UPResourceUninstaller.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A89248888BC00414384 /* UPResourceUninstaller.m */; };
		AD4A195F26535630006D87CD /* UPResourceOperator.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A87248888BC00414384 /* UPResourceOperator.m */; };
		AD4A196026535630006D87CD /* UPResourceDownloader.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AB1248888BD00414384 /* UPResourceDownloader.m */; };
		AD4A196126535630006D87CD /* UPResourceExtractor.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AB5248888BD00414384 /* UPResourceExtractor.m */; };
		AD4A196226535630006D87CD /* UPResourceRemover.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AA8248888BD00414384 /* UPResourceRemover.m */; };
		AD4A196326535630006D87CD /* UPResourceScanner.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AB9248888BD00414384 /* UPResourceScanner.m */; };
		AD4A196426535630006D87CD /* UPResourceTransporter.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AAB248888BD00414384 /* UPResourceTransporter.m */; };
		AD4A196526535630006D87CD /* UPResourceProcessorBase.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AAE248888BD00414384 /* UPResourceProcessorBase.m */; };
		AD4A196626535630006D87CD /* UPResourceProcessPipeline.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AAD248888BD00414384 /* UPResourceProcessPipeline.m */; };
		AD4A196726535630006D87CD /* UPResourceValidator.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AA5248888BD00414384 /* UPResourceValidator.m */; };
		AD4A196826535630006D87CD /* UPResourceDataSourceBase.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A76248888BC00414384 /* UPResourceDataSourceBase.m */; };
		AD4A196926535630006D87CD /* UPResourceRepository.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A79248888BC00414384 /* UPResourceRepository.m */; };
		AD4A196A26535630006D87CD /* UPAutoUpgradeResTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A7B248888BC00414384 /* UPAutoUpgradeResTask.m */; };
		AD4A196B26535630006D87CD /* UpAutoUpgradeLocalResTask.m in Sources */ = {isa = PBXBuildFile; fileRef = 2220FBDB263BF895008E1F29 /* UpAutoUpgradeLocalResTask.m */; };
		AD4A196C26535630006D87CD /* UpResourceBatchTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A73248888BC00414384 /* UpResourceBatchTask.m */; };
		AD4A196D26535630006D87CD /* UpResourceCallbackHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A6D248888BC00414384 /* UpResourceCallbackHolder.m */; };
		AD4A196E26535630006D87CD /* UpResourceCleanerImpl.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A80248888BC00414384 /* UpResourceCleanerImpl.m */; };
		AD4A196F26535630006D87CD /* UPResourceCondition.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12AA0248888BD00414384 /* UPResourceCondition.m */; };
		AD4A197026535630006D87CD /* UPResourceDirectory.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A72248888BC00414384 /* UPResourceDirectory.m */; };
		AD4A197126535630006D87CD /* UPResourceErrCodes.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A71248888BC00414384 /* UPResourceErrCodes.m */; };
		AD4A197226535630006D87CD /* UPResourceHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A8F248888BD00414384 /* UPResourceHelper.m */; };
		AD4A197326535630006D87CD /* UpResourceHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A7C248888BC00414384 /* UpResourceHolder.m */; };
		AD4A197426535630006D87CD /* UPResourceInstallDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A74248888BC00414384 /* UPResourceInstallDelegate.m */; };
		AD4A197526535630006D87CD /* UpResourceListenerHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A65248888BC00414384 /* UpResourceListenerHolder.m */; };
		AD4A197626535630006D87CD /* UPResourceManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A9A248888BD00414384 /* UPResourceManager.m */; };
		AD4A197726535630006D87CD /* UPResourcePreLoadTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A68248888BC00414384 /* UPResourcePreLoadTask.m */; };
		AD4A197826535630006D87CD /* UPResourcePresetHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A81248888BC00414384 /* UPResourcePresetHelper.m */; };
		AD4A197926535630006D87CD /* UpResourcePresetTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A9B248888BD00414384 /* UpResourcePresetTask.m */; };
		AD4A197A26535630006D87CD /* UPResourceRelationDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A8E248888BD00414384 /* UPResourceRelationDelegate.m */; };
		AD4A197B26535630006D87CD /* UPResourceRequestDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A90248888BD00414384 /* UPResourceRequestDelegate.m */; };
		AD4A197C26535630006D87CD /* UpResourceSyncPresetTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A7D248888BC00414384 /* UpResourceSyncPresetTask.m */; };
		AD4A197D26535630006D87CD /* UPResourceTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A7F248888BC00414384 /* UPResourceTask.m */; };
		AD4A197E26535630006D87CD /* UpResourceTaskMan.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A91248888BD00414384 /* UpResourceTaskMan.m */; };
		AD4A197F26535630006D87CD /* UPResourceCleanUnusefulResTask.m in Sources */ = {isa = PBXBuildFile; fileRef = F87B8DAB24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.m */; };
		AD4A198126535659006D87CD /* readme.md in Resources */ = {isa = PBXBuildFile; fileRef = F81445AA24B6BD3F0011C07D /* readme.md */; };
		AD4A198226535659006D87CD /* release.md in Resources */ = {isa = PBXBuildFile; fileRef = F81445AB24B6BD3F0011C07D /* release.md */; };
		AD4A1983265358F4006D87CD /* libPods-UPRes.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 2861ED74B38CCC38BBAD1CB9 /* libPods-UPRes.a */; };
		AD4A19A026536705006D87CD /* UPResourceRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = F87624FD24C18F0E003D508F /* UPResourceRes.bundle */; };
		AD4A19A1265367E6006D87CD /* NSString+Paths.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BEC24888AA900414384 /* NSString+Paths.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A2265367E6006D87CD /* UPResourceDatabaseImpl.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BE824888AA900414384 /* UPResourceDatabaseImpl.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A3265367E6006D87CD /* UPResourceSQLiteOpenHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BEE24888AA900414384 /* UPResourceSQLiteOpenHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A4265367E6006D87CD /* UPResourceSQLiteOpenHelper+SQL.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BEB24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A5265367E6006D87CD /* UPOmsResInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BE624888AA900414384 /* UPOmsResInfo.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A6265367E6006D87CD /* UPResourceConvertResult.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BE324888AA900414384 /* UPResourceConvertResult.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A7265367E6006D87CD /* UPOmsConfigFileRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BDD24888AA900414384 /* UPOmsConfigFileRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19A8265367E6006D87CD /* UPOmsDeviceSourceRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BDB24888AA900414384 /* UPOmsDeviceSourceRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19AB265367E6006D87CD /* UPOmsResDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BD424888AA900414384 /* UPOmsResDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19AC265367E6006D87CD /* UPOmsResRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BDE24888AA900414384 /* UPOmsResRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19AD265367E6006D87CD /* UPOmsDeviceCustomInfoRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B238AA24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19AE265367E6006D87CD /* UPResourceRequestBase.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BDF24888AA900414384 /* UPResourceRequestBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19AF265367E6006D87CD /* UPResourceTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BDA24888AA900414384 /* UPResourceTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B0265367E6006D87CD /* UPResourceDeviceCustomInfoTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F8B238AE24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B1265367E6006D87CD /* UPResourceDownloaderController.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BF924888AA900414384 /* UPResourceDownloaderController.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B2265367E6006D87CD /* UPResourceDownloaderHelper.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BFA24888AA900414384 /* UPResourceDownloaderHelper.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B3265367E6006D87CD /* UPResourceDownloadGifBar.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BF124888AA900414384 /* UPResourceDownloadGifBar.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B4265367E6006D87CD /* UPResourceDownloadProgressBar.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BF824888AA900414384 /* UPResourceDownloadProgressBar.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B5265367E6006D87CD /* UPResourceSouthAsiaDownloadProgressBar.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BF624888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B6265367E6006D87CD /* UPAssetPresetFileLoader.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12BFC24888AA900414384 /* UPAssetPresetFileLoader.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B7265367E6006D87CD /* SEAOmsConfigFileRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0924888AAA00414384 /* SEAOmsConfigFileRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B8265367E6006D87CD /* SEAOmsDeviceSourceRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0D24888AAA00414384 /* SEAOmsDeviceSourceRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19B9265367E6006D87CD /* SEAOmsResDataSource.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1324888AAA00414384 /* SEAOmsResDataSource.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BA265367E6006D87CD /* SEAOmsResRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1024888AAA00414384 /* SEAOmsResRequest.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BB265367E6006D87CD /* SEARequestBase.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0824888AAA00414384 /* SEARequestBase.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BC265367E6006D87CD /* SEAResourceTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1124888AAA00414384 /* SEAResourceTransformer.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BD265367E6006D87CD /* UPResourceFileSystem.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1924888AAB00414384 /* UPResourceFileSystem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BE265367E6006D87CD /* UPResourceTimeSystem.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1A24888AAB00414384 /* UPResourceTimeSystem.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19BF265367E6006D87CD /* UPConnectionMonitor.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1524888AAA00414384 /* UPConnectionMonitor.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C0265367E6006D87CD /* UPDownloaderDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0324888AAA00414384 /* UPDownloaderDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C1265367E6006D87CD /* UPDownloaderHandle.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C1424888AAA00414384 /* UPDownloaderHandle.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C2265367E6006D87CD /* UPResourceConfig.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0424888AAA00414384 /* UPResourceConfig.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C3265367E6006D87CD /* UPResourceInjection.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12C0024888AAA00414384 /* UPResourceInjection.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C4265367E6006D87CD /* UPResDelegateIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = F8D12A49248886BF00414384 /* UPResDelegateIMP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		AD4A19C526536841006D87CD /* NSString+Paths.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BE924888AA900414384 /* NSString+Paths.m */; };
		AD4A19C626536841006D87CD /* UPResourceDatabaseImpl.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BED24888AA900414384 /* UPResourceDatabaseImpl.m */; };
		AD4A19C726536841006D87CD /* UPResourceSQLiteOpenHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BEA24888AA900414384 /* UPResourceSQLiteOpenHelper.m */; };
		AD4A19C826536841006D87CD /* UPResourceSQLiteOpenHelper+SQL.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BEF24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.m */; };
		AD4A19C926536841006D87CD /* UPOmsResInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BE424888AA900414384 /* UPOmsResInfo.m */; };
		AD4A19CA26536841006D87CD /* UPResourceConvertResult.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BE524888AA900414384 /* UPResourceConvertResult.m */; };
		AD4A19CB26536841006D87CD /* UPOmsConfigFileRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BD824888AA900414384 /* UPOmsConfigFileRequest.m */; };
		AD4A19CC26536841006D87CD /* UPOmsDeviceSourceRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BD524888AA900414384 /* UPOmsDeviceSourceRequest.m */; };
		AD4A19CF26536841006D87CD /* UPOmsResDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BDC24888AA900414384 /* UPOmsResDataSource.m */; };
		AD4A19D026536841006D87CD /* UPOmsResRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BD624888AA900414384 /* UPOmsResRequest.m */; };
		AD4A19D126536841006D87CD /* UPOmsDeviceCustomInfoRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B238AB24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.m */; };
		AD4A19D226536841006D87CD /* UPResourceRequestBase.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BD724888AA900414384 /* UPResourceRequestBase.m */; };
		AD4A19D326536841006D87CD /* UPResourceTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BE024888AA900414384 /* UPResourceTransformer.m */; };
		AD4A19D426536841006D87CD /* UPResourceDeviceCustomInfoTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F8B238AF24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.m */; };
		AD4A19D526536841006D87CD /* UPResourceDownloaderController.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BF324888AA900414384 /* UPResourceDownloaderController.m */; };
		AD4A19D626536841006D87CD /* UPResourceDownloaderHelper.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BF524888AA900414384 /* UPResourceDownloaderHelper.m */; };
		AD4A19D726536841006D87CD /* UPResourceDownloadGifBar.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BF724888AA900414384 /* UPResourceDownloadGifBar.m */; };
		AD4A19D826536841006D87CD /* UPResourceDownloadProgressBar.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BF424888AA900414384 /* UPResourceDownloadProgressBar.m */; };
		AD4A19D926536841006D87CD /* UPResourceSouthAsiaDownloadProgressBar.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BF224888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.m */; };
		AD4A19DA26536841006D87CD /* UPAssetPresetFileLoader.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BFD24888AA900414384 /* UPAssetPresetFileLoader.m */; };
		AD4A19DB26536841006D87CD /* SEAOmsConfigFileRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0F24888AAA00414384 /* SEAOmsConfigFileRequest.m */; };
		AD4A19DC26536841006D87CD /* SEAOmsDeviceSourceRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C1224888AAA00414384 /* SEAOmsDeviceSourceRequest.m */; };
		AD4A19DD26536841006D87CD /* SEAOmsResDataSource.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0C24888AAA00414384 /* SEAOmsResDataSource.m */; };
		AD4A19DE26536841006D87CD /* SEAOmsResRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0B24888AAA00414384 /* SEAOmsResRequest.m */; };
		AD4A19DF26536841006D87CD /* SEARequestBase.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0E24888AAA00414384 /* SEARequestBase.m */; };
		AD4A19E026536841006D87CD /* SEAResourceTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0A24888AAA00414384 /* SEAResourceTransformer.m */; };
		AD4A19E126536841006D87CD /* UPResourceFileSystem.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C1B24888AAB00414384 /* UPResourceFileSystem.m */; };
		AD4A19E226536841006D87CD /* UPResourceTimeSystem.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C1824888AAB00414384 /* UPResourceTimeSystem.m */; };
		AD4A19E326536841006D87CD /* UPConnectionMonitor.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0224888AAA00414384 /* UPConnectionMonitor.m */; };
		AD4A19E426536841006D87CD /* UPDownloaderDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BFE24888AAA00414384 /* UPDownloaderDelegate.m */; };
		AD4A19E526536841006D87CD /* UPDownloaderHandle.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BFF24888AAA00414384 /* UPDownloaderHandle.m */; };
		AD4A19E626536841006D87CD /* UPResourceConfig.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C1624888AAA00414384 /* UPResourceConfig.m */; };
		AD4A19E726536841006D87CD /* UPResourceInjection.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12C0524888AAA00414384 /* UPResourceInjection.m */; };
		AD4A19EA26536915006D87CD /* libUPRes.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD4A1905265354E9006D87CD /* libUPRes.a */; };
		AD4A19EC26536B66006D87CD /* libUPResDelegateIMP.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD4A1995265366CC006D87CD /* libUPResDelegateIMP.a */; };
		AD4A19F026536BA5006D87CD /* libUPResDelegateIMP.a in Frameworks */ = {isa = PBXBuildFile; fileRef = AD4A1995265366CC006D87CD /* libUPResDelegateIMP.a */; };
		DF5F427A29272AD4008BA9D7 /* CommonResCallbackImp.m in Sources */ = {isa = PBXBuildFile; fileRef = DF5F427929272AD4008BA9D7 /* CommonResCallbackImp.m */; };
		DF6D055E290A66B10084A0CF /* UPResourceSEADownloadGifBar.h in Headers */ = {isa = PBXBuildFile; fileRef = DF6D055C290A66B10084A0CF /* UPResourceSEADownloadGifBar.h */; };
		DF6D055F290A66B10084A0CF /* UPResourceSEADownloadGifBar.m in Sources */ = {isa = PBXBuildFile; fileRef = DF6D055D290A66B10084A0CF /* UPResourceSEADownloadGifBar.m */; };
		DF6D0561290A6DF10084A0CF /* UPResourceUIMacros.h in Headers */ = {isa = PBXBuildFile; fileRef = DF6D0560290A6DF10084A0CF /* UPResourceUIMacros.h */; };
		DF6D0564290A726B0084A0CF /* UPResourceUITool.h in Headers */ = {isa = PBXBuildFile; fileRef = DF6D0562290A726B0084A0CF /* UPResourceUITool.h */; };
		DF6D0565290A726B0084A0CF /* UPResourceUITool.m in Sources */ = {isa = PBXBuildFile; fileRef = DF6D0563290A726B0084A0CF /* UPResourceUITool.m */; };
		E7352B1D003E276DC0A7147B /* libPods-UPResDelegateIMP.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 4EB782260D69A2400677A140 /* libPods-UPResDelegateIMP.a */; };
		F7062AF72886A00E00B29353 /* UPRAppLifeCycleManager.m in Sources */ = {isa = PBXBuildFile; fileRef = F7062AF42886A00E00B29353 /* UPRAppLifeCycleManager.m */; };
		F7062AF82886A00E00B29353 /* UPRAppLifeCycleManager.h in Headers */ = {isa = PBXBuildFile; fileRef = F7062AF52886A00E00B29353 /* UPRAppLifeCycleManager.h */; };
		F7062AF92886A00E00B29353 /* UPRAppManagerProtocol.h in Headers */ = {isa = PBXBuildFile; fileRef = F7062AF62886A00E00B29353 /* UPRAppManagerProtocol.h */; };
		F7096C4929C16B0F004F9A62 /* UPResourceReporter.h in Headers */ = {isa = PBXBuildFile; fileRef = F7096C4729C16B0F004F9A62 /* UPResourceReporter.h */; };
		F70D8B7F29C441D900FAD23F /* ReportResLoadedSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F70D8B7E29C441D900FAD23F /* ReportResLoadedSteps.m */; };
		F70D8B8429CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F70D8B8229CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.h */; };
		F70D8B8529CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F70D8B8329CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.m */; };
		F70D8B9029CAE30600FAD23F /* UPResourceReporterSetter.h in Headers */ = {isa = PBXBuildFile; fileRef = F70D8B8E29CAE30600FAD23F /* UPResourceReporterSetter.h */; };
		F724F8B129CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.h in Headers */ = {isa = PBXBuildFile; fileRef = F724F8AF29CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.h */; };
		F724F8B229CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.m in Sources */ = {isa = PBXBuildFile; fileRef = F724F8B029CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.m */; };
		F743827C29B593490058489B /* UPRLoadReporterImp.h in Headers */ = {isa = PBXBuildFile; fileRef = F743827A29B593490058489B /* UPRLoadReporterImp.h */; };
		F743827D29B593490058489B /* UPRLoadReporterImp.m in Sources */ = {isa = PBXBuildFile; fileRef = F743827B29B593490058489B /* UPRLoadReporterImp.m */; };
		F743828229B596FA0058489B /* UPRLoadedReportInfo.h in Headers */ = {isa = PBXBuildFile; fileRef = F743828029B596FA0058489B /* UPRLoadedReportInfo.h */; };
		F743828329B596FA0058489B /* UPRLoadedReportInfo.m in Sources */ = {isa = PBXBuildFile; fileRef = F743828129B596FA0058489B /* UPRLoadedReportInfo.m */; };
		F876250024C18F37003D508F /* UPResourceRes.bundle in Resources */ = {isa = PBXBuildFile; fileRef = F87624FD24C18F0E003D508F /* UPResourceRes.bundle */; };
		F89E5ADE2E25210600E41151 /* UPOmsLuaRequest.h in Headers */ = {isa = PBXBuildFile; fileRef = F89E5ADC2E25210600E41151 /* UPOmsLuaRequest.h */; };
		F89E5ADF2E25210600E41151 /* UPOmsLuaRequest.m in Sources */ = {isa = PBXBuildFile; fileRef = F89E5ADD2E25210600E41151 /* UPOmsLuaRequest.m */; };
		F8D12A152488865D00414384 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A142488865D00414384 /* AppDelegate.m */; };
		F8D12A1B2488865D00414384 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12A1A2488865D00414384 /* ViewController.m */; };
		F8D12A1E2488865D00414384 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F8D12A1C2488865D00414384 /* Main.storyboard */; };
		F8D12A232488865F00414384 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F8D12A212488865F00414384 /* LaunchScreen.storyboard */; };
		F8D12B582488895200414384 /* UPResTableViewCell.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B4B2488895200414384 /* UPResTableViewCell.m */; };
		F8D12B592488895200414384 /* UPTableViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B4D2488895200414384 /* UPTableViewController.m */; };
		F8D12B5A2488895200414384 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = F8D12B4F2488895200414384 /* Assets.xcassets */; };
		F8D12B5B2488895200414384 /* main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F8D12B502488895200414384 /* main.storyboard */; };
		F8D12B5C2488895200414384 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = F8D12B512488895200414384 /* <EMAIL> */; };
		F8D12B5D2488895200414384 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B522488895200414384 /* main.m */; };
		F8D12B5F2488895200414384 /* RootViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B542488895200414384 /* RootViewController.m */; };
		F8D12B602488895200414384 /* RootViewController+TableView.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B562488895200414384 /* RootViewController+TableView.m */; };
		F8D12BA82488897700414384 /* StepUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B622488897600414384 /* StepUtils.m */; };
		F8D12BAA2488897700414384 /* DataSourceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B672488897600414384 /* DataSourceSteps.m */; };
		F8D12BAB2488897700414384 /* TimeSystemSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B692488897600414384 /* TimeSystemSteps.m */; };
		F8D12BAC2488897700414384 /* DownloadSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B6A2488897600414384 /* DownloadSteps.m */; };
		F8D12BAD2488897700414384 /* FileSystemSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B6C2488897600414384 /* FileSystemSteps.m */; };
		F8D12BAE2488897700414384 /* NetworkSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B6F2488897600414384 /* NetworkSteps.m */; };
		F8D12BB02488897700414384 /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B712488897700414384 /* CucumberRunner.m */; };
		F8D12BC12488897700414384 /* ResourceHolder.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B932488897700414384 /* ResourceHolder.m */; };
		F8D12BC22488897700414384 /* TaskResourceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B952488897700414384 /* TaskResourceSteps.m */; };
		F8D12BC32488897700414384 /* UpdateSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B962488897700414384 /* UpdateSteps.m */; };
		F8D12BC42488897700414384 /* PresetSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B9A2488897700414384 /* PresetSteps.m */; };
		F8D12BC52488897700414384 /* RequestSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B9B2488897700414384 /* RequestSteps.m */; };
		F8D12BC62488897700414384 /* RelationSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12B9F2488897700414384 /* RelationSteps.m */; };
		F8D12BC72488897700414384 /* CombineResInfoSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BA12488897700414384 /* CombineResInfoSteps.m */; };
		F8D12BC82488897700414384 /* SearchResourceSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BA22488897700414384 /* SearchResourceSteps.m */; };
		F8D12BC92488897700414384 /* LocalCacheSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BA32488897700414384 /* LocalCacheSteps.m */; };
		F8D12BCA2488897700414384 /* InitializationSteps.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D12BA42488897700414384 /* InitializationSteps.m */; };
		F8D12C6C2488961300414384 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F8D12C682488961300414384 /* LaunchScreen.storyboard */; };
		F8D12C6D2488961300414384 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = F8D12C6A2488961300414384 /* Main.storyboard */; };
		F8D12C6F2488969D00414384 /* PresetResPkg in Resources */ = {isa = PBXBuildFile; fileRef = F8D12C6E2488969D00414384 /* PresetResPkg */; };
		F8D4365E2547C8FB0089A911 /* UPResourceTrackerIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = F8D4365D2547C8FB0089A911 /* UPResourceTrackerIMP.m */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		AD4A19E826536852006D87CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8D129F92488864400414384 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD4A1904265354E9006D87CD;
			remoteInfo = UPRes;
		};
		AD4A19ED26536B92006D87CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8D129F92488864400414384 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD4A1994265366CC006D87CD;
			remoteInfo = UPResDelegateIMP;
		};
		AD4A19F126536CFA006D87CD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = F8D129F92488864400414384 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = AD4A1994265366CC006D87CD;
			remoteInfo = UPResDelegateIMP;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		AD4A1903265354E9006D87CD /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD4A1993265366CC006D87CD /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		0D513A0FFDC2F9EA49DBA8B7 /* Pods-ResDebuggerTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ResDebuggerTests.debug.xcconfig"; path = "Target Support Files/Pods-ResDebuggerTests/Pods-ResDebuggerTests.debug.xcconfig"; sourceTree = "<group>"; };
		1F1D4295206D6F942F466E9A /* Pods-UPResDelegateIMP.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPResDelegateIMP.release.xcconfig"; path = "Target Support Files/Pods-UPResDelegateIMP/Pods-UPResDelegateIMP.release.xcconfig"; sourceTree = "<group>"; };
		2220FBDA263BF895008E1F29 /* UpAutoUpgradeLocalResTask.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAutoUpgradeLocalResTask.h; sourceTree = "<group>"; };
		2220FBDB263BF895008E1F29 /* UpAutoUpgradeLocalResTask.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAutoUpgradeLocalResTask.m; sourceTree = "<group>"; };
		225FE6F329CA8DB900A8CEC1 /* UPOmsNormalRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsNormalRequest.m; sourceTree = "<group>"; };
		225FE6F529CA8DB900A8CEC1 /* UPOmsNormalRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsNormalRequest.h; sourceTree = "<group>"; };
		22A252E1292DFBD600A46C48 /* DataBaseSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DataBaseSteps.m; sourceTree = "<group>"; };
		22A252E2292DFBD600A46C48 /* DataBaseSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DataBaseSteps.h; sourceTree = "<group>"; };
		22C485E7293DF928007032AC /* CacheCleanCallback.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CacheCleanCallback.m; sourceTree = "<group>"; };
		22C485E8293DF928007032AC /* CacheCleanCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CacheCleanCallback.h; sourceTree = "<group>"; };
		22C485EA293DFA01007032AC /* ResourceSelectorDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ResourceSelectorDelegate.m; sourceTree = "<group>"; };
		22C485EB293DFA01007032AC /* ResourceSelectorDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ResourceSelectorDelegate.h; sourceTree = "<group>"; };
		22E3E5AA29481F0C00F10806 /* UPOmsAppFuncModelReques.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsAppFuncModelReques.h; sourceTree = "<group>"; };
		22E3E5AB29481F0C00F10806 /* UPOmsAppFuncModelReques.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsAppFuncModelReques.m; sourceTree = "<group>"; };
		22F421942669C24C005C2F43 /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		22FDE05826560A00003001F1 /* AutoUpdateLocalResSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AutoUpdateLocalResSteps.h; sourceTree = "<group>"; };
		22FDE05926560A00003001F1 /* AutoUpdateLocalResSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AutoUpdateLocalResSteps.m; sourceTree = "<group>"; };
		2861ED74B38CCC38BBAD1CB9 /* libPods-UPRes.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPRes.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		2C0489A3298A8571001D4C3F /* UPResourceStatusTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceStatusTransformer.h; sourceTree = "<group>"; };
		2C0489A4298A8571001D4C3F /* UPResourceStatusTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceStatusTransformer.m; sourceTree = "<group>"; };
		2C0489A7298A96A8001D4C3F /* UPResourceStatusConvertResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceStatusConvertResult.h; sourceTree = "<group>"; };
		2C0489A8298A96A8001D4C3F /* UPResourceStatusConvertResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceStatusConvertResult.m; sourceTree = "<group>"; };
		2CAF316329406E9A009DB26B /* UPOmsResStatusRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsResStatusRequest.h; sourceTree = "<group>"; };
		2CAF316429406E9A009DB26B /* UPOmsResStatusRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsResStatusRequest.m; sourceTree = "<group>"; };
		2CC23B1C28E17FC6004DB57E /* UPOmsResListRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsResListRequest.h; sourceTree = "<group>"; };
		2CC23B1D28E17FC6004DB57E /* UPOmsResListRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsResListRequest.m; sourceTree = "<group>"; };
		2CC23B2028E42188004DB57E /* UPRequestNormalViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPRequestNormalViewController.h; sourceTree = "<group>"; };
		2CC23B2128E42188004DB57E /* UPRequestNormalViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPRequestNormalViewController.m; sourceTree = "<group>"; };
		3710EC6E3F2BB08028AD5056 /* Pods-ResDebugger.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ResDebugger.release.xcconfig"; path = "Target Support Files/Pods-ResDebugger/Pods-ResDebugger.release.xcconfig"; sourceTree = "<group>"; };
		37D82FF20BC73B478087B8E2 /* Pods-UPRes.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPRes.debug.xcconfig"; path = "Target Support Files/Pods-UPRes/Pods-UPRes.debug.xcconfig"; sourceTree = "<group>"; };
		4093262026B3A70B0067EA80 /* SpecifyResourceVersionController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpecifyResourceVersionController.h; sourceTree = "<group>"; };
		4093262126B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpecifyResourceVersionAlertAnimator.h; sourceTree = "<group>"; };
		4093262326B3A70B0067EA80 /* SpecifyResourceVersionView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpecifyResourceVersionView.m; sourceTree = "<group>"; };
		4093262426B3A70B0067EA80 /* SpecifyResourceVersionView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpecifyResourceVersionView.h; sourceTree = "<group>"; };
		4093262526B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpecifyResourceVersionAlertAnimator.m; sourceTree = "<group>"; };
		4093262626B3A70B0067EA80 /* SpecifyResourceVersionController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpecifyResourceVersionController.m; sourceTree = "<group>"; };
		4093262726B3A70B0067EA80 /* SpecifyResourceVersionPatcher.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpecifyResourceVersionPatcher.m; sourceTree = "<group>"; };
		4093262826B3A70B0067EA80 /* SpecifyResourceVersionPatcher.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpecifyResourceVersionPatcher.h; sourceTree = "<group>"; };
		4093262A26B3A70B0067EA80 /* SpecifyResourceVersionModel.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SpecifyResourceVersionModel.h; sourceTree = "<group>"; };
		4093262B26B3A70B0067EA80 /* SpecifyResourceVersionModel.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SpecifyResourceVersionModel.m; sourceTree = "<group>"; };
		4EB782260D69A2400677A140 /* libPods-UPResDelegateIMP.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UPResDelegateIMP.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5790CA85BC007452009D4C38 /* Pods-UPRes.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPRes.release.xcconfig"; path = "Target Support Files/Pods-UPRes/Pods-UPRes.release.xcconfig"; sourceTree = "<group>"; };
		5D644F0F266DBC1500F64F59 /* UpResourcePresetDownloader.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpResourcePresetDownloader.h; sourceTree = "<group>"; };
		5D644F10266DBC1500F64F59 /* UpResourcePresetDownloader.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpResourcePresetDownloader.m; sourceTree = "<group>"; };
		8A9E000D106A158CC3CB32E9 /* libPods-ResDebuggerTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ResDebuggerTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		952FEA762C85C080006061B2 /* UPResourceDownloadGifBar.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = UPResourceDownloadGifBar.xib; sourceTree = "<group>"; };
		997ADF4FD9600234F13C92FE /* libPods-ResDebugger.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-ResDebugger.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		AD4A1905265354E9006D87CD /* libUPRes.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPRes.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AD4A1995265366CC006D87CD /* libUPResDelegateIMP.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libUPResDelegateIMP.a; sourceTree = BUILT_PRODUCTS_DIR; };
		AE91C74C81E0886153FA3D73 /* Pods-ResDebuggerTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ResDebuggerTests.release.xcconfig"; path = "Target Support Files/Pods-ResDebuggerTests/Pods-ResDebuggerTests.release.xcconfig"; sourceTree = "<group>"; };
		CE0EC0391743A94A7FEF95DE /* Pods-UPResDelegateIMP.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UPResDelegateIMP.debug.xcconfig"; path = "Target Support Files/Pods-UPResDelegateIMP/Pods-UPResDelegateIMP.debug.xcconfig"; sourceTree = "<group>"; };
		DF5F427829272AD4008BA9D7 /* CommonResCallbackImp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = CommonResCallbackImp.h; sourceTree = "<group>"; };
		DF5F427929272AD4008BA9D7 /* CommonResCallbackImp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CommonResCallbackImp.m; sourceTree = "<group>"; };
		DF6D055C290A66B10084A0CF /* UPResourceSEADownloadGifBar.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceSEADownloadGifBar.h; sourceTree = "<group>"; };
		DF6D055D290A66B10084A0CF /* UPResourceSEADownloadGifBar.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceSEADownloadGifBar.m; sourceTree = "<group>"; };
		DF6D0560290A6DF10084A0CF /* UPResourceUIMacros.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceUIMacros.h; sourceTree = "<group>"; };
		DF6D0562290A726B0084A0CF /* UPResourceUITool.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceUITool.h; sourceTree = "<group>"; };
		DF6D0563290A726B0084A0CF /* UPResourceUITool.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceUITool.m; sourceTree = "<group>"; };
		F507A8F876CEC2FB3C8BD7A3 /* Pods-ResDebugger.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-ResDebugger.debug.xcconfig"; path = "Target Support Files/Pods-ResDebugger/Pods-ResDebugger.debug.xcconfig"; sourceTree = "<group>"; };
		F7062AF42886A00E00B29353 /* UPRAppLifeCycleManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPRAppLifeCycleManager.m; sourceTree = "<group>"; };
		F7062AF52886A00E00B29353 /* UPRAppLifeCycleManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPRAppLifeCycleManager.h; sourceTree = "<group>"; };
		F7062AF62886A00E00B29353 /* UPRAppManagerProtocol.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPRAppManagerProtocol.h; sourceTree = "<group>"; };
		F7096C4729C16B0F004F9A62 /* UPResourceReporter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceReporter.h; sourceTree = "<group>"; };
		F70D8B7D29C441D900FAD23F /* ReportResLoadedSteps.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ReportResLoadedSteps.h; sourceTree = "<group>"; };
		F70D8B7E29C441D900FAD23F /* ReportResLoadedSteps.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ReportResLoadedSteps.m; sourceTree = "<group>"; };
		F70D8B8229CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsReportResLoadedInfoRequest.h; sourceTree = "<group>"; };
		F70D8B8329CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsReportResLoadedInfoRequest.m; sourceTree = "<group>"; };
		F70D8B8E29CAE30600FAD23F /* UPResourceReporterSetter.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceReporterSetter.h; sourceTree = "<group>"; };
		F724F8AF29CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceReportLoadedInfoTransformer.h; sourceTree = "<group>"; };
		F724F8B029CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceReportLoadedInfoTransformer.m; sourceTree = "<group>"; };
		F73C2E6927B642DC00CA5FDA /* UPResourceDownloadGifType.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloadGifType.h; sourceTree = "<group>"; };
		F743827A29B593490058489B /* UPRLoadReporterImp.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPRLoadReporterImp.h; sourceTree = "<group>"; };
		F743827B29B593490058489B /* UPRLoadReporterImp.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPRLoadReporterImp.m; sourceTree = "<group>"; };
		F743828029B596FA0058489B /* UPRLoadedReportInfo.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPRLoadedReportInfo.h; sourceTree = "<group>"; };
		F743828129B596FA0058489B /* UPRLoadedReportInfo.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPRLoadedReportInfo.m; sourceTree = "<group>"; };
		F81445A524B6A56D0011C07D /* UPResourceResult.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceResult.h; sourceTree = "<group>"; };
		F81445A624B6A56D0011C07D /* UPResourceResult.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceResult.m; sourceTree = "<group>"; };
		F81445AA24B6BD3F0011C07D /* readme.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		F81445AB24B6BD3F0011C07D /* release.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = release.md; sourceTree = "<group>"; };
		F87624FD24C18F0E003D508F /* UPResourceRes.bundle */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.plug-in"; path = UPResourceRes.bundle; sourceTree = "<group>"; };
		F87B8DAA24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceCleanUnusefulResTask.h; sourceTree = "<group>"; };
		F87B8DAB24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceCleanUnusefulResTask.m; sourceTree = "<group>"; };
		F89E5ADC2E25210600E41151 /* UPOmsLuaRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsLuaRequest.h; sourceTree = "<group>"; };
		F89E5ADD2E25210600E41151 /* UPOmsLuaRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsLuaRequest.m; sourceTree = "<group>"; };
		F8B238AA24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPOmsDeviceCustomInfoRequest.h; sourceTree = "<group>"; };
		F8B238AB24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPOmsDeviceCustomInfoRequest.m; sourceTree = "<group>"; };
		F8B238AE24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResourceDeviceCustomInfoTransformer.h; sourceTree = "<group>"; };
		F8B238AF24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UPResourceDeviceCustomInfoTransformer.m; sourceTree = "<group>"; };
		F8D12A112488865D00414384 /* ResDebugger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = ResDebugger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		F8D12A132488865D00414384 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		F8D12A142488865D00414384 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		F8D12A192488865D00414384 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		F8D12A1A2488865D00414384 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		F8D12A1D2488865D00414384 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		F8D12A222488865F00414384 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		F8D12A2E2488867C00414384 /* ResDebuggerTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = ResDebuggerTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		F8D12A49248886BF00414384 /* UPResDelegateIMP.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UPResDelegateIMP.h; sourceTree = "<group>"; };
		F8D12A5E248888BC00414384 /* UPPresetFileLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPPresetFileLoader.h; sourceTree = "<group>"; };
		F8D12A60248888BC00414384 /* UPResCacheCleanCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResCacheCleanCallback.h; sourceTree = "<group>"; };
		F8D12A62248888BC00414384 /* UPResCommonFunctions.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResCommonFunctions.h; sourceTree = "<group>"; };
		F8D12A63248888BC00414384 /* UPResCommonFunctions.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResCommonFunctions.m; sourceTree = "<group>"; };
		F8D12A65248888BC00414384 /* UpResourceListenerHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceListenerHolder.m; sourceTree = "<group>"; };
		F8D12A66248888BC00414384 /* UPResourceCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceCallback.h; sourceTree = "<group>"; };
		F8D12A67248888BC00414384 /* UpResourceCleaner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceCleaner.h; sourceTree = "<group>"; };
		F8D12A68248888BC00414384 /* UPResourcePreLoadTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourcePreLoadTask.m; sourceTree = "<group>"; };
		F8D12A69248888BC00414384 /* UPResourceRequestDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceRequestDelegate.h; sourceTree = "<group>"; };
		F8D12A6A248888BC00414384 /* UpResourceTaskMan.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceTaskMan.h; sourceTree = "<group>"; };
		F8D12A6B248888BC00414384 /* UPResourceHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceHelper.h; sourceTree = "<group>"; };
		F8D12A6C248888BC00414384 /* UPResourceRelationDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceRelationDelegate.h; sourceTree = "<group>"; };
		F8D12A6D248888BC00414384 /* UpResourceCallbackHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceCallbackHolder.m; sourceTree = "<group>"; };
		F8D12A6E248888BC00414384 /* UPResEnvironment.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResEnvironment.h; sourceTree = "<group>"; };
		F8D12A6F248888BC00414384 /* UpResourcePresetTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourcePresetTask.h; sourceTree = "<group>"; };
		F8D12A70248888BC00414384 /* UPResourceManager.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceManager.h; sourceTree = "<group>"; };
		F8D12A71248888BC00414384 /* UPResourceErrCodes.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceErrCodes.m; sourceTree = "<group>"; };
		F8D12A72248888BC00414384 /* UPResourceDirectory.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDirectory.m; sourceTree = "<group>"; };
		F8D12A73248888BC00414384 /* UpResourceBatchTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceBatchTask.m; sourceTree = "<group>"; };
		F8D12A74248888BC00414384 /* UPResourceInstallDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceInstallDelegate.m; sourceTree = "<group>"; };
		F8D12A76248888BC00414384 /* UPResourceDataSourceBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDataSourceBase.m; sourceTree = "<group>"; };
		F8D12A77248888BC00414384 /* UPResourceRepository.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceRepository.h; sourceTree = "<group>"; };
		F8D12A78248888BC00414384 /* UPResourceDataSourceBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDataSourceBase.h; sourceTree = "<group>"; };
		F8D12A79248888BC00414384 /* UPResourceRepository.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceRepository.m; sourceTree = "<group>"; };
		F8D12A7A248888BC00414384 /* UPResourceDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDataSource.h; sourceTree = "<group>"; };
		F8D12A7B248888BC00414384 /* UPAutoUpgradeResTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPAutoUpgradeResTask.m; sourceTree = "<group>"; };
		F8D12A7C248888BC00414384 /* UpResourceHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceHolder.m; sourceTree = "<group>"; };
		F8D12A7D248888BC00414384 /* UpResourceSyncPresetTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceSyncPresetTask.m; sourceTree = "<group>"; };
		F8D12A7E248888BC00414384 /* UPResourceCondition.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceCondition.h; sourceTree = "<group>"; };
		F8D12A7F248888BC00414384 /* UPResourceTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceTask.m; sourceTree = "<group>"; };
		F8D12A80248888BC00414384 /* UpResourceCleanerImpl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceCleanerImpl.m; sourceTree = "<group>"; };
		F8D12A81248888BC00414384 /* UPResourcePresetHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourcePresetHelper.m; sourceTree = "<group>"; };
		F8D12A82248888BC00414384 /* UPResourcePreLoadTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourcePreLoadTask.h; sourceTree = "<group>"; };
		F8D12A85248888BC00414384 /* UPResourceInstaller.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceInstaller.m; sourceTree = "<group>"; };
		F8D12A86248888BC00414384 /* UPResourceInstaller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceInstaller.h; sourceTree = "<group>"; };
		F8D12A87248888BC00414384 /* UPResourceOperator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceOperator.m; sourceTree = "<group>"; };
		F8D12A89248888BC00414384 /* UPResourceUninstaller.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceUninstaller.m; sourceTree = "<group>"; };
		F8D12A8A248888BC00414384 /* UPResourceUninstaller.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceUninstaller.h; sourceTree = "<group>"; };
		F8D12A8B248888BC00414384 /* UPResourceOperator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceOperator.h; sourceTree = "<group>"; };
		F8D12A8C248888BD00414384 /* UpResourceListenerHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceListenerHolder.h; sourceTree = "<group>"; };
		F8D12A8D248888BD00414384 /* UpResourceCallbackHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceCallbackHolder.h; sourceTree = "<group>"; };
		F8D12A8E248888BD00414384 /* UPResourceRelationDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceRelationDelegate.m; sourceTree = "<group>"; };
		F8D12A8F248888BD00414384 /* UPResourceHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceHelper.m; sourceTree = "<group>"; };
		F8D12A90248888BD00414384 /* UPResourceRequestDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceRequestDelegate.m; sourceTree = "<group>"; };
		F8D12A91248888BD00414384 /* UpResourceTaskMan.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourceTaskMan.m; sourceTree = "<group>"; };
		F8D12A92248888BD00414384 /* UPResourceListCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceListCallback.h; sourceTree = "<group>"; };
		F8D12A93248888BD00414384 /* UPAutoUpgradeResTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPAutoUpgradeResTask.h; sourceTree = "<group>"; };
		F8D12A94248888BD00414384 /* UPResourceFilter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceFilter.h; sourceTree = "<group>"; };
		F8D12A95248888BD00414384 /* UPResourceListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceListener.h; sourceTree = "<group>"; };
		F8D12A96248888BD00414384 /* UpResourceBatchTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceBatchTask.h; sourceTree = "<group>"; };
		F8D12A97248888BD00414384 /* UPResourceInstallDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceInstallDelegate.h; sourceTree = "<group>"; };
		F8D12A98248888BD00414384 /* UPResourceDirectory.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDirectory.h; sourceTree = "<group>"; };
		F8D12A99248888BD00414384 /* UPResourceErrCodes.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceErrCodes.h; sourceTree = "<group>"; };
		F8D12A9A248888BD00414384 /* UPResourceManager.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceManager.m; sourceTree = "<group>"; };
		F8D12A9B248888BD00414384 /* UpResourcePresetTask.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpResourcePresetTask.m; sourceTree = "<group>"; };
		F8D12A9C248888BD00414384 /* UPResourcePresetHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourcePresetHelper.h; sourceTree = "<group>"; };
		F8D12A9D248888BD00414384 /* UpResourceCleanerImpl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceCleanerImpl.h; sourceTree = "<group>"; };
		F8D12A9E248888BD00414384 /* UpResourceSelector.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceSelector.h; sourceTree = "<group>"; };
		F8D12A9F248888BD00414384 /* UPResourceTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTask.h; sourceTree = "<group>"; };
		F8D12AA0248888BD00414384 /* UPResourceCondition.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceCondition.m; sourceTree = "<group>"; };
		F8D12AA1248888BD00414384 /* UpResourceSyncPresetTask.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceSyncPresetTask.h; sourceTree = "<group>"; };
		F8D12AA4248888BD00414384 /* UPResourceValidator.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceValidator.h; sourceTree = "<group>"; };
		F8D12AA5248888BD00414384 /* UPResourceValidator.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceValidator.m; sourceTree = "<group>"; };
		F8D12AA6248888BD00414384 /* UPValidationAlgorithm.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPValidationAlgorithm.h; sourceTree = "<group>"; };
		F8D12AA8248888BD00414384 /* UPResourceRemover.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceRemover.m; sourceTree = "<group>"; };
		F8D12AA9248888BD00414384 /* UPResourceRemover.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceRemover.h; sourceTree = "<group>"; };
		F8D12AAB248888BD00414384 /* UPResourceTransporter.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceTransporter.m; sourceTree = "<group>"; };
		F8D12AAC248888BD00414384 /* UPResourceTransporter.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTransporter.h; sourceTree = "<group>"; };
		F8D12AAD248888BD00414384 /* UPResourceProcessPipeline.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceProcessPipeline.m; sourceTree = "<group>"; };
		F8D12AAE248888BD00414384 /* UPResourceProcessorBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceProcessorBase.m; sourceTree = "<group>"; };
		F8D12AAF248888BD00414384 /* UPResourceProcessor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceProcessor.h; sourceTree = "<group>"; };
		F8D12AB1248888BD00414384 /* UPResourceDownloader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDownloader.m; sourceTree = "<group>"; };
		F8D12AB2248888BD00414384 /* UPResourceDownloader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloader.h; sourceTree = "<group>"; };
		F8D12AB3248888BD00414384 /* UPResourceProcessorBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceProcessorBase.h; sourceTree = "<group>"; };
		F8D12AB5248888BD00414384 /* UPResourceExtractor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceExtractor.m; sourceTree = "<group>"; };
		F8D12AB6248888BD00414384 /* UPResourceExtractor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceExtractor.h; sourceTree = "<group>"; };
		F8D12AB7248888BD00414384 /* UPResourceProcessPipeline.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceProcessPipeline.h; sourceTree = "<group>"; };
		F8D12AB9248888BD00414384 /* UPResourceScanner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceScanner.m; sourceTree = "<group>"; };
		F8D12ABA248888BD00414384 /* UPResourceScanner.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceScanner.h; sourceTree = "<group>"; };
		F8D12ABB248888BD00414384 /* UpResourceHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpResourceHolder.h; sourceTree = "<group>"; };
		F8D12ABE248888BD00414384 /* UPDatabaseDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDatabaseDelegate.h; sourceTree = "<group>"; };
		F8D12AC0248888BD00414384 /* UPRelationDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPRelationDelegate.h; sourceTree = "<group>"; };
		F8D12AC2248888BD00414384 /* UPInstallDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPInstallDelegate.h; sourceTree = "<group>"; };
		F8D12AC4248888BD00414384 /* UPConnectionDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPConnectionDelegate.h; sourceTree = "<group>"; };
		F8D12AC5248888BD00414384 /* UPConnectionListener.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPConnectionListener.h; sourceTree = "<group>"; };
		F8D12AC7248888BD00414384 /* UPResourceTracker.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTracker.h; sourceTree = "<group>"; };
		F8D12AC8248888BD00414384 /* UPResourceTrackerProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTrackerProvider.h; sourceTree = "<group>"; };
		F8D12ACA248888BD00414384 /* UPTimeDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPTimeDelegate.h; sourceTree = "<group>"; };
		F8D12ACB248888BD00414384 /* UPFileDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPFileDelegate.h; sourceTree = "<group>"; };
		F8D12ACD248888BD00414384 /* UPDownloadHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloadHandle.h; sourceTree = "<group>"; };
		F8D12ACE248888BD00414384 /* UPDownloadCallback.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloadCallback.h; sourceTree = "<group>"; };
		F8D12ACF248888BD00414384 /* UPDownloadPolicyProvider.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloadPolicyProvider.h; sourceTree = "<group>"; };
		F8D12AD0248888BD00414384 /* UPDownloadHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDownloadHandle.m; sourceTree = "<group>"; };
		F8D12AD1248888BD00414384 /* UPDownloadDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloadDelegate.h; sourceTree = "<group>"; };
		F8D12AD3248888BD00414384 /* UPRequestDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPRequestDelegate.h; sourceTree = "<group>"; };
		F8D12AD5248888BD00414384 /* UPResourceItem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceItem.m; sourceTree = "<group>"; };
		F8D12AD6248888BD00414384 /* UPResourceQuery.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceQuery.h; sourceTree = "<group>"; };
		F8D12AD7248888BD00414384 /* UpPreloadResourceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpPreloadResourceInfo.m; sourceTree = "<group>"; };
		F8D12AD8248888BD00414384 /* UPResourceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceInfo.h; sourceTree = "<group>"; };
		F8D12AD9248888BD00414384 /* UPResourceReportInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceReportInfo.m; sourceTree = "<group>"; };
		F8D12ADA248888BD00414384 /* UPResourceInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceInfo.m; sourceTree = "<group>"; };
		F8D12ADB248888BD00414384 /* UPResourceQuery.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceQuery.m; sourceTree = "<group>"; };
		F8D12ADC248888BD00414384 /* UpPreloadResourceInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpPreloadResourceInfo.h; sourceTree = "<group>"; };
		F8D12ADD248888BD00414384 /* UPResourceItem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceItem.h; sourceTree = "<group>"; };
		F8D12ADE248888BD00414384 /* UPResourceReportInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceReportInfo.h; sourceTree = "<group>"; };
		F8D12ADF248888BD00414384 /* UPResourceType.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceType.h; sourceTree = "<group>"; };
		F8D12B4A2488895200414384 /* RootViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RootViewController.h; sourceTree = "<group>"; };
		F8D12B4B2488895200414384 /* UPResTableViewCell.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResTableViewCell.m; sourceTree = "<group>"; };
		F8D12B4C2488895200414384 /* UPTableViewController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPTableViewController.h; sourceTree = "<group>"; };
		F8D12B4D2488895200414384 /* UPTableViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPTableViewController.m; sourceTree = "<group>"; };
		F8D12B4F2488895200414384 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		F8D12B502488895200414384 /* main.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = main.storyboard; sourceTree = "<group>"; };
		F8D12B512488895200414384 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		F8D12B522488895200414384 /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		F8D12B542488895200414384 /* RootViewController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RootViewController.m; sourceTree = "<group>"; };
		F8D12B552488895200414384 /* UPResTableViewCell.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResTableViewCell.h; sourceTree = "<group>"; };
		F8D12B562488895200414384 /* RootViewController+TableView.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "RootViewController+TableView.m"; sourceTree = "<group>"; };
		F8D12B572488895200414384 /* RootViewController+TableView.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "RootViewController+TableView.h"; sourceTree = "<group>"; };
		F8D12B612488897600414384 /* ResourceHolder.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = ResourceHolder.h; sourceTree = "<group>"; };
		F8D12B622488897600414384 /* StepUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepUtils.m; sourceTree = "<group>"; };
		F8D12B652488897600414384 /* DownloadSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DownloadSteps.h; sourceTree = "<group>"; };
		F8D12B662488897600414384 /* FileSystemSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = FileSystemSteps.h; sourceTree = "<group>"; };
		F8D12B672488897600414384 /* DataSourceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DataSourceSteps.m; sourceTree = "<group>"; };
		F8D12B682488897600414384 /* NetworkSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = NetworkSteps.h; sourceTree = "<group>"; };
		F8D12B692488897600414384 /* TimeSystemSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TimeSystemSteps.m; sourceTree = "<group>"; };
		F8D12B6A2488897600414384 /* DownloadSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = DownloadSteps.m; sourceTree = "<group>"; };
		F8D12B6C2488897600414384 /* FileSystemSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = FileSystemSteps.m; sourceTree = "<group>"; };
		F8D12B6D2488897600414384 /* DataSourceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = DataSourceSteps.h; sourceTree = "<group>"; };
		F8D12B6E2488897600414384 /* TimeSystemSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TimeSystemSteps.h; sourceTree = "<group>"; };
		F8D12B6F2488897600414384 /* NetworkSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = NetworkSteps.m; sourceTree = "<group>"; };
		F8D12B712488897700414384 /* CucumberRunner.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		F8D12B932488897700414384 /* ResourceHolder.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = ResourceHolder.m; sourceTree = "<group>"; };
		F8D12B952488897700414384 /* TaskResourceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = TaskResourceSteps.m; sourceTree = "<group>"; };
		F8D12B962488897700414384 /* UpdateSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpdateSteps.m; sourceTree = "<group>"; };
		F8D12B972488897700414384 /* RelationSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RelationSteps.h; sourceTree = "<group>"; };
		F8D12B982488897700414384 /* CombineResInfoSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = CombineResInfoSteps.h; sourceTree = "<group>"; };
		F8D12B992488897700414384 /* SearchResourceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SearchResourceSteps.h; sourceTree = "<group>"; };
		F8D12B9A2488897700414384 /* PresetSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = PresetSteps.m; sourceTree = "<group>"; };
		F8D12B9B2488897700414384 /* RequestSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RequestSteps.m; sourceTree = "<group>"; };
		F8D12B9C2488897700414384 /* InitializationSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = InitializationSteps.h; sourceTree = "<group>"; };
		F8D12B9D2488897700414384 /* LocalCacheSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = LocalCacheSteps.h; sourceTree = "<group>"; };
		F8D12B9E2488897700414384 /* TaskResourceSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = TaskResourceSteps.h; sourceTree = "<group>"; };
		F8D12B9F2488897700414384 /* RelationSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = RelationSteps.m; sourceTree = "<group>"; };
		F8D12BA02488897700414384 /* UpdateSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpdateSteps.h; sourceTree = "<group>"; };
		F8D12BA12488897700414384 /* CombineResInfoSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = CombineResInfoSteps.m; sourceTree = "<group>"; };
		F8D12BA22488897700414384 /* SearchResourceSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SearchResourceSteps.m; sourceTree = "<group>"; };
		F8D12BA32488897700414384 /* LocalCacheSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = LocalCacheSteps.m; sourceTree = "<group>"; };
		F8D12BA42488897700414384 /* InitializationSteps.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = InitializationSteps.m; sourceTree = "<group>"; };
		F8D12BA52488897700414384 /* RequestSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = RequestSteps.h; sourceTree = "<group>"; };
		F8D12BA62488897700414384 /* PresetSteps.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = PresetSteps.h; sourceTree = "<group>"; };
		F8D12BA72488897700414384 /* StepUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepUtils.h; sourceTree = "<group>"; };
		F8D12BD424888AA900414384 /* UPOmsResDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsResDataSource.h; sourceTree = "<group>"; };
		F8D12BD524888AA900414384 /* UPOmsDeviceSourceRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsDeviceSourceRequest.m; sourceTree = "<group>"; };
		F8D12BD624888AA900414384 /* UPOmsResRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsResRequest.m; sourceTree = "<group>"; };
		F8D12BD724888AA900414384 /* UPResourceRequestBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceRequestBase.m; sourceTree = "<group>"; };
		F8D12BD824888AA900414384 /* UPOmsConfigFileRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsConfigFileRequest.m; sourceTree = "<group>"; };
		F8D12BDA24888AA900414384 /* UPResourceTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTransformer.h; sourceTree = "<group>"; };
		F8D12BDB24888AA900414384 /* UPOmsDeviceSourceRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsDeviceSourceRequest.h; sourceTree = "<group>"; };
		F8D12BDC24888AA900414384 /* UPOmsResDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsResDataSource.m; sourceTree = "<group>"; };
		F8D12BDD24888AA900414384 /* UPOmsConfigFileRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsConfigFileRequest.h; sourceTree = "<group>"; };
		F8D12BDE24888AA900414384 /* UPOmsResRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsResRequest.h; sourceTree = "<group>"; };
		F8D12BDF24888AA900414384 /* UPResourceRequestBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceRequestBase.h; sourceTree = "<group>"; };
		F8D12BE024888AA900414384 /* UPResourceTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceTransformer.m; sourceTree = "<group>"; };
		F8D12BE324888AA900414384 /* UPResourceConvertResult.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceConvertResult.h; sourceTree = "<group>"; };
		F8D12BE424888AA900414384 /* UPOmsResInfo.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPOmsResInfo.m; sourceTree = "<group>"; };
		F8D12BE524888AA900414384 /* UPResourceConvertResult.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceConvertResult.m; sourceTree = "<group>"; };
		F8D12BE624888AA900414384 /* UPOmsResInfo.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPOmsResInfo.h; sourceTree = "<group>"; };
		F8D12BE824888AA900414384 /* UPResourceDatabaseImpl.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDatabaseImpl.h; sourceTree = "<group>"; };
		F8D12BE924888AA900414384 /* NSString+Paths.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "NSString+Paths.m"; sourceTree = "<group>"; };
		F8D12BEA24888AA900414384 /* UPResourceSQLiteOpenHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceSQLiteOpenHelper.m; sourceTree = "<group>"; };
		F8D12BEB24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "UPResourceSQLiteOpenHelper+SQL.h"; sourceTree = "<group>"; };
		F8D12BEC24888AA900414384 /* NSString+Paths.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "NSString+Paths.h"; sourceTree = "<group>"; };
		F8D12BED24888AA900414384 /* UPResourceDatabaseImpl.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDatabaseImpl.m; sourceTree = "<group>"; };
		F8D12BEE24888AA900414384 /* UPResourceSQLiteOpenHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceSQLiteOpenHelper.h; sourceTree = "<group>"; };
		F8D12BEF24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = "UPResourceSQLiteOpenHelper+SQL.m"; sourceTree = "<group>"; };
		F8D12BF124888AA900414384 /* UPResourceDownloadGifBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloadGifBar.h; sourceTree = "<group>"; };
		F8D12BF224888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceSouthAsiaDownloadProgressBar.m; sourceTree = "<group>"; };
		F8D12BF324888AA900414384 /* UPResourceDownloaderController.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDownloaderController.m; sourceTree = "<group>"; };
		F8D12BF424888AA900414384 /* UPResourceDownloadProgressBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDownloadProgressBar.m; sourceTree = "<group>"; };
		F8D12BF524888AA900414384 /* UPResourceDownloaderHelper.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDownloaderHelper.m; sourceTree = "<group>"; };
		F8D12BF624888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceSouthAsiaDownloadProgressBar.h; sourceTree = "<group>"; };
		F8D12BF724888AA900414384 /* UPResourceDownloadGifBar.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceDownloadGifBar.m; sourceTree = "<group>"; };
		F8D12BF824888AA900414384 /* UPResourceDownloadProgressBar.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloadProgressBar.h; sourceTree = "<group>"; };
		F8D12BF924888AA900414384 /* UPResourceDownloaderController.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloaderController.h; sourceTree = "<group>"; };
		F8D12BFA24888AA900414384 /* UPResourceDownloaderHelper.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceDownloaderHelper.h; sourceTree = "<group>"; };
		F8D12BFC24888AA900414384 /* UPAssetPresetFileLoader.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPAssetPresetFileLoader.h; sourceTree = "<group>"; };
		F8D12BFD24888AA900414384 /* UPAssetPresetFileLoader.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPAssetPresetFileLoader.m; sourceTree = "<group>"; };
		F8D12BFE24888AAA00414384 /* UPDownloaderDelegate.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDownloaderDelegate.m; sourceTree = "<group>"; };
		F8D12BFF24888AAA00414384 /* UPDownloaderHandle.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPDownloaderHandle.m; sourceTree = "<group>"; };
		F8D12C0024888AAA00414384 /* UPResourceInjection.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceInjection.h; sourceTree = "<group>"; };
		F8D12C0224888AAA00414384 /* UPConnectionMonitor.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPConnectionMonitor.m; sourceTree = "<group>"; };
		F8D12C0324888AAA00414384 /* UPDownloaderDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloaderDelegate.h; sourceTree = "<group>"; };
		F8D12C0424888AAA00414384 /* UPResourceConfig.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceConfig.h; sourceTree = "<group>"; };
		F8D12C0524888AAA00414384 /* UPResourceInjection.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceInjection.m; sourceTree = "<group>"; };
		F8D12C0824888AAA00414384 /* SEARequestBase.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEARequestBase.h; sourceTree = "<group>"; };
		F8D12C0924888AAA00414384 /* SEAOmsConfigFileRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEAOmsConfigFileRequest.h; sourceTree = "<group>"; };
		F8D12C0A24888AAA00414384 /* SEAResourceTransformer.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEAResourceTransformer.m; sourceTree = "<group>"; };
		F8D12C0B24888AAA00414384 /* SEAOmsResRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEAOmsResRequest.m; sourceTree = "<group>"; };
		F8D12C0C24888AAA00414384 /* SEAOmsResDataSource.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEAOmsResDataSource.m; sourceTree = "<group>"; };
		F8D12C0D24888AAA00414384 /* SEAOmsDeviceSourceRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEAOmsDeviceSourceRequest.h; sourceTree = "<group>"; };
		F8D12C0E24888AAA00414384 /* SEARequestBase.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEARequestBase.m; sourceTree = "<group>"; };
		F8D12C0F24888AAA00414384 /* SEAOmsConfigFileRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEAOmsConfigFileRequest.m; sourceTree = "<group>"; };
		F8D12C1024888AAA00414384 /* SEAOmsResRequest.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEAOmsResRequest.h; sourceTree = "<group>"; };
		F8D12C1124888AAA00414384 /* SEAResourceTransformer.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEAResourceTransformer.h; sourceTree = "<group>"; };
		F8D12C1224888AAA00414384 /* SEAOmsDeviceSourceRequest.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = SEAOmsDeviceSourceRequest.m; sourceTree = "<group>"; };
		F8D12C1324888AAA00414384 /* SEAOmsResDataSource.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = SEAOmsResDataSource.h; sourceTree = "<group>"; };
		F8D12C1424888AAA00414384 /* UPDownloaderHandle.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPDownloaderHandle.h; sourceTree = "<group>"; };
		F8D12C1524888AAA00414384 /* UPConnectionMonitor.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPConnectionMonitor.h; sourceTree = "<group>"; };
		F8D12C1624888AAA00414384 /* UPResourceConfig.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceConfig.m; sourceTree = "<group>"; };
		F8D12C1824888AAB00414384 /* UPResourceTimeSystem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceTimeSystem.m; sourceTree = "<group>"; };
		F8D12C1924888AAB00414384 /* UPResourceFileSystem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceFileSystem.h; sourceTree = "<group>"; };
		F8D12C1A24888AAB00414384 /* UPResourceTimeSystem.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTimeSystem.h; sourceTree = "<group>"; };
		F8D12C1B24888AAB00414384 /* UPResourceFileSystem.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceFileSystem.m; sourceTree = "<group>"; };
		F8D12C692488961300414384 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = LaunchScreen.storyboard; sourceTree = "<group>"; };
		F8D12C6B2488961300414384 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Main.storyboard; sourceTree = "<group>"; };
		F8D12C6E2488969D00414384 /* PresetResPkg */ = {isa = PBXFileReference; lastKnownFileType = folder; path = PresetResPkg; sourceTree = "<group>"; };
		F8D4365C2547C8FB0089A911 /* UPResourceTrackerIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPResourceTrackerIMP.h; sourceTree = "<group>"; };
		F8D4365D2547C8FB0089A911 /* UPResourceTrackerIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPResourceTrackerIMP.m; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		AD4A1902265354E9006D87CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A1983265358F4006D87CD /* libPods-UPRes.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD4A1992265366CC006D87CD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19EA26536915006D87CD /* libUPRes.a in Frameworks */,
				E7352B1D003E276DC0A7147B /* libPods-UPResDelegateIMP.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A0E2488865D00414384 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19F026536BA5006D87CD /* libUPResDelegateIMP.a in Frameworks */,
				88B5D2FC49B5A38A0E472DA7 /* libPods-ResDebugger.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A2B2488867C00414384 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19EC26536B66006D87CD /* libUPResDelegateIMP.a in Frameworks */,
				83285ED6D1DF2A9BCEFEF69F /* libPods-ResDebuggerTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4093261D26B3A70B0067EA80 /* Patcher */ = {
			isa = PBXGroup;
			children = (
				4093261E26B3A70B0067EA80 /* SpecifyResourceVersion */,
			);
			path = Patcher;
			sourceTree = "<group>";
		};
		4093261E26B3A70B0067EA80 /* SpecifyResourceVersion */ = {
			isa = PBXGroup;
			children = (
				4093262926B3A70B0067EA80 /* Model */,
				4093262826B3A70B0067EA80 /* SpecifyResourceVersionPatcher.h */,
				4093262726B3A70B0067EA80 /* SpecifyResourceVersionPatcher.m */,
				4093261F26B3A70B0067EA80 /* UI */,
			);
			path = SpecifyResourceVersion;
			sourceTree = "<group>";
		};
		4093261F26B3A70B0067EA80 /* UI */ = {
			isa = PBXGroup;
			children = (
				4093262126B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.h */,
				4093262526B3A70B0067EA80 /* SpecifyResourceVersionAlertAnimator.m */,
				4093262026B3A70B0067EA80 /* SpecifyResourceVersionController.h */,
				4093262626B3A70B0067EA80 /* SpecifyResourceVersionController.m */,
				4093262226B3A70B0067EA80 /* View */,
			);
			path = UI;
			sourceTree = "<group>";
		};
		4093262226B3A70B0067EA80 /* View */ = {
			isa = PBXGroup;
			children = (
				4093262326B3A70B0067EA80 /* SpecifyResourceVersionView.m */,
				4093262426B3A70B0067EA80 /* SpecifyResourceVersionView.h */,
			);
			path = View;
			sourceTree = "<group>";
		};
		4093262926B3A70B0067EA80 /* Model */ = {
			isa = PBXGroup;
			children = (
				4093262A26B3A70B0067EA80 /* SpecifyResourceVersionModel.h */,
				4093262B26B3A70B0067EA80 /* SpecifyResourceVersionModel.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		F457BC7C75EBC80686CB5DAF /* Pods */ = {
			isa = PBXGroup;
			children = (
				F507A8F876CEC2FB3C8BD7A3 /* Pods-ResDebugger.debug.xcconfig */,
				3710EC6E3F2BB08028AD5056 /* Pods-ResDebugger.release.xcconfig */,
				0D513A0FFDC2F9EA49DBA8B7 /* Pods-ResDebuggerTests.debug.xcconfig */,
				AE91C74C81E0886153FA3D73 /* Pods-ResDebuggerTests.release.xcconfig */,
				37D82FF20BC73B478087B8E2 /* Pods-UPRes.debug.xcconfig */,
				5790CA85BC007452009D4C38 /* Pods-UPRes.release.xcconfig */,
				CE0EC0391743A94A7FEF95DE /* Pods-UPResDelegateIMP.debug.xcconfig */,
				1F1D4295206D6F942F466E9A /* Pods-UPResDelegateIMP.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		F7062AF32886A00E00B29353 /* AppManager */ = {
			isa = PBXGroup;
			children = (
				F7062AF52886A00E00B29353 /* UPRAppLifeCycleManager.h */,
				F7062AF42886A00E00B29353 /* UPRAppLifeCycleManager.m */,
				F7062AF62886A00E00B29353 /* UPRAppManagerProtocol.h */,
			);
			path = AppManager;
			sourceTree = "<group>";
		};
		F743827429B591E70058489B /* Report */ = {
			isa = PBXGroup;
			children = (
				F7096C4729C16B0F004F9A62 /* UPResourceReporter.h */,
				F70D8B8E29CAE30600FAD23F /* UPResourceReporterSetter.h */,
			);
			path = Report;
			sourceTree = "<group>";
		};
		F743827E29B596630058489B /* loadreport */ = {
			isa = PBXGroup;
			children = (
				F743827F29B596D00058489B /* Model */,
				F743827A29B593490058489B /* UPRLoadReporterImp.h */,
				F743827B29B593490058489B /* UPRLoadReporterImp.m */,
			);
			path = loadreport;
			sourceTree = "<group>";
		};
		F743827F29B596D00058489B /* Model */ = {
			isa = PBXGroup;
			children = (
				F743828029B596FA0058489B /* UPRLoadedReportInfo.h */,
				F743828129B596FA0058489B /* UPRLoadedReportInfo.m */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		F81445A924B6BD3F0011C07D /* doc */ = {
			isa = PBXGroup;
			children = (
				F81445AA24B6BD3F0011C07D /* readme.md */,
				F81445AB24B6BD3F0011C07D /* release.md */,
			);
			path = doc;
			sourceTree = "<group>";
		};
		F8D129F82488864400414384 = {
			isa = PBXGroup;
			children = (
				F81445A924B6BD3F0011C07D /* doc */,
				F8D12A042488864400414384 /* UPRes */,
				F8D12A122488865D00414384 /* ResDebugger */,
				F8D12A2F2488867C00414384 /* ResDebuggerTests */,
				F8D12A48248886BF00414384 /* UPResDelegateIMP */,
				F8D12A032488864400414384 /* Products */,
				F8D12A4F248886D400414384 /* Frameworks */,
				F457BC7C75EBC80686CB5DAF /* Pods */,
			);
			sourceTree = "<group>";
		};
		F8D12A032488864400414384 /* Products */ = {
			isa = PBXGroup;
			children = (
				F8D12A112488865D00414384 /* ResDebugger.app */,
				F8D12A2E2488867C00414384 /* ResDebuggerTests.xctest */,
				AD4A1905265354E9006D87CD /* libUPRes.a */,
				AD4A1995265366CC006D87CD /* libUPResDelegateIMP.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		F8D12A042488864400414384 /* UPRes */ = {
			isa = PBXGroup;
			children = (
				F8D12A5F248888BC00414384 /* Callback */,
				F8D12A61248888BC00414384 /* common */,
				F8D12ABC248888BD00414384 /* Delegate */,
				F8D12AD4248888BD00414384 /* Domain */,
				F8D12A5D248888BC00414384 /* Preset */,
				F8D12A64248888BC00414384 /* resource */,
			);
			path = UPRes;
			sourceTree = "<group>";
		};
		F8D12A122488865D00414384 /* ResDebugger */ = {
			isa = PBXGroup;
			children = (
				F8D4365C2547C8FB0089A911 /* UPResourceTrackerIMP.h */,
				F8D4365D2547C8FB0089A911 /* UPResourceTrackerIMP.m */,
				F8D12C672488961300414384 /* Base.lproj */,
				F8D12B4A2488895200414384 /* RootViewController.h */,
				F8D12B542488895200414384 /* RootViewController.m */,
				F8D12B572488895200414384 /* RootViewController+TableView.h */,
				F8D12B562488895200414384 /* RootViewController+TableView.m */,
				2CC23B2028E42188004DB57E /* UPRequestNormalViewController.h */,
				2CC23B2128E42188004DB57E /* UPRequestNormalViewController.m */,
				F8D12B4E2488895200414384 /* Supporting Files */,
				F8D12B552488895200414384 /* UPResTableViewCell.h */,
				F8D12B4B2488895200414384 /* UPResTableViewCell.m */,
				F8D12B4C2488895200414384 /* UPTableViewController.h */,
				F8D12B4D2488895200414384 /* UPTableViewController.m */,
				F8D12A132488865D00414384 /* AppDelegate.h */,
				F8D12A142488865D00414384 /* AppDelegate.m */,
				F8D12A192488865D00414384 /* ViewController.h */,
				F8D12A1A2488865D00414384 /* ViewController.m */,
				F8D12A1C2488865D00414384 /* Main.storyboard */,
				F8D12A212488865F00414384 /* LaunchScreen.storyboard */,
			);
			path = ResDebugger;
			sourceTree = "<group>";
		};
		F8D12A2F2488867C00414384 /* ResDebuggerTests */ = {
			isa = PBXGroup;
			children = (
				22F421942669C24C005C2F43 /* features */,
				F8D12B712488897700414384 /* CucumberRunner.m */,
				F8D12B632488897600414384 /* Dependence */,
				F8D12B612488897600414384 /* ResourceHolder.h */,
				F8D12B932488897700414384 /* ResourceHolder.m */,
				F8D12B942488897700414384 /* ResourceSteps */,
				F8D12BA72488897700414384 /* StepUtils.h */,
				F8D12B622488897600414384 /* StepUtils.m */,
				DF5F427829272AD4008BA9D7 /* CommonResCallbackImp.h */,
				DF5F427929272AD4008BA9D7 /* CommonResCallbackImp.m */,
				22C485E8293DF928007032AC /* CacheCleanCallback.h */,
				22C485E7293DF928007032AC /* CacheCleanCallback.m */,
				22C485EB293DFA01007032AC /* ResourceSelectorDelegate.h */,
				22C485EA293DFA01007032AC /* ResourceSelectorDelegate.m */,
			);
			path = ResDebuggerTests;
			sourceTree = "<group>";
		};
		F8D12A48248886BF00414384 /* UPResDelegateIMP */ = {
			isa = PBXGroup;
			children = (
				F7062AF32886A00E00B29353 /* AppManager */,
				4093261D26B3A70B0067EA80 /* Patcher */,
				F8D12BE724888AA900414384 /* DatabaseIMP */,
				F8D12BE224888AA900414384 /* DatasourceCommon */,
				F8D12BD324888AA900414384 /* DatasourceIMP */,
				F8D12BF024888AA900414384 /* DowloaderUI */,
				F8D12BFB24888AA900414384 /* PersetFileLoaderIMP */,
				F8D12C0724888AAA00414384 /* SEADataSourceIMP */,
				F8D12C1724888AAB00414384 /* SystemIMP */,
				F8D12C1524888AAA00414384 /* UPConnectionMonitor.h */,
				F8D12C0224888AAA00414384 /* UPConnectionMonitor.m */,
				F8D12C0324888AAA00414384 /* UPDownloaderDelegate.h */,
				F8D12BFE24888AAA00414384 /* UPDownloaderDelegate.m */,
				F8D12C1424888AAA00414384 /* UPDownloaderHandle.h */,
				F8D12BFF24888AAA00414384 /* UPDownloaderHandle.m */,
				F8D12C0424888AAA00414384 /* UPResourceConfig.h */,
				F8D12C1624888AAA00414384 /* UPResourceConfig.m */,
				F87624FD24C18F0E003D508F /* UPResourceRes.bundle */,
				F8D12C0024888AAA00414384 /* UPResourceInjection.h */,
				F8D12C0524888AAA00414384 /* UPResourceInjection.m */,
				F8D12A49248886BF00414384 /* UPResDelegateIMP.h */,
			);
			path = UPResDelegateIMP;
			sourceTree = "<group>";
		};
		F8D12A4F248886D400414384 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				8A9E000D106A158CC3CB32E9 /* libPods-ResDebuggerTests.a */,
				2861ED74B38CCC38BBAD1CB9 /* libPods-UPRes.a */,
				4EB782260D69A2400677A140 /* libPods-UPResDelegateIMP.a */,
				997ADF4FD9600234F13C92FE /* libPods-ResDebugger.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		F8D12A5D248888BC00414384 /* Preset */ = {
			isa = PBXGroup;
			children = (
				F8D12A5E248888BC00414384 /* UPPresetFileLoader.h */,
			);
			path = Preset;
			sourceTree = "<group>";
		};
		F8D12A5F248888BC00414384 /* Callback */ = {
			isa = PBXGroup;
			children = (
				F8D12A60248888BC00414384 /* UPResCacheCleanCallback.h */,
			);
			path = Callback;
			sourceTree = "<group>";
		};
		F8D12A61248888BC00414384 /* common */ = {
			isa = PBXGroup;
			children = (
				F8D12A62248888BC00414384 /* UPResCommonFunctions.h */,
				F8D12A63248888BC00414384 /* UPResCommonFunctions.m */,
			);
			path = common;
			sourceTree = "<group>";
		};
		F8D12A64248888BC00414384 /* resource */ = {
			isa = PBXGroup;
			children = (
				F743827E29B596630058489B /* loadreport */,
				F8D12A83248888BC00414384 /* operate */,
				F8D12AA2248888BD00414384 /* process */,
				F8D12A75248888BC00414384 /* source */,
				F8D12A93248888BD00414384 /* UPAutoUpgradeResTask.h */,
				F8D12A7B248888BC00414384 /* UPAutoUpgradeResTask.m */,
				2220FBDA263BF895008E1F29 /* UpAutoUpgradeLocalResTask.h */,
				2220FBDB263BF895008E1F29 /* UpAutoUpgradeLocalResTask.m */,
				F8D12A6E248888BC00414384 /* UPResEnvironment.h */,
				F8D12A96248888BD00414384 /* UpResourceBatchTask.h */,
				F8D12A73248888BC00414384 /* UpResourceBatchTask.m */,
				F8D12A66248888BC00414384 /* UPResourceCallback.h */,
				F8D12A8D248888BD00414384 /* UpResourceCallbackHolder.h */,
				F8D12A6D248888BC00414384 /* UpResourceCallbackHolder.m */,
				F8D12A67248888BC00414384 /* UpResourceCleaner.h */,
				F8D12A9D248888BD00414384 /* UpResourceCleanerImpl.h */,
				F8D12A80248888BC00414384 /* UpResourceCleanerImpl.m */,
				F8D12A7E248888BC00414384 /* UPResourceCondition.h */,
				F8D12AA0248888BD00414384 /* UPResourceCondition.m */,
				F8D12A98248888BD00414384 /* UPResourceDirectory.h */,
				F8D12A72248888BC00414384 /* UPResourceDirectory.m */,
				F8D12A99248888BD00414384 /* UPResourceErrCodes.h */,
				F8D12A71248888BC00414384 /* UPResourceErrCodes.m */,
				F8D12A94248888BD00414384 /* UPResourceFilter.h */,
				F8D12A6B248888BC00414384 /* UPResourceHelper.h */,
				F8D12A8F248888BD00414384 /* UPResourceHelper.m */,
				F8D12ABB248888BD00414384 /* UpResourceHolder.h */,
				F8D12A7C248888BC00414384 /* UpResourceHolder.m */,
				F8D12A97248888BD00414384 /* UPResourceInstallDelegate.h */,
				F8D12A74248888BC00414384 /* UPResourceInstallDelegate.m */,
				F8D12A92248888BD00414384 /* UPResourceListCallback.h */,
				F8D12A95248888BD00414384 /* UPResourceListener.h */,
				F8D12A8C248888BD00414384 /* UpResourceListenerHolder.h */,
				F8D12A65248888BC00414384 /* UpResourceListenerHolder.m */,
				F8D12A70248888BC00414384 /* UPResourceManager.h */,
				F8D12A9A248888BD00414384 /* UPResourceManager.m */,
				F8D12A82248888BC00414384 /* UPResourcePreLoadTask.h */,
				F8D12A68248888BC00414384 /* UPResourcePreLoadTask.m */,
				F8D12A9C248888BD00414384 /* UPResourcePresetHelper.h */,
				F8D12A81248888BC00414384 /* UPResourcePresetHelper.m */,
				F8D12A6F248888BC00414384 /* UpResourcePresetTask.h */,
				F8D12A9B248888BD00414384 /* UpResourcePresetTask.m */,
				F8D12A6C248888BC00414384 /* UPResourceRelationDelegate.h */,
				F8D12A8E248888BD00414384 /* UPResourceRelationDelegate.m */,
				F8D12A69248888BC00414384 /* UPResourceRequestDelegate.h */,
				F8D12A90248888BD00414384 /* UPResourceRequestDelegate.m */,
				F8D12A9E248888BD00414384 /* UpResourceSelector.h */,
				F8D12AA1248888BD00414384 /* UpResourceSyncPresetTask.h */,
				F8D12A7D248888BC00414384 /* UpResourceSyncPresetTask.m */,
				F8D12A9F248888BD00414384 /* UPResourceTask.h */,
				F8D12A7F248888BC00414384 /* UPResourceTask.m */,
				F8D12A6A248888BC00414384 /* UpResourceTaskMan.h */,
				F8D12A91248888BD00414384 /* UpResourceTaskMan.m */,
				F87B8DAA24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.h */,
				F87B8DAB24DCE2010027FCCF /* UPResourceCleanUnusefulResTask.m */,
			);
			path = resource;
			sourceTree = "<group>";
		};
		F8D12A75248888BC00414384 /* source */ = {
			isa = PBXGroup;
			children = (
				F8D12A7A248888BC00414384 /* UPResourceDataSource.h */,
				F8D12A78248888BC00414384 /* UPResourceDataSourceBase.h */,
				F8D12A76248888BC00414384 /* UPResourceDataSourceBase.m */,
				F8D12A77248888BC00414384 /* UPResourceRepository.h */,
				F8D12A79248888BC00414384 /* UPResourceRepository.m */,
			);
			path = source;
			sourceTree = "<group>";
		};
		F8D12A83248888BC00414384 /* operate */ = {
			isa = PBXGroup;
			children = (
				F8D12A84248888BC00414384 /* install */,
				F8D12A88248888BC00414384 /* uninstall */,
				F8D12A8B248888BC00414384 /* UPResourceOperator.h */,
				F8D12A87248888BC00414384 /* UPResourceOperator.m */,
			);
			path = operate;
			sourceTree = "<group>";
		};
		F8D12A84248888BC00414384 /* install */ = {
			isa = PBXGroup;
			children = (
				F8D12A86248888BC00414384 /* UPResourceInstaller.h */,
				F8D12A85248888BC00414384 /* UPResourceInstaller.m */,
			);
			path = install;
			sourceTree = "<group>";
		};
		F8D12A88248888BC00414384 /* uninstall */ = {
			isa = PBXGroup;
			children = (
				F8D12A8A248888BC00414384 /* UPResourceUninstaller.h */,
				F8D12A89248888BC00414384 /* UPResourceUninstaller.m */,
			);
			path = uninstall;
			sourceTree = "<group>";
		};
		F8D12AA2248888BD00414384 /* process */ = {
			isa = PBXGroup;
			children = (
				F8D12AB0248888BD00414384 /* download */,
				F8D12AB4248888BD00414384 /* extract */,
				F8D12AA7248888BD00414384 /* remove */,
				F8D12AB8248888BD00414384 /* scanner */,
				F8D12AAA248888BD00414384 /* transport */,
				F8D12AAF248888BD00414384 /* UPResourceProcessor.h */,
				F8D12AB3248888BD00414384 /* UPResourceProcessorBase.h */,
				F8D12AAE248888BD00414384 /* UPResourceProcessorBase.m */,
				F8D12AB7248888BD00414384 /* UPResourceProcessPipeline.h */,
				F8D12AAD248888BD00414384 /* UPResourceProcessPipeline.m */,
				F8D12AA3248888BD00414384 /* validate */,
			);
			path = process;
			sourceTree = "<group>";
		};
		F8D12AA3248888BD00414384 /* validate */ = {
			isa = PBXGroup;
			children = (
				F8D12AA4248888BD00414384 /* UPResourceValidator.h */,
				F8D12AA5248888BD00414384 /* UPResourceValidator.m */,
				F8D12AA6248888BD00414384 /* UPValidationAlgorithm.h */,
			);
			path = validate;
			sourceTree = "<group>";
		};
		F8D12AA7248888BD00414384 /* remove */ = {
			isa = PBXGroup;
			children = (
				F8D12AA9248888BD00414384 /* UPResourceRemover.h */,
				F8D12AA8248888BD00414384 /* UPResourceRemover.m */,
			);
			path = remove;
			sourceTree = "<group>";
		};
		F8D12AAA248888BD00414384 /* transport */ = {
			isa = PBXGroup;
			children = (
				F8D12AAC248888BD00414384 /* UPResourceTransporter.h */,
				F8D12AAB248888BD00414384 /* UPResourceTransporter.m */,
			);
			path = transport;
			sourceTree = "<group>";
		};
		F8D12AB0248888BD00414384 /* download */ = {
			isa = PBXGroup;
			children = (
				F8D12AB2248888BD00414384 /* UPResourceDownloader.h */,
				F8D12AB1248888BD00414384 /* UPResourceDownloader.m */,
				5D644F0F266DBC1500F64F59 /* UpResourcePresetDownloader.h */,
				5D644F10266DBC1500F64F59 /* UpResourcePresetDownloader.m */,
			);
			path = download;
			sourceTree = "<group>";
		};
		F8D12AB4248888BD00414384 /* extract */ = {
			isa = PBXGroup;
			children = (
				F8D12AB6248888BD00414384 /* UPResourceExtractor.h */,
				F8D12AB5248888BD00414384 /* UPResourceExtractor.m */,
			);
			path = extract;
			sourceTree = "<group>";
		};
		F8D12AB8248888BD00414384 /* scanner */ = {
			isa = PBXGroup;
			children = (
				F8D12ABA248888BD00414384 /* UPResourceScanner.h */,
				F8D12AB9248888BD00414384 /* UPResourceScanner.m */,
			);
			path = scanner;
			sourceTree = "<group>";
		};
		F8D12ABC248888BD00414384 /* Delegate */ = {
			isa = PBXGroup;
			children = (
				F743827429B591E70058489B /* Report */,
				F8D12ABD248888BD00414384 /* Database */,
				F8D12ABF248888BD00414384 /* Relation */,
				F8D12AC1248888BD00414384 /* Install */,
				F8D12AC3248888BD00414384 /* Connection */,
				F8D12AC6248888BD00414384 /* Tracker */,
				F8D12AC9248888BD00414384 /* System */,
				F8D12ACC248888BD00414384 /* Download */,
				F8D12AD2248888BD00414384 /* Request */,
			);
			path = Delegate;
			sourceTree = "<group>";
		};
		F8D12ABD248888BD00414384 /* Database */ = {
			isa = PBXGroup;
			children = (
				F8D12ABE248888BD00414384 /* UPDatabaseDelegate.h */,
			);
			path = Database;
			sourceTree = "<group>";
		};
		F8D12ABF248888BD00414384 /* Relation */ = {
			isa = PBXGroup;
			children = (
				F8D12AC0248888BD00414384 /* UPRelationDelegate.h */,
			);
			path = Relation;
			sourceTree = "<group>";
		};
		F8D12AC1248888BD00414384 /* Install */ = {
			isa = PBXGroup;
			children = (
				F8D12AC2248888BD00414384 /* UPInstallDelegate.h */,
			);
			path = Install;
			sourceTree = "<group>";
		};
		F8D12AC3248888BD00414384 /* Connection */ = {
			isa = PBXGroup;
			children = (
				F8D12AC4248888BD00414384 /* UPConnectionDelegate.h */,
				F8D12AC5248888BD00414384 /* UPConnectionListener.h */,
			);
			path = Connection;
			sourceTree = "<group>";
		};
		F8D12AC6248888BD00414384 /* Tracker */ = {
			isa = PBXGroup;
			children = (
				F8D12AC7248888BD00414384 /* UPResourceTracker.h */,
				F8D12AC8248888BD00414384 /* UPResourceTrackerProvider.h */,
			);
			path = Tracker;
			sourceTree = "<group>";
		};
		F8D12AC9248888BD00414384 /* System */ = {
			isa = PBXGroup;
			children = (
				F8D12ACA248888BD00414384 /* UPTimeDelegate.h */,
				F8D12ACB248888BD00414384 /* UPFileDelegate.h */,
			);
			path = System;
			sourceTree = "<group>";
		};
		F8D12ACC248888BD00414384 /* Download */ = {
			isa = PBXGroup;
			children = (
				F8D12ACD248888BD00414384 /* UPDownloadHandle.h */,
				F8D12ACE248888BD00414384 /* UPDownloadCallback.h */,
				F8D12ACF248888BD00414384 /* UPDownloadPolicyProvider.h */,
				F8D12AD0248888BD00414384 /* UPDownloadHandle.m */,
				F8D12AD1248888BD00414384 /* UPDownloadDelegate.h */,
			);
			path = Download;
			sourceTree = "<group>";
		};
		F8D12AD2248888BD00414384 /* Request */ = {
			isa = PBXGroup;
			children = (
				F8D12AD3248888BD00414384 /* UPRequestDelegate.h */,
			);
			path = Request;
			sourceTree = "<group>";
		};
		F8D12AD4248888BD00414384 /* Domain */ = {
			isa = PBXGroup;
			children = (
				F8D12ADC248888BD00414384 /* UpPreloadResourceInfo.h */,
				F8D12AD7248888BD00414384 /* UpPreloadResourceInfo.m */,
				F8D12AD8248888BD00414384 /* UPResourceInfo.h */,
				F8D12ADA248888BD00414384 /* UPResourceInfo.m */,
				F8D12ADD248888BD00414384 /* UPResourceItem.h */,
				F8D12AD5248888BD00414384 /* UPResourceItem.m */,
				F8D12AD6248888BD00414384 /* UPResourceQuery.h */,
				F8D12ADB248888BD00414384 /* UPResourceQuery.m */,
				F8D12ADE248888BD00414384 /* UPResourceReportInfo.h */,
				F8D12AD9248888BD00414384 /* UPResourceReportInfo.m */,
				F8D12ADF248888BD00414384 /* UPResourceType.h */,
				F81445A524B6A56D0011C07D /* UPResourceResult.h */,
				F81445A624B6A56D0011C07D /* UPResourceResult.m */,
			);
			path = Domain;
			sourceTree = "<group>";
		};
		F8D12B4E2488895200414384 /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				F8D12C6E2488969D00414384 /* PresetResPkg */,
				F8D12B4F2488895200414384 /* Assets.xcassets */,
				F8D12B502488895200414384 /* main.storyboard */,
				F8D12B512488895200414384 /* <EMAIL> */,
				F8D12B522488895200414384 /* main.m */,
			);
			path = "Supporting Files";
			sourceTree = "<group>";
		};
		F8D12B632488897600414384 /* Dependence */ = {
			isa = PBXGroup;
			children = (
				22A252E2292DFBD600A46C48 /* DataBaseSteps.h */,
				22A252E1292DFBD600A46C48 /* DataBaseSteps.m */,
				F8D12B6D2488897600414384 /* DataSourceSteps.h */,
				F8D12B672488897600414384 /* DataSourceSteps.m */,
				F8D12B652488897600414384 /* DownloadSteps.h */,
				F8D12B6A2488897600414384 /* DownloadSteps.m */,
				F8D12B662488897600414384 /* FileSystemSteps.h */,
				F8D12B6C2488897600414384 /* FileSystemSteps.m */,
				F8D12B682488897600414384 /* NetworkSteps.h */,
				F8D12B6F2488897600414384 /* NetworkSteps.m */,
				F8D12B6E2488897600414384 /* TimeSystemSteps.h */,
				F8D12B692488897600414384 /* TimeSystemSteps.m */,
			);
			path = Dependence;
			sourceTree = "<group>";
		};
		F8D12B942488897700414384 /* ResourceSteps */ = {
			isa = PBXGroup;
			children = (
				F8D12B982488897700414384 /* CombineResInfoSteps.h */,
				F8D12BA12488897700414384 /* CombineResInfoSteps.m */,
				F8D12B9C2488897700414384 /* InitializationSteps.h */,
				F8D12BA42488897700414384 /* InitializationSteps.m */,
				F8D12B9D2488897700414384 /* LocalCacheSteps.h */,
				F8D12BA32488897700414384 /* LocalCacheSteps.m */,
				F8D12BA62488897700414384 /* PresetSteps.h */,
				F8D12B9A2488897700414384 /* PresetSteps.m */,
				F8D12B972488897700414384 /* RelationSteps.h */,
				F8D12B9F2488897700414384 /* RelationSteps.m */,
				F8D12BA52488897700414384 /* RequestSteps.h */,
				F8D12B9B2488897700414384 /* RequestSteps.m */,
				F8D12B992488897700414384 /* SearchResourceSteps.h */,
				F8D12BA22488897700414384 /* SearchResourceSteps.m */,
				F8D12B9E2488897700414384 /* TaskResourceSteps.h */,
				F8D12B952488897700414384 /* TaskResourceSteps.m */,
				F8D12BA02488897700414384 /* UpdateSteps.h */,
				F8D12B962488897700414384 /* UpdateSteps.m */,
				22FDE05826560A00003001F1 /* AutoUpdateLocalResSteps.h */,
				22FDE05926560A00003001F1 /* AutoUpdateLocalResSteps.m */,
				F70D8B7D29C441D900FAD23F /* ReportResLoadedSteps.h */,
				F70D8B7E29C441D900FAD23F /* ReportResLoadedSteps.m */,
			);
			path = ResourceSteps;
			sourceTree = "<group>";
		};
		F8D12BD324888AA900414384 /* DatasourceIMP */ = {
			isa = PBXGroup;
			children = (
				225FE6F529CA8DB900A8CEC1 /* UPOmsNormalRequest.h */,
				225FE6F329CA8DB900A8CEC1 /* UPOmsNormalRequest.m */,
				F8D12BDD24888AA900414384 /* UPOmsConfigFileRequest.h */,
				F8D12BD824888AA900414384 /* UPOmsConfigFileRequest.m */,
				F89E5ADC2E25210600E41151 /* UPOmsLuaRequest.h */,
				F89E5ADD2E25210600E41151 /* UPOmsLuaRequest.m */,
				F8D12BDB24888AA900414384 /* UPOmsDeviceSourceRequest.h */,
				F8D12BD524888AA900414384 /* UPOmsDeviceSourceRequest.m */,
				2CC23B1C28E17FC6004DB57E /* UPOmsResListRequest.h */,
				2CC23B1D28E17FC6004DB57E /* UPOmsResListRequest.m */,
				2CAF316329406E9A009DB26B /* UPOmsResStatusRequest.h */,
				2CAF316429406E9A009DB26B /* UPOmsResStatusRequest.m */,
				F8D12BD424888AA900414384 /* UPOmsResDataSource.h */,
				F8D12BDC24888AA900414384 /* UPOmsResDataSource.m */,
				F8D12BDE24888AA900414384 /* UPOmsResRequest.h */,
				F8D12BD624888AA900414384 /* UPOmsResRequest.m */,
				F8B238AA24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.h */,
				F8B238AB24B2FB9B006BF8CF /* UPOmsDeviceCustomInfoRequest.m */,
				F8D12BDF24888AA900414384 /* UPResourceRequestBase.h */,
				F8D12BD724888AA900414384 /* UPResourceRequestBase.m */,
				F8D12BDA24888AA900414384 /* UPResourceTransformer.h */,
				F8D12BE024888AA900414384 /* UPResourceTransformer.m */,
				2C0489A3298A8571001D4C3F /* UPResourceStatusTransformer.h */,
				2C0489A4298A8571001D4C3F /* UPResourceStatusTransformer.m */,
				F8B238AE24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.h */,
				F8B238AF24B30189006BF8CF /* UPResourceDeviceCustomInfoTransformer.m */,
				22E3E5AA29481F0C00F10806 /* UPOmsAppFuncModelReques.h */,
				22E3E5AB29481F0C00F10806 /* UPOmsAppFuncModelReques.m */,
				F70D8B8229CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.h */,
				F70D8B8329CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.m */,
				F724F8AF29CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.h */,
				F724F8B029CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.m */,
			);
			path = DatasourceIMP;
			sourceTree = "<group>";
		};
		F8D12BE224888AA900414384 /* DatasourceCommon */ = {
			isa = PBXGroup;
			children = (
				F8D12BE624888AA900414384 /* UPOmsResInfo.h */,
				F8D12BE424888AA900414384 /* UPOmsResInfo.m */,
				F8D12BE324888AA900414384 /* UPResourceConvertResult.h */,
				F8D12BE524888AA900414384 /* UPResourceConvertResult.m */,
				2C0489A7298A96A8001D4C3F /* UPResourceStatusConvertResult.h */,
				2C0489A8298A96A8001D4C3F /* UPResourceStatusConvertResult.m */,
			);
			path = DatasourceCommon;
			sourceTree = "<group>";
		};
		F8D12BE724888AA900414384 /* DatabaseIMP */ = {
			isa = PBXGroup;
			children = (
				F8D12BEC24888AA900414384 /* NSString+Paths.h */,
				F8D12BE924888AA900414384 /* NSString+Paths.m */,
				F8D12BE824888AA900414384 /* UPResourceDatabaseImpl.h */,
				F8D12BED24888AA900414384 /* UPResourceDatabaseImpl.m */,
				F8D12BEE24888AA900414384 /* UPResourceSQLiteOpenHelper.h */,
				F8D12BEA24888AA900414384 /* UPResourceSQLiteOpenHelper.m */,
				F8D12BEB24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.h */,
				F8D12BEF24888AA900414384 /* UPResourceSQLiteOpenHelper+SQL.m */,
			);
			path = DatabaseIMP;
			sourceTree = "<group>";
		};
		F8D12BF024888AA900414384 /* DowloaderUI */ = {
			isa = PBXGroup;
			children = (
				F73C2E6927B642DC00CA5FDA /* UPResourceDownloadGifType.h */,
				F8D12BF924888AA900414384 /* UPResourceDownloaderController.h */,
				F8D12BF324888AA900414384 /* UPResourceDownloaderController.m */,
				F8D12BFA24888AA900414384 /* UPResourceDownloaderHelper.h */,
				F8D12BF524888AA900414384 /* UPResourceDownloaderHelper.m */,
				F8D12BF124888AA900414384 /* UPResourceDownloadGifBar.h */,
				F8D12BF724888AA900414384 /* UPResourceDownloadGifBar.m */,
				952FEA762C85C080006061B2 /* UPResourceDownloadGifBar.xib */,
				DF6D055C290A66B10084A0CF /* UPResourceSEADownloadGifBar.h */,
				DF6D055D290A66B10084A0CF /* UPResourceSEADownloadGifBar.m */,
				F8D12BF824888AA900414384 /* UPResourceDownloadProgressBar.h */,
				F8D12BF424888AA900414384 /* UPResourceDownloadProgressBar.m */,
				F8D12BF624888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.h */,
				F8D12BF224888AA900414384 /* UPResourceSouthAsiaDownloadProgressBar.m */,
				DF6D0560290A6DF10084A0CF /* UPResourceUIMacros.h */,
				DF6D0562290A726B0084A0CF /* UPResourceUITool.h */,
				DF6D0563290A726B0084A0CF /* UPResourceUITool.m */,
			);
			path = DowloaderUI;
			sourceTree = "<group>";
		};
		F8D12BFB24888AA900414384 /* PersetFileLoaderIMP */ = {
			isa = PBXGroup;
			children = (
				F8D12BFC24888AA900414384 /* UPAssetPresetFileLoader.h */,
				F8D12BFD24888AA900414384 /* UPAssetPresetFileLoader.m */,
			);
			path = PersetFileLoaderIMP;
			sourceTree = "<group>";
		};
		F8D12C0724888AAA00414384 /* SEADataSourceIMP */ = {
			isa = PBXGroup;
			children = (
				F8D12C0924888AAA00414384 /* SEAOmsConfigFileRequest.h */,
				F8D12C0F24888AAA00414384 /* SEAOmsConfigFileRequest.m */,
				F8D12C0D24888AAA00414384 /* SEAOmsDeviceSourceRequest.h */,
				F8D12C1224888AAA00414384 /* SEAOmsDeviceSourceRequest.m */,
				F8D12C1324888AAA00414384 /* SEAOmsResDataSource.h */,
				F8D12C0C24888AAA00414384 /* SEAOmsResDataSource.m */,
				F8D12C1024888AAA00414384 /* SEAOmsResRequest.h */,
				F8D12C0B24888AAA00414384 /* SEAOmsResRequest.m */,
				F8D12C0824888AAA00414384 /* SEARequestBase.h */,
				F8D12C0E24888AAA00414384 /* SEARequestBase.m */,
				F8D12C1124888AAA00414384 /* SEAResourceTransformer.h */,
				F8D12C0A24888AAA00414384 /* SEAResourceTransformer.m */,
			);
			path = SEADataSourceIMP;
			sourceTree = "<group>";
		};
		F8D12C1724888AAB00414384 /* SystemIMP */ = {
			isa = PBXGroup;
			children = (
				F8D12C1924888AAB00414384 /* UPResourceFileSystem.h */,
				F8D12C1B24888AAB00414384 /* UPResourceFileSystem.m */,
				F8D12C1A24888AAB00414384 /* UPResourceTimeSystem.h */,
				F8D12C1824888AAB00414384 /* UPResourceTimeSystem.m */,
			);
			path = SystemIMP;
			sourceTree = "<group>";
		};
		F8D12C672488961300414384 /* Base.lproj */ = {
			isa = PBXGroup;
			children = (
				F8D12C682488961300414384 /* LaunchScreen.storyboard */,
				F8D12C6A2488961300414384 /* Main.storyboard */,
			);
			path = Base.lproj;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		AD4A190E26535538006D87CD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A190F265355DF006D87CD /* UPResCacheCleanCallback.h in Headers */,
				AD4A1910265355DF006D87CD /* UPResCommonFunctions.h in Headers */,
				AD4A1911265355DF006D87CD /* UPDatabaseDelegate.h in Headers */,
				AD4A1912265355DF006D87CD /* UPRelationDelegate.h in Headers */,
				AD4A1913265355DF006D87CD /* UPInstallDelegate.h in Headers */,
				F70D8B9029CAE30600FAD23F /* UPResourceReporterSetter.h in Headers */,
				AD4A1914265355DF006D87CD /* UPConnectionDelegate.h in Headers */,
				AD4A1915265355DF006D87CD /* UPConnectionListener.h in Headers */,
				AD4A1916265355DF006D87CD /* UPResourceTracker.h in Headers */,
				AD4A1917265355DF006D87CD /* UPResourceTrackerProvider.h in Headers */,
				AD4A1918265355DF006D87CD /* UPTimeDelegate.h in Headers */,
				AD4A1919265355DF006D87CD /* UPFileDelegate.h in Headers */,
				AD4A191A265355DF006D87CD /* UPDownloadHandle.h in Headers */,
				AD4A191B265355DF006D87CD /* UPDownloadCallback.h in Headers */,
				AD4A191C265355DF006D87CD /* UPDownloadPolicyProvider.h in Headers */,
				AD4A191D265355DF006D87CD /* UPDownloadDelegate.h in Headers */,
				AD4A191E265355DF006D87CD /* UPRequestDelegate.h in Headers */,
				AD4A191F265355DF006D87CD /* UpPreloadResourceInfo.h in Headers */,
				AD4A1920265355DF006D87CD /* UPResourceInfo.h in Headers */,
				AD4A1921265355DF006D87CD /* UPResourceItem.h in Headers */,
				AD4A1922265355DF006D87CD /* UPResourceQuery.h in Headers */,
				AD4A1923265355DF006D87CD /* UPResourceReportInfo.h in Headers */,
				AD4A1924265355DF006D87CD /* UPResourceType.h in Headers */,
				AD4A1925265355DF006D87CD /* UPResourceResult.h in Headers */,
				AD4A1926265355DF006D87CD /* UPPresetFileLoader.h in Headers */,
				AD4A1927265355DF006D87CD /* UPResourceInstaller.h in Headers */,
				AD4A1928265355DF006D87CD /* UPResourceUninstaller.h in Headers */,
				AD4A1929265355DF006D87CD /* UPResourceOperator.h in Headers */,
				AD4A192A265355DF006D87CD /* UPResourceDownloader.h in Headers */,
				AD4A192B265355DF006D87CD /* UPResourceExtractor.h in Headers */,
				AD4A192C265355DF006D87CD /* UPResourceRemover.h in Headers */,
				AD4A192D265355DF006D87CD /* UPResourceScanner.h in Headers */,
				AD4A192E265355DF006D87CD /* UPResourceTransporter.h in Headers */,
				AD4A192F265355DF006D87CD /* UPResourceProcessor.h in Headers */,
				AD4A1930265355DF006D87CD /* UPResourceProcessorBase.h in Headers */,
				AD4A1931265355DF006D87CD /* UPResourceProcessPipeline.h in Headers */,
				AD4A1932265355DF006D87CD /* UPResourceValidator.h in Headers */,
				AD4A1933265355DF006D87CD /* UPValidationAlgorithm.h in Headers */,
				AD4A1934265355DF006D87CD /* UPResourceDataSource.h in Headers */,
				AD4A1935265355DF006D87CD /* UPResourceDataSourceBase.h in Headers */,
				AD4A1936265355DF006D87CD /* UPResourceRepository.h in Headers */,
				F743828229B596FA0058489B /* UPRLoadedReportInfo.h in Headers */,
				AD4A1937265355DF006D87CD /* UPAutoUpgradeResTask.h in Headers */,
				AD4A1938265355DF006D87CD /* UpAutoUpgradeLocalResTask.h in Headers */,
				AD4A1939265355DF006D87CD /* UPResEnvironment.h in Headers */,
				AD4A193A265355DF006D87CD /* UpResourceBatchTask.h in Headers */,
				AD4A193B265355DF006D87CD /* UPResourceCallback.h in Headers */,
				AD4A193C265355DF006D87CD /* UpResourceCallbackHolder.h in Headers */,
				AD4A193D265355DF006D87CD /* UpResourceCleaner.h in Headers */,
				AD4A193E265355DF006D87CD /* UpResourceCleanerImpl.h in Headers */,
				AD4A193F265355DF006D87CD /* UPResourceCondition.h in Headers */,
				AD4A1940265355DF006D87CD /* UPResourceDirectory.h in Headers */,
				AD4A1941265355DF006D87CD /* UPResourceErrCodes.h in Headers */,
				AD4A1942265355DF006D87CD /* UPResourceFilter.h in Headers */,
				AD4A1943265355DF006D87CD /* UPResourceHelper.h in Headers */,
				AD4A1944265355DF006D87CD /* UpResourceHolder.h in Headers */,
				AD4A1945265355DF006D87CD /* UPResourceInstallDelegate.h in Headers */,
				AD4A1946265355DF006D87CD /* UPResourceListCallback.h in Headers */,
				AD4A1947265355E0006D87CD /* UPResourceListener.h in Headers */,
				F743827C29B593490058489B /* UPRLoadReporterImp.h in Headers */,
				AD4A1948265355E0006D87CD /* UpResourceListenerHolder.h in Headers */,
				AD4A1949265355E0006D87CD /* UPResourceManager.h in Headers */,
				AD4A194A265355E0006D87CD /* UPResourcePreLoadTask.h in Headers */,
				AD4A194B265355E0006D87CD /* UPResourcePresetHelper.h in Headers */,
				5D644F11266DBC1500F64F59 /* UpResourcePresetDownloader.h in Headers */,
				AD4A194C265355E0006D87CD /* UpResourcePresetTask.h in Headers */,
				AD4A194D265355E0006D87CD /* UPResourceRelationDelegate.h in Headers */,
				AD4A194E265355E0006D87CD /* UPResourceRequestDelegate.h in Headers */,
				AD4A194F265355E0006D87CD /* UpResourceSelector.h in Headers */,
				AD4A1950265355E0006D87CD /* UpResourceSyncPresetTask.h in Headers */,
				F7096C4929C16B0F004F9A62 /* UPResourceReporter.h in Headers */,
				AD4A1951265355E0006D87CD /* UPResourceTask.h in Headers */,
				AD4A1952265355E0006D87CD /* UpResourceTaskMan.h in Headers */,
				AD4A1953265355E0006D87CD /* UPResourceCleanUnusefulResTask.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD4A199E265366F2006D87CD /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19A1265367E6006D87CD /* NSString+Paths.h in Headers */,
				AD4A19A2265367E6006D87CD /* UPResourceDatabaseImpl.h in Headers */,
				22E3E5AC29481F0C00F10806 /* UPOmsAppFuncModelReques.h in Headers */,
				AD4A19A3265367E6006D87CD /* UPResourceSQLiteOpenHelper.h in Headers */,
				AD4A19A4265367E6006D87CD /* UPResourceSQLiteOpenHelper+SQL.h in Headers */,
				AD4A19A5265367E6006D87CD /* UPOmsResInfo.h in Headers */,
				2C0489A9298A96A8001D4C3F /* UPResourceStatusConvertResult.h in Headers */,
				2C0489A5298A8571001D4C3F /* UPResourceStatusTransformer.h in Headers */,
				4093263326B3A70C0067EA80 /* SpecifyResourceVersionPatcher.h in Headers */,
				4093262D26B3A70C0067EA80 /* SpecifyResourceVersionAlertAnimator.h in Headers */,
				AD4A19A6265367E6006D87CD /* UPResourceConvertResult.h in Headers */,
				AD4A19A7265367E6006D87CD /* UPOmsConfigFileRequest.h in Headers */,
				F7062AF82886A00E00B29353 /* UPRAppLifeCycleManager.h in Headers */,
				AD4A19A8265367E6006D87CD /* UPOmsDeviceSourceRequest.h in Headers */,
				2CC23B1E28E17FC6004DB57E /* UPOmsResListRequest.h in Headers */,
				DF6D0564290A726B0084A0CF /* UPResourceUITool.h in Headers */,
				AD4A19AB265367E6006D87CD /* UPOmsResDataSource.h in Headers */,
				AD4A19AC265367E6006D87CD /* UPOmsResRequest.h in Headers */,
				AD4A19AD265367E6006D87CD /* UPOmsDeviceCustomInfoRequest.h in Headers */,
				AD4A19AE265367E6006D87CD /* UPResourceRequestBase.h in Headers */,
				AD4A19AF265367E6006D87CD /* UPResourceTransformer.h in Headers */,
				2CAF316529406E9A009DB26B /* UPOmsResStatusRequest.h in Headers */,
				AD4A19B0265367E6006D87CD /* UPResourceDeviceCustomInfoTransformer.h in Headers */,
				AD4A19B1265367E6006D87CD /* UPResourceDownloaderController.h in Headers */,
				DF6D055E290A66B10084A0CF /* UPResourceSEADownloadGifBar.h in Headers */,
				AD4A19B2265367E6006D87CD /* UPResourceDownloaderHelper.h in Headers */,
				F7062AF92886A00E00B29353 /* UPRAppManagerProtocol.h in Headers */,
				AD4A19B3265367E6006D87CD /* UPResourceDownloadGifBar.h in Headers */,
				AD4A19B4265367E6006D87CD /* UPResourceDownloadProgressBar.h in Headers */,
				AD4A19B5265367E6006D87CD /* UPResourceSouthAsiaDownloadProgressBar.h in Headers */,
				AD4A19B6265367E6006D87CD /* UPAssetPresetFileLoader.h in Headers */,
				AD4A19B7265367E6006D87CD /* SEAOmsConfigFileRequest.h in Headers */,
				AD4A19B8265367E6006D87CD /* SEAOmsDeviceSourceRequest.h in Headers */,
				AD4A19B9265367E6006D87CD /* SEAOmsResDataSource.h in Headers */,
				225FE6F929CA8DBA00A8CEC1 /* UPOmsNormalRequest.h in Headers */,
				AD4A19BA265367E6006D87CD /* SEAOmsResRequest.h in Headers */,
				AD4A19BB265367E6006D87CD /* SEARequestBase.h in Headers */,
				AD4A19BC265367E6006D87CD /* SEAResourceTransformer.h in Headers */,
				4093263426B3A70C0067EA80 /* SpecifyResourceVersionModel.h in Headers */,
				AD4A19BD265367E6006D87CD /* UPResourceFileSystem.h in Headers */,
				AD4A19BE265367E6006D87CD /* UPResourceTimeSystem.h in Headers */,
				AD4A19BF265367E6006D87CD /* UPConnectionMonitor.h in Headers */,
				AD4A19C0265367E6006D87CD /* UPDownloaderDelegate.h in Headers */,
				4093262F26B3A70C0067EA80 /* SpecifyResourceVersionView.h in Headers */,
				AD4A19C1265367E6006D87CD /* UPDownloaderHandle.h in Headers */,
				AD4A19C2265367E6006D87CD /* UPResourceConfig.h in Headers */,
				F89E5ADE2E25210600E41151 /* UPOmsLuaRequest.h in Headers */,
				AD4A19C3265367E6006D87CD /* UPResourceInjection.h in Headers */,
				F70D8B8429CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.h in Headers */,
				DF6D0561290A6DF10084A0CF /* UPResourceUIMacros.h in Headers */,
				4093262C26B3A70C0067EA80 /* SpecifyResourceVersionController.h in Headers */,
				F724F8B129CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.h in Headers */,
				AD4A19C4265367E6006D87CD /* UPResDelegateIMP.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		AD4A1904265354E9006D87CD /* UPRes */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD4A190B265354E9006D87CD /* Build configuration list for PBXNativeTarget "UPRes" */;
			buildPhases = (
				E06CC930B645AA8294476AD3 /* [CP] Check Pods Manifest.lock */,
				AD4A190E26535538006D87CD /* Headers */,
				AD4A1901265354E9006D87CD /* Sources */,
				AD4A1902265354E9006D87CD /* Frameworks */,
				AD4A1903265354E9006D87CD /* CopyFiles */,
				AD4A198026535653006D87CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UPRes;
			productName = UPRes;
			productReference = AD4A1905265354E9006D87CD /* libUPRes.a */;
			productType = "com.apple.product-type.library.static";
		};
		AD4A1994265366CC006D87CD /* UPResDelegateIMP */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = AD4A199B265366CD006D87CD /* Build configuration list for PBXNativeTarget "UPResDelegateIMP" */;
			buildPhases = (
				B3CB13D312C21B1020495E70 /* [CP] Check Pods Manifest.lock */,
				AD4A199E265366F2006D87CD /* Headers */,
				AD4A1991265366CC006D87CD /* Sources */,
				AD4A1992265366CC006D87CD /* Frameworks */,
				AD4A1993265366CC006D87CD /* CopyFiles */,
				AD4A199F265366F4006D87CD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD4A19E926536852006D87CD /* PBXTargetDependency */,
			);
			name = UPResDelegateIMP;
			productName = UPResDelegateIMP;
			productReference = AD4A1995265366CC006D87CD /* libUPResDelegateIMP.a */;
			productType = "com.apple.product-type.library.static";
		};
		F8D12A102488865D00414384 /* ResDebugger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8D12A272488865F00414384 /* Build configuration list for PBXNativeTarget "ResDebugger" */;
			buildPhases = (
				7B08D4DF1607CE73E592477F /* [CP] Check Pods Manifest.lock */,
				F8D12A0D2488865D00414384 /* Sources */,
				F8D12A0E2488865D00414384 /* Frameworks */,
				F8D12A0F2488865D00414384 /* Resources */,
				A27C1495C3B729D411D0C5D9 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD4A19EE26536B92006D87CD /* PBXTargetDependency */,
			);
			name = ResDebugger;
			productName = ResDebugger;
			productReference = F8D12A112488865D00414384 /* ResDebugger.app */;
			productType = "com.apple.product-type.application";
		};
		F8D12A2D2488867C00414384 /* ResDebuggerTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = F8D12A352488867C00414384 /* Build configuration list for PBXNativeTarget "ResDebuggerTests" */;
			buildPhases = (
				EF1593CA3EA756C908781119 /* [CP] Check Pods Manifest.lock */,
				F8D12A2A2488867C00414384 /* Sources */,
				F8D12A2B2488867C00414384 /* Frameworks */,
				F8D12A2C2488867C00414384 /* Resources */,
				D6283A7E7A79AB2047079585 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				AD4A19F226536CFA006D87CD /* PBXTargetDependency */,
			);
			name = ResDebuggerTests;
			productName = ResDebuggerTests;
			productReference = F8D12A2E2488867C00414384 /* ResDebuggerTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		F8D129F92488864400414384 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				DefaultBuildSystemTypeForWorkspace = Original;
				LastUpgradeCheck = 1130;
				ORGANIZATIONNAME = "海尔优家智能科技（北京）有限公司";
				TargetAttributes = {
					AD4A1904265354E9006D87CD = {
						CreatedOnToolsVersion = 12.3;
					};
					AD4A1994265366CC006D87CD = {
						CreatedOnToolsVersion = 12.3;
					};
					F8D12A102488865D00414384 = {
						CreatedOnToolsVersion = 11.3;
					};
					F8D12A2D2488867C00414384 = {
						CreatedOnToolsVersion = 11.3;
					};
				};
			};
			buildConfigurationList = F8D129FC2488864400414384 /* Build configuration list for PBXProject "UPRes" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = F8D129F82488864400414384;
			productRefGroup = F8D12A032488864400414384 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				F8D12A102488865D00414384 /* ResDebugger */,
				F8D12A2D2488867C00414384 /* ResDebuggerTests */,
				AD4A1904265354E9006D87CD /* UPRes */,
				AD4A1994265366CC006D87CD /* UPResDelegateIMP */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		AD4A198026535653006D87CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A198126535659006D87CD /* readme.md in Resources */,
				AD4A198226535659006D87CD /* release.md in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD4A199F265366F4006D87CD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19A026536705006D87CD /* UPResourceRes.bundle in Resources */,
				952FEA772C85C080006061B2 /* UPResourceDownloadGifBar.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A0F2488865D00414384 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F876250024C18F37003D508F /* UPResourceRes.bundle in Resources */,
				F8D12B5B2488895200414384 /* main.storyboard in Resources */,
				F8D12C6C2488961300414384 /* LaunchScreen.storyboard in Resources */,
				F8D12A232488865F00414384 /* LaunchScreen.storyboard in Resources */,
				F8D12B5A2488895200414384 /* Assets.xcassets in Resources */,
				F8D12A1E2488865D00414384 /* Main.storyboard in Resources */,
				F8D12C6D2488961300414384 /* Main.storyboard in Resources */,
				F8D12C6F2488969D00414384 /* PresetResPkg in Resources */,
				F8D12B5C2488895200414384 /* <EMAIL> in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A2C2488867C00414384 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				22F421952669C24C005C2F43 /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		7B08D4DF1607CE73E592477F /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ResDebugger-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		A27C1495C3B729D411D0C5D9 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ResDebugger/Pods-ResDebugger-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ResDebugger/Pods-ResDebugger-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ResDebugger/Pods-ResDebugger-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		B3CB13D312C21B1020495E70 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPResDelegateIMP-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		D6283A7E7A79AB2047079585 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ResDebuggerTests/Pods-ResDebuggerTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-ResDebuggerTests/Pods-ResDebuggerTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-ResDebuggerTests/Pods-ResDebuggerTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		E06CC930B645AA8294476AD3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UPRes-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		EF1593CA3EA756C908781119 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-ResDebuggerTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		AD4A1901265354E9006D87CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A195526535630006D87CD /* UPResCommonFunctions.m in Sources */,
				AD4A195626535630006D87CD /* UPDownloadHandle.m in Sources */,
				AD4A195726535630006D87CD /* UpPreloadResourceInfo.m in Sources */,
				AD4A195826535630006D87CD /* UPResourceInfo.m in Sources */,
				AD4A195926535630006D87CD /* UPResourceItem.m in Sources */,
				AD4A195A26535630006D87CD /* UPResourceQuery.m in Sources */,
				F743828329B596FA0058489B /* UPRLoadedReportInfo.m in Sources */,
				AD4A195B26535630006D87CD /* UPResourceReportInfo.m in Sources */,
				AD4A195C26535630006D87CD /* UPResourceResult.m in Sources */,
				AD4A195D26535630006D87CD /* UPResourceInstaller.m in Sources */,
				AD4A195E26535630006D87CD /* UPResourceUninstaller.m in Sources */,
				AD4A195F26535630006D87CD /* UPResourceOperator.m in Sources */,
				AD4A196026535630006D87CD /* UPResourceDownloader.m in Sources */,
				AD4A196126535630006D87CD /* UPResourceExtractor.m in Sources */,
				AD4A196226535630006D87CD /* UPResourceRemover.m in Sources */,
				5D644F12266DBC1500F64F59 /* UpResourcePresetDownloader.m in Sources */,
				AD4A196326535630006D87CD /* UPResourceScanner.m in Sources */,
				AD4A196426535630006D87CD /* UPResourceTransporter.m in Sources */,
				AD4A196526535630006D87CD /* UPResourceProcessorBase.m in Sources */,
				AD4A196626535630006D87CD /* UPResourceProcessPipeline.m in Sources */,
				AD4A196726535630006D87CD /* UPResourceValidator.m in Sources */,
				AD4A196826535630006D87CD /* UPResourceDataSourceBase.m in Sources */,
				AD4A196926535630006D87CD /* UPResourceRepository.m in Sources */,
				AD4A196A26535630006D87CD /* UPAutoUpgradeResTask.m in Sources */,
				AD4A196B26535630006D87CD /* UpAutoUpgradeLocalResTask.m in Sources */,
				AD4A196C26535630006D87CD /* UpResourceBatchTask.m in Sources */,
				AD4A196D26535630006D87CD /* UpResourceCallbackHolder.m in Sources */,
				AD4A196E26535630006D87CD /* UpResourceCleanerImpl.m in Sources */,
				AD4A196F26535630006D87CD /* UPResourceCondition.m in Sources */,
				AD4A197026535630006D87CD /* UPResourceDirectory.m in Sources */,
				AD4A197126535630006D87CD /* UPResourceErrCodes.m in Sources */,
				AD4A197226535630006D87CD /* UPResourceHelper.m in Sources */,
				AD4A197326535630006D87CD /* UpResourceHolder.m in Sources */,
				AD4A197426535630006D87CD /* UPResourceInstallDelegate.m in Sources */,
				AD4A197526535630006D87CD /* UpResourceListenerHolder.m in Sources */,
				AD4A197626535630006D87CD /* UPResourceManager.m in Sources */,
				AD4A197726535630006D87CD /* UPResourcePreLoadTask.m in Sources */,
				AD4A197826535630006D87CD /* UPResourcePresetHelper.m in Sources */,
				AD4A197926535630006D87CD /* UpResourcePresetTask.m in Sources */,
				AD4A197A26535630006D87CD /* UPResourceRelationDelegate.m in Sources */,
				AD4A197B26535630006D87CD /* UPResourceRequestDelegate.m in Sources */,
				AD4A197C26535630006D87CD /* UpResourceSyncPresetTask.m in Sources */,
				AD4A197D26535630006D87CD /* UPResourceTask.m in Sources */,
				AD4A197E26535630006D87CD /* UpResourceTaskMan.m in Sources */,
				AD4A197F26535630006D87CD /* UPResourceCleanUnusefulResTask.m in Sources */,
				F743827D29B593490058489B /* UPRLoadReporterImp.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		AD4A1991265366CC006D87CD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				AD4A19C526536841006D87CD /* NSString+Paths.m in Sources */,
				AD4A19C626536841006D87CD /* UPResourceDatabaseImpl.m in Sources */,
				AD4A19C726536841006D87CD /* UPResourceSQLiteOpenHelper.m in Sources */,
				AD4A19C826536841006D87CD /* UPResourceSQLiteOpenHelper+SQL.m in Sources */,
				AD4A19C926536841006D87CD /* UPOmsResInfo.m in Sources */,
				F70D8B8529CACCDF00FAD23F /* UPOmsReportResLoadedInfoRequest.m in Sources */,
				DF6D055F290A66B10084A0CF /* UPResourceSEADownloadGifBar.m in Sources */,
				F724F8B229CD7729002BAB6C /* UPResourceReportLoadedInfoTransformer.m in Sources */,
				AD4A19CA26536841006D87CD /* UPResourceConvertResult.m in Sources */,
				AD4A19CB26536841006D87CD /* UPOmsConfigFileRequest.m in Sources */,
				4093262E26B3A70C0067EA80 /* SpecifyResourceVersionView.m in Sources */,
				4093263126B3A70C0067EA80 /* SpecifyResourceVersionController.m in Sources */,
				AD4A19CC26536841006D87CD /* UPOmsDeviceSourceRequest.m in Sources */,
				2CAF316629406E9A009DB26B /* UPOmsResStatusRequest.m in Sources */,
				2C0489A6298A8571001D4C3F /* UPResourceStatusTransformer.m in Sources */,
				AD4A19CF26536841006D87CD /* UPOmsResDataSource.m in Sources */,
				AD4A19D026536841006D87CD /* UPOmsResRequest.m in Sources */,
				AD4A19D126536841006D87CD /* UPOmsDeviceCustomInfoRequest.m in Sources */,
				AD4A19D226536841006D87CD /* UPResourceRequestBase.m in Sources */,
				AD4A19D326536841006D87CD /* UPResourceTransformer.m in Sources */,
				2C0489AA298A96A8001D4C3F /* UPResourceStatusConvertResult.m in Sources */,
				AD4A19D426536841006D87CD /* UPResourceDeviceCustomInfoTransformer.m in Sources */,
				AD4A19D526536841006D87CD /* UPResourceDownloaderController.m in Sources */,
				AD4A19D626536841006D87CD /* UPResourceDownloaderHelper.m in Sources */,
				AD4A19D726536841006D87CD /* UPResourceDownloadGifBar.m in Sources */,
				AD4A19D826536841006D87CD /* UPResourceDownloadProgressBar.m in Sources */,
				F7062AF72886A00E00B29353 /* UPRAppLifeCycleManager.m in Sources */,
				AD4A19D926536841006D87CD /* UPResourceSouthAsiaDownloadProgressBar.m in Sources */,
				AD4A19DA26536841006D87CD /* UPAssetPresetFileLoader.m in Sources */,
				225FE6F729CA8DBA00A8CEC1 /* UPOmsNormalRequest.m in Sources */,
				AD4A19DB26536841006D87CD /* SEAOmsConfigFileRequest.m in Sources */,
				22E3E5AD29481F0C00F10806 /* UPOmsAppFuncModelReques.m in Sources */,
				4093263226B3A70C0067EA80 /* SpecifyResourceVersionPatcher.m in Sources */,
				4093263526B3A70C0067EA80 /* SpecifyResourceVersionModel.m in Sources */,
				AD4A19DC26536841006D87CD /* SEAOmsDeviceSourceRequest.m in Sources */,
				F89E5ADF2E25210600E41151 /* UPOmsLuaRequest.m in Sources */,
				AD4A19DD26536841006D87CD /* SEAOmsResDataSource.m in Sources */,
				AD4A19DE26536841006D87CD /* SEAOmsResRequest.m in Sources */,
				AD4A19DF26536841006D87CD /* SEARequestBase.m in Sources */,
				DF6D0565290A726B0084A0CF /* UPResourceUITool.m in Sources */,
				AD4A19E026536841006D87CD /* SEAResourceTransformer.m in Sources */,
				AD4A19E126536841006D87CD /* UPResourceFileSystem.m in Sources */,
				AD4A19E226536841006D87CD /* UPResourceTimeSystem.m in Sources */,
				AD4A19E326536841006D87CD /* UPConnectionMonitor.m in Sources */,
				2CC23B1F28E17FC6004DB57E /* UPOmsResListRequest.m in Sources */,
				AD4A19E426536841006D87CD /* UPDownloaderDelegate.m in Sources */,
				AD4A19E526536841006D87CD /* UPDownloaderHandle.m in Sources */,
				AD4A19E626536841006D87CD /* UPResourceConfig.m in Sources */,
				4093263026B3A70C0067EA80 /* SpecifyResourceVersionAlertAnimator.m in Sources */,
				AD4A19E726536841006D87CD /* UPResourceInjection.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A0D2488865D00414384 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8D4365E2547C8FB0089A911 /* UPResourceTrackerIMP.m in Sources */,
				F8D12B5D2488895200414384 /* main.m in Sources */,
				F8D12B582488895200414384 /* UPResTableViewCell.m in Sources */,
				F8D12B5F2488895200414384 /* RootViewController.m in Sources */,
				2CC23B2228E42189004DB57E /* UPRequestNormalViewController.m in Sources */,
				F8D12A1B2488865D00414384 /* ViewController.m in Sources */,
				F8D12B602488895200414384 /* RootViewController+TableView.m in Sources */,
				F8D12B592488895200414384 /* UPTableViewController.m in Sources */,
				F8D12A152488865D00414384 /* AppDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		F8D12A2A2488867C00414384 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F8D12BC42488897700414384 /* PresetSteps.m in Sources */,
				F8D12BC52488897700414384 /* RequestSteps.m in Sources */,
				F8D12BC12488897700414384 /* ResourceHolder.m in Sources */,
				F8D12BC32488897700414384 /* UpdateSteps.m in Sources */,
				F8D12BC62488897700414384 /* RelationSteps.m in Sources */,
				F8D12BAE2488897700414384 /* NetworkSteps.m in Sources */,
				22A252E3292DFBD700A46C48 /* DataBaseSteps.m in Sources */,
				F8D12BC82488897700414384 /* SearchResourceSteps.m in Sources */,
				22C485E9293DF928007032AC /* CacheCleanCallback.m in Sources */,
				F8D12BB02488897700414384 /* CucumberRunner.m in Sources */,
				F8D12BCA2488897700414384 /* InitializationSteps.m in Sources */,
				F8D12BAA2488897700414384 /* DataSourceSteps.m in Sources */,
				F8D12BA82488897700414384 /* StepUtils.m in Sources */,
				22FDE05A26560A01003001F1 /* AutoUpdateLocalResSteps.m in Sources */,
				F70D8B7F29C441D900FAD23F /* ReportResLoadedSteps.m in Sources */,
				22C485EC293DFA01007032AC /* ResourceSelectorDelegate.m in Sources */,
				F8D12BC72488897700414384 /* CombineResInfoSteps.m in Sources */,
				F8D12BC92488897700414384 /* LocalCacheSteps.m in Sources */,
				F8D12BAB2488897700414384 /* TimeSystemSteps.m in Sources */,
				F8D12BAC2488897700414384 /* DownloadSteps.m in Sources */,
				F8D12BC22488897700414384 /* TaskResourceSteps.m in Sources */,
				DF5F427A29272AD4008BA9D7 /* CommonResCallbackImp.m in Sources */,
				F8D12BAD2488897700414384 /* FileSystemSteps.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		AD4A19E926536852006D87CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD4A1904265354E9006D87CD /* UPRes */;
			targetProxy = AD4A19E826536852006D87CD /* PBXContainerItemProxy */;
		};
		AD4A19EE26536B92006D87CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD4A1994265366CC006D87CD /* UPResDelegateIMP */;
			targetProxy = AD4A19ED26536B92006D87CD /* PBXContainerItemProxy */;
		};
		AD4A19F226536CFA006D87CD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = AD4A1994265366CC006D87CD /* UPResDelegateIMP */;
			targetProxy = AD4A19F126536CFA006D87CD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		F8D12A1C2488865D00414384 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F8D12A1D2488865D00414384 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		F8D12A212488865F00414384 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F8D12A222488865F00414384 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		F8D12C682488961300414384 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F8D12C692488961300414384 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		F8D12C6A2488961300414384 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				F8D12C6B2488961300414384 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		AD4A190C265354E9006D87CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 37D82FF20BC73B478087B8E2 /* Pods-UPRes.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD4A190D265354E9006D87CD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 5790CA85BC007452009D4C38 /* Pods-UPRes.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		AD4A199C265366CD006D87CD /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = CE0EC0391743A94A7FEF95DE /* Pods-UPResDelegateIMP.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		AD4A199D265366CD006D87CD /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1F1D4295206D6F942F466E9A /* Pods-UPResDelegateIMP.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = "$(inherited)";
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CODE_SIGN_STYLE = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 9.0;
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F8D12A082488864400414384 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		F8D12A092488864400414384 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		F8D12A282488865F00414384 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F507A8F876CEC2FB3C8BD7A3 /* Pods-ResDebugger.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = PP27UD8NYZ;
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = ResDebugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					/usr/lib/swift,
				);
				MARKETING_VERSION = 10.5.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = Uplus99Dev;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F8D12A292488865F00414384 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 3710EC6E3F2BB08028AD5056 /* Pods-ResDebugger.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ALLOW_NON_MODULAR_INCLUDES_IN_FRAMEWORK_MODULES = YES;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				DEVELOPMENT_TEAM = "";
				ENABLE_BITCODE = NO;
				INFOPLIST_FILE = ResDebugger/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 15.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					/usr/lib/swift,
				);
				MARKETING_VERSION = 10.5.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.haier.uhome.Uplus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		F8D12A362488867C00414384 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0D513A0FFDC2F9EA49DBA8B7 /* Pods-ResDebuggerTests.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = ResDebuggerTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/usr/lib/swift,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "Zhu-KaiQi.ResDebuggerTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		F8D12A372488867C00414384 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = AE91C74C81E0886153FA3D73 /* Pods-ResDebuggerTests.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_IDENTITY = "iPhone Developer";
				"CODE_SIGN_IDENTITY[sdk=macosx*]" = "-";
				CODE_SIGN_STYLE = Manual;
				DEVELOPMENT_TEAM = "";
				INFOPLIST_FILE = ResDebuggerTests/Info.plist;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
					/usr/lib/swift,
				);
				PRODUCT_BUNDLE_IDENTIFIER = "Zhu-KaiQi.ResDebuggerTests";
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=macosx*]" = "";
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		AD4A190B265354E9006D87CD /* Build configuration list for PBXNativeTarget "UPRes" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD4A190C265354E9006D87CD /* Debug */,
				AD4A190D265354E9006D87CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		AD4A199B265366CD006D87CD /* Build configuration list for PBXNativeTarget "UPResDelegateIMP" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				AD4A199C265366CD006D87CD /* Debug */,
				AD4A199D265366CD006D87CD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8D129FC2488864400414384 /* Build configuration list for PBXProject "UPRes" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8D12A082488864400414384 /* Debug */,
				F8D12A092488864400414384 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8D12A272488865F00414384 /* Build configuration list for PBXNativeTarget "ResDebugger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8D12A282488865F00414384 /* Debug */,
				F8D12A292488865F00414384 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		F8D12A352488867C00414384 /* Build configuration list for PBXNativeTarget "ResDebuggerTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				F8D12A362488867C00414384 /* Debug */,
				F8D12A372488867C00414384 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = F8D129F92488864400414384 /* Project object */;
}

<?xml version="1.0" encoding="UTF-8"?>
<Scheme
   LastUpgradeVersion = "1130"
   version = "1.3">
   <BuildAction
      parallelizeBuildables = "YES"
      buildImplicitDependencies = "YES">
      <BuildActionEntries>
         <BuildActionEntry
            buildForTesting = "YES"
            buildForRunning = "YES"
            buildForProfiling = "YES"
            buildForArchiving = "YES"
            buildForAnalyzing = "YES">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F8D12A102488865D00414384"
               BuildableName = "ResDebugger.app"
               BlueprintName = "ResDebugger"
               ReferencedContainer = "container:UPRes.xcodeproj">
            </BuildableReference>
         </BuildActionEntry>
      </BuildActionEntries>
   </BuildAction>
   <TestAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      shouldUseLaunchSchemeArgsEnv = "YES"
      codeCoverageEnabled = "YES"
      onlyGenerateCoverageForSpecifiedTargets = "YES">
      <CodeCoverageTargets>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "AD4A1904265354E9006D87CD"
            BuildableName = "libUPRes.a"
            BlueprintName = "UPRes"
            ReferencedContainer = "container:UPRes.xcodeproj">
         </BuildableReference>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F8D12A2D2488867C00414384"
            BuildableName = "ResDebuggerTests.xctest"
            BlueprintName = "ResDebuggerTests"
            ReferencedContainer = "container:UPRes.xcodeproj">
         </BuildableReference>
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "AD4A1994265366CC006D87CD"
            BuildableName = "libUPResDelegateIMP.a"
            BlueprintName = "UPResDelegateIMP"
            ReferencedContainer = "container:UPRes.xcodeproj">
         </BuildableReference>
      </CodeCoverageTargets>
      <Testables>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F8D12A2D2488867C00414384"
               BuildableName = "ResDebuggerTests.xctest"
               BlueprintName = "ResDebuggerTests"
               ReferencedContainer = "container:UPRes.xcodeproj">
            </BuildableReference>
         </TestableReference>
         <TestableReference
            skipped = "NO">
            <BuildableReference
               BuildableIdentifier = "primary"
               BlueprintIdentifier = "F8A59872244077B5002C288A"
               BuildableName = "ResDebuggerUITests.xctest"
               BlueprintName = "ResDebuggerUITests"
               ReferencedContainer = "container:UPRes.xcodeproj">
            </BuildableReference>
         </TestableReference>
      </Testables>
   </TestAction>
   <LaunchAction
      buildConfiguration = "Debug"
      selectedDebuggerIdentifier = "Xcode.DebuggerFoundation.Debugger.LLDB"
      selectedLauncherIdentifier = "Xcode.DebuggerFoundation.Launcher.LLDB"
      launchStyle = "0"
      useCustomWorkingDirectory = "NO"
      ignoresPersistentStateOnLaunch = "NO"
      debugDocumentVersioning = "YES"
      debugServiceExtension = "internal"
      allowLocationSimulation = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F8D12A102488865D00414384"
            BuildableName = "ResDebugger.app"
            BlueprintName = "ResDebugger"
            ReferencedContainer = "container:UPRes.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </LaunchAction>
   <ProfileAction
      buildConfiguration = "Release"
      shouldUseLaunchSchemeArgsEnv = "YES"
      savedToolIdentifier = ""
      useCustomWorkingDirectory = "NO"
      debugDocumentVersioning = "YES">
      <BuildableProductRunnable
         runnableDebuggingMode = "0">
         <BuildableReference
            BuildableIdentifier = "primary"
            BlueprintIdentifier = "F8D12A102488865D00414384"
            BuildableName = "ResDebugger.app"
            BlueprintName = "ResDebugger"
            ReferencedContainer = "container:UPRes.xcodeproj">
         </BuildableReference>
      </BuildableProductRunnable>
   </ProfileAction>
   <AnalyzeAction
      buildConfiguration = "Debug">
   </AnalyzeAction>
   <ArchiveAction
      buildConfiguration = "Release"
      revealArchiveInOrganizer = "YES">
   </ArchiveAction>
</Scheme>

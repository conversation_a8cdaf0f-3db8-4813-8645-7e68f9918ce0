"use strict";
//  Hainer-bridge.js
(window.AlipayJSBridge && window.HainerJSBridge) || (function () {
    var version = '0.1.0';
    var utils = {
        generateMsg: function (raw) {
            return '[Hainer][JSBridge]' + raw;
        },
        createEvent: function (type, bubbles, cancelable) {
            var result;
            if (window.CustomEvent) {
                result = new CustomEvent(type, { bubbles: bubbles, cancelable: cancelable });
            } else {
                result = document.createEvent('Events');
                result.initEvent(type, bubbles, cancelable);
            }
            return result;
        },
        executeOnDOMReady: function (callback) {
            if ('function' !== typeof callback || !callback) {
                return;
            }
            
            if (/complete|loaded|interactive/.test(document.readyState)) {
                setTimeout(() => callback(), 0);
            } else {
                document.defaultView.addEventListener('DOMContentLoaded', () => callback(), false);
            }
        },
        parseJSONString: function (json, invalidMsg) {
            var obj = json
            if ('string' === typeof json && !!json) {
                try {
                    obj = JSON.parse(json);
                } catch (error) {
                    console.error(invalidMsg);
                }
            }
            return obj;
        }
    };
    var bridge = {
        version: version,
        isReady: false,
        callbackCache: {},
        sendOutMsgQueue: [],
        docEventsQueue: [],
            
        call: function (api, param, callback) {
            if ('string' !== typeof api || !api) {
                return;
            }
            if ('function' === typeof param) {
                callback = param;
                param = null;
            } else if ('object' !== typeof param) {
                param = null
            }
            
            var cId = this._cacheCallback(api, callback);
            var msg = JSON.stringify({
                api: api,
                param: param || {},
                callbackId: cId
            });
            
            if (window.webkit &&
                window.webkit.messageHandlers &&
                window.webkit.messageHandlers.HAINERMESSAGEHANDLER &&
                window.webkit.messageHandlers.HAINERMESSAGEHANDLER.postMessage) {
                this._clearSendOutMsgQueue();
                window.webkit.messageHandlers.HAINERMESSAGEHANDLER.postMessage(msg);
            } else {
                this.sendOutMsgQueue.push(msg);
                console.warn(utils.generateMsg('HAINERMESSAGEHANDLER.postMessage not found!'));
            }
             console.error("加载的是远端配置JS");

        },
        dispatchDocEvent: function (name, data, callbackId) {
            if ('string' !== typeof name || !name) {
                return;
            }
            
            var obj = utils.parseJSONString(data, utils.generateMsg(`The data of document event '${name}' is invalid JSON string!`));
            if (!this.isReady && this.docEventsQueue) {
                this.docEventsQueue.push({
                    type: name,
                    data: obj,
                    callbackId: callbackId
                });
                return;
            }
            
            setTimeout(() => bridge._dispatchDocEventSync(name, obj, callbackId), 0);
        },
        invokeJSCallback: function (callbackId, response) {

            console.error("加载的是远端配置JS");
  
            if ('string' !== typeof callbackId || !callbackId) {
                return;
            }
            
            var callback = this.callbackCache[callbackId];
            if (!callback) {
                return;
            }
            delete this.callbackCache[callbackId];
            
            var obj = utils.parseJSONString(response, utils.generateMsg(`The response for callback '${callbackId}' is invalid JSON string: '${response}'!`));
            setTimeout(() => callback(obj), 0);
        },
        getReady: function () {
            var ready1 = utils.createEvent('AlipayJSBridgeReady', false, false);
            var ready2 = utils.createEvent('HainerJSBridgeReady', false, false);
            
            var rawDocAddEventListener = document.addEventListener;
            document.addEventListener = function (event, func, useCapture) {
                var evts = [ready1, ready2];
                var index = (evts.map((value) => value.type)).indexOf(event);
                if (index > -1) {
                    setTimeout(() => func(evts[index]), 0);
                } else {
                    rawDocAddEventListener.apply(document, arguments);
                }
            };
            
            document.dispatchEvent(ready1);
            document.dispatchEvent(ready2);
            
            this.isReady = true;
            console.info(utils.generateMsg('JSBridge ready!' + ' -> URL: ' + document.URL));
            this._clearDocEventsQueue();
            this._clearSendOutMsgQueue();
        },
        _clearSendOutMsgQueue: function () {
            if (!this.sendOutMsgQueue || this.sendOutMsgQueue.length == 0) {
                return;
            }
            this.sendOutMsgQueue.forEach((msg) => {
                window.webkit.messageHandlers.HAINERMESSAGEHANDLER.postMessage(msg);
            });
            delete this.sendOutMsgQueue;
        },
        _clearDocEventsQueue: function () {
            if (!this.docEventsQueue || this.docEventsQueue.length == 0) {
                return;
            }
            this.docEventsQueue.forEach((value) => {
                setTimeout(() => bridge._dispatchDocEventSync(value.type, value.data, value.callbackId), 0);
            });
            delete this.docEventsQueue;
        },
        _cacheCallback: function (api, callback) {
            if ('function' === typeof callback) {
                var callbackId = api + '_' + Date.now() + '_' + Math.random();
                if (this.callbackCache[callbackId]) {
                    console.warn(utils.generateMsg('Callback ID `' + callbackId + '` has exist!'));
                }
                this.callbackCache[callbackId] = callback;
                return callbackId;
            } else {
                return '';
            }
        },
        _dispatchDocEventSync: function (name, data, callbackId) {
            var evt = utils.createEvent(name, false, true);
            if (data) {
                evt.data = data;
            }
            var canceled = !document.dispatchEvent(evt);
            console.info(utils.generateMsg('Dispatched event: ' + name));
            
            if (!!callbackId) {
                this.call('__dispatchDocEventReceipt__', {
                    nativeCallbackId: callbackId,
                    eventCanceled: canceled
                });
            }
        }
    };
    
    window.HainerJSBridge = bridge;
    window.AlipayJSBridge = bridge;

    if (window.top == window.self) {
        bridge.call('__mainFrameJSBridgeInstalled__');
    }
    
    utils.executeOnDOMReady(() => bridge.getReady());
})();

//  Hainer-visibility.js
window.top == window.self && ((!!document.lastLifeCycleFlag) || (function () {
    document.lastLifeCycleFlag = {
        type: null,
        time: Date.now()
    };
    function _dispatchLifeCycleEvtIfRequired(type, data, cId) {
        if (type === document.lastLifeCycleFlag.type &&
            Date.now() - document.lastLifeCycleFlag.time < 300) {
            return;
        }
        
        document.lastLifeCycleFlag.type = type;
        document.lastLifeCycleFlag.time = Date.now();
        
        _dispatchLifeCycleEvtAsync(type, data, cId);
    }
    function _dispatchLifeCycleEvtAsync(type, data, cId) {
        if (window.HainerJSBridge) {
            window.HainerJSBridge.dispatchDocEvent(type, data, cId);
        } else {
            document.addEventListener('HainerJSBridgeReady', () => {
                setTimeout(() => window.HainerJSBridge.dispatchDocEvent(type, data, cId), 5);
            }, { once: true });
        }
    }
    
    window.HainerJSBridge.triggerPageLifeCycleEvent = _dispatchLifeCycleEvtIfRequired;
    
    var visibilityChangeListener = () => {
        if (document.visibilityState === 'hidden') {
            _dispatchLifeCycleEvtIfRequired('pause');
        } else if (document.visibilityState === 'visible') {
            _dispatchLifeCycleEvtIfRequired('resume');
        }
    };
    
    for (var name of ['visibilitychange', 'webkitvisibilitychange']) {
        document.addEventListener(name, visibilityChangeListener);
    }
    for (var name of ['pagehide', 'beforeunload', 'unload', 'blur']) { // 一把糊涂药，望保无虞
        document.addEventListener(name, () => _dispatchLifeCycleEvtIfRequired('pause'));
    }
    
    visibilityChangeListener();
})());

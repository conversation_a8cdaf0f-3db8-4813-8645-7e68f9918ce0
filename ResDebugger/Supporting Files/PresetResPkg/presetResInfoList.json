{"infoList": [{"resName": "wzService", "resType": "apicloud", "version": "1.8.2", "filename": "apicloud@<EMAIL>", "hashMD5": "5135a81e41c54f84aaf0c316db83f1e4"}, {"resName": "apphome", "resType": "mPaaS", "version": "2.7.10", "filename": "apphome", "hashMD5": "5135a81e41c54f84aaf0c316db83f1e4"}, {"resName": "EShop", "resType": "mPaaS", "version": "1.1.98", "filename": "EShop", "hashMD5": "5135a81e41c54f84aaf0c316db83f1e4"}, {"resName": "appmine", "resType": "mPaaS", "version": "3.5.5", "filename": "appmine", "hashMD5": "7b8c36f06652d12e9bf0999e6c753de9"}, {"resName": "bindingFailure", "resType": "apicloud", "version": "1.5.0", "filename": "apicloud@<EMAIL>", "hashMD5": "6f35d79b95013de8ee8198f29d576ab0"}, {"resName": "enjoyFood", "resType": "mPaaS", "version": "1.5.11", "filename": "mPaaS@<EMAIL>", "hashMD5": "3f475e424b68f8806c334d831c3ef027"}, {"resName": "feedback", "resType": "mPaaS", "version": "3.1.3", "filename": "mPaaS@<EMAIL>", "hashMD5": "568082009854b9015c900485c83e73f6"}, {"resName": "haierVipCode", "resType": "mPaaS", "version": "1.2.17", "filename": "mPaaS@<EMAIL>", "hashMD5": "427f98547be1d5350a83825af7945d6e"}, {"resName": "memberPoints", "resType": "mPaaS", "version": "2.0.6", "filename": "mPaaS@<EMAIL>", "hashMD5": "9f0db1db53bb5a40bea678800bb1a7ba"}, {"resName": "onlineService", "resType": "mPaaS", "version": "2.0.24", "filename": "mPaaS@<EMAIL>", "hashMD5": "a84274b6f02624cb97e304aeb75f249e"}, {"resName": "popularEvents", "resType": "mPaaS", "version": "2.0.4", "filename": "mPaaS@<EMAIL>", "hashMD5": "6891b796d52fe66b269ff2cda80274bd"}, {"resName": "smartdevice", "resType": "mPaaS", "version": "1.1.0", "filename": "mPaaS@<EMAIL>", "hashMD5": "6cb555c1195111614e9e0344ba1ddea0"}, {"resName": "smarthome", "resType": "mPaaS", "version": "3.2.23", "filename": "mPaaS@<EMAIL>", "hashMD5": "587f7901f3ab18949659cced091ba9ac"}, {"resName": "tvAuthorization", "resType": "mPaaS", "version": "1.0.18", "filename": "mPaaS@<EMAIL>", "hashMD5": "ddcde47ee61f9e1f95018bd236f3f5f6"}, {"resName": "ugc", "resType": "mPaaS", "version": "1.1.21", "filename": "mPaaS@<EMAIL>", "hashMD5": "556ee4a988549257463378d41e177be2"}, {"resName": "usercenter", "resType": "apicloud", "version": "1.6.29", "filename": "apicloud@<EMAIL>", "hashMD5": "73f4008e32f979adb01ad63d7f07f36a"}]}
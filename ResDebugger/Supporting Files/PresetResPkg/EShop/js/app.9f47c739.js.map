{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?4241", "webpack:///./src/views/home/<USER>/Jg_05.png", "webpack:///./src/views/home/<USER>/Jg_03.png", "webpack:///./src/components/refreshLoading/index.vue?7b9d", "webpack:///./src/views/search/components/filter.vue?0baa", "webpack:///./src/views/home/<USER>/bg.png", "webpack:///./src/views/search/index.vue?b4b9", "webpack:///./src/views/home/<USER>/Jg_04.png", "webpack:///./src/views/home/<USER>/Jg_01.png", "webpack:///./src/App.vue?a498", "webpack:///./src/App.vue?bff9", "webpack:///./src/views/home/<USER>", "webpack:///./src/components/refreshLoading/index.vue?c637", "webpack:///src/components/refreshLoading/index.vue", "webpack:///./src/components/refreshLoading/index.vue?1a2a", "webpack:///./src/components/refreshLoading/index.vue?1a99", "webpack:///./src/store/index.js", "webpack:///./src/views/home/<USER>", "webpack:///./src/utils/utils.js", "webpack:///src/views/home/<USER>", "webpack:///./src/views/home/<USER>", "webpack:///./src/views/home/<USER>", "webpack:///./src/views/search/index.vue?c6fe", "webpack:///./src/views/search/components/hotSearch.vue?e7bb", "webpack:///./src/utils/uplushttp.js", "webpack:///./src/utils/http.js", "webpack:///./src/views/search/searchService.js", "webpack:///src/views/search/components/hotSearch.vue", "webpack:///./src/views/search/components/hotSearch.vue?c542", "webpack:///./src/views/search/components/hotSearch.vue?0372", "webpack:///./src/views/search/components/recentSearch.vue?b1eb", "webpack:///src/views/search/components/recentSearch.vue", "webpack:///./src/views/search/components/recentSearch.vue?993b", "webpack:///./src/views/search/components/recentSearch.vue?cc61", "webpack:///./src/views/search/components/filter.vue?82e2", "webpack:///src/views/search/components/filter.vue", "webpack:///./src/views/search/components/filter.vue?d823", "webpack:///./src/views/search/components/filter.vue?9cfb", "webpack:///./src/views/search/components/shopList.vue?e1bd", "webpack:///src/views/search/components/shopList.vue", "webpack:///./src/views/search/components/shopList.vue?d67c", "webpack:///./src/views/search/components/shopList.vue?a670", "webpack:///./src/views/search/components/prodList.vue?9590", "webpack:///src/views/search/components/prodList.vue", "webpack:///./src/views/search/components/prodList.vue?4adb", "webpack:///./src/views/search/components/prodList.vue?6e01", "webpack:///src/views/search/index.vue", "webpack:///./src/views/search/index.vue?2b70", "webpack:///./src/views/search/index.vue?5f54", "webpack:///./src/router.js", "webpack:///./src/main.js", "webpack:///./src/views/home/<USER>/gotop.png", "webpack:///./src/assets/images/icon_search.png", "webpack:///./src/views/search/components/hotSearch.vue?2ebe", "webpack:///./src/views/search/components/prodList.vue?df6c", "webpack:///./src/views/home/<USER>/empty.png", "webpack:///./src/views/home/<USER>/icon_more.png", "webpack:///./src/views/home/<USER>", "webpack:///./src/views/home/<USER>/icon_cart.png", "webpack:///./src/views/search/components/shopList.vue?92d1", "webpack:///./src/assets/images/delete.png", "webpack:///./src/views/home/<USER>/Jg_02.png", "webpack:///./src/assets/images/item_more.png", "webpack:///./src/views/home/<USER>/icon_kefu.png", "webpack:///./src/config/index.js", "webpack:///./src/views/home/<USER>", "webpack:///./src/views/search/components/recentSearch.vue?b813"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "_vm", "this", "_h", "$createElement", "_c", "_self", "attrs", "staticRenderFns", "script", "component", "staticClass", "style", "pdtHeight", "_v", "on", "toSearch", "_s", "searchWord", "$event", "uplusBuriedPoint", "toShoppingCart", "cartNum", "_e", "showModal", "_l", "item", "productCateId", "changeCate", "class", "curproductCateId", "onRefresh", "scopedSlots", "_u", "fn", "proxy", "model", "callback", "$$v", "isLoading", "expression", "isOnline", "bannerList", "index", "_f", "imageUrl", "bannerClick", "middleBannerImg", "pic", "hyperLinkType", "linkType", "hyperLink", "link", "goPage", "path", "img", "title", "categoryList", "keywordId", "iconUrl", "keywordValue", "programTabs", "tabId", "curProgramIndex", "changeProgram", "tabName", "programLists", "curProgramTab", "ref", "proTabChange", "idx", "prod", "planDetailsRelativeUrl", "hasVideo", "planThumbImg", "salesHighlights", "saleList", "toNextPage", "activeTab", "changeTab", "timeTip", "productId", "toProduct", "flashsalePrice", "miniPrice", "nearbyList", "id", "go<PERSON><PERSON><PERSON>", "distance", "pg", "sg", "lg", "requireGoods", "one", "relationId", "two", "three", "productCateList", "fetchEnd", "defaultImageUrl", "productFullName", "point", "indx", "finalPrice", "hasStock", "comments", "goodRate", "directives", "rawName", "isShowModal", "maskHeight", "toGotop", "_m", "text", "props", "config", "require", "instance", "UplusApi", "initDeviceReady", "<PERSON><PERSON>", "use", "Vuex", "httpModule", "defaultLocation", "region", "provinceId", "province", "cityId", "city", "districtId", "district", "townshipId", "township", "latitude", "longitude", "Store", "state", "locationInfo", "sessionValue", "historySearch", "localStorage", "getItem", "getters", "getlocationInfo", "getHistorySearch", "getLatLong", "mutations", "setSessionValue", "setLatLong", "location", "addHistorySearch", "keyword", "curText", "hisTextArr", "split", "unshift", "Set", "join", "setItem", "deleteHistorySearch", "actions", "doLocation", "commit", "Promise", "resolve", "then", "upLocationModule", "getLocation", "res", "retData", "console", "log", "catch", "getAuthorize", "sdToken", "dispatch", "url", "HOST", "encodeURIComponent", "axios", "headers", "success", "mId", "getMember", "post", "qs", "stringify", "token", "mid", "userInfo", "username", "userName", "avatarImage", "avatarImageFileId", "loginName", "birthday", "phone", "mobile", "email", "cartNumber", "gender", "isNewUser", "isStoreMember", "nick<PERSON><PERSON>", "rankName", "promotionCode", "resultSign", "defaults", "baseURL", "interceptors", "request", "common", "error", "reject", "getCartNumber", "bannerUrl", "getBanner", "params", "getMessageList", "getProgramData", "getRecommondKeyWord", "getMiddleBanner", "getFlashSale", "getNearbyShop", "getRequireGoods", "getDefaultSearchWord", "SHOST", "getCommonLoadItemNew", "getPriceByProduct", "windowToRem", "width", "doc", "win", "docEl", "documentElement", "resizeEvt", "recalc", "clientWidth", "fontSize", "addEventListener", "document", "isIphonex", "test", "navigator", "userAgent", "screen", "height", "isAndroid", "indexOf", "isUplus", "netWorkStatusHandler", "onFn", "offFn", "uplusBuriedUrlPoint", "upTraceModule", "reportSelfPageChange", "type", "selfPageUrl", "selfPageTitle", "ret", "upVdnModule", "goToPage", "components", "refreshLoading", "textIndex", "textList", "tabList", "isSaling", "saleProductList", "btmCategoryNav", "fetching", "curpage", "pageSize", "showTotop", "scroll", "inserted", "homHeight", "getElementById", "binding", "classList", "add", "curHeadNavId", "scrollIntoView", "curBtmNavId", "remove", "computed", "textSelect", "val", "con", "content", "mounted", "fetchUserCartInfo", "filters", "toFixedTwo", "price", "imageToHttps", "replace", "methods", "getDeviceList", "checkIsLogin", "dataService", "fetchData", "pholder", "streetId", "fromType", "memberId", "filterData", "pageIndex", "productList", "for<PERSON>ach", "prodsArr", "commission", "tab", "$refs", "proSwiper", "swipeTo", "ev", "target", "textStartMove", "setTimeout", "itemsId", "regionId", "street", "plans", "race", "banner", "message", "category", "middleBanner", "nearby", "flashSale", "defaultSearch", "clearTimeout", "timer", "values", "loginByH5", "loginSuccess", "getDefaultSearch", "hot_word", "getFalshSale", "cloneList", "findIndex", "Date", "startTime", "getDate", "systemTime", "endTime", "listLength", "list", "map", "promotionState", "timeStr", "products", "tempArr", "urls", "intact", "user_center_access_token", "scrollSmoothTo", "requestAnimationFrame", "step", "goBack", "placeholder", "domProps", "composing", "searchInput", "inputFocus", "clearFn", "doSearch", "curTab", "curStatus", "parseFloat", "filterShow", "doKeySearch", "formatHtml", "tag", "stopPropagation", "tsh", "closeFilter", "<PERSON><PERSON><PERSON>er", "hotTags", "targetUrl", "decodeURIComponent", "upHttpModule", "TokenAuthorization", "method", "retCode", "status", "statusText", "err", "transform", "JSON", "sdkHttp", "hotSearch", "searchDropdown", "searchService", "$emit", "recentList", "deleteHistory", "recentStr", "$dialog", "confirm", "curStockStatus", "changeFilterStock", "lowestPrice", "highestPrice", "doSure", "filterStock", "arrow", "el", "toggle", "shopList", "curPage", "curProTab", "nofilter", "sort", "changeProTab", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "closeAllFilterShow", "productTab", "allFilterShow", "recentSearch", "filterList", "tabType", "searchingList", "clear", "$route", "query", "$toast", "tse", "condition", "produtRefList", "filterFn", "flag", "$router", "back", "Router", "base", "process", "routes", "Home", "Search", "<PERSON><PERSON>", "productionTip", "$axios", "router", "store", "render", "h", "App", "$mount", "ENV", "DEBUG", "HOSTS"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,yBAAqb,EAAG,G,uBCAxbW,EAAOD,QAAU,IAA0B,0B,uBCA3CC,EAAOD,QAAU,IAA0B,0B,kCCA3C,yBAAojB,EAAG,G,oCCAvjB,yBAA0kB,EAAG,G,uBCA7kBC,EAAOD,QAAU,IAA0B,uB,2DCA3C,yBAAojB,EAAG,G,qBCAvjBC,EAAOD,QAAU,IAA0B,0B,qBCA3CC,EAAOD,QAAU,IAA0B,0B,4HCAvC,EAAS,WAAa,IAAI+B,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACE,MAAM,CAAC,GAAK,QAAQ,CAACF,EAAG,gBAAgB,IAC9IG,EAAkB,G,wBCAlBC,EAAS,GAMTC,EAAY,eACdD,EACA,EACAD,GACA,EACA,KACA,KACA,MAIa,EAAAE,E,oBClBX,EAAS,WAAa,IAAIT,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,QAAQ,CAACN,EAAG,MAAM,CAACM,YAAY,cAAcC,MAAM,CAAE,cAAeX,EAAIY,WAAYN,MAAM,CAAC,GAAK,eAAe,CAACF,EAAG,MAAM,CAACM,YAAY,UAAU,CAACN,EAAG,MAAM,CAACM,YAAY,SAAS,CAACV,EAAIa,GAAG,QAAQT,EAAG,MAAM,CAACM,YAAY,SAASI,GAAG,CAAC,MAAQd,EAAIe,WAAW,CAACX,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,WAA0CF,EAAG,OAAO,CAACM,YAAY,QAAQ,CAACV,EAAIa,GAAGb,EAAIgB,GAAGhB,EAAIiB,iBAAiBb,EAAG,MAAM,CAACM,YAAY,UAAUJ,MAAM,CAAC,IAAM,EAAQ,SAA2BQ,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAImB,iBAAiB,+HAA+Hf,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQd,EAAIoB,iBAAiB,CAAChB,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,WAA8BN,EAAW,QAAEI,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGhB,EAAIqB,YAAYrB,EAAIsB,OAAOlB,EAAG,MAAM,CAACM,YAAY,OAAOJ,MAAM,CAAC,IAAM,EAAQ,SAA2BQ,GAAG,CAAC,MAAQd,EAAIuB,eAAenB,EAAG,MAAM,CAACM,YAAY,kBAAkBJ,MAAM,CAAC,GAAK,kBAAkB,CAACF,EAAG,MAAM,CAACM,YAAY,sBAAsBV,EAAIwB,GAAIxB,EAAkB,gBAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKC,cAAcpB,MAAM,CAAC,GAAM,WAAcmB,EAAkB,eAAIX,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI2B,WAAWF,EAAM,SAAUP,MAAW,CAACd,EAAG,IAAI,CAACwB,MAAM,CAAC,OAAU5B,EAAI6B,kBAAoBJ,EAAKC,eAAepB,MAAM,CAAC,KAAO,iBAAiB,CAACN,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,SAAS6B,EAAG,OAAO,CAACJ,EAAIa,GAAG,YAAW,OAAOT,EAAG,MAAM,CAACM,YAAY,iBAAiBC,MAAM,CAAE,IAAOX,EAAIY,WAAYN,MAAM,CAAC,GAAK,kBAAkB,CAACF,EAAG,mBAAmB,CAACM,YAAY,SAASI,GAAG,CAAC,QAAUd,EAAI8B,WAAWC,YAAY/B,EAAIgC,GAAG,CAAC,CAAC1C,IAAI,UAAU2C,GAAG,WAAW,MAAO,CAAC7B,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,YAAY4B,OAAM,GAAM,CAAC5C,IAAI,UAAU2C,GAAG,WAAW,MAAO,CAAC7B,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,YAAY4B,OAAM,GAAM,CAAC5C,IAAI,UAAU2C,GAAG,WAAW,MAAO,CAAC7B,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,WAAW4B,OAAM,GAAM,CAAC5C,IAAI,UAAU2C,GAAG,WAAW,MAAO,CAAC7B,EAAG,iBAAiB,CAACE,MAAM,CAAC,KAAO,YAAY4B,OAAM,KAAQC,MAAM,CAACnD,MAAOgB,EAAa,UAAEoC,SAAS,SAAUC,GAAMrC,EAAIsC,UAAUD,GAAKE,WAAW,cAAc,CAACnC,EAAG,MAAM,CAACM,YAAY,cAAc,CAAGV,EAAIwC,UAAaxC,EAAIyC,WAAW9F,OAAgGyD,EAAG,YAAY,CAACM,YAAY,SAASJ,MAAM,CAAC,SAAW,IAAK,kBAAkB,UAAUN,EAAIwB,GAAIxB,EAAc,YAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,iBAAiB,CAACd,IAAIoD,GAAO,CAACtC,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuByB,EAAKmB,UAAU,KAAO,WAAW9B,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI6C,YAAYpB,YAAc,GAAtZrB,EAAG,MAAM,CAACM,YAAY,UAAU,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,aAA0VN,EAAmB,gBAAEI,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuBA,EAAI8C,gBAAgBC,KAAK,KAAO,WAAWjC,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI6C,YAAY,CAAEG,cAAe,GAAGhD,EAAI8C,gBAAgBG,SAAUC,UAAWlD,EAAI8C,gBAAgBK,aAAcnD,EAAIsB,KAAKlB,EAAG,MAAM,CAACM,YAAY,WAAWV,EAAIwB,GAAIxB,EAAW,SAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAMhC,YAAY,MAAMI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO3B,EAAK4B,SAAS,CAACjD,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMmB,EAAK6B,OAAOlD,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAK8B,eAAc,IAAI,GAAIvD,EAAIwD,aAAmB,OAAEpD,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACV,EAAIwB,GAAIxB,EAAgB,cAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKgC,UAAU/C,YAAY,gBAAgBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIe,SAASU,MAAS,CAACrB,EAAG,YAAY,CAACE,MAAM,CAAC,MAAQ,GAAG,MAAQ,GAAG,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuByB,EAAKiC,YAAYtD,EAAG,IAAI,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKkC,kBAAkB,MAAKvD,EAAG,MAAM,CAACM,YAAY,gBAAgBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,gBAAgB,CAAChD,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,QAAqC,IAAM,MAAMF,EAAG,IAAI,CAACJ,EAAIa,GAAG,WAAW,GAAGb,EAAIsB,KAAMtB,EAAI4D,YAAkB,OAAExD,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,YAAYT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,aAAa,CAACpD,EAAIa,GAAG,MAAMT,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,WAAW,KAAKF,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,eAAeV,EAAIwB,GAAIxB,EAAe,aAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,OAAO,CAACd,IAAImC,EAAKoC,MAAMjC,MAAM,CAAC,OAAUc,GAAS1C,EAAI8D,iBAAiBxD,MAAM,CAAC,GAAM,QAAWmB,EAAU,OAAIX,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI+D,cAActC,EAAKoC,MAAOnB,MAAU,CAAC1C,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKuC,eAAc,KAAMhE,EAAIiE,aAAajE,EAAIkE,eAAgB9D,EAAG,MAAM,CAACA,EAAG,YAAY,CAAC+D,IAAI,YAAYzD,YAAY,iBAAiBJ,MAAM,CAAC,mBAAkB,GAAOQ,GAAG,CAAC,OAASd,EAAIoE,eAAepE,EAAIwB,GAAIxB,EAAe,aAAE,SAASyB,EAAK4C,GAAK,OAAOjE,EAAG,iBAAiB,CAACd,IAAI+E,EAAI3D,YAAY,mBAAmBV,EAAIwB,GAAIC,EAAU,OAAE,SAAS6C,EAAK5B,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAMhC,YAAY,uBAAuBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAOkB,EAAKC,wBAAwB,GAAO,MAAS,CAACnE,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAAG4D,EAAKE,SAA8DxE,EAAIsB,KAAxDlB,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,UAAmBA,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuBsE,EAAKG,cAAc,IAAM,GAAG,KAAO,eAAerE,EAAG,IAAI,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGsD,EAAKI,yBAAwB,MAAK,IAAI,GAAG1E,EAAIsB,OAAOtB,EAAIsB,KAAMtB,EAAI2E,SAAShI,OAAS,EAAGyD,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQd,EAAI4E,aAAa,CAAC5E,EAAIa,GAAG,MAAMT,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,WAAW,KAAKF,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmBV,EAAIwB,GAAIxB,EAAY,UAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAMhC,YAAY,YAAYkB,MAAM,CAAC,OAAU5B,EAAI6E,WAAanC,GAAO5B,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI8E,UAAUrD,EAAKiB,MAAU,CAACtC,EAAG,SAAS,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAK8B,UAAUnD,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKsD,iBAAgB,KAAK3E,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmBV,EAAIwB,GAAIxB,EAAmB,iBAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKuD,UAAUtE,YAAY,aAAaI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIiF,UAAUxD,MAAS,CAACrB,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuByB,EAAKmB,UAAU,KAAO,aAAaxC,EAAG,MAAM,CAACA,EAAG,IAAI,CAACE,MAAM,CAAC,GAAKmB,EAAKyD,iBAAiB,CAAC9E,EAAG,OAAO,CAACJ,EAAIa,GAAG,OAAOb,EAAIa,GAAGb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAKyD,oBAAoB9E,EAAG,MAAM,CAACJ,EAAIa,GAAG,IAAIb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAK0D,sBAAqB,SAASnF,EAAIsB,KAAMtB,EAAIoF,WAAiB,OAAEhF,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,kBAAkB,CAACpD,EAAIa,GAAG,MAAMT,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,QAAQ,MAAQ,WAAW,KAAKF,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAACM,YAAY,oBAAoBV,EAAIwB,GAAIxB,EAAc,YAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAK4D,GAAG3E,YAAY,mBAAmBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIsF,SAAS7D,MAAS,CAACrB,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,KAAKJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,SAAS6B,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,IAAI,CAACA,EAAG,IAAI,CAACM,YAAY,cAAcV,EAAIa,GAAG,UAAUT,EAAG,QAAQA,EAAG,IAAI,CAACA,EAAG,IAAI,CAACM,YAAY,iBAAiBV,EAAIa,GAAG,KAAKb,EAAIgB,GAAGS,EAAK8D,aAAc9D,EAAiB,aAAErB,EAAG,KAAKJ,EAAIsB,OAAOlB,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,IAAI,CAACJ,EAAIa,GAAG,QAAQb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAK+D,GAAG,OAAOpF,EAAG,IAAI,CAACJ,EAAIa,GAAG,QAAQb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAKgE,GAAG,OAAOrF,EAAG,IAAI,CAACJ,EAAIa,GAAG,QAAQb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAKiE,GAAG,cAAa,OAAO1F,EAAIsB,KAAKlB,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,YAAYT,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACN,EAAG,MAAM,CAACM,YAAY,qBAAqBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI6C,YAAY,CAAEG,cAAe,GAAGhD,EAAI2F,aAAaC,IAAI3C,SAAUC,UAAWlD,EAAI2F,aAAaC,IAAIzC,KAAM0C,WAAY7F,EAAI2F,aAAaC,IAAIC,gBAAiB,CAACzF,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuBA,EAAI2F,aAAaC,IAAI7C,KAAK,KAAO,eAAe3C,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,MAAM,CAACU,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI6C,YAAY,CAAEG,cAAe,GAAGhD,EAAI2F,aAAaG,IAAI7C,SAAUC,UAAWlD,EAAI2F,aAAaG,IAAI3C,KAAM0C,WAAY7F,EAAI2F,aAAaG,IAAID,gBAAiB,CAACzF,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuBA,EAAI2F,aAAaG,IAAI/C,KAAK,KAAO,eAAe3C,EAAG,MAAM,CAACU,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI6C,YAAY,CAAEG,cAAe,GAAGhD,EAAI2F,aAAaI,MAAM9C,SAAUC,UAAWlD,EAAI2F,aAAaI,MAAM5C,KAAM0C,WAAY7F,EAAI2F,aAAaI,MAAMF,gBAAiB,CAACzF,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuBA,EAAI2F,aAAaI,MAAMhD,KAAK,KAAO,qBAAqB3C,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmBJ,MAAM,CAAC,GAAK,mBAAmB,CAACF,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgBV,EAAIwB,GAAIxB,EAAkB,gBAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKC,cAAcpB,MAAM,CAAC,GAAM,UAAamB,EAAkB,eAAIX,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI2B,WAAWF,EAAM,KAAMP,MAAW,CAACd,EAAG,IAAI,CAACwB,MAAM,CAAC,OAAU5B,EAAI6B,kBAAoBJ,EAAKC,eAAepB,MAAM,CAAC,KAAO,iBAAiB,CAACN,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,SAAS6B,EAAG,OAAO,CAACJ,EAAIa,GAAG,YAAW,QAASb,EAAIgG,gBAAgBrJ,QAAUqD,EAAIiG,SAAU7F,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,QAAsB,IAAM,QAAQF,EAAG,MAAM,CAACM,YAAY,qBAAqBV,EAAIwB,GAAIxB,EAAmB,iBAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKuD,UAAUtE,YAAY,gBAAgBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIiF,UAAUxD,MAAS,CAACrB,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAMN,EAAI2C,GAAG,eAAP3C,CAAuByB,EAAKyE,iBAAiB,KAAO,eAAe9F,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAAEe,EAAc,UAAErB,EAAG,OAAO,CAACJ,EAAIa,GAAG,QAAQb,EAAIsB,KAAKtB,EAAIa,GAAGb,EAAIgB,GAAGS,EAAK0E,oBAAoB/F,EAAG,MAAM,CAACM,YAAY,sBAAsBV,EAAIwB,GAAIC,EAAc,WAAE,SAAS2E,EAAMC,GAAM,OAAOjG,EAAG,OAAO,CAACd,IAAI+G,GAAM,CAACrG,EAAIa,GAAGb,EAAIgB,GAAGoF,SAAY,GAAGhG,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,IAAI,CAACA,EAAG,OAAO,CAACJ,EAAIa,GAAG,OAAOb,EAAIa,GAAGb,EAAIgB,GAAGhB,EAAI2C,GAAG,aAAP3C,CAAqByB,EAAK6E,gBAAgBlG,EAAG,SAAS,CAACwB,MAAM,CAAC,SAA8B,OAAlBH,EAAK8E,WAAoB,CAACvG,EAAIa,GAAGb,EAAIgB,GAAGS,EAAK8E,eAAenG,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAoB,QAAjBS,EAAK+E,SAAmB/E,EAAK+E,SAAS/E,EAAK+E,SAAS,UAA6B,SAAlB/E,EAAK+E,SAAqBpG,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKgF,UAAU,QAAQzG,EAAIsB,YAAW,KAAKlB,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,SAASoI,QAAQ,WAAW3H,MAAOgB,EAAa,UAAEuC,WAAW,cAAc7B,YAAY,eAAe,CAAGV,EAAIiG,SAAwE7F,EAAG,MAAM,CAACJ,EAAIa,GAAG,WAA/ET,EAAG,cAAc,CAACE,MAAM,CAAC,KAAO,UAAU,CAACN,EAAIa,GAAG,aAAyC,MAAM,GAAGT,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAe,YAAEuC,WAAW,gBAAgB7B,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQlB,EAAI4G,aAAc,MAAUxG,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAe,YAAEuC,WAAW,gBAAgB7B,YAAY,kBAAkBC,MAAM,CAAE,IAAOX,EAAI6G,YAAa/F,GAAG,CAAC,MAAQ,SAASI,GAAQlB,EAAI4G,aAAc,KAAS,CAACxG,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,kBAAkB,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,iBAAiBV,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,cAAc,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,eAAeV,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,iBAAiB,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,aAAaV,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,WAAW,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,cAAcV,EAAIa,GAAG,SAAST,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,kEAAkE,GAAM,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,eAAeV,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,OAAOI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoD,OAAO,2DAA2D,GAAO,MAAS,CAAChD,EAAG,IAAI,CAACM,YAAY,aAAaV,EAAIa,GAAG,YAAYT,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAa,UAAEuC,WAAW,cAAc7B,YAAY,SAASI,GAAG,CAAC,MAAQd,EAAI8G,UAAU,CAAC1G,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,gBAChoY,EAAkB,G,4ICDlB,EAAS,WAAa,IAAIN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACV,EAAI+G,GAAG,GAAI/G,EAAQ,KAAEI,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACV,EAAIa,GAAGb,EAAIgB,GAAGhB,EAAIgH,SAAShH,EAAIsB,QAC9N,EAAkB,CAAC,WAAa,IAAItB,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,WAAW,CAACN,EAAG,MAAM,CAACM,YAAY,UAAUN,EAAG,MAAM,CAACM,YAAY,UAAUN,EAAG,MAAM,CAACM,YAAY,UAAUN,EAAG,MAAM,CAACM,YAAY,UAAUN,EAAG,MAAM,CAACM,YAAY,cCalS,GACEnC,KAAM,iBACN0I,MAAO,CAAC,SChBoV,ICQ1V,G,UAAY,eACd,EACA,EACA,GACA,EACA,KACA,WACA,OAIa,I,kGCdTC,EAASC,EAAQ,QAEjBC,EAAW,IAAIC,OACrBD,EAASE,kBAGTC,OAAIC,IAAIC,QAER,ICRIC,EDQEC,EAAkB,CACtBC,OAAQ,iBACRC,WAAY,GACZC,SAAU,KACVC,OAAQ,IACRC,KAAM,KACNC,WAAY,KACZC,SAAU,MACVC,WAAY,SACZC,SAAU,OAKVC,SAAU,WACVC,UAAW,aAGE,MAAIb,OAAKc,MAAM,CAC5BC,MAAO,CACLH,SAAUV,EAAgBU,SAC1BC,UAAWX,EAAgBW,UAC3BT,WAAYF,EAAgBE,WAC5BE,OAAQJ,EAAgBI,OACxBE,WAAYN,EAAgBM,WAC5BE,WAAYR,EAAgBQ,WAC5BM,aAAcd,EACde,aAAc,GACdC,cAAeC,aAAaC,QAAQ,kBAAoB,IAE1DC,QAAS,CACPC,gBAAiB,SAAAP,GAAK,OAAIA,EAAMC,cAChCO,iBAAkB,SAAAR,GAAK,OAAIA,EAAMG,eACjCM,WAAY,SAAAT,GAAK,MAAK,CAACH,SAAUG,EAAMH,SAAUC,UAAWE,EAAMF,aAEpEY,UAAW,CACTC,gBAAiB,SAACX,EAAME,GAAP,OAAwBF,EAAME,aAAeA,GAC9DU,WAAY,SAACZ,EAAOa,GAClBb,EAAMH,SAAWgB,EAAShB,SAC1BG,EAAMF,UAAYe,EAASf,WAE7BgB,iBAAkB,SAACd,EAAOe,GACxB,IAAIC,EAAUZ,aAAaC,QAAQ,iBAC/BY,EAAaD,EAAUA,EAAQE,MAAM,KAAO,GAChDD,EAAWE,QAAQJ,GACnBE,EAAa,eAAI,IAAIG,IAAIH,IACzBA,EAAW9M,OAAS,KAAK8M,EAAaA,EAAW5L,OAAO,EAAE,KAC1D2K,EAAMG,cAAgBc,EAAWI,KAAK,KACtCjB,aAAakB,QAAQ,gBAAgBL,EAAWI,KAAK,OAEvDE,oBAAqB,SAAAvB,GACnBA,EAAMG,cAAgB,GACtBC,aAAakB,QAAQ,gBAAgB,MAGzCE,QAAS,CACPC,WADO,YACa,IAARC,EAAQ,EAARA,OACV,OAAO,IAAIC,SAAQ,SAACC,GAClBhD,EAASE,kBAAkB+C,MAAK,WAC9BjD,EAASkD,iBAAiBC,cAAcF,MAAK,SAAAG,GAC3C,GAAIA,EAAIC,QAAS,CACf,IAAInC,EAAYkC,EAAIC,QAAQnC,UACxBD,EAAWmC,EAAIC,QAAQpC,SAC3BqC,QAAQC,IAAI,iBAAkBtC,EAAUC,GACxC4B,EAAO,aAAc,CAAE7B,WAAUC,cACjC8B,EAAQ,CAAE/B,WAAUC,mBAEpB8B,EAAQ,CACN/B,SAAUV,EAAgBU,SAC1BC,UAAWX,EAAgBW,eAG9BsC,OAAM,WACPR,EAAQ,CACN/B,SAAUV,EAAgBU,SAC1BC,UAAWX,EAAgBW,sBAM/BuC,aA1BC,sEA0B0BC,GA1B1B,gGA0BcC,EA1Bd,EA0BcA,SAEfC,EAAuK9D,EAAO+D,KAAO,yEAA2EC,mBAAmB,wBA5BlR,kBA6BEC,IAAMvM,IAAIoM,EAAK,CACpBI,QAAS,CAAE,cAAiBN,KAC3BT,MAAK,YAAc,IAAXlO,EAAW,EAAXA,KACT,GAAIA,EAAKkP,QAAS,CAChB,IAAIC,EAAMnP,EAAKA,KAAKmP,IAEpB,OAAOP,EAAS,YAAa,CAAED,UAASQ,QAExC,OAAO,QArCN,sGAyCDC,UAzCC,6KAyCWrB,EAzCX,EAyCWA,OAAYY,EAzCvB,EAyCuBA,QAASQ,EAzChC,EAyCgCA,IAEjCN,EAA0F9D,EAAO+D,KAAM,yCA3CtG,kBA4CEE,IAAMK,KAAKR,EAAKS,IAAGC,UAAU,CAAEC,MAAOL,IAAQ,CACnDF,QAAS,CACP,cAAiBN,EACjB,eAAgB,uCAEjBT,MAAK,YAAc,IAAXlO,EAAW,EAAXA,KAET,GAAIA,EAAKkP,QAAS,CAChB,IAAI7N,EAASrB,EAAKA,KACdyP,EAAMpO,EAAOoO,IAIblD,EAAelL,EAAOkL,aAEtBmD,EAAW,GAkBf,OAjBAA,EAASC,SAAWtO,EAAOuO,SAC3BF,EAASG,YAAcxO,EAAOyO,kBAC9BJ,EAASK,UAAY1O,EAAO0O,UAC5BL,EAASM,SAAW3O,EAAO2O,SAC3BN,EAASO,MAAQ5O,EAAO6O,OACxBR,EAASS,MAAQ9O,EAAO8O,MACxBT,EAASU,WAAa/O,EAAO+O,WAC7BV,EAASW,OAAShP,EAAOgP,OACzBX,EAASY,UAAYjP,EAAOiP,UAC5BZ,EAASa,cAAgBlP,EAAOkP,cAChCb,EAASc,SAAWnP,EAAOmP,SAC3Bd,EAASe,SAAWpP,EAAOoP,SAC3Bf,EAASgB,cAAgBrP,EAAOqP,cAChChB,EAASiB,WAAatP,EAAOsP,WAE7B5C,EAAO,SAAU0B,GACjB1B,EAAO,kBAAmBxB,GACnBlL,EAEP,OAAO,QA/EN,yGCjEL0J,EAASC,EAAQ,QAuBvBgE,IAAM4B,SAASC,QAAU,UAAiD9F,EAAO+D,MACjFE,IAAM8B,aAAaC,QAAQ1F,KACzB,SAAUN,GAER,OADAA,EAAOkE,QAAQ+B,OAAO,sBAAwB,SAAW5E,EAAMC,MAAME,aAC9DxB,KAET,SAAUkG,GAER,OAAOjD,QAAQkD,OAAOD,MAG1B1F,EAAayD,IAMb,IAAMmC,EAAgB,kBAAM5F,EAAW9I,IAAX,0BAGtB2O,EAAY,2BACZC,EAAY,SAACC,GACjB,OAAO/F,EAAW9I,IAAI2O,EAAW,CAC/BE,YAIEC,EAAiB,SAACD,GACtB,OAAO/F,EAAW8D,KAAX,2DAAoEiC,KAIvEE,EAAiB,WACrB,OAAOjG,EAAW9I,IAAX,8CAGHgP,EAAsB,WAC1B,OAAOlG,EAAW8D,KAAK,8CAGnBqC,EAAkB,WACtB,OAAOnG,EAAW9I,IAAI,mCAGlBkP,EAAe,SAACL,GACpB,OAAO/F,EAAW9I,IAAI,2CAA4C,CAChE6O,YAIEM,EAAgB,SAACN,GACrB,OAAO/F,EAAW8D,KAAX,2CAA4DiC,IAG/DO,EAAkB,SAACP,GACvB,OAAO/F,EAAW9I,IAAI,gCAAgC,CAAC6O,YAGnDQ,EAAuB,SAACR,GAC5B,IAAIzC,EAAqF9D,EAAOgH,MAAQ,oCACxG,OAAOxG,EAAW9I,IAAIoM,EAAK,CACzByC,YAIEU,EAAuB,SAACV,GAC5B,IAAIzC,EAAkF9D,EAAOgH,MAAQ,iCACrG,OAAOxG,EAAW9I,IAAIoM,EAAI,CAACyC,YAGvBW,EAAoB,SAACX,GACzB,IAAIzC,EAAsF9D,EAAOgH,MAAQ,qCACzG,OAAOxG,EAAW9I,IAAIoM,EAAI,CAACyC,YAId,GACbH,gBACAE,YACAE,iBACAC,iBACAC,sBACAC,kBACAC,eACAC,gBACAC,kBACAC,uBACAE,uBACAC,qBChHIhH,EAAW,IAAIC,OACrBD,EAASE,kBAEF,IAAM+G,EAAc,WAAe,IAAdC,EAAc,uDAAR,KAChC,SAAUC,EAAKC,GACb,IAAIC,EAAQF,EAAIG,gBACdC,EAAY,sBAAuB9O,OAAS,oBAAsB,SAClE+O,EAAS,WACP,IAAIC,EAAcJ,EAAMI,YACpBA,IACJJ,EAAM9N,MAAMmO,SAAkBD,EAAcP,EAArB,IAA8B,OAIrDC,EAAIQ,mBACRP,EAAIO,iBAAiBJ,EAAWC,GAAQ,GACxCL,EAAIQ,iBAAiB,mBAAoBH,GAAQ,KAZnD,CAaGI,SAAUnP,SAoCFoP,EAAY,WACvB,QAAsB,qBAAXpP,SAA0BA,UAC5B,WAAWqP,KAAKrP,OAAOsP,UAAUC,YAAcvP,OAAOwP,OAAOC,QAAU,MAKrEC,EAAY,WACvB,OAAOJ,UAAUC,UAAUI,QAAQ,YAAc,GAGtCC,EAAU,kBAAMN,UAAUC,UAAUI,QAAQ,cAAgB,GAE5DE,EAAuB,SAACC,EAAMC,GACzC/P,OAAOkP,iBAAiB,UAAU,WAChCY,GAAQA,OACP,GACH9P,OAAOkP,iBAAiB,WAAW,WACjCa,GAASA,OACR,IAIQC,GAAsB,SAAC7E,GAClC5D,EAASE,kBAAkB+C,MAAK,WAC9BjD,EAAS0I,cAAcC,qBAAqB,CAC1CC,KAAM,OACNC,YAAajF,EACbkF,cAAe,KACd7F,MAAK,SAAA8F,GACNzF,QAAQC,IAAIwF,EAAI,SAChB/I,EAASgJ,YAAYC,SAAS,CAAErF,IAAKA,W,glBCkI3C,IAUA,MAVA,aAOA,cACA,qBAIA,QACEzM,KAAM,OACN+R,WAAY,CACVC,eAAJ,GAEEpU,KALF,WAMI,MAAO,CACL8E,WAAY,WACZL,UAAW,GACXiG,WAAY,GACZvE,WAAW,EACXG,WAAY,GACZK,gBAAiB,GACjB0N,UAAW,EACXnP,QAAS,GACToP,SAAU,GACVjN,aAAc,GACdU,cAAe,GACfJ,gBAAiB,EACjBF,YAAa,GACbK,aAAc,GACdyM,QAAS,CACf,CACQ,IAAR,UACQ,MAAR,OACQ,KAAR,YAEA,CACQ,IAAR,UACQ,MAAR,MACQ,KAAR,eAEA,CACQ,IAAR,UACQ,MAAR,OACQ,KAAR,mBAGA,CACQ,IAAR,UACQ,MAAR,OAEQ,KAAR,UAEA,CACQ,IAAR,UACQ,MAAR,OAEQ,KAAR,iBAGM9J,aAAa,EAEbxB,WAAY,GACZP,UAAW,EACX8L,UAAU,EACVhM,SAAU,GACViM,gBAAiB,GACjBjL,aAAc,CACZC,IAAK,GACLE,IAAK,GACLC,MAAO,IAETlE,iBAAkB,KAClBgP,eAAgB,CACtB,CACQ,cAAR,KACQ,KAAR,MACA,CACQ,cAAR,KACQ,KAAR,OACA,CACQ,cAAR,KACQ,KAAR,MACA,CACQ,cAAR,KACQ,KAAR,MACA,CACQ,cAAR,KACQ,KAAR,OACA,CACQ,cAAR,KACQ,KAAR,QACA,CACQ,cAAR,KACQ,KAAR,MACA,CACQ,cAAR,KACQ,KAAR,QACA,CACQ,cAAR,KACQ,KAAR,QACA,CACQ,cAAR,KACQ,KAAR,OACA,CACQ,cAAR,KACQ,KAAR,WAGM7K,gBAAiB,GACjB8K,UAAU,EACV7K,UAAU,EACV8K,QAAS,EACTC,SAAU,GACVxO,UAAU,EACVyO,WAAW,IAGfvK,WAAY,CACVwK,OAAQ,CACNC,SAAU,SAAhB,KACQ,IAAIC,EACJpC,SAASqC,eAAe,iBAAiBtC,iBAAiB,UAAlE,WACUqC,EAAYA,GAAwB,SAA9C,0CACA,sDACYE,EAAQtS,QAEpB,yEACYgQ,SAASqC,eAAe,iBAAiBE,UAAUC,IAAI,UACvDC,IAAgBzC,SAASqC,eAAeI,IAAcC,eAAe,CAAjF,kCACYD,GAAe,KAEfE,IAAe3C,SAASqC,eAAeM,IAAaD,eAAe,CAA/E,kCACYC,GAAc,GACd3C,SAASqC,eAAe,iBAAiBE,UAAUK,OAAO,iBAMpEC,SAAU,GAAZ,MACA,gBACI,SAAJ,qBAFA,IAIIC,WAJJ,WAKM,MAAO,CACLpP,MAAOzC,KAAKuQ,UACZnL,GAAIpF,KAAKwQ,SAASxQ,KAAKuQ,YAAcvQ,KAAKwQ,SAASxQ,KAAKuQ,WAAWnL,GACnE0M,IAAK9R,KAAKwQ,SAASxQ,KAAKuQ,YAAcvQ,KAAKwQ,SAASxQ,KAAKuQ,WAAWjN,MACpEyO,IAAK/R,KAAKwQ,SAASxQ,KAAKuQ,YAAcvQ,KAAKwQ,SAASxQ,KAAKuQ,WAAWyB,QACpEpM,WAAY5F,KAAKwQ,SAASxQ,KAAKuQ,YAAcvQ,KAAKwQ,SAASxQ,KAAKuQ,WAAW3K,eAIjFqM,QAjJF,WAiJA,WACA,KAQMjS,KAAKW,UAAY,SACjBX,KAAK4G,WAAa,WACxB,KACM5G,KAAKW,UAAY,SACjBX,KAAK4G,WAAa,YAElB5G,KAAKW,UAAY,QACjBX,KAAK4G,WAAa,WAGxB,KACM5G,KAAK6B,YACL7B,KAAKkS,oBACL,GAAN,mCAEQ,GAAR,mCACUzH,QAAQC,IAAI,QACZ,EAAV,sBACA,YAEQ,GAAR,sDACUD,QAAQC,IAAI,aAAtB,GACA,aACY,EAAZ,YACY,EAAZ,YACY,EAAZ,YACY,EAAZ,YACY,EAAZ,sBAEY,EAAZ,YACY,EAAZ,YACY,EAAZ,eAEA,iBAIM+E,GAAqB,WACnB,EAAR,YACQ,EAAR,YACQ,EAAR,YACQ,EAAR,YACQ,EAAR,uBACA,WACQ,EAAR,YACQ,EAAR,YACQ,EAAR,eAEMzP,KAAK6B,aAGPkN,SAASqC,eAAe,iBAAiBtC,iBAAiB,UAAU,SAAxE,GACA,uBACQ,EAAR,aAEQ,EAAR,iBAKEqD,QAAS,CAIPC,WAJJ,SAIA,kEACA,8CACM,OAAOC,GAKTC,aAXJ,SAWA,GAIM,OAHIvH,IAAiC,IAA1BA,EAAIwE,QAAQ,WACrBxE,EAAMA,EAAIwH,QAAQ,QAAS,WAEtBxH,IAGXyH,QAAS,CAEPC,cAFJ,WAGM,OAAO,IAAIvI,SAAQ,SAAzB,GACQ,GAAR,iDACA,oBACYC,EAAQ5M,EAAOiN,SAEfL,EAAQ,QAEpB,kBACUA,EAAQ,YAMd+H,kBAjBJ,WAiBA,WACMlS,KAAK0S,eAAetI,MAAK,SAA/B,GACA,EACU,EAAV,oDACA,QACYuI,EAAYtF,gBAAgBjD,MAAK,SAA7C,gBACA,SACc,EAAd,2BAIU,EAAV,eAKIwI,UAjCJ,SAiCA,cACA,+BACM5S,KAAK6Q,UAAW,EAChB8B,EAAYzE,qBAAqB,CAC/B2E,QAAS,EACTpR,cAAezB,KAAK4B,iBACpBgG,WAAY5H,KAAKoJ,SAASxB,WAC1BE,OAAQ9H,KAAKoJ,SAAStB,OACtBE,WAAYhI,KAAKoJ,SAASpB,WAC1B8K,SAAU9S,KAAKoJ,SAASlB,WACxB6K,SAAU,EACVC,SAAU,GACVxH,GAAI,YACJyH,WAAY,GACZC,UAAWlT,KAAK8Q,QAChBC,SAAU/Q,KAAK+Q,WACvB,kBACQ,IAAR,SACQ,GAAR,WACU,IAAV,qBACA,iBACA,YACY4B,EAAYxE,kBAAkB,CAA1C,2CACc,GAAd,WACgB,IAAhB,aACgBgF,EAAYC,SAAQ,SAApC,GACkBC,EAASD,SAAQ,SAAnC,GACA,eACsB5R,EAAK6E,WAAahC,EAAKgC,WACvB7E,EAAK8R,WAAajP,EAAKiP,kBAI7C,0BAEgB,EAAhB,4CACgB,EAAhB,YACgB,EAAhB,WAEA,sBACkB,EAAlB,kBAKA,0BACY,EAAZ,iBAGA,kBACQ,EAAR,iBAKIxP,cAxFJ,SAwFA,KACM9D,KAAKiE,cAAgBsP,EACrBvT,KAAK6D,gBAAkBpB,EACvBzC,KAAKwT,MAAMC,UAAUC,QAAQjR,GACnC,KACQsM,SAASqC,eAAe,QAAhC,gFAGIjN,aAhGJ,SAgGA,GAEMnE,KAAK6D,gBAAkBpB,EACvB,IAAN,4BACMgI,QAAQC,IAAI,QAAlB,WACMqE,SAASqC,eAAe,QAA9B,gFAGI1P,WAxGJ,SAwGA,OACM1B,KAAK4B,iBAAmBJ,EAAKC,cAC7BzB,KAAK6Q,UAAW,EAChB7Q,KAAKgG,UAAW,EAChBhG,KAAK8Q,QAAU,EACf9Q,KAAK4S,WAAU,GACrB,GACQ7D,SAASqC,eAAe,kBAAkBK,iBAE5CkC,EAAGC,OAAOnC,eAAe,CAA/B,oDACMD,GAAe,WAArB,gBACME,GAAc,UAApB,iBAEImC,cArHJ,WAqHA,WACMC,YAAW,WACL,EAAZ,gCACU,EAAV,YAEU,EAAV,aAEQ,EAAR,kBACA,MAEI5S,iBA/HJ,SA+HA,GACMuJ,QAAQC,IAAIK,GACZ6E,GAAoB7E,IAEtBlJ,UAnIJ,WAmIA,WACA,eACQkS,QAAS,IACTnM,WAAY5H,KAAKoJ,SAASxB,WAC1BE,OAAQ9H,KAAKoJ,SAAStB,OACtBkM,SAAUhU,KAAKoJ,SAASpB,WACxBiM,OAAQjU,KAAKoJ,SAASlB,aAC9B,kBAEQ,OADA,EAAR,aACegC,QAAQC,QAAQ,MAG/B,wCACQM,QAAQC,IAAIH,EAApB,kBACQ,IAAR,SAGQ,OAFA,EAAR,oBACQ,EAAR,gBACeL,QAAQC,QAAQ,MAG/B,oDACA,wBACQ5M,EAAO6V,SAAQ,SAAvB,GACU5R,EAAK0S,MAAQ1S,EAAK0S,MAAMtW,OAAO,EAAzC,GACU,EAAV,iCAEQ,EAAR,cACQ,EAAR,yBACQ6M,QAAQC,IAAI,EAApB,2BACQD,QAAQC,IAAI,EAApB,iBAGA,4CACQD,QAAQC,IAAIH,EAAK,YACjB,IAAR,SAEQ,OADA,EAAR,gCACeL,QAAQC,QAAQ,MAG/B,wCACQ,IAAR,SAIQ,OAHR,YACU,EAAV,yCAEeD,QAAQC,QAAQ,MAG/B,sFAEQ,OADAM,QAAQC,IAAItC,EAApB,wBACeuK,EAAY7E,cAAc,CAC/B1F,SAAV,EACUC,UAAV,IACA,kBACU,IAAV,SAGU,OADA,EAAV,kBACiB6B,QAAQC,QAAQ,SAajC,qBACQvC,WAAY5H,KAAKoJ,SAASxB,WAC1BE,OAAQ9H,KAAKoJ,SAAStB,OACtBkM,SAAUhU,KAAKoJ,SAASpB,WACxBiM,OAAQjU,KAAKoJ,SAASlB,aAC9B,kBACQ,IAAR,SACQ,GAAR,UAIU,OAHA,EAAV,6CACU,EAAV,6CACU,EAAV,+CACiBgC,QAAQC,QAAQ,MAIjC,kBACQvC,WAAY5H,KAAKoJ,SAASxB,WAC1BE,OAAQ9H,KAAKoJ,SAAStB,OACtBE,WAAYhI,KAAKoJ,SAASpB,WAC1B8K,SAAU9S,KAAKoJ,SAASlB,aAChC,kBAEQ,OADA,EAAR,gBACegC,QAAQC,QAAQ,MAG/B,yDAEQ,OADA,EAAR,oBACeD,QAAQC,QAAQ,MAGzBD,QAAQiK,KAAK,CAACC,EAAQC,EAAS1Q,EAAa2Q,EAAUC,EAAcC,EAAQ9O,EAAc+O,EAAWC,IAAgBtK,MAAK,SAAhI,GACQ,IAAR,yBACUuK,aAAaC,GACb,EAAV,eACA,KACQ,EAAR,YACQnK,QAAQC,IAAImK,EAApB,sBACA,kBACQ,EAAR,iBAGI/T,SAjPJ,SAiPA,GAWA,eACQd,KAAKkB,iBAAiB,GAA9B,4IAGQlB,KAAKkB,iBAAiB,GAA9B,sFAGIC,eAnQJ,WAmQA,WACMsJ,QAAQC,IAAI,GAAlB,8EAEM1K,KAAK0S,eAAetI,MAAK,SAA/B,GAEA,EACU,EAAV,kGAEU,GAAV,mCACY,GAAZ,+BACcW,IAAK,0BACnB,mBACkBR,EAAIC,QAAQsK,WAAavK,EAAIC,QAAQuK,eACvC,EAAhB,qGAEA,6BAQIzT,UA1RJ,WA2RM,GAAN,mCACQ,GAAR,cACA,sBACU,WAAV,iBACU,WAAV,QAGMtB,KAAK2G,aAAe3G,KAAK2G,aAK3B4G,UAvSJ,SAuSA,SACA,SADA,EACA,OADA,EACA,QACUnC,IACFpL,KAAKwC,WAAatG,IAGtB8Y,iBA7SJ,SA6SA,GACUzK,EAAIrO,KAAKkP,UACXpL,KAAKgB,WAAauJ,EAAIrO,KAAKA,KAAK+Y,WAMpCC,aArTJ,SAqTA,GACM,GAAI3K,EAAIrO,KAAKkP,QAAS,CAA5B,IACA,cACA,mCACA,eACA,IACA,WACQ+J,EAAUC,WAAU,SAA5B,KACU,GAAI,IAAIC,KAAK7T,EAAK8T,WAAWC,YAAc,IAAIF,KAAKG,GAAYD,WAC1DC,EAAahU,EAAKiU,SAAWD,EAAahU,EAAK8T,UAEjD,OADA7S,EAAQjG,GAAK,EAAIkZ,EAAalZ,EAAI,EAAI,GAC/B,KAIbN,EAAKyZ,MAAQzZ,EAAKyZ,KAAKC,KAAI,SAAnC,GACcJ,EAAahU,EAAKiU,SAEpBjU,EAAKqU,eAAiB,EACtBrU,EAAKkP,UAAW,GAC5B,eAEYlP,EAAKqU,eAAiB,EACtBrU,EAAKkP,UAAW,IAGhBlP,EAAKqU,eAAiB,EACtBrU,EAAKkP,UAAW,GAElBlP,EAAK8B,MAAQ9B,EAAKsU,QAclBrL,QAAQC,IAAIlJ,EAAK8B,UAEnBtD,KAAK4E,UAAYnC,EACjBzC,KAAK0Q,SAAWxU,EAAKyZ,KAAKlT,GAAOiO,SACjC1Q,KAAK0E,SAAW,OAAxB,OAAwB,CAAxB,QACQ1E,KAAK2Q,gBAAkB3Q,KAAK0E,SAAS1E,KAAK4E,WAAWmR,SACrDtL,QAAQC,IAAI1K,KAAK2Q,gBAAzB,UAGI/N,YAzWJ,SAyWA,GACM6H,QAAQC,IAAI,eAAgBlJ,GADlC,IAEA,+CACMiJ,QAAQC,IAAI,yBAA0BzH,EAAWF,GACjD,IAAN,0BACM,OAAQA,GACN,IAAK,KACH,MACF,IAAK,IAEH/C,KAAKkB,iBAAiB,GAAhC,mGACU,MACF,IAAK,IAED,IAAZ,kCAEYlB,KAAKkB,iBAAiB,GAAlC,6GAEU,MACF,IAAK,IACH,GAAK+B,EAGf,CACY,IAAZ,gCACA,gCAEYjD,KAAKkB,iBAAiB,GAAlC,uHALYlB,KAAKkB,iBAAiB,GAAlC,iFAOU,MACF,IAAK,IACHlB,KAAKkB,iBAAiB,GAAhC,qGACU,MACF,IAAK,IACH,GAAsD,SAAlD8U,EAAQ,GAAGlW,MAAMkW,EAAQ,GAAGzG,QAAQ,KAAO,GAAe,CAC5D,IAAZ,kCAEYvP,KAAKkB,iBAAiB,GAAlC,uGACA,0CAEYlB,KAAKkB,iBAAiB,GAAlC,qFAEU,MACF,IAAK,IAED,IAAZ,eACY,GAAI+U,EAAK1G,QAAQ,eAAiB,EAAG,CACnC,IAAd,cAEcvP,KAAKkB,iBAAiB,GAApC,mGACA,kCAEc,IAAd,8BAGclB,KAAKkB,iBAAiB,GAApC,wGACA,mCACc,IAAd,+DAOclB,KAAKkB,iBAAiB,GAApC,8GACA,mCAUclB,KAAKkB,iBAAiB,GAApC,WAGU,MACF,IAAK,IACH,GAAI+B,EAAW,CACb,IAAZ,IAEYjD,KAAKkB,iBAAiB,GAAlC,6GAEU,MACF,IAAK,IACH,QAQNiC,OAxcJ,SAwcA,kBACM,IAAN,6BAK6B,IAAvBC,EAAKmM,QAAQ,KAAcnM,GAAQ,+CAAiDA,GAAQ,+CAElG,GACQqH,QAAQC,IAAIwL,EAAS9S,EAAO,GAApC,uCACQpD,KAAK0S,eAAetI,MAAK,SAAjC,GAEA,EACY,EAAZ,+DAEY,GAAZ,mCACc,GAAd,+BACgBW,IAAK,0BACrB,mBACoBR,EAAIC,QAAQsK,WAAavK,EAAIC,QAAQuK,eACvC,EAAlB,kEAEA,6BAOQ/U,KAAKkB,iBAAiBgV,EAAS9S,EAAO,GAA9C,2CA5BM,CACE,IAAR,kBACQpD,KAAKkB,iBAAiB0S,KA+B1BjP,WA1eJ,WA4eM3E,KAAKkB,iBAAiB,GAA5B,oFAGImE,SA/eJ,SA+eA,GACMrF,KAAKkB,iBAAiB,GAA5B,gHAEI8D,UAlfJ,SAkfA,GAEMhF,KAAKkB,iBAAiB,GAA5B,wHAOI2D,UA3fJ,SA2fA,KACM7E,KAAK2Q,gBAAkB,GACvB3Q,KAAK4E,UAAYnC,EAIjBzC,KAAK2Q,gBAAkB3Q,KAAK0E,SAAS1E,KAAK4E,WAAWmR,SACrDtL,QAAQC,IAAI1K,KAAK2Q,gBAAvB,WAII+B,aAtgBJ,WAugBM,OAAO,IAAIxI,SAAQ,SAAzB,GACQ,GAAR,mCAGU,GAAV,6CAEgBK,EAAIC,QAAQ2L,yBACdhM,EAAQI,EAAIC,QAAQ2L,0BAEpBhM,GAAQ,MAEtB,kBACYA,GAAQ,aAMhBtD,QAzhBJ,WA0hBM7G,KAAKoW,eAAerH,SAASqC,eAAe,iBAAlD,IAEIgF,eA5hBJ,SA4hBA,KACWxW,OAAOyW,wBACV,OAAR,kCACU,OAAV,mBAIM,IAAN,cAEA,eAEQ,IAAR,MAEQ,GAAR,IACA,cACU,EAAV,eAEU,EAAV,cACU,sBAAV,KAGMC,OC7/BwV,MCS1V,I,oBAAY,eACd,GACA,EACA,GACA,EACA,KACA,KACA,OAIa,M,QCpBX,GAAS,WAAa,IAAIvW,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgBC,MAAM,CAAE,IAAOX,EAAIY,YAAa,CAACR,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,cAAcI,GAAG,CAAC,MAAQd,EAAIwW,SAAS,CAACpW,EAAG,WAAW,CAACE,MAAM,CAAC,KAAO,aAAa,KAAO,YAAY,GAAGF,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,QAAuC,IAAM,MAAMF,EAAG,QAAQ,CAACsG,WAAW,CAAC,CAACnI,KAAK,QAAQoI,QAAQ,UAAU3H,MAAOgB,EAAW,QAAEuC,WAAW,YAAYjC,MAAM,CAAC,KAAO,OAAO,YAAcN,EAAIyW,aAAaC,SAAS,CAAC,MAAS1W,EAAW,SAAGc,GAAG,CAAC,MAAQ,CAAC,SAASI,GAAWA,EAAO2S,OAAO8C,YAAqB3W,EAAIuJ,QAAQrI,EAAO2S,OAAO7U,QAAO,SAASkC,GAAQ,OAAOlB,EAAI4W,YAAY1V,KAAU,MAAQlB,EAAI6W,cAAczW,EAAG,WAAW,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAS,MAAEuC,WAAW,UAAUjC,MAAM,CAAC,KAAO,QAAQ,KAAO,QAAQ,MAAQ,QAAQQ,GAAG,CAAC,MAAQd,EAAI8W,YAAY,GAAG1W,EAAG,MAAM,CAACM,YAAY,aAAaI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI+W,SAAS/W,EAAIuJ,YAAY,CAACvJ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,cAAcV,EAAIwB,GAAIxB,EAAW,SAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKuO,KAAKpO,MAAM,CAAC,OAAUH,EAAKuO,MAAQhQ,EAAIgX,QAAQlW,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI8E,UAAUrD,EAAKuO,SAAS,CAAC5P,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,cAAa,KAAOyB,EAAIiX,UAAmhB7W,EAAG,MAAM,CAACM,YAAY,cAAcC,MAAM,CAAE,IAAOuW,WAAWlX,EAAIY,WAAa,KAAO,QAAS,CAAmB,GAAjBZ,EAAIiX,UAAgB7W,EAAG,MAAM,CAACM,YAAY,yBAAyB,CAACN,EAAG,YAAY,CAACU,GAAG,CAAC,OAASd,EAAI+W,YAAY3W,EAAG,eAAe,CAACU,GAAG,CAAC,OAASd,EAAI+W,aAAa,GAAG3W,EAAG,MAAM,CAACM,YAAY,yBAAyB,CAAmB,GAAjBV,EAAIiX,UAAgB7W,EAAG,cAAc,CAAC+D,IAAI,gBAAgBrD,GAAG,CAAC,OAAS,SAASI,GAAQlB,EAAImX,YAAa,MAASnX,EAAIsB,KAAuB,GAAjBtB,EAAIiX,UAAgB7W,EAAG,YAAYJ,EAAIsB,MAAM,KAAx+BlB,EAAG,MAAM,CAACM,YAAY,aAAaV,EAAIwB,GAAIxB,EAAiB,eAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAMhC,YAAY,iBAAiBI,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIoX,YAAY3V,MAAS,CAACrB,EAAG,MAAM,CAACsW,SAAS,CAAC,UAAY1W,EAAIgB,GAAGhB,EAAIqX,WAAW5V,EAAKnC,SAAUmC,EAAK6V,IAAU,OAAElX,EAAG,MAAMJ,EAAIwB,GAAIC,EAAQ,KAAE,SAAS6V,EAAI7a,GAAG,OAAO2D,EAAG,OAAO,CAACd,IAAI7C,EAAEiE,YAAY,qBAAqBI,GAAG,CAAC,MAAQ,SAASI,GAAiC,OAAzBA,EAAOqW,kBAAyBvX,EAAIoX,YAAYE,MAAQ,CAACtX,EAAIa,GAAGb,EAAIgB,GAAGsW,EAAIE,WAAU,GAAGxX,EAAIsB,UAAS,GAAwelB,EAAG,aAAa,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAc,WAAEuC,WAAW,eAAe5B,MAAM,CAAE,IAAOX,EAAIY,WAAYE,GAAG,CAAC,MAAQd,EAAIyX,YAAY,KAAOzX,EAAI0X,eAAe,IAC1jF,GAAkB,GCDlB,I,UAAS,WAAa,IAAI1X,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAQF,EAAI2X,QAAc,OAAEvX,EAAG,MAAM,CAACM,YAAY,cAAc,CAACN,EAAG,KAAK,CAACJ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,YAAYV,EAAIwB,GAAIxB,EAAW,SAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAM5B,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI+W,SAAStV,MAAS,CAACrB,EAAG,OAAO,CAACJ,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKyT,kBAAiB,KAAKlV,EAAIsB,OAC1Y,GAAkB,G,glBCCtB,IAAM4F,GAASC,EAAQ,QAGjBC,GAAW,IAAIC,OACrBD,GAASE,kBAEM,ICHXI,GDGW,IACb9I,IADa,SACToM,EAAI7O,EAAKiP,GACX,OAAO,IAAIjB,SAAQ,SAACC,EAAQiD,GAC1BjG,GAASE,kBAAkB+C,MAAK,WAC9B,IAAIuN,EAAYzb,EAAO6O,EAAM,IAAM6M,mBAAmBpM,IAAGC,UAAUvP,EAAKsR,SAAWzC,GAClD,GAA9B4M,EAAUpI,QAAQ,UACnBoI,EAAY1Q,GAAO+D,KAAO2M,GAE5BlN,QAAQC,IAAIiN,EAAU,aACtBxQ,GAAS0Q,aAAalZ,IAAI,CACxBoM,IAAK4M,EACLxM,QAAS,SACJA,GADE,IAEL2M,mBAAoB,SAAWxP,EAAMC,MAAME,iBAE5C2B,MAAK,SAAC7M,GACP,IAAIrB,EAAO,CACT+K,OAAQ,CAAC8D,MAAIgN,OAAQ,MAAO5M,WAC5BjP,KAAM,KACNiP,QAAS,MAEW,WAAnB5N,EAAOya,SACR9b,EAAKiP,QAAU5N,EAAOiN,QAAQW,QAC9BjP,EAAKA,KAAOqB,EAAOiN,QAAQtO,KAC3BA,EAAK+b,OAAS,IACd/b,EAAKgc,WAAa,KAClB/N,EAAQjO,IAERiO,EAAQjO,MAETyO,OAAM,SAACwN,GACR/K,EAAO+K,aAKf5M,KApCa,SAoCRR,GAAgD,IAA3C7O,EAA2C,uDAApC,GAAIiP,EAAgC,uDAAtB,GAAIiN,EAAkB,wDACnD,OAAO,IAAIlO,SAAQ,SAACC,EAAQiD,GAC1BjG,GAASE,kBAAkB+C,MAAK,WAC9BK,QAAQC,IAAI,QAAUK,EAAMsN,KAAK5M,UAAUvP,KAChB,GAAxB6O,EAAIwE,QAAQ,UACbxE,EAAM9D,GAAO+D,KAAOD,GAEtB5D,GAAS0Q,aAAatM,KAAK,CACzBR,MACAI,QAAS,SACJA,GADE,IAEL+B,OAAQ,CACN4K,mBAAoB,SAAWxP,EAAMC,MAAME,gBAG/CvM,OACAkc,cACChO,MAAK,SAAC7M,GACP,IAAIrB,EAAO,CACT+K,OAAQ,CAAC8D,MAAIgN,OAAQ,OAAQ5M,WAC7BjP,KAAM,KACNiP,QAAS,MAEW,WAAnB5N,EAAOya,SACR9b,EAAKiP,QAAU5N,EAAOiN,QAAQW,QAC9BjP,EAAKA,KAAOqB,EAAOiN,QAAQtO,KAC3BA,EAAK+b,OAAS,IACd/b,EAAKgc,WAAa,KAClB/N,EAAQjO,IAERiO,EAAQjO,MAETyO,OAAM,SAACwN,GACR/K,EAAO+K,cC1EXlR,GAASC,EAAQ,QAIpBgI,UAAUC,UAAUI,QAAQ,cAAgB,EAC7C9H,GAAa6Q,IAEbpN,IAAM4B,SAASC,QAAU,UAAiD9F,GAAO+D,MAEjFE,IAAM8B,aAAaC,QAAQ1F,KACzB,SAAUN,GAER,OADAA,EAAOkE,QAAQ+B,OAAO,sBAAwB,SAAW5E,EAAMC,MAAME,aAC9DxB,KAET,SAAUkG,GAER,OAAOjD,QAAQkD,OAAOD,MAG1B1F,GAAayD,KAMAzD,UC3BTR,GAASC,EAAQ,QAKjBwN,GAAgB,SAAClH,GACrB,IAAIzC,EAAqF9D,GAAOgH,MAAQ,oCACxG,OAAOxG,GAAW9I,IAAIoM,EAAK,CACzByC,YAKE+K,GAAY,SAAC/K,GACjB,IAAIzC,EAAiF9D,GAAOgH,MAC5F,OAAOxG,GAAW9I,IAAIoM,EAAI,CAACyC,YAIvBgL,GAAiB,SAAChL,GACtB,IAAIzC,EAA+E9D,GAAOgH,MAC1F,OAAOxG,GAAW9I,IAAIoM,EAAI,CAACyC,YAId,IACbkH,iBACA6D,aACAC,mBChBF,aAEA,IACEla,KAAM,YACNpC,KAFF,WAGI,MAAO,CACLwb,QAAS,KAGbzF,QAPF,WAOA,WACIwG,GAAcF,UAAU,CAA5B,4CACM,EAAN,mBAGE/F,QAAF,CACIsE,SADJ,SACA,GAC4B,MAAlBtV,EAAKwB,SAEP4M,GAAoB,GAA5B,iFAOA,iBACQA,GAAoB,GAA5B,qFAEA,iBACQA,GAAoB,GAA5B,yEAIQ5P,KAAK0Y,MAAM,SAAUlX,EAAKyT,aC9C+U,MCQ7W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAIlV,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,KAAK,CAACJ,EAAIa,GAAG,UAAWb,EAAI4Y,WAAiB,OAAExY,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,EAAQ,QAAqC,IAAM,IAAIQ,GAAG,CAAC,MAAQd,EAAI6Y,iBAAiB7Y,EAAIsB,OAAQtB,EAAI4Y,WAAiB,OAAExY,EAAG,MAAM,CAACM,YAAY,eAAeV,EAAIwB,GAAIxB,EAAc,YAAE,SAASyB,EAAKiB,GAAO,OAAOtC,EAAG,MAAM,CAACd,IAAIoD,EAAM5B,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI+W,SAAStV,MAAS,CAACzB,EAAIa,GAAGb,EAAIgB,GAAGS,SAAW,GAAGrB,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,2EAA2E,IAAM,MAAMF,EAAG,IAAI,CAACJ,EAAIa,GAAG,iBACxtB,GAAkB,G,glBCkBtB,QACEtC,KAAM,eACNpC,KAFF,WAGI,MAAO,CACLyc,WAAY,KAGhB1G,QAPF,WAQI,IAAJ,0BACA,IACMxH,QAAQC,IAAImO,EAAlB,aACM7Y,KAAK2Y,WAAaE,EAAUpP,MAAM,OAGtC+I,QAAS,GAAX,SACA,sCACA,yCAFA,IAGIoG,cAHJ,WAGA,WACM5Y,KAAK8Y,QAAQC,QAAQ,CACnB1E,QAAS,iBACjB,iBACQ,EAAR,sBACQ,EAAR,iBACA,uBAIIyC,SAbJ,SAaA,GACM9W,KAAK0Y,MAAM,SAAjB,OC/CoX,MCQhX,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAI3Y,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,cAAcI,GAAG,CAAC,MAAQd,EAAIyX,cAAc,CAACrX,EAAG,MAAM,CAACM,YAAY,mBAAmBI,GAAG,CAAC,MAAQ,SAASI,GAAQA,EAAOqW,qBAAsB,CAACnX,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,WAAW,CAACM,YAAY,eAAeJ,MAAM,CAAC,KAAO,QAAQ,KAAO,SAASQ,GAAG,CAAC,MAAQd,EAAIyX,eAAerX,EAAG,OAAO,CAACJ,EAAIa,GAAG,SAAS,GAAGT,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,gBAAgBV,EAAIwB,GAAIxB,EAAe,aAAE,SAASyB,GAAM,OAAOrB,EAAG,OAAO,CAACd,IAAImC,EAAK4D,GAAGzD,MAAM,CAAC,OAAU5B,EAAIiZ,gBAAkBxX,EAAK4D,IAAIvE,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAIkZ,kBAAkBzX,EAAK4D,OAAO,CAACrF,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,YAAW,GAAG6B,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,QAAQoI,QAAQ,YAAYjG,YAAY,4BAA4B,CAACN,EAAG,KAAK,CAACJ,EAAIa,GAAG,QAAQT,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,oBAAoBF,EAAG,MAAM,CAACM,YAAY,gBAAgB,CAACN,EAAG,QAAQ,CAACsG,WAAW,CAAC,CAACnI,KAAK,QAAQoI,QAAQ,UAAU3H,MAAOgB,EAAe,YAAEuC,WAAW,gBAAgBjC,MAAM,CAAC,YAAc,MAAM,oBAAoB,6BAA6B,KAAO,UAAUoW,SAAS,CAAC,MAAS1W,EAAe,aAAGc,GAAG,CAAC,MAAQ,SAASI,GAAWA,EAAO2S,OAAO8C,YAAqB3W,EAAImZ,YAAYjY,EAAO2S,OAAO7U,WAAUoB,EAAG,QAAQA,EAAG,QAAQ,CAACsG,WAAW,CAAC,CAACnI,KAAK,QAAQoI,QAAQ,UAAU3H,MAAOgB,EAAgB,aAAEuC,WAAW,iBAAiBjC,MAAM,CAAC,YAAc,MAAM,oBAAoB,6BAA6B,KAAO,UAAUoW,SAAS,CAAC,MAAS1W,EAAgB,cAAGc,GAAG,CAAC,MAAQ,SAASI,GAAWA,EAAO2S,OAAO8C,YAAqB3W,EAAIoZ,aAAalY,EAAO2S,OAAO7U,aAAYoB,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,QAAQoI,QAAQ,YAAYjG,YAAY,4BAA4B,CAACN,EAAG,KAAK,CAACJ,EAAIa,GAAG,QAAQT,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,oBAAoBN,EAAI+G,GAAG,KAAK3G,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,iBAAiB,CAACN,EAAIa,GAAG,QAAQT,EAAG,IAAI,CAACM,YAAY,OAAOJ,MAAM,CAAC,KAAO,gBAAgBQ,GAAG,CAAC,MAAQd,EAAIqZ,SAAS,CAACrZ,EAAIa,GAAG,eACp9D,GAAkB,CAAC,WAAa,IAAIb,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,0BAA0B,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,UAAUT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,gBAAgBT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,kBAAkBT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,gBAAgBT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,oBCwC/f,IACEtC,KAAM,SACNpC,KAFF,WAGI,MAAO,CACL8c,eAAgB,MAChBK,YAAa,CACnB,CAAQ,GAAR,MAAQ,KAAR,UACA,CAAQ,GAAR,WAAQ,KAAR,SAEMH,YAAa,GACbC,aAAc,KAGlB1S,WAAY,CACV6S,MAAO,CACLha,KADN,SACA,GACQia,EAAGzK,iBAAiB,SAA5B,WACUyK,EAAGjI,UAAUkI,OAAO,gBAK5BhH,QAAS,CACPyG,kBADJ,SACA,GACMjZ,KAAKgZ,eAAiB5T,GAExBoS,YAJJ,WAKMxX,KAAK0Y,MAAM,UAEbU,OAPJ,WAQMpZ,KAAK0Y,MAAM,OAAQ,CACjBrG,MAAO,EACPgF,IAAK,OCzEiW,MCQ1W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAItX,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmBJ,MAAM,CAAC,GAAK,iBAAiB,CAACF,EAAG,MAAM,CAACM,YAAY,mBAAmBV,EAAIwB,GAAIxB,EAAY,UAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKf,YAAY,eAAe,CAACV,EAAI+G,GAAG,GAAE,GAAM/G,EAAI+G,GAAG,GAAE,QAAU,GAAK/G,EAAIiG,SAA8J7F,EAAG,MAAM,CAACM,YAAY,aAAa,CAACV,EAAIa,GAAG,SAA/LT,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,SAASoI,QAAQ,WAAW3H,MAAOgB,EAAa,UAAEuC,WAAW,cAAc7B,YAAY,aAAa,CAACV,EAAIa,GAAG,iBAC1gB,GAAkB,CAAC,WAAa,IAAIb,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,8EAA8E,IAAM,QAAQF,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,KAAK,CAACJ,EAAIa,GAAG,qBAAqBT,EAAG,IAAI,CAACA,EAAG,OAAO,CAACJ,EAAIa,GAAG,cAAcT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,IAAI,CAACE,MAAM,CAAC,KAAO,KAAK,CAACN,EAAIa,GAAG,aAAa,WAAa,IAAIb,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,uBAAuB,CAACN,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,uHAAuH,IAAM,MAAMF,EAAG,IAAI,CAACJ,EAAIa,GAAG,gBAAgBT,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,uHAAuH,IAAM,MAAMF,EAAG,IAAI,CAACJ,EAAIa,GAAG,gBAAgBT,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,uHAAuH,IAAM,MAAMF,EAAG,IAAI,CAACJ,EAAIa,GAAG,oBCoCjyC,IACEtC,KAAM,aACNmI,WAAY,CACVwK,OAAQ,CACNC,SADN,SACA,KACQnC,SAASqC,eAAe,gBAAgBtC,iBAAiB,UAAjE,WACA,kDACYuC,EAAQtS,cAMlB7C,KAbF,WAcI,MAAO,CACLud,SAAU,CAAC,EAAjB,iBAEMC,QAAS,EACT7I,UAAU,EACV7K,UAAU,IAGdiM,QAtBF,WAuBIjS,KAAK4S,aAEPJ,QAAS,CACPI,UADJ,WAEMnI,QAAQC,IAAI,qBChE8V,MCQ5W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCnBX,GAAS,WAAa,IAAI3K,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,eAAe,CAACN,EAAG,MAAM,CAACM,YAAY,oBAAoB,CAACN,EAAG,MAAM,CAACM,YAAY,sBAAsB,CAACV,EAAIwB,GAAIxB,EAAc,YAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAK4D,GAAG3E,YAAY,cAAckB,MAAM,CAAC,OAAUH,EAAK4D,IAAMrF,EAAI4Z,UAAW,YAAanY,EAAKoY,SAAU,GAAMpY,EAAKqY,KAAM,MAASrY,EAAKqY,MAAMhZ,GAAG,CAAC,MAAQ,SAASI,GAAQ,OAAOlB,EAAI+Z,aAAatY,MAAS,CAACzB,EAAIa,GAAGb,EAAIgB,GAAGS,EAAKlD,MAAM,UAAoByb,IAAdvY,EAAKqY,KAAoB1Z,EAAG,QAAQA,EAAG,UAASA,EAAG,QAAQA,EAAG,MAAM,CAACM,YAAY,4BAA4BI,GAAG,CAAC,MAAQd,EAAIia,WAAW,CAACja,EAAIa,GAAG,MAAMT,EAAG,SAAS,GAAGA,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,OAAOoI,QAAQ,SAAS3H,MAAOgB,EAAiB,cAAEuC,WAAW,kBAAkB7B,YAAY,uBAAuBI,GAAG,CAAC,MAAQ,SAASI,GAAiC,OAAzBA,EAAOqW,kBAAyBvX,EAAIka,mBAAmBhZ,MAAW,CAAClB,EAAI+G,GAAG,KAAK3G,EAAG,MAAM,CAACM,YAAY,sCAAsCJ,MAAM,CAAC,GAAK,iBAAiB,CAACF,EAAG,MAAM,CAACM,YAAY,kBAAkBV,EAAIwB,GAAIxB,EAAe,aAAE,SAASyB,GAAM,OAAOrB,EAAG,MAAM,CAACd,IAAImC,EAAKf,YAAY,aAAa,CAACV,EAAI+G,GAAG,GAAE,GAAM/G,EAAI+G,GAAG,GAAE,QAAU,GAAK/G,EAAIiG,SAA8J7F,EAAG,MAAM,CAACM,YAAY,aAAa,CAACV,EAAIa,GAAG,SAA/LT,EAAG,MAAM,CAACsG,WAAW,CAAC,CAACnI,KAAK,SAASoI,QAAQ,WAAW3H,MAAOgB,EAAa,UAAEuC,WAAW,cAAc7B,YAAY,aAAa,CAACV,EAAIa,GAAG,mBAC5zC,GAAkB,CAAC,WAAa,IAAIb,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,6BAA6B,CAACN,EAAG,OAAO,CAACJ,EAAIa,GAAG,UAAUT,EAAG,QAAQ,WAAa,IAAIJ,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,iBAAiB,CAACN,EAAG,MAAM,CAACE,MAAM,CAAC,IAAM,kHAAkH,IAAM,SAAS,WAAa,IAAIN,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAOE,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACN,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACV,EAAIa,GAAG,iCAAiCT,EAAG,MAAM,CAACM,YAAY,kBAAkB,CAACV,EAAIa,GAAG,mCAAmCT,EAAG,MAAM,CAACM,YAAY,mBAAmB,CAACN,EAAG,MAAM,CAACA,EAAG,IAAI,CAACA,EAAG,SAAS,CAACJ,EAAIa,GAAG,kBAAkBT,EAAG,MAAM,CAACA,EAAG,QAAQ,CAACJ,EAAIa,GAAG,gBC6Cx3B,I,UAAA,CACEtC,KAAM,aACNmI,WAAY,CACVwK,OAAQ,CACNC,SADN,SACA,KACQnC,SAASqC,eAAe,gBAAgBtC,iBAAiB,UAAjE,WACA,kDACYuC,EAAQtS,cAMlB7C,KAbF,WAcI,MAAO,CACLyd,UAAW,MACXO,WAAY,CAClB,CAAQ,GAAR,MAAQ,KAAR,MACA,CAAQ,GAAR,OAAQ,KAAR,KAAQ,UAAR,GACA,CAAQ,GAAR,MAAQ,KAAR,KAAQ,MAAR,GACA,CAAQ,GAAR,QAAQ,KAAR,KAAQ,MAAR,IAEMC,eAAe,EACfhH,YAAa,CAAC,EAApB,iBAEMuG,QAAS,EACT7I,UAAU,EACV7K,UAAU,IAGdiM,QA9BF,WA+BIjS,KAAK4S,aAEPJ,QAAS,CACPI,UADJ,SACA,GACMnI,QAAQC,IAAI,iBAAlB,IAEIoP,aAJJ,SAIA,GACA,qCACQ9Z,KAAKma,eAAgB,QAE7B,mCACQ3Y,EAAKqY,MAAQrY,EAAKqY,MAEpB7Z,KAAK2Z,UAAYnY,EAAK4D,GACtBpF,KAAK4S,aAEPoH,SAdJ,WAeMha,KAAK0Y,MAAM,WAEbuB,mBAjBJ,WAkBMja,KAAKma,eAAgB,MCjGqV,MCQ5W,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,wlBCqCf,iBAEA,IACE7b,KAAM,SACN+R,WAAF,CACIkI,UAAJ,GACI6B,aAAJ,GACIC,WAAJ,GACIZ,SAAJ,GACItG,YAAJ,IAEEjX,KATF,WAUI,MAAJ,CACMyE,UAAW,GACXqW,UAAW,EACXD,OAAQ,OACRuD,QAAS,CACf,CAAQ,KAAR,OAAQ,KAAR,MACA,CAAQ,KAAR,OAAQ,KAAR,OAEMhR,QAAS,GACTkN,YAAa,WACb+D,cAAe,GACfC,OAAO,EACPtD,YAAY,IAGhBjF,QAzBF,WAyBA,WACA,IACMjS,KAAKW,UAAY,SACvB,IACMX,KAAKW,UAAY,SAEjBX,KAAKW,UAAY,QAEnB8J,QAAQC,IAAI1K,KAAKya,OAAOC,MAA5B,gBACI,IAAJ,oBACA,UACMjQ,QAAQC,IAAIgQ,GAEZjC,GAAc/D,cAAc,CAAlC,4CAEA,YACU,EAAV,iCAKElC,QAAS,GAAX,MACA,sCADA,IAEI3N,UAFJ,SAEA,GAEM7E,KAAK+W,OAAShH,EACpB,eACA,UACU/P,KAAKgX,UAAY,EAC3B,YACUhX,KAAKgX,UAAY,GAEnBhX,KAAK8W,SAAS9W,KAAKsJ,WAIvBwN,SAfJ,SAeA,GACM9W,KAAKsJ,QAAU9H,EACfiJ,QAAQC,IAAI,WAAY1K,KAAKsJ,SACnC,eAIA,iCACQtJ,KAAK+W,OAAS,OACd/W,KAAKsJ,QAAUtJ,KAAKsJ,QAAQG,MAAM,KAAK,IAG/C,qBACQzJ,KAAKgX,UAAY,EACjBvM,QAAQC,IAAI,KAApB,gBACQ1K,KAAKqJ,iBAAiBrJ,KAAKsJ,UACnC,sBACQtJ,KAAKgX,UAAY,EACjBvM,QAAQC,IAAI,KAAM1K,KAAKgX,WACvBhX,KAAKqJ,iBAAiB,IAA9B,8BAfQrJ,KAAK2a,OAAO,YAmBhBhE,YAtCJ,SAsCA,cACA,iBACM,GAAN,GACQ3W,KAAKwa,OAAQ,EACbxa,KAAKsJ,QAAUvK,EACf,IAAR,yBACU4V,aAAaC,GACb6D,GAAcD,eAAe,CAC3BnZ,IAAKN,IACjB,+BAEA,IACA,YACc,EAAd,qBACc,EAAd,kBAGA,UAEQiB,KAAKwa,OAAQ,EACbxa,KAAKgX,UAAY,GAGrBJ,WA7DJ,WA8DA,eACQ5W,KAAKwa,OAAQ,IAGjB3D,QAlEJ,WAmEM7W,KAAKsJ,QAAU,GACftJ,KAAKwa,OAAQ,EACbxa,KAAKgX,UAAY,GAGnBG,YAxEJ,SAwEA,GACU3V,EAAKnC,KAEPW,KAAKsJ,QAAU9H,EAAKnC,IACpBW,KAAK8W,SAAS9W,KAAKsJ,UAGG,MAAlB9H,EAAKwB,SAEP4M,GAAoB,GAA9B,gIAEA,iBACUA,GAAoB,GAA9B,oIAEA,iBACUA,GAAoB,GAA9B,0EAIU5P,KAAKsJ,QAAU9H,EAAKoZ,IACpB5a,KAAK8W,SAAS9W,KAAKsJ,WAIzB8N,WAhGJ,SAgGA,GAEM,OAAOrY,EAAM0K,MAAMzJ,KAAKsJ,SAASM,KAAK,4BAA5C,iCAGI6N,WArGJ,SAqGA,GACMhN,QAAQC,IAAImQ,GACZ7a,KAAKkX,YAAa,EAClBlX,KAAK8W,SAAS9W,KAAKsJ,SACnBtJ,KAAKwT,MAAMsH,cAAclI,UAAUiI,IAErCE,SA3GJ,SA2GA,GACM/a,KAAKkX,WAAa8D,GAEpBxD,YA9GJ,WA+GMxX,KAAKkX,YAAa,GAEpBX,OAjHJ,WAkHMvW,KAAKib,QAAQC,WC1N2U,MCQ1V,I,UAAY,eACd,GACA,GACA,IACA,EACA,KACA,WACA,OAIa,M,QCdf5T,OAAIC,IAAI4T,QAEO,WAAIA,OAAO,CACxBlc,KAAM,OACNmc,KAAMC,GACNC,OAAQ,CACN,CACElY,KAAM,IACN9E,KAAM,OACNkC,UAAW+a,IAEb,CACEnY,KAAM,UACN9E,KAAM,SACNkC,UAAWgb,O,mCCRjBlU,OAAIC,IAAIkU,SAERnU,OAAIL,OAAOyU,eAAgB,EAExBlM,IACDlI,OAAI1K,UAAU+e,OAASrD,GAEvBhR,OAAI1K,UAAU+e,OAASzQ,GAGzBkD,IAEA,IAAI9G,OAAI,CACNsU,UACAC,QACAC,OAAQ,SAAAC,GAAC,OAAIA,EAAEC,MACdC,OAAO,S,8CC3BVhe,EAAOD,QAAU,IAA0B,0B,mBCA3CC,EAAOD,QAAU,8b,oFCAjB,yBAA6kB,EAAG,G,kCCAhlB,yBAA4kB,EAAG,G,gDCA/kBC,EAAOD,QAAU,IAA0B,0B,qECA3CC,EAAOD,QAAU,kN,yDCAjB,yBAA2d,EAAG,G,mBCA9dC,EAAOD,QAAU,0jG,yDCAjB,yBAA4kB,EAAG,G,mBCA/kBC,EAAOD,QAAU,8vB,qBCAjBC,EAAOD,QAAU,IAA0B,0B,mBCA3CC,EAAOD,QAAU,s/F,mBCAjBC,EAAOD,QAAU,0mE,0CCCjB,IAAIgN,EAAO,GAAIkR,EAAM,GAAIjO,EAAQ,GAAIkO,EAAQ,GAS7CD,EAAM,EAENC,GAAQ,EAER,IAAIC,EAAQ,CACV,uBACA,2BACA,gCAEFpR,EAAOoR,EAAMF,GAEDjO,EAAJ,IAARiO,EAAoB,uBAAiC,0BAErDje,EAAOD,QAAU,CACfgN,KAAMA,EACNiD,MAAOA,EACPkO,U,kCC1BF,yBAA4hB,EAAG,G,kCCA/hB,yBAAglB,EAAG", "file": "js/app.9f47c739.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "import mod from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"", "module.exports = __webpack_public_path__ + \"img/Jg_05.0c8d7339.png\";", "module.exports = __webpack_public_path__ + \"img/Jg_03.80a43bdd.png\";", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=dc6d3272&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=dc6d3272&lang=scss&scoped=true&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./filter.vue?vue&type=style&index=0&id=ff1ae770&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./filter.vue?vue&type=style&index=0&id=ff1ae770&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/bg.d85e7ace.png\";", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2bbaa86a&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&id=2bbaa86a&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/Jg_04.c4495afe.png\";", "module.exports = __webpack_public_path__ + \"img/Jg_01.b34f9923.png\";", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{attrs:{\"id\":\"app\"}},[_c('router-view')],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=787d3e4b&\"\nvar script = {}\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"home\"},[_c('div',{staticClass:\"home-header\",style:({'padding-top': _vm.pdtHeight}),attrs:{\"id\":\"homeHeader\"}},[_c('div',{staticClass:\"header\"},[_c('div',{staticClass:\"title\"},[_vm._v(\"商城\")]),_c('div',{staticClass:\"search\",on:{\"click\":_vm.toSearch}},[_c('img',{attrs:{\"src\":require(\"../../assets/images/icon_search.png\")}}),_c('span',{staticClass:\"text\"},[_vm._v(_vm._s(_vm.searchWord))])]),_c('img',{staticClass:\"service\",attrs:{\"src\":require(\"./images/icon_kefu.png\")},on:{\"click\":function($event){return _vm.uplusBuriedPoint('https://m.ehaier.com/v3/h5/sg/common/customService.html?container_type=3&show_title_bar=true&hidesBottomBarWhenPushed=1')}}}),_c('div',{staticClass:\"cart\",on:{\"click\":_vm.toShoppingCart}},[_c('img',{attrs:{\"src\":require(\"./images/icon_cart.png\")}}),(_vm.cartNum)?_c('span',[_vm._v(_vm._s(_vm.cartNum))]):_vm._e()]),_c('img',{staticClass:\"more\",attrs:{\"src\":require(\"./images/icon_more.png\")},on:{\"click\":_vm.showModal}})]),_c('div',{staticClass:\"header-nav-warp\",attrs:{\"id\":\"headerNavWarp\"}},[_c('div',{staticClass:\"header-nav-top-box\"},_vm._l((_vm.btmCategoryNav),function(item){return _c('div',{key:item.productCateId,attrs:{\"id\":(\"headNav-\" + (item.productCateId))},on:{\"click\":function($event){return _vm.changeCate(item, 'scroll', $event)}}},[_c('a',{class:{'active': _vm.curproductCateId == item.productCateId},attrs:{\"href\":\"javascript:;\"}},[_vm._v(_vm._s(item.name))]),_c('span',[_vm._v(\"|\")])])}),0)])]),_c('div',{staticClass:\"home-container\",style:({'top': _vm.pdtHeight}),attrs:{\"id\":\"homeContainer\"}},[_c('van-pull-refresh',{staticClass:\"flex-1\",on:{\"refresh\":_vm.onRefresh},scopedSlots:_vm._u([{key:\"pulling\",fn:function(){return [_c('refreshLoading',{attrs:{\"text\":\"下拉刷新\"}})]},proxy:true},{key:\"loosing\",fn:function(){return [_c('refreshLoading',{attrs:{\"text\":\"下拉刷新\"}})]},proxy:true},{key:\"loading\",fn:function(){return [_c('refreshLoading',{attrs:{\"text\":\"加载中\"}})]},proxy:true},{key:\"success\",fn:function(){return [_c('refreshLoading',{attrs:{\"text\":\"加载完成\"}})]},proxy:true}]),model:{value:(_vm.isLoading),callback:function ($$v) {_vm.isLoading=$$v},expression:\"isLoading\"}},[_c('div',{staticClass:\"header-top\"},[(!_vm.isOnline && !_vm.bannerList.length)?_c('div',{staticClass:\"banner\"},[_c('img',{attrs:{\"src\":require(\"./images/bg.png\")}})]):_c('van-swipe',{staticClass:\"banner\",attrs:{\"autoplay\":3000,\"indicator-color\":\"white\"}},_vm._l((_vm.bannerList),function(item,index){return _c('van-swipe-item',{key:index},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(item.imageUrl),\"lazy\":\"loading\"},on:{\"click\":function($event){return _vm.bannerClick(item)}}})])}),1),(_vm.middleBannerImg)?_c('div',{staticClass:\"middle-banner\"},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(_vm.middleBannerImg.pic),\"lazy\":\"loading\"},on:{\"click\":function($event){return _vm.bannerClick({ hyperLinkType: ''+_vm.middleBannerImg.linkType, hyperLink: _vm.middleBannerImg.link })}}})]):_vm._e(),_c('div',{staticClass:\"tab-row\"},_vm._l((_vm.tabList),function(item,index){return _c('div',{key:index,staticClass:\"tab\",on:{\"click\":function($event){return _vm.goPage(item.path)}}},[_c('img',{attrs:{\"src\":item.img}}),_c('span',[_vm._v(_vm._s(item.title))])])}),0)],1),(_vm.categoryList.length)?_c('div',{staticClass:\"home-category\"},[_vm._l((_vm.categoryList),function(item){return _c('div',{key:item.keywordId,staticClass:\"category-item\",on:{\"click\":function($event){return _vm.toSearch(item)}}},[_c('van-image',{attrs:{\"round\":\"\",\"cover\":\"\",\"src\":_vm._f(\"imageToHttps\")(item.iconUrl)}}),_c('p',[_vm._v(_vm._s(item.keywordValue))])],1)}),_c('div',{staticClass:\"category-item\",on:{\"click\":function($event){return _vm.goPage('/category')}}},[_c('img',{attrs:{\"src\":require(\"../../assets/images/item_more.png\"),\"alt\":\"\"}}),_c('p',[_vm._v(\"更多\")])])],2):_vm._e(),(_vm.programTabs.length)?_c('div',{staticClass:\"zj-program\"},[_c('div',{staticClass:\"home-title\"},[_c('span',[_vm._v(\"智家场景超市\")]),_c('div',{staticClass:\"more\",on:{\"click\":function($event){return _vm.goPage('/scene')}}},[_vm._v(\"更多\"),_c('van-icon',{attrs:{\"name\":\"arrow\",\"color\":\"#999\"}})],1)]),_c('div',{staticClass:\"program-tab-warp\"},[_c('div',{staticClass:\"program-tab\"},_vm._l((_vm.programTabs),function(item,index){return _c('span',{key:item.tabId,class:{'active': index == _vm.curProgramIndex},attrs:{\"id\":(\"ptab-\" + (item.tabId))},on:{\"click\":function($event){return _vm.changeProgram(item.tabId, index)}}},[_vm._v(_vm._s(item.tabName))])}),0)]),(_vm.programLists[_vm.curProgramTab])?_c('div',[_c('van-swipe',{ref:\"proSwiper\",staticClass:\"program-swiper\",attrs:{\"show-indicators\":false},on:{\"change\":_vm.proTabChange}},_vm._l((_vm.programTabs),function(item,idx){return _c('van-swipe-item',{key:idx,staticClass:\"program-product\"},_vm._l((item.plans),function(prod,index){return _c('div',{key:index,staticClass:\"program-product-item\",on:{\"click\":function($event){return _vm.goPage(prod.planDetailsRelativeUrl, false, true)}}},[_c('div',{staticClass:\"program-product-img\"},[(!prod.hasVideo)?_c('div',{staticClass:\"program-play\"},[_c('span')]):_vm._e(),_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(prod.planThumbImg),\"alt\":\"\",\"lazy\":\"loading\"}})]),_c('p',[_vm._v(_vm._s(prod.salesHighlights))])])}),0)}),1)],1):_vm._e()]):_vm._e(),(_vm.saleList.length > 0)?_c('div',{staticClass:\"find-goods\"},[_c('div',{staticClass:\"home-title\"},[_c('span',[_vm._v(\"发现好货\")]),_c('div',{staticClass:\"more\",on:{\"click\":_vm.toNextPage}},[_vm._v(\"更多\"),_c('van-icon',{attrs:{\"name\":\"arrow\",\"color\":\"#999\"}})],1)]),_c('div',{staticClass:\"goods-time\"},[_c('div',{staticClass:\"goods-time-warp\"},_vm._l((_vm.saleList),function(item,index){return _c('div',{key:index,staticClass:\"time-item\",class:{'active': _vm.activeTab == index},on:{\"click\":function($event){return _vm.changeTab(item,index)}}},[_c('strong',[_vm._v(_vm._s(item.title))]),_c('span',[_vm._v(_vm._s(item.timeTip))])])}),0)]),_c('div',{staticClass:\"goods-list\"},[_c('div',{staticClass:\"goods-list-box\"},[_c('div',{staticClass:\"goods-list-warp\"},_vm._l((_vm.saleProductList),function(item){return _c('div',{key:item.productId,staticClass:\"goods-item\",on:{\"click\":function($event){return _vm.toProduct(item)}}},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(item.imageUrl),\"lazy\":\"loading\"}}),_c('div',[_c('p',{attrs:{\"pd\":item.flashsalePrice}},[_c('span',[_vm._v(\"￥\")]),_vm._v(_vm._s(_vm._f(\"toFixedTwo\")(item.flashsalePrice)))]),_c('del',[_vm._v(\"￥\"+_vm._s(_vm._f(\"toFixedTwo\")(item.miniPrice)))])])])}),0)])])]):_vm._e(),(_vm.nearbyList.length)?_c('div',{staticClass:\"nearby-shop\"},[_c('div',{staticClass:\"home-title\"},[_c('span',[_vm._v(\"附近好店\")]),_c('div',{staticClass:\"more\",on:{\"click\":function($event){return _vm.goPage('/shopNearby')}}},[_vm._v(\"更多\"),_c('van-icon',{attrs:{\"name\":\"arrow\",\"color\":\"#999\"}})],1)]),_c('div',{staticClass:\"nearby-shop-box\"},[_c('div',{staticClass:\"nearby-shop-warp\"},_vm._l((_vm.nearbyList),function(item){return _c('div',{key:item.id,staticClass:\"nearby-shop-item\",on:{\"click\":function($event){return _vm.goNearby(item)}}},[_c('div',{staticClass:\"nearby-title\"},[_c('i'),_vm._v(_vm._s(item.name))]),_c('div',{staticClass:\"nearby-tips\"},[_c('p',[_c('i',{staticClass:\"icon-auth\"}),_vm._v(\"官方认证\")]),_c('span'),_c('p',[_c('i',{staticClass:\"icon-address\"}),_vm._v(\"距您\"+_vm._s(item.distance))]),(item.couponStatus)?_c('i'):_vm._e()]),_c('div',{staticClass:\"nearby-info\"},[_c('p',[_vm._v(\"商品描述 \"+_vm._s(_vm._f(\"toFixedTwo\")(item.pg,1)))]),_c('p',[_vm._v(\"卖家服务 \"+_vm._s(_vm._f(\"toFixedTwo\")(item.sg,1)))]),_c('p',[_vm._v(\"物流服务 \"+_vm._s(_vm._f(\"toFixedTwo\")(item.lg,1)))])])])}),0)])]):_vm._e(),_c('div',{staticClass:\"require-good\"},[_c('div',{staticClass:\"home-title\"},[_c('span',[_vm._v(\"必选单品\")])]),_c('div',{staticClass:\"require-goods-warp\"},[_c('div',{staticClass:\"require-goods-left\",on:{\"click\":function($event){return _vm.bannerClick({ hyperLinkType: ''+_vm.requireGoods.one.linkType, hyperLink: _vm.requireGoods.one.link, relationId: _vm.requireGoods.one.relationId })}}},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(_vm.requireGoods.one.pic),\"lazy\":\"loading\"}})]),_c('div',{staticClass:\"require-goods-right\"},[_c('div',{on:{\"click\":function($event){return _vm.bannerClick({ hyperLinkType: ''+_vm.requireGoods.two.linkType, hyperLink: _vm.requireGoods.two.link, relationId: _vm.requireGoods.two.relationId })}}},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(_vm.requireGoods.two.pic),\"lazy\":\"loading\"}})]),_c('div',{on:{\"click\":function($event){return _vm.bannerClick({ hyperLinkType: ''+_vm.requireGoods.three.linkType, hyperLink: _vm.requireGoods.three.link, relationId: _vm.requireGoods.three.relationId })}}},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(_vm.requireGoods.three.pic),\"lazy\":\"loading\"}})])])])]),_c('div',{staticClass:\"bottom-category\"},[_c('div',{staticClass:\"btm-category-nav\",attrs:{\"id\":\"btmCategoryNav\"}},[_c('div',{staticClass:\"btm-nav-box\"},[_c('div',{staticClass:\"btm-nav-warp\"},_vm._l((_vm.btmCategoryNav),function(item){return _c('div',{key:item.productCateId,attrs:{\"id\":(\"btmNav-\" + (item.productCateId))},on:{\"click\":function($event){return _vm.changeCate(item, null, $event)}}},[_c('a',{class:{'active': _vm.curproductCateId == item.productCateId},attrs:{\"href\":\"javascript:;\"}},[_vm._v(_vm._s(item.name))]),_c('span',[_vm._v(\"|\")])])}),0)])]),(!_vm.productCateList.length && _vm.fetchEnd)?_c('div',{staticClass:\"category-empty\"},[_c('img',{attrs:{\"src\":require(\"./images/empty.png\"),\"alt\":\"\"}})]):_c('div',{staticClass:\"btm-category-list\"},_vm._l((_vm.productCateList),function(item){return _c('div',{key:item.productId,staticClass:\"btm-list-item\",on:{\"click\":function($event){return _vm.toProduct(item)}}},[_c('div',{staticClass:\"btm-item-img\"},[_c('img',{attrs:{\"src\":_vm._f(\"imageToHttps\")(item.defaultImageUrl),\"lazy\":\"loading\"}})]),_c('div',{staticClass:\"btm-item-name\"},[(item.recommend)?_c('span',[_vm._v(\"推荐\")]):_vm._e(),_vm._v(_vm._s(item.productFullName))]),_c('div',{staticClass:\"btm-item-sellpoint\"},_vm._l((item.sellPoint),function(point,indx){return _c('span',{key:indx},[_vm._v(_vm._s(point))])}),0),_c('div',{staticClass:\"btm-item-footer\"},[_c('p',[_c('span',[_vm._v(\"￥\")]),_vm._v(_vm._s(_vm._f(\"toFixedTwo\")(item.finalPrice)))]),_c('strong',{class:{'disabled': item.hasStock === '无货'}},[_vm._v(_vm._s(item.hasStock))])]),_c('div',{staticClass:\"btm-item-commit\"},[_c('span',[_vm._v(_vm._s(item.comments == '暂无评价'?item.comments:item.comments+'条评价'))]),(item.comments !== '暂无评价')?_c('span',[_vm._v(_vm._s(item.goodRate)+\"好评\")]):_vm._e()])])}),0)]),_c('div',{directives:[{name:\"scroll\",rawName:\"v-scroll\",value:(_vm.fetchData),expression:\"fetchData\"}],staticClass:\"bottom-text\"},[(!_vm.fetchEnd)?_c('van-loading',{attrs:{\"size\":\".4rem\"}},[_vm._v(\"加载中...\")]):_c('div',[_vm._v(\"没有更多了\")])],1)])],1),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isShowModal),expression:\"isShowModal\"}],staticClass:\"mask\",on:{\"click\":function($event){_vm.isShowModal = false}}}),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.isShowModal),expression:\"isShowModal\"}],staticClass:\"popup-container\",style:({'top': _vm.maskHeight}),on:{\"click\":function($event){_vm.isShowModal = false}}},[_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('/messageCenter', true)}}},[_c('i',{staticClass:\"icon-message\"}),_vm._v(\"平台消息\")]),_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('/orderlist', true)}}},[_c('i',{staticClass:\"icon-order\"}),_vm._v(\"我的订单\")]),_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('/myCollection', true)}}},[_c('i',{staticClass:\"icon-fav\"}),_vm._v(\"我的收藏\")]),_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('/coupon', true)}}},[_c('i',{staticClass:\"icon-quan\"}),_vm._v(\"优惠券\")]),_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('https://uplus.haier.com/uplusapp/problemFeedBack/feedback.html', true, true)}}},[_c('i',{staticClass:\"icon-tucao\"}),_vm._v(\"我要吐槽\")]),_c('div',{staticClass:\"text\",on:{\"click\":function($event){return _vm.goPage('https://uplus.haier.com/uplusapp/scan/homescanpage.html', false, true)}}},[_c('i',{staticClass:\"icon-sao\"}),_vm._v(\"扫码购物\")])]),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.showTotop),expression:\"showTotop\"}],staticClass:\"go-top\",on:{\"click\":_vm.toGotop}},[_c('img',{attrs:{\"src\":require(\"./images/gotop.png\")}})])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"refresh-load\"},[_vm._m(0),(_vm.text)?_c('div',{staticClass:\"spinner-text\"},[_vm._v(_vm._s(_vm.text))]):_vm._e()])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"spinner\"},[_c('div',{staticClass:\"rect1\"}),_c('div',{staticClass:\"rect2\"}),_c('div',{staticClass:\"rect3\"}),_c('div',{staticClass:\"rect4\"}),_c('div',{staticClass:\"rect5\"})])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"refresh-load\">\n    <div class=\"spinner\">\n      <div class=\"rect1\"></div>\n      <div class=\"rect2\"></div>\n      <div class=\"rect3\"></div>\n      <div class=\"rect4\"></div>\n      <div class=\"rect5\"></div>\n    </div>\n    <div class=\"spinner-text\" v-if=\"text\">{{text}}</div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'refreshLoading',\n  props: ['text']\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.refresh-load{\n  padding-top: .2rem;\n  padding-bottom: .2rem;\n  height: 100%;\n  box-sizing: border-box;\n}\n.spinner-text{\n  height: .4rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  font-size: .2rem;\n  color: #999;\n  text-align: center;\n}\n.spinner {\n  margin: 0 auto;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  width: 30px;\n  height: 21px;\n  text-align: center;\n  font-size: .2rem;\n}\n\n.spinner > div {\n  background-color: #2283E2;\n  height: 100%;\n  width: 3px;\n  display: block;\n  -webkit-animation: stretchdelay 1.2s infinite ease-in-out;\n  animation: stretchdelay 1.2s infinite ease-in-out;\n}\n\n.spinner .rect2 {\n  -webkit-animation-delay: -1.1s;\n  animation-delay: -1.1s;\n}\n\n.spinner .rect3 {\n  -webkit-animation-delay: -1.0s;\n  animation-delay: -1.0s;\n}\n\n.spinner .rect4 {\n  -webkit-animation-delay: -0.9s;\n  animation-delay: -0.9s;\n}\n\n.spinner .rect5 {\n  -webkit-animation-delay: -0.8s;\n  animation-delay: -0.8s;\n}\n\n@-webkit-keyframes stretchdelay {\n  0%, 40%, 100% { -webkit-transform: scaleY(0.4) }\n  20% { -webkit-transform: scaleY(1.0) }\n}\n\n@keyframes stretchdelay {\n  0%, 40%, 100% {\n    transform: scaleY(0.4);\n    -webkit-transform: scaleY(0.4);\n  }  20% {\n    transform: scaleY(1.0);\n    -webkit-transform: scaleY(1.0);\n  }\n}\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=dc6d3272&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=dc6d3272&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"dc6d3272\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport Vuex from 'vuex'\nimport axios from 'axios'\nimport qs from 'qs';\nimport UplusApi from \"@uplus/uplus-api\";\nconst config = require('../config')\n\nconst instance = new UplusApi();\ninstance.initDeviceReady()\n\n// import UplusApi from '@uplus/uplus-api';\nVue.use(Vuex)\n\nconst defaultLocation = { // 默认地址\n  region: '山东 青岛 崂山区 中韩街道',\n  provinceId: 16,\n  province: '山东',\n  cityId: 173,\n  city: '青岛',\n  districtId: 2450,\n  district: '崂山区',\n  townshipId: 12036596,\n  township: '中韩街道',\n  //济南\n  // latitude: '36.657017',  \n  // longitude: '116.945875',\n  //青岛\n  latitude: '36.12784', \n  longitude: '120.41803',\n}\n\nexport default new Vuex.Store({\n  state: {\n    latitude: defaultLocation.latitude,\n    longitude: defaultLocation.longitude,\n    provinceId: defaultLocation.provinceId,\n    cityId: defaultLocation.cityId,\n    districtId: defaultLocation.districtId,\n    townshipId: defaultLocation.townshipId,\n    locationInfo: defaultLocation,\n    sessionValue: '',\n    historySearch: localStorage.getItem('historySearch') || '',\n  },\n  getters: {\n    getlocationInfo: state => state.locationInfo,\n    getHistorySearch: state => state.historySearch,\n    getLatLong: state => ({latitude: state.latitude, longitude: state.longitude})\n  },\n  mutations: {\n    setSessionValue: (state,sessionValue) => state.sessionValue = sessionValue,\n    setLatLong: (state, location) => {\n      state.latitude = location.latitude\n      state.longitude = location.longitude\n    },\n    addHistorySearch: (state, keyword) => {\n      let curText = localStorage.getItem('historySearch')\n      let hisTextArr = curText ? curText.split(',') : []\n      hisTextArr.unshift(keyword)\n      hisTextArr = [...new Set(hisTextArr)]\n      hisTextArr.length > 10 ? hisTextArr = hisTextArr.splice(0,10) : null\n      state.historySearch = hisTextArr.join(',')\n      localStorage.setItem('historySearch',hisTextArr.join(','))\n    },\n    deleteHistorySearch: state => {\n      state.historySearch = ''\n      localStorage.setItem('historySearch','')\n    }\n  },\n  actions: {\n    doLocation({commit}){\n      return new Promise((resolve)=>{\n        instance.initDeviceReady().then(() => {\n          instance.upLocationModule.getLocation().then(res => {\n            if (res.retData) {\n              let longitude = res.retData.longitude;\n              let latitude = res.retData.latitude;\n              console.log('uplus lat long', latitude, longitude)\n              commit('setLatLong', { latitude, longitude })\n              resolve({ latitude, longitude })\n            }else{\n              resolve({\n                latitude: defaultLocation.latitude,\n                longitude: defaultLocation.longitude,\n              })\n            }\n          }).catch(()=>{\n            resolve({\n              latitude: defaultLocation.latitude,\n              longitude: defaultLocation.longitude,\n            })\n          })\n        })\n      })\n    },\n    async getAuthorize({ dispatch }, sdToken) {\n      // axios.get(`${config.HOST}/bbb.json?sdToken=${sdToken}`)\n      let url = process.env.NODE_ENV === 'development' ? '/sg/h5/web/auth/mobile/customer/authorize.json?platform=2&callbackUrl=' + encodeURIComponent('https://m.ehaier.com') : config.HOST + '/sg/h5/web/auth/mobile/customer/authorize.json?platform=2&callbackUrl=' + encodeURIComponent('https://m.ehaier.com');\n      return axios.get(url, {\n        headers: { 'Authorization': sdToken }, // sdToken\n      }).then(({ data }) => {\n        if (data.success) {\n          let mId = data.data.mId\n          // axios.get(`${config.HOST}/ccc.json?mid=${mId}`)\n          return dispatch('getMember', { sdToken, mId })\n        } else {\n          return 400\n        }\n      })\n    },\n    async getMember({ commit }, { sdToken, mId }) {\n      // axios.get(`${config.HOST}/ddd.json?res=${mId},${sdToken}`)\n      let url = process.env.NODE_ENV === 'development' ? '/v3/platform/web/member/getMember.json' : config.HOST +'/v3/platform/web/member/getMember.json'\n      return axios.post(url, qs.stringify({ token: mId }), {\n        headers: {\n          'Authorization': sdToken,\n          'Content-Type': 'application/x-www-form-urlencoded'\n        }\n      }).then(({ data }) => {\n        // axios.get(`${config.HOST}/eee.json`)\n        if (data.success) {\n          let result = data.data\n          let mid = result.mid\n          // let ucid = result.ucId\n          // let accessToken = result.accessToken\n          // let sessionKey = result.sessionKey\n          let sessionValue = result.sessionValue\n          // let token = result.token\n          let userInfo = {}\n          userInfo.username = result.userName\n          userInfo.avatarImage = result.avatarImageFileId\n          userInfo.loginName = result.loginName\n          userInfo.birthday = result.birthday\n          userInfo.phone = result.mobile\n          userInfo.email = result.email\n          userInfo.cartNumber = result.cartNumber\n          userInfo.gender = result.gender\n          userInfo.isNewUser = result.isNewUser\n          userInfo.isStoreMember = result.isStoreMember\n          userInfo.nickName = result.nickName\n          userInfo.rankName = result.rankName\n          userInfo.promotionCode = result.promotionCode\n          userInfo.resultSign = result.resultSign\n\n          commit('setmId', mid);\n          commit('setSessionValue', sessionValue)\n          return result\n        } else {\n          return 400\n        }\n      })\n    },\n  }\n});", "import axios from 'axios';\nimport Store from '../../store'\n// import sdkHttp from '../../utils/uplushttp'\nconst config = require('../../config')\n\nvar httpModule;\n\n// if(navigator.userAgent.indexOf('App/Uplus') > -1){ \n//   httpModule = sdkHttp\n// }else{\n//   axios.defaults.baseURL = process.env.NODE_ENV === 'development' ? '' : `${config.HOST}`;\n\n//   axios.interceptors.request.use(\n//     function (config) {\n//       config.headers.common['TokenAuthorization'] = 'Bearer' + Store.state.sessionValue\n//       return config\n//     },\n//     function (error) {\n//       // Do something with request error\n//       return Promise.reject(error)\n//     }\n//   )\n//   httpModule = axios\n// }\n\n\naxios.defaults.baseURL = process.env.NODE_ENV === 'development' ? '' : `${config.HOST}`;\naxios.interceptors.request.use(\n  function (config) {\n    config.headers.common['TokenAuthorization'] = 'Bearer' + Store.state.sessionValue\n    return config\n  },\n  function (error) {\n    // Do something with request error\n    return Promise.reject(error)\n  }\n)\nhttpModule = axios\n\n\n\n\n//购物车列表\nconst getCartNumber = () => httpModule.get(`/v3/h5/scart/num.json`)\n\n\nconst bannerUrl = '/sg/cms/home/<USER>';\nconst getBanner = (params) => {\n  return httpModule.get(bannerUrl, {\n    params\n  });\n};\n\nconst getMessageList = (params) => {\n  return httpModule.post(`/v3/mstore/sg/getSgMessageLimit.json?messageType=${params}`)\n}\n\n\nconst getProgramData = () => {\n  return httpModule.get(`/sg/cms/home/<USER>/getTabsByPageInfo.json`)\n}\n\nconst getRecommondKeyWord = () => {\n  return httpModule.post('/sg/cms/home/<USER>')\n}\n\nconst getMiddleBanner = ()=>{\n  return httpModule.get('/sg/cms/middleImageConfig.json')\n}\n\nconst getFlashSale = (params) => {\n  return httpModule.get('/sg/cms/flashSales/fourth/indexsale.json', {\n    params\n  });\n}\n\nconst getNearbyShop = (params) => {\n  return httpModule.post(`/sg/cms/home/<USER>\n} \n\nconst getRequireGoods = (params) => {\n  return httpModule.get('/sg/cms/electricalSecond.json',{params})\n}\n\nconst getDefaultSearchWord = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/search/defaultSearch.html' : config.SHOST + '/search/search/defaultSearch.html'\n  return httpModule.get(url, {\n    params\n  });\n}\n\nconst getCommonLoadItemNew = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/commonLoadItemNew.html' : config.SHOST + '/search/commonLoadItemNew.html'\n  return httpModule.get(url,{params})\n}\n\nconst getPriceByProduct = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/getPriceByProductList.html' : config.SHOST + '/search/getPriceByProductList.html'\n  return httpModule.get(url,{params})\n}\n\n\nexport default {\n  getCartNumber,\n  getBanner,\n  getMessageList,\n  getProgramData,\n  getRecommondKeyWord,\n  getMiddleBanner,\n  getFlashSale,\n  getNearbyShop,\n  getRequireGoods,\n  getDefaultSearchWord,\n  getCommonLoadItemNew,\n  getPriceByProduct,\n}\n\n", "import UplusApi from \"@uplus/uplus-api\";\n\nconst instance = new UplusApi();\ninstance.initDeviceReady()\n\nexport const windowToRem = (width=750) => {\n  (function(doc, win) {\n    var docEl = doc.documentElement,\n      resizeEvt = 'orientationchange' in window ? 'orientationchange' : 'resize',\n      recalc = function() {\n        var clientWidth = docEl.clientWidth;\n        if(!clientWidth) return;\n        docEl.style.fontSize = 100 * (clientWidth / width) + 'px';\n      };\n  \n  \n    if(!doc.addEventListener) return;\n    win.addEventListener(resizeEvt, recalc, false);\n    doc.addEventListener('DOMContentLoaded', recalc, false);\n  })(document, window);        \n}\n\nexport const isPhone = (phone) => {\n  let reg = /^1[3456789]\\d{9}$/\n  return reg.test(phone)\n}\n\nexport const getCurrentPosition = () =>{\n  return new Promise((resolve,reject)=>{\n    if (\"geolocation\" in navigator) {       \n      navigator.geolocation.getCurrentPosition(position => { \n        resolve({\n          latitude: position.coords.latitude, \n          longitude: position.coords.longitude\n        })\n      })\n    }else{\n      reject(new Error())\n    }\n  })\n}\n\nexport const serilizerQuery = json => {\n  let result = '',index = 1;\n  for(var key in json){\n    if(index == 1){\n      result += key + '=' + json[key]\n    }else{\n      result += '&'+ key + '=' + json[key]\n    }\n    index += 1\n  }\n  return result\n}\n\nexport const isIphonex = () => {\n  if (typeof window !== 'undefined' && window) {\n    return /iphone/gi.test(window.navigator.userAgent) && window.screen.height >= 812;\n  }\n  return false;\n};\n\nexport const isAndroid = () => {\n  return navigator.userAgent.indexOf('Android') > -1\n}\n\nexport const isUplus = () => navigator.userAgent.indexOf('App/Uplus') > -1\n\nexport const netWorkStatusHandler = (onFn, offFn) => {\n  window.addEventListener('online', () => {\n    onFn && onFn()\n  }, false);\n  window.addEventListener('offline', () => {\n    offFn && offFn()\n  }, false);\n}\n\n\nexport const uplusBuriedUrlPoint = (url) =>{\n  instance.initDeviceReady().then(() => {\n    instance.upTraceModule.reportSelfPageChange({\n      type: \"GOTO\",\n      selfPageUrl: url,\n      selfPageTitle: \"\"\n    }).then(ret => {\n      console.log(ret,'utils');\n      instance.upVdnModule.goToPage({ url: url });\n    });\n  });\n}", "<template>\n  <div class=\"home\">\n    <div id=\"homeHeader\" class=\"home-header\" :style=\"{'padding-top': pdtHeight}\">\n      <div class=\"header\">  \n        <div class=\"title\">商城</div>\n        <div class=\"search\" @click=\"toSearch\">\n          <img src=\"../../assets/images/icon_search.png\">\n          <span class=\"text\">{{searchWord}}</span>\n        </div>\n        <img class=\"service\" src=\"./images/icon_kefu.png\"  @click=\"uplusBuriedPoint('https://m.ehaier.com/v3/h5/sg/common/customService.html?container_type=3&show_title_bar=true&hidesBottomBarWhenPushed=1')\"/>\n        <div class=\"cart\" @click=\"toShoppingCart\">\n          <img src=\"./images/icon_cart.png\" />\n          <span v-if=\"cartNum\">{{cartNum}}</span>\n        </div>\n        <img src=\"./images/icon_more.png\" class=\"more\" @click=\"showModal\" />\n      </div>\n      <div id=\"headerNavWarp\" class=\"header-nav-warp\">\n          <div class=\"header-nav-top-box\">\n            <div :id=\"`headNav-${item.productCateId}`\" v-for=\"item in btmCategoryNav\" :key=\"item.productCateId\" @click=\"changeCate(item, 'scroll', $event)\">\n              <a href=\"javascript:;\" :class=\"{'active': curproductCateId == item.productCateId}\">{{item.name}}</a><span>|</span>\n            </div>\n          </div>\n      </div>\n    </div> \n    <div id=\"homeContainer\" class=\"home-container\" :style=\"{'top': pdtHeight}\">\n      <van-pull-refresh class=\"flex-1\" v-model=\"isLoading\" @refresh=\"onRefresh\">\n        <template #pulling><refreshLoading text=\"下拉刷新\"/></template>\n        <template #loosing><refreshLoading text=\"下拉刷新\"/></template>\n        <template #loading><refreshLoading text=\"加载中\"/></template>\n        <template #success><refreshLoading text=\"加载完成\"/></template>\n        <div class=\"header-top\">\n          <div v-if=\"!isOnline && !bannerList.length\" class=\"banner\">\n            <img src=\"./images/bg.png\"/>\n          </div>\n          <van-swipe :autoplay=\"3000\" indicator-color=\"white\" class=\"banner\" v-else>\n            <van-swipe-item v-for=\"(item, index) in bannerList\" :key=\"index\">\n              <img :src=\"item.imageUrl | imageToHttps\" @click=\"bannerClick(item)\" lazy=\"loading\"/>\n            </van-swipe-item>\n          </van-swipe>\n          <div class=\"middle-banner\" v-if=\"middleBannerImg\">\n            <img :src=\"middleBannerImg.pic | imageToHttps\" @click=\"bannerClick({ hyperLinkType: ''+middleBannerImg.linkType, hyperLink: middleBannerImg.link })\" lazy=\"loading\" />\n          </div>\n          <div class=\"tab-row\">\n            <div\n              v-for=\"(item, index) in tabList\"\n              :key=\"index\"\n              class=\"tab\"\n              @click=\"goPage(item.path)\">\n              <img :src=\"item.img\">\n              <span>{{item.title}}</span>\n            </div>\n          </div>\n        </div>\n        <!-- <div class=\"shop-notice\" v-if=\"textList.length\" style=\"display: none;\">\n          <strong>商城公告</strong><span>|</span>\n          <div class=\"notice-list\">\n            <transition name=\"slide\">\n              <p :key=\"textSelect.index\" @click=\"goPage(`/messageDetail/${textSelect.id}/${textSelect.relationId}`,true)\">{{textSelect.con}}</p>\n            </transition>\n          </div>\n          <div class=\"notice-more\" @click=\"goPage('/messageCenter', true)\">更多<van-icon name=\"arrow\" color=\"#999\"/></div>\n        </div> -->\n        <div class=\"home-category\" v-if=\"categoryList.length\">\n          <div class=\"category-item\" v-for=\"item in categoryList\" :key=\"item.keywordId\" @click=\"toSearch(item)\">\n            <van-image round cover :src=\"item.iconUrl | imageToHttps\"/>\n            <p>{{item.keywordValue}}</p>\n          </div>\n          <div class=\"category-item\" @click=\"goPage('/category')\">\n            <img src=\"../../assets/images/item_more.png\" alt=\"\">\n            <p>更多</p>\n          </div>\n        </div>\n        <div class=\"zj-program\" v-if=\"programTabs.length\">\n          <div class=\"home-title\">\n            <span>智家场景超市</span>\n            <!-- <div class=\"more\" @click=\"goPage('appVdnPage=mpaas://apphome#scene')\">更多<van-icon name=\"arrow\" color=\"#999\"/></div> -->\n            <div class=\"more\" @click=\"goPage('/scene')\">更多<van-icon name=\"arrow\" color=\"#999\"/></div>\n          </div>\n          <div class=\"program-tab-warp\">\n            <div class=\"program-tab\">\n              <span :id=\"`ptab-${item.tabId}`\" v-for=\"(item, index) in programTabs\" :class=\"{'active': index == curProgramIndex}\" :key=\"item.tabId\" @click=\"changeProgram(item.tabId, index)\">{{item.tabName}}</span>\n            </div>\n          </div>\n          <div v-if=\"programLists[curProgramTab]\">\n            <van-swipe ref=\"proSwiper\" class=\"program-swiper\" :show-indicators=\"false\" @change=\"proTabChange\">\n              <van-swipe-item class=\"program-product\" v-for=\"(item, idx) in programTabs\" :key=\"idx\">\n                <div class=\"program-product-item\" v-for=\"(prod, index) in item.plans\" :key=\"index\" @click=\"goPage(prod.planDetailsRelativeUrl, false, true)\">\n                  <div class=\"program-product-img\">\n                    <div class=\"program-play\" v-if=\"!prod.hasVideo\"><span></span></div>\n                    <img :src=\"prod.planThumbImg | imageToHttps\" alt=\"\" lazy=\"loading\"/>\n                  </div>\n                  <p>{{prod.salesHighlights}}</p>\n                </div>\n              </van-swipe-item>\n            </van-swipe>\n          </div>\n        </div>\n        <div class=\"find-goods\" v-if=\"saleList.length > 0\">\n          <div class=\"home-title\">\n            <span>发现好货</span>\n            <div class=\"more\" @click=\"toNextPage\">更多<van-icon name=\"arrow\" color=\"#999\"/></div>\n          </div>\n          <div class=\"goods-time\">\n            <div class=\"goods-time-warp\">\n              <div class=\"time-item\" :class=\"{'active': activeTab == index}\" v-for=\"(item,index) in saleList\"\n              :key=\"index\" @click=\"changeTab(item,index)\">\n                <strong>{{item.title}}</strong>\n                <span>{{item.timeTip}}</span>\n              </div>\n            </div>\n          </div>\n          <div class=\"goods-list\">\n            <div class=\"goods-list-box\">\n              <div class=\"goods-list-warp\">\n                <div class=\"goods-item\" v-for=\"item in saleProductList\" :key=\"item.productId\" @click=\"toProduct(item)\">\n                  <img :src=\"item.imageUrl | imageToHttps\" lazy=\"loading\"/>\n                  <div><p :pd=\"item.flashsalePrice\"><span>￥</span>{{item.flashsalePrice | toFixedTwo}}</p><del>￥{{item.miniPrice|toFixedTwo}}</del></div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"nearby-shop\" v-if=\"nearbyList.length\">\n          <div class=\"home-title\">\n            <span>附近好店</span>\n            <div class=\"more\" @click=\"goPage('/shopNearby')\">更多<van-icon name=\"arrow\" color=\"#999\"/></div>\n          </div>\n          <div class=\"nearby-shop-box\">\n            <div class=\"nearby-shop-warp\">\n              <div class=\"nearby-shop-item\" v-for=\"item in nearbyList\" :key=\"item.id\" @click=\"goNearby(item)\">\n                <div class=\"nearby-title\"><i></i>{{item.name}}</div>\n                <div class=\"nearby-tips\">\n                  <p><i class=\"icon-auth\"></i>官方认证</p>\n                  <span></span>\n                  <p><i class=\"icon-address\"></i>距您{{item.distance}}</p>\n                  <i v-if=\"item.couponStatus\"></i>\n                </div>\n                <div class=\"nearby-info\">\n                  <p>商品描述 {{item.pg | toFixedTwo(1)}}</p>\n                  <p>卖家服务 {{item.sg | toFixedTwo(1)}}</p>\n                  <p>物流服务 {{item.lg | toFixedTwo(1)}}</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n  \n        <div class=\"require-good\">\n          <div class=\"home-title\">\n            <span>必选单品</span>\n          </div>\n          <div class=\"require-goods-warp\">\n            <div class=\"require-goods-left\" @click=\"bannerClick({ hyperLinkType: ''+requireGoods.one.linkType, hyperLink: requireGoods.one.link, relationId: requireGoods.one.relationId })\">\n              <img :src=\"requireGoods.one.pic | imageToHttps\" lazy=\"loading\"/>\n            </div>\n            <div class=\"require-goods-right\">\n              <div @click=\"bannerClick({ hyperLinkType: ''+requireGoods.two.linkType, hyperLink: requireGoods.two.link, relationId: requireGoods.two.relationId })\"><img :src=\"requireGoods.two.pic | imageToHttps\" lazy=\"loading\"/></div>\n              <div @click=\"bannerClick({ hyperLinkType: ''+requireGoods.three.linkType, hyperLink: requireGoods.three.link, relationId: requireGoods.three.relationId })\"><img :src=\"requireGoods.three.pic | imageToHttps\" lazy=\"loading\"/></div>\n            </div>\n          </div>\n        </div>\n        <div class=\"bottom-category\">\n            <div id=\"btmCategoryNav\" class=\"btm-category-nav\">\n              <div class=\"btm-nav-box\">\n                <div class=\"btm-nav-warp\">\n                  <div :id=\"`btmNav-${item.productCateId}`\" v-for=\"item in btmCategoryNav\" :key=\"item.productCateId\" @click=\"changeCate(item, null, $event)\">\n                    <a href=\"javascript:;\" :class=\"{'active': curproductCateId == item.productCateId}\">{{item.name}}</a><span>|</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n          <div class=\"category-empty\" v-if=\"!productCateList.length && fetchEnd\">\n            <img src=\"./images/empty.png\" alt=\"\">\n          </div>\n          <div class=\"btm-category-list\" v-else>\n            <div class=\"btm-list-item\" v-for=\"item in productCateList\" :key=\"item.productId\" @click=\"toProduct(item)\">\n              <div class=\"btm-item-img\"><img :src=\"item.defaultImageUrl | imageToHttps\" lazy=\"loading\"/></div>\n              <div class=\"btm-item-name\"><span v-if=\"item.recommend\">推荐</span>{{item.productFullName}}</div>\n              <div class=\"btm-item-sellpoint\">\n                <span v-for=\"(point,indx) in item.sellPoint\" :key=\"indx\">{{point}}</span>\n              </div>\n              <div class=\"btm-item-footer\">\n                <p><span>￥</span>{{item.finalPrice | toFixedTwo}}</p>\n                <strong :class=\"{'disabled': item.hasStock === '无货'}\">{{item.hasStock}}</strong>\n              </div>\n              <div class=\"btm-item-commit\">\n                <span>{{item.comments == '暂无评价'?item.comments:item.comments+'条评价'}}</span>\n                <span v-if=\"item.comments !== '暂无评价'\">{{item.goodRate}}好评</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        \n        <div class=\"bottom-text\" v-scroll=\"fetchData\">\n          <van-loading size=\".4rem\" v-if=\"!fetchEnd\">加载中...</van-loading>\n          <div v-else>没有更多了</div>\n        </div>\n      </van-pull-refresh>\n    </div>\n    <div class=\"mask\" v-show=\"isShowModal\" @click=\"isShowModal = false\"></div>\n    <div v-show=\"isShowModal\" @click=\"isShowModal = false\" class=\"popup-container\" :style=\"{'top': maskHeight}\">\n      <div class=\"text\" @click=\"goPage('/messageCenter', true)\"><i class=\"icon-message\"></i>平台消息</div>\n      <div class=\"text\" @click=\"goPage('/orderlist', true)\"><i class=\"icon-order\"></i>我的订单</div>\n      <div class=\"text\" @click=\"goPage('/myCollection', true)\"><i class=\"icon-fav\"></i>我的收藏</div>\n      <div class=\"text\" @click=\"goPage('/coupon', true)\"><i class=\"icon-quan\"></i>优惠券</div>\n      <div class=\"text\" @click=\"goPage('https://uplus.haier.com/uplusapp/problemFeedBack/feedback.html', true, true)\"><i class=\"icon-tucao\"></i>我要吐槽</div>\n      <div class=\"text\" @click=\"goPage('https://uplus.haier.com/uplusapp/scan/homescanpage.html', false, true)\"><i class=\"icon-sao\"></i>扫码购物</div>\n    </div>\n    <div class=\"go-top\" v-show=\"showTotop\" @click=\"toGotop\">\n      <img src=\"./images/gotop.png\" />\n    </div>\n  </div>\n</template>\n\n<script>\nconst config = require('../../config');\nimport UplusApi from \"@uplus/uplus-api\";\nimport refreshLoading from '@/components/refreshLoading';\nimport dataService from './dataService';\nimport { mapGetters } from 'vuex'\nimport {isIphonex,isAndroid, isUplus, netWorkStatusHandler, uplusBuriedUrlPoint} from '../../utils/utils'\n\nconst instance = new UplusApi();\ninstance.initDeviceReady()\n\nlet curHeadNavId, curBtmNavId;\n\nexport default {\n  name: 'home',\n  components: {\n    refreshLoading,\n  },\n  data() {\n    return {\n      searchWord: '在千万商品中搜索',\n      pdtHeight: '',\n      maskHeight: '', //弹层高度\n      isLoading: true,\n      bannerList: [],\n      middleBannerImg: {},\n      textIndex: 0,\n      cartNum: '',\n      textList: [],\n      categoryList: [],\n      curProgramTab: '',\n      curProgramIndex: 0,\n      programTabs: [],\n      programLists: {},\n      tabList: [\n        {\n          img: require('./images/Jg_01.png'),\n          title: '领券中心',\n          path: '/voucher',\n        },\n        {\n          img: require('./images/Jg_02.png'),\n          title: '分期购',\n          path: '/banner/554',\n        },\n        {\n          img: require('./images/Jg_03.png'),\n          title: '海尔智选',\n          path: '/specialtyStore',\n          // path: '/banner/1347',\n        },\n        {\n          img: require('./images/Jg_04.png'),\n          title: '场景超市',\n          // path: 'appVdnPage=mpaas://apphome#scene',\n          path: '/scene',\n        },\n        {\n          img: require('./images/Jg_05.png'),\n          title: '智家魔方',\n          // path: '/banner/1837',\n          path: '/banner/1971',\n        },\n      ],\n      isShowModal: false,\n      \n      nearbyList: [],\n      activeTab: 0,\n      isSaling: false, // 是否是正在抢购\n      saleList: [], //发现好货 tab\n      saleProductList: [], //发现好货tab 对应的 list\n      requireGoods: { //必选单品\n        one: {},\n        two: {},\n        three: {}\n      },\n      curproductCateId: 2729,\n      btmCategoryNav: [\n        {\n          productCateId: 2729,\n          name: '空调',\n        }, {\n          productCateId: 2725,\n          name: '洗衣机',\n        }, {\n          productCateId: 2723,\n          name: '冰箱',\n        }, {\n          productCateId: 2743,\n          name: '彩电',\n        }, {\n          productCateId: 2741,\n          name: '热水器',\n        }, {\n          productCateId: 2742,\n          name: '厨房电器',\n        }, {\n          productCateId: 2726,\n          name: '冷柜',\n        }, {\n          productCateId: 2973,\n          name: '智能产品',\n        }, {\n          productCateId: 4255,\n          name: '生活电器',\n        }, {\n          productCateId: 2774,\n          name: '水家电',\n        }, {\n          productCateId: 2811,\n          name: '家用中央空调',\n        }\n      ],\n      productCateList: [],\n      fetching: false,\n      fetchEnd: false,\n      curpage: 1,\n      pageSize: 10,\n      isOnline: true, //是否联网\n      showTotop: false, //回到顶部\n    }\n  },\n  directives: {\n    scroll: {\n      inserted: function(el,binding){\n        var homHeight\n        document.getElementById('homeContainer').addEventListener('scroll',function(){\n          homHeight = homHeight ? homHeight :  document.getElementById('homeHeader').offsetHeight\n          if(el.getBoundingClientRect().top < window.innerHeight + 200){\n            binding.value()\n          }\n          if(homHeight > document.getElementById('btmCategoryNav').getBoundingClientRect().top){\n            document.getElementById('headerNavWarp').classList.add('active')\n            curHeadNavId && document.getElementById(curHeadNavId).scrollIntoView({inline: \"center\", block: 'nearest'})\n            curHeadNavId = ''\n          }else{\n            curBtmNavId && document.getElementById(curBtmNavId).scrollIntoView({inline: \"center\", block: 'nearest'})\n            curBtmNavId = ''\n            document.getElementById('headerNavWarp').classList.remove('active')\n          }\n        })\n      }\n    }\n  },\n  computed: {\n    ...mapGetters({\n      location: 'getlocationInfo',\n    }),\n    textSelect () {\n      return {\n        index: this.textIndex,\n        id: this.textList[this.textIndex] && this.textList[this.textIndex].id,\n        val: this.textList[this.textIndex] && this.textList[this.textIndex].title,\n        con: this.textList[this.textIndex] && this.textList[this.textIndex].content,\n        relationId: this.textList[this.textIndex] && this.textList[this.textIndex].relationId\n      }\n    },\n  },\n  mounted() {\n    if(isAndroid()){\n      // instance.initDeviceReady().then(() => { \n      //   this.$axios.get(`${config.HOST}/111.json`)\n      //   instance.getStatusBarHeight().then(res=>{\n      //     this.$axios.get(`${config.HOST}/222.json?${JSON.stringify(res)}`)\n      //     this.androidHeight = res.retData + 'px'\n      //   });\n      // })\n      this.pdtHeight = '.64rem'\n      this.maskHeight = '1.74rem'\n    }else if(isIphonex()){\n      this.pdtHeight = '.88rem'\n      this.maskHeight = '1.98rem'\n    }else{\n      this.pdtHeight = '.3rem'\n      this.maskHeight = '1.45rem'\n    }\n\n    if(isUplus()){\n      this.onRefresh();\n      this.fetchUserCartInfo()\n      instance.initDeviceReady().then(() => {\n        //容器进入前台生命周期\n        instance.addResumeEventListener(()=>{\n          console.log('回到前台');\n          this.fetchUserCartInfo()\n        },'homeShow');\n        //监听网络状态\n        instance.upNetworkModule.addNetStateEventListener((ret) =>{\n          console.log('ret online',ret)\n          if(ret == 'online'){\n            this.isOnline = true;\n            this.fetching = false;\n            this.fetchEnd = false;\n            this.onRefresh();\n            this.fetchUserCartInfo()\n          }else{\n            this.isOnline = false;\n            this.fetching = false;\n            this.fetchEnd = true;\n          }\n        },'itsFirst');\n      });\n      \n    }else{\n      netWorkStatusHandler(()=>{\n        this.isOnline = true\n        this.fetching = false;\n        this.fetchEnd = false;\n        this.onRefresh();\n        this.fetchUserCartInfo()\n      },()=>{\n        this.isOnline = false;\n        this.fetching = false;\n        this.fetchEnd = true;\n      })\n      this.onRefresh();\n    }\n\n    document.getElementById('homeContainer').addEventListener('scroll', ev => {\n      if(ev.target.scrollTop > 200){\n        this.showTotop = true\n      }else{\n        this.showTotop = false\n      }\n    });\n\n  },\n  filters: {\n    /**\n     * 价格保留两位小数\n     */\n    toFixedTwo (number, pos = 2) {\n      const price = number ? Number(number).toFixed(pos) : Number(0).toFixed(pos);\n      return price;\n    },\n    /**\n     * 将图片url地址改为https\n     */\n    imageToHttps (url) {\n      if (url && url.indexOf('http:') !== -1) {\n        url = url.replace('http:', 'https:');\n      }\n      return url;\n    },\n  },\n  methods: {\n    //获取设备信息\n    getDeviceList(){\n      return new Promise((resolve)=>{\n        instance.upDeviceModule.getDeviceList().then(result=>{\n          if(result.retCode == '000000'){\n            resolve(result.retData)\n          }else{\n            resolve(400)\n          }\n        }).catch(()=>{\n          resolve(400)\n        })\n      })\n    },\n    \n    //获取用户购物车信息\n    fetchUserCartInfo(){\n      this.checkIsLogin().then(sdToken=>{\n        if(sdToken){\n          this.$store.dispatch('getAuthorize',sdToken).then(result=>{\n            if(result == 400) return\n            dataService.getCartNumber().then(({data})=>{\n              let len = data.data;\n              this.cartNum = len > 99 ? '99+' : len\n            })\n          })\n        }else{\n          this.cartNum = ''\n        }\n      }); \n    },\n\n    fetchData(flag){\n      if(this.fetching || this.fetchEnd) return\n      this.fetching = true;\n      dataService.getCommonLoadItemNew({\n        pholder: 1,\n        productCateId: this.curproductCateId,\n        provinceId: this.location.provinceId,\n        cityId: this.location.cityId,\n        districtId: this.location.districtId,\n        streetId: this.location.townshipId,\n        fromType: 1,\n        memberId: '',\n        qs: 'isHotDesc',\n        filterData: '',\n        pageIndex: this.curpage,\n        pageSize: this.pageSize,\n      }).then(res => {\n        let result = res.data;\n        if(result.success){\n          let productList = result.data.productList\n          let traceId = result.data.traceId\n          if(productList.length && traceId){\n            dataService.getPriceByProduct({traceId}).then(({data})=>{\n              if(data.success){\n                let prodsArr = data.products\n                productList.forEach(item=>{\n                  prodsArr.forEach(prod=>{\n                    if(prod.sku == item.sku){\n                      item.finalPrice = prod.finalPrice\n                      item.commission = prod.commission\n                    }\n                  })\n                })\n                if(flag) this.productCateList = []\n\n                this.productCateList = this.productCateList.concat(productList)\n                this.fetching = false;\n                this.curpage += 1;\n\n                if(productList.length < this.pageSize){\n                  this.fetchEnd = true\n                }\n              }\n            })\n          }else{\n            if(flag) this.productCateList = []\n            this.fetchEnd = true\n          }\n        }\n      }).catch(()=>{\n        this.fetchEnd = true\n      })\n    },\n    \n    //切换场景超市\n    changeProgram(tab,index){\n      this.curProgramTab = tab\n      this.curProgramIndex = index\n      this.$refs.proSwiper.swipeTo(index)\n      if(!isAndroid()){\n        document.getElementById(`ptab-${tab}`).scrollIntoView({behavior: \"smooth\", inline: \"center\", block: 'nearest'})\n      }\n    },\n    proTabChange(index){\n      // console.log(index);\n      this.curProgramIndex = index;\n      let tab = this.programTabs[index].tabId\n      console.log(`ptab-${tab}`)\n      document.getElementById(`ptab-${tab}`).scrollIntoView({behavior: \"smooth\", inline: \"center\", block: 'nearest'})\n    },\n\n    changeCate(item, flag, ev){\n      this.curproductCateId = item.productCateId\n      this.fetching = false\n      this.fetchEnd = false\n      this.curpage = 1\n      this.fetchData(true)\n      if(flag){\n        document.getElementById('btmCategoryNav').scrollIntoView()\n      }\n      ev.target.scrollIntoView({behavior: \"smooth\", inline: \"center\", block: 'nearest'})\n      curHeadNavId = 'headNav-'+item.productCateId\n      curBtmNavId = 'btmNav-'+item.productCateId\n    },\n    textStartMove(){\n      setTimeout(() => {\n        if (this.textIndex === this.textList.length -1) {\n          this.textIndex = 0;\n        } else {\n          this.textIndex += 1;\n        }\n        this.textStartMove();\n      }, 2000); // 滚动不需要停顿则将2000改成动画持续时间\n    },\n    uplusBuriedPoint(url){\n      console.log(url);\n      uplusBuriedUrlPoint(url)\n    },\n    onRefresh () {\n      let banner = dataService.getBanner({\n        itemsId: 100,\n        provinceId: this.location.provinceId,\n        cityId: this.location.cityId,\n        regionId: this.location.districtId,\n        street: this.location.townshipId,\n      }).then((res)=>{\n        this.getBanner(res);\n        return Promise.resolve(1)\n      });\n\n      let message = dataService.getMessageList(3).then(res=>{\n        console.log(res,'axios response')\n        let result = res.data;\n        this.textList = result.data || [];\n        this.textStartMove()\n        return Promise.resolve(1)\n      })\n\n      let programTabs = dataService.getProgramData().then(({data}) => {\n        let result = data.data.tabs.splice(1);\n        result.forEach(item => {\n          item.plans = item.plans.splice(0,2)\n          this.programLists[item.tabId] = item.plans\n        })\n        this.programTabs = result;\n        this.curProgramTab = result[0].tabId\n        console.log(this.programTabs, 'programTabs');\n        console.log(this.programLists);\n      })\n\n      let category = dataService.getRecommondKeyWord().then(res=>{\n        console.log(res, 'category')\n        let result = res.data;\n        this.categoryList = result.data.splice(0,7)\n        return Promise.resolve(1)\n      })\n\n      let middleBanner = dataService.getMiddleBanner().then(res=>{\n        let result = res.data;\n        if(result.success){\n          this.middleBannerImg = result.data.middleImagePart1\n        }\n        return Promise.resolve(1)\n      })\n\n      let nearby = this.$store.dispatch('doLocation').then(({latitude,longitude})=>{\n        console.log(latitude,longitude,'latitude,longitude')\n        return dataService.getNearbyShop({\n          latitude,\n          longitude,\n        }).then(res=>{\n          let result = res.data;\n          // console.log(result)\n          this.nearbyList = result.data\n          return Promise.resolve(1)\n        })\n      })\n      // let nearby = dataService.getNearbyShop({\n      //     latitude: this.location.latitude,\n      //     longitude: this.location.longitude,\n      //   }).then(res=>{\n      //     let result = res.data;\n      //     // console.log(result)\n      //     this.nearbyList = result.data\n      //     return Promise.resolve(1)\n      //   })\n\n      let requireGoods = dataService.getRequireGoods({\n        provinceId: this.location.provinceId,\n        cityId: this.location.cityId,\n        regionId: this.location.districtId,\n        street: this.location.townshipId,\n      }).then(res=>{\n        let result = res.data;\n        if(result.success){\n          this.requireGoods.one = result.data.midActivtyList[0][0]\n          this.requireGoods.two = result.data.midActivtyList[0][1]\n          this.requireGoods.three = result.data.midActivtyList[0][2]\n          return Promise.resolve(1)\n        }\n      });\n      \n      let flashSale = dataService.getFlashSale({\n        provinceId: this.location.provinceId,\n        cityId: this.location.cityId,\n        districtId: this.location.districtId,\n        streetId: this.location.townshipId,\n      }).then(res=>{\n        this.getFalshSale(res);\n        return Promise.resolve(1)\n      });\n      \n      let defaultSearch = dataService.getDefaultSearchWord({ platform: 3 }).then(res=>{\n        this.getDefaultSearch(res);\n        return Promise.resolve(1)\n      });\n\n      Promise.race([banner, message, programTabs, category, middleBanner, nearby, requireGoods, flashSale, defaultSearch]).then((values) => {\n        let timer = setTimeout(()=>{\n          clearTimeout(timer);\n          this.isLoading = false;\n        },1000)\n        this.isOnline = true;\n        console.log(values,'===============');\n      }).catch(()=>{\n        this.isLoading = false;\n      });\n    },\n    toSearch (item) {\n      // if(item.keywordValue){\n      //   this.$router.push('/search',{\n      //     query:{\n      //       keyword: item\n      //     }\n      //   })\n      // }else{\n      //   this.$router.push('/search')\n      // }\n      \n      if(item.keywordValue){\n        this.uplusBuriedPoint(`${config.HOST}/sgmobile/goodsSearch?keyword=${encodeURIComponent(item.keywordValue)}&container_type=3&hidesBottomBarWhenPushed=1`)\n      }else{\n        // this.$router.push('/goodsSearch');\n        this.uplusBuriedPoint(`${config.HOST}/sgmobile/goodsSearch?container_type=3&hidesBottomBarWhenPushed=1`)\n      }\n    },\n    toShoppingCart () {\n      console.log(`${config.HOST}/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1`)\n      // this.$router.push('/cart');\n      this.checkIsLogin().then(res=>{\n        // this.$axios.get(`${config.HOST}/bbb.json?${JSON.stringify(res)}`)\n        if(res){\n          this.uplusBuriedPoint(`${config.HOST}/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1`)\n        }else{ //唤起智家登录页\n          instance.initDeviceReady().then(() => {\n            instance.upVdnModule.goToPageForResult({\n              url: 'apicloud://usercenter'\n            }).then((res)=>{\n              if (res.retData.loginByH5 || res.retData.loginSuccess) {\n                this.uplusBuriedPoint(`${config.HOST}/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1`)\n              }\n            }).catch(()=>{\n              \n            })\n          })\n        }\n      })\n      // this.uplusBuriedPoint(`${config.HOST}/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1`)\n    },\n    showModal () {\n      instance.initDeviceReady().then(() => {\n        instance.upTraceModule\n          .reportPageClickEvent({\n            actionCode: 'right-top-more',\n            extentInfo: ''\n          })\n      })\n      this.isShowModal = !this.isShowModal;\n    },\n    /**\n     * 获取banner\n     */\n    getBanner (res) {\n      const { data, success } = res.data;\n      if (success) {\n        this.bannerList = data;\n      }\n    },\n    getDefaultSearch (res) {\n      if (res.data.success) {\n        this.searchWord = res.data.data.hot_word;\n      }\n    },\n    /**\n     * 获取限时抢购数据\n     */\n    getFalshSale(res) {\n      if (res.data.success) {\n        const { data } = res.data;\n        const cloneList = [...data.list].reverse();\n        const systemTime = data.systemTime;\n        let index = 0;\n        const listLength = cloneList.length;\n        cloneList.findIndex((item, i) => {\n          if (new Date(item.startTime).getDate() === new Date(systemTime).getDate()) {\n            if (systemTime < item.endTime && systemTime > item.startTime) {\n              index = i > -1 ? listLength - i - 1 : 0\n              return true\n            }\n          }\n        });\n        data.list && data.list.map((item) => {\n          if (systemTime > item.endTime) {\n            // 已结束\n            item.promotionState = 2\n            item.isSaling = false\n          } else if (systemTime < item.startTime) {\n            // 即将开始\n            item.promotionState = 0\n            item.isSaling = false\n          } else {\n            // 正在疯抢\n            item.promotionState = 1\n            item.isSaling = true\n          }\n          item.title = item.timeStr\n          // const today = new Date(item.systemTime);\n          // const yesterday = new Date(item.systemTime - 86400000);\n          // const tommorow = new Date(item.systemTime + 86400000);\n          // let current = new Date(item.dateStr.replace(/-/g, '/'));\n          // if (today.setHours(0, 0, 0, 0) === current.setHours(0, 0, 0, 0)) {\n          //   item.title = `今日${item.timeStr}`;\n          // } else if (tommorow.setHours(0, 0, 0, 0) === current.setHours(0, 0, 0, 0)) {\n          //   item.title = `明日${item.timeStr}`;\n          // } else if (yesterday.setHours(0, 0, 0, 0) === current.setHours(0, 0, 0, 0)) {\n          //   item.title = `昨日${item.timeStr}`;\n          // } else {\n          //   item.title = item.timeStr;\n          // }\n          console.log(item.title);\n        });\n        this.activeTab = index;\n        this.isSaling = data.list[index].isSaling;\n        this.saleList = [...data.list];\n        this.saleProductList = this.saleList[this.activeTab].products\n        console.log(this.saleProductList,'init');\n      }\n    },\n    bannerClick (item) {\n      console.log('bannerClick:', item);\n      const { hyperLinkType, hyperLink, relationId } = item;\n      console.log('bannerClick-hyperLink:', hyperLink, hyperLinkType);\n      let tempArr = hyperLink.toString().split('&');\n      switch (hyperLinkType) {\n        case '-1': // 日常活动 bannerDaily\n          break;\n        case '0': // 主题活动\n          // this.$router.push(`/bannerTheme/${relationId}`);\n          this.uplusBuriedPoint(`${config.HOST}/sgmobile/bannerTheme/${relationId}?container_type=3&hidesBottomBarWhenPushed=1`)\n          break;\n        case '1': // 单品\n          {\n            const productId = tempArr[0].slice(tempArr[0].indexOf('=') + 1);\n            // this.$router.push(`/goodsDetail?productId=${productId}`);\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/goodsDetail?productId=${productId}&container_type=3&hidesBottomBarWhenPushed=1`)\n          }\n          break;\n        case '2': // 领券中心/优惠券详情页\n          if (!hyperLink) {\n            // this.$router.push('/voucher');\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/voucher?container_type=3&hidesBottomBarWhenPushed=1`)\n          } else {\n            const couponsId = hyperLink.split('=')[1].split(',')[0];\n            const couponFrom = hyperLink.split('=')[1].split(',')[1];\n            // this.$router.push(`/couponDetail/${couponsId}`);\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/couponDetail/${couponsId}/${couponFrom}?container_type=3&hidesBottomBarWhenPushed=1`)\n          }\n          break;\n        case '3':\n          this.uplusBuriedPoint(`${config.HOST}/sgmobile/games/luckyWheel?container_type=3&hidesBottomBarWhenPushed=1&${hyperLink}`)\n          break;\n        case '4': // 主题活动\n          if (tempArr[0].slice(tempArr[0].indexOf('=') + 1) === '主题活动') {\n            const bannerId = tempArr[1].slice(tempArr[1].indexOf('=') + 1);\n            // this.$router.push(`/bannerTheme/${bannerId}`);\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/bannerTheme/${bannerId}?container_type=3&hidesBottomBarWhenPushed=1`)\n          } else if (tempArr[0].slice(tempArr[0].indexOf('=') + 1) === '日常活动') {\n            // this.$router.push(`/bannerDaily`);\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/bannerDaily?container_type=3&hidesBottomBarWhenPushed=1`)\n          }\n          break;\n        case '5': // 自定义类型页\n          {\n            let urls = hyperLink.split('/');\n            if (urls.indexOf('CustomPage') >= 0) { // 跳转bannerDetail\n              const detailId = urls.slice(-1);\n              // this.$router.push(`/banner/${detailId}`);\n              this.uplusBuriedPoint(`${config.HOST}/sgmobile/banner/${detailId}?container_type=3&hidesBottomBarWhenPushed=1`)\n            } else if (urls.indexOf('couponsDetail') >= 0) {\n              // 跳转到优惠券详情\n              const couponsId = hyperLink.split('couponsDetail')[1];\n              // const couponsId = urls.split('couponsDetail')[1].replace(new RegExp('/', 'g'), '');\n              // this.$router.push(`/couponDetail${couponsId}`);\n              this.uplusBuriedPoint(`${config.HOST}/sgmobile/couponDetail${couponsId}?container_type=3&hidesBottomBarWhenPushed=1`)\n            } else if (urls.indexOf('itSoluteDetail') >= 0) { // 成套家装方案\n              const id = urls.split('itSoluteDetail')[1].replace(new RegExp('/', 'g'), '');\n              // this.$router.push({\n              //   path: '/itSoluteDetail',\n              //   query: {\n              //     id: id,\n              //   },\n              // });\n              this.uplusBuriedPoint(`${config.HOST}/sgmobile/itSoluteDetail?id=${id}&container_type=3&hidesBottomBarWhenPushed=1`)\n            } else if (urls.indexOf('fullHouseSolution') >= 0) { // 全屋方案列表\n              // const id = param.split('fullHouseSolution')[1].replace(new RegExp('/', 'g'), '');\n              // this.$router.push({\n              //   path: '/intelligentScene',\n              //   query: {\n              //     scenesId: id,\n              //   },\n              // });\n            } else {\n              // location.href = decodeURIComponent(hyperLink);\n              this.uplusBuriedPoint(`${hyperLink}`)\n            }\n          }\n          break;\n        case '7': // 新品\n          if (hyperLink) {\n            const productId = hyperLink;\n            // this.$router.push(`/goodsDetail?productId=${productId}`);\n            this.uplusBuriedPoint(`${config.HOST}/sgmobile/goodsDetail?productId=${productId}&container_type=3&hidesBottomBarWhenPushed=1`)\n          }\n          break;\n        case '8': // 社区\n          break;\n      }\n    },\n    /**\n     * path 跳转路径\n     * needLogin 是否需要登录\n     * intact 是否是全路径\n     */\n    goPage (path, needLogin, intact) {\n      if(path.indexOf('appVdnPage')!==-1){\n        let target = path.split('=')[1]\n        this.uplusBuriedPoint(target);\n        return;\n      }\n      path.indexOf('?') !== -1 ? path += '&container_type=3&hidesBottomBarWhenPushed=1' : path += '?container_type=3&hidesBottomBarWhenPushed=1'\n\n      if(needLogin){ //强制登录\n        console.log(intact ? path : `${config.HOST}/sgmobile${path}`)\n        this.checkIsLogin().then(res=>{\n          // this.$axios.get(`${config.HOST}/bbb.json?${JSON.stringify(res)}`)\n          if(res){\n            this.uplusBuriedPoint(intact ? path : `${config.HOST}/sgmobile${path}`)\n          }else{ //唤起智家登录页\n            instance.initDeviceReady().then(() => {\n              instance.upVdnModule.goToPageForResult({\n                url: 'apicloud://usercenter'\n              }).then((res)=>{\n                if (res.retData.loginByH5 || res.retData.loginSuccess) {\n                  this.uplusBuriedPoint(intact ? path : `${config.HOST}/sgmobile${path}`)\n                }\n              }).catch(()=>{\n\n              })\n            })\n          }\n        })\n      }else{\n        this.uplusBuriedPoint(intact ? path : `${config.HOST}/sgmobile${path}`)\n      }\n      // this.$router.push(path);\n      // this.uplusBuriedPoint(`${config.HOST}/sgmobile${path}?container_type=3&hidesBottomBarWhenPushed=1`)\n    },\n    toNextPage () {\n      // this.$router.push('/flashSale');\n      this.uplusBuriedPoint(`${config.HOST}/sgmobile/flashSale?container_type=3&hidesBottomBarWhenPushed=1`)\n      // this.uplusBuriedPoint(`${config.HOST}/sgmobile/shopNearby?container_type=3&hidesBottomBarWhenPushed=1`)\n    },\n    goNearby(item){\n      this.uplusBuriedPoint(`${config.HOST}/sgmobile/nearshop?nearStoreId=${item.id}&container_type=3&hidesBottomBarWhenPushed=1`)\n    },\n    toProduct(prod) {\n      // this.$router.push(`/goodsDetail?productId=${prod.productId}`);\n      this.uplusBuriedPoint(`${config.HOST}/sgmobile/goodsDetail?productId=${prod.productId}&container_type=3&hidesBottomBarWhenPushed=1`)\n    },\n    /**\n     * @description 切换tab\n     * @param item tab内容\n     * @param index 下标\n     */\n    changeTab (item, index) {\n      this.saleProductList = [];\n      this.activeTab = index;\n      // this.isSaling = this.saleList[this.activeTab].isSaling;\n      // this.saleList[this.activeTab].products.length < 4 ? this.canShare = false : this.canShare = true\n      // const currentSale = this.saleList[this.activeTab];\n      this.saleProductList = this.saleList[this.activeTab].products\n      console.log(this.saleProductList,'change');\n    },\n    \n    //检查登录\n    checkIsLogin(){\n      return new Promise((resolve)=>{\n        instance.initDeviceReady().then(() => {\n          // instance.upUserModule.getUserInfo().then((res) => {  let sdToken = result.retData.sdToken || result.retData.extras.sdToken\n          // this.$axios.get(`${config.HOST}/111.json`)\n          instance.upUserModule.getUserInfo().then((res) => {\n            // this.$axios.get(`${config.HOST}/222.json?${JSON.stringify(res)}`)\n            if (res.retData.user_center_access_token) {\n              resolve(res.retData.user_center_access_token)\n            } else {\n              resolve(false)\n            }\n          }).catch(()=>{\n            resolve(false)\n          });\n        })\n      })\n    },\n    //回到顶部\n    toGotop(){\n      this.scrollSmoothTo(document.getElementById('homeContainer'),0)\n    },\n    scrollSmoothTo(dom,position){\n      if (!window.requestAnimationFrame) {\n          window.requestAnimationFrame = function(callback) {\n              return setTimeout(callback, 17);\n          };\n      }\n      // 当前滚动高度\n      let scrollTop = dom.scrollTop;\n      // 滚动step方法\n      let step = function () {\n          // 距离目标滚动距离\n          var distance = position - scrollTop;\n          // 目标滚动位置\n          scrollTop = scrollTop + distance / 5;\n          if (Math.abs(distance) < 1) {\n              dom.scrollTo(0, position);\n          } else {\n              dom.scrollTo(0, scrollTop);\n              requestAnimationFrame(step);\n          }\n      };\n      step();\n    },\n  },\n}\n</script>\n<style>\nbody{\n  padding-top: constant(safe-area-inset-top);\n  padding-top: env(safe-area-inset-top);\n}\n</style>\n<style lang=\"scss\">\n::-webkit-scrollbar { width: 0 !important; height: 0 !important; }\n@import '@/assets/scss/minxin.scss';\n\nimg[lazy=\"loading\"]{\n  display: inline-block;\n  width: 100%;\n  height: 100%;\n  background: url(./images/bg.png) center center no-repeat;\n  background-size: cover;\n}\n\n\n.header-android-pd{\n  padding-top: .64rem;\n}\n.header-safe-pd{\n  padding-top: .88rem;\n}\n\n\n.header-nav-warp{\n  display: none;\n  height: .85rem;\n  overflow: hidden;\n  box-shadow: 0 .2rem .2rem rgba(0, 0, 0, .1);\n  &.active{\n    display: block;\n  }\n}\n\n.header-nav-top-box{\n  display: flex;\n  align-items: center;\n  height: 1rem;\n  padding: 0 .24rem;\n  background: #fff;\n  overflow: hidden;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch;\n  div{\n    vertical-align: top;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    flex-wrap: nowrap;\n    &:last-child{\n      span{\n        visibility: hidden;\n        padding-right: 0;\n      }\n    }\n    span{\n      font-size: .24rem;\n      color: #999;\n      padding: 0 .3rem;\n    }\n    a{\n      font-weight: bold;\n      font-size: .32rem;\n      color: #333;\n      white-space: nowrap;;\n      &.active{\n        color: #2283E2;\n        position: relative;\n        font-size: .36rem;\n        &::after{\n          content: '';\n          position: absolute;\n          width: 70%;\n          height: .06rem;\n          background: #2283E2;\n          left: 50%;\n          bottom: -5px;\n          transform: translateX(-50%);\n          border-radius: 6px;\n        }\n      }\n    }\n  }\n}\n\n\n.home {\n  position: relative;\n  height: 100%;\n  background-color: #f5f5f5;\n}\n.home-header{\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  z-index: 3;\n  background: #fff;\n}\n.home-container{\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  overflow-y: auto;\n  -webkit-overflow-scrolling: touch;\n  transform: translateZ(0);\n}\n.flex-1{\n  flex: 1;\n  padding-top: .78rem;\n}\n.header {\n  display: flex;\n  flex-direction: row;\n  height: 0.78rem;\n  background-color: white;\n  align-items: center;\n  justify-content: space-between;\n  padding: 0 0.2rem;\n  .title {\n    color:#333;\n    font-size: 0.4rem;\n    margin: 0, 2rem;\n    font-weight: bold;\n    margin-top: 0.04rem;\n  }\n  .search {\n    display: flex;\n    flex-direction: row;\n    align-items: center;\n    width:4rem;\n    height:0.56rem;\n    line-height: 0.56rem;\n    background:rgba(243,243,243,1);\n    border-radius:2rem;\n    padding: 0 0 0 0.2rem;\n    img {\n      width: 0.24rem;\n      height: 0.24rem;\n      margin-right: 0.08rem;\n    }\n    .text {\n      font-size: 0.28rem;\n      height: 0.4rem;\n      line-height: 0.4rem;\n      vertical-align: middle;\n      text-align: center;\n      color: #999;\n      padding:0;\n      margin-top: 0.04rem;\n    }\n  }\n  .cart {\n    position: relative;\n    display: inline-block;\n    height: 0.42rem;\n    width: 0.42rem;\n    font-size: 0;\n    span{\n      position: absolute;\n      right: -.21rem;\n      top: -.1rem;\n      padding: 0 .08rem;\n      height: .28rem;\n      line-height: .28rem;\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      font-size: .2rem;\n      color: #fff;\n      border-radius: .3rem;\n      background: #ED2856;\n    }\n    img{\n      width: 100%;\n      height: 100%;\n    }\n  }\n  .service{\n    width: 0.5rem;\n    height: 0.5rem;\n    margin: 0 .2rem;\n    object-fit: contain;\n  }\n  .more {\n    width: 0.4rem;\n    height: 0.4rem;\n    object-fit: contain;\n    margin-left: .2rem;\n  }\n}\n\n.header-top{\n  padding-top: .12rem;\n  /* padding-bottom: .32rem; */\n  font-size: 0;\n  background: linear-gradient(to bottom, #ffffff 0%, #ffffff 70%,#f5f5f5 100%);\n}\n\n.banner {\n  height: 3.6rem;\n  /* margin: .24rem; */\n  margin-top: 0;\n  /* border-radius: .2rem; */\n  background-color: white;\n  overflow: hidden;\n  img{\n    width: 100%;\n    transform: translateZ(0);\n    vertical-align: top;\n  }\n}\n.tab-row {\n  margin: .24rem;\n  height: 1.6rem;\n  display: flex;\n  flex-direction: row;\n  align-items: center;\n  justify-content: space-around;\n  .tab {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    img {\n      height: 1rem;\n      width: 1rem;\n      margin-bottom: .25rem;\n      object-fit: contain;\n    }\n    span {\n      color: #333;\n      font-size: 0.22rem;\n    }\n  }\n}\n.middle-banner{\n  /* padding: .08rem .24rem .2rem; */\n  margin-top: -1px;\n  text-align: center;\n  height: 2.24rem;\n  vertical-align: top;\n  img{\n    width: 100%;\n    vertical-align: top;\n    transform: translateZ(0);\n  }\n}\n.shop-notice{\n  margin: .24rem;\n  padding: 0 .26rem;\n  height: .76rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #fff;\n  border-radius: .16rem;\n  strong{\n    font-size: .32rem;\n  }\n  span{\n    font-size: .24rem;\n    color: #D8D8D8;\n  }\n  img{\n    width: 1.3rem;\n    height: .34rem;\n    margin-left: .15rem;\n  }\n  .notice-list{\n    width: 4rem;\n    height: .44rem;\n    overflow: hidden;\n    font-size: .24rem;\n    color: #333;\n    \n    p{\n      width: 4rem;\n      line-height: .44rem;\n    }\n  }\n  .notice-more{\n    display: inline-flex;\n    align-items: center;\n    font-size: .24rem;\n    color: #999;\n  }\n}\n\n.slide-enter-active, .slide-leave-active {\n  transition: all 0.6s linear;\n}\n.slide-enter{\n  transform: translateY(.44rem);\n}\n.slide-leave-to {\n  transform: translateY(-.44rem);\n}\n\n.home-category{\n  margin: .24rem;\n  padding: .12rem 0;\n  height: 3.8rem;\n  display: flex;\n  flex-wrap: wrap;\n  background: #fff;\n  border-radius: .16rem;\n}\n.category-item{\n  width: 1rem;\n  margin: .12rem .375rem;\n  font-size: 0;\n  img{\n    width: 1rem;\n    height: 1rem;\n    border-radius: .5rem;\n    vertical-align: top;\n  }\n  p{\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin-top: .2rem;\n    text-align: center;\n    color: #333;\n    font-size: .22rem;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n    height: .3rem;\n  }\n}\n\n.zj-program{\n  margin: .24rem;\n  border-radius: .16rem;\n  background: #fff;\n}\n\n.program-tab-warp{\n  height: .5rem;\n  line-height: .4rem;\n  overflow: hidden;\n  margin-bottom: .1rem;\n}\n\n.program-tab{\n  margin-top: -.1rem;\n  height: .8rem;\n  white-space: nowrap;\n  padding: 0 .26rem;\n  padding-top: .1rem;\n  overflow: hidden;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch;\n  span{\n    display: inline-block;\n    vertical-align: top;\n    margin-right: .36rem;\n    height: .36rem;\n    font-size: .26rem;\n    color: #666;\n    &.active{\n      position: relative;\n      color: #2283E2;\n      font-weight: bold;\n      &::after{\n        content: '';\n        position: absolute;\n        bottom: -.1rem;\n        left: 50%;\n        transform: translateX(-50%);\n        display: block;\n        width: .28rem;\n        height: .06rem;\n        background: #2283E2;\n        border-radius: .1rem;\n      }\n    }\n  }\n}\n\n.program-product{\n  display: flex;\n  padding: 0 .22rem;\n  justify-content: space-between;\n  align-items: flex-start;\n}\n\n.program-swiper{\n  width: 100%;\n  height: 2.9rem;\n  padding-bottom: .24rem;\n}\n\n.program-product-item{\n  width: 3.2rem;\n  font-size: 0;\n  padding-bottom: .24rem;\n  p{\n    height: .88rem;\n    padding-top: .2rem;\n    font-size: .26rem;\n    color: #333;\n    line-height: .36rem;\n    @include text-overflow(2);\n  }\n}\n\n.program-product-img{\n  position: relative;\n  width: 3.2rem;\n  height: 1.72rem;\n  display: inline-flex;\n  border-radius: .1rem;\n  overflow: hidden;\n  .van-image{\n    overflow: hidden;\n    border-radius: .1rem;\n  }\n  img{\n    width: 100%;\n    height: 100%;\n  }\n}\n\n.program-play{\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: .64rem;\n  height: .64rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  background: rgba(0,0,0, .5);\n  transform: translateX(-50%) translateY(-50%);\n  border-radius: .32rem;\n  span{\n    margin-left: 1px;\n    display: inline-block;\n    width: .24rem;\n    height: .28rem;\n    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAcCAYAAAB75n/uAAABFElEQVRIS7WWsU4CQRRFz/0TGgsTfw0SodFafwCtafgH/QdsTSiwhERq6ExwyU12ErMRhlnmbbmZvWfmzd37ngh+1DTNDTABNsBU0r4m04BP4K4V/QYegbmkpgbIgF9AHbEFMJL0cS3EgFM7NXjuE0na9gWdAyTNHfAEvEj6KQVdAkiaK+Be0nsJpASQdN9a0NcloD4A67pUU5cuZ+u+gLT5rK2vBSTQSVvXAhj0r61rAv7aeixp5hcRAOsegIGkTRTA5TJgHQHwnz+U5JipWqLQSw6zqVP24Vz/6HsHoVHhsLPPna7Zp+QEYXFt2z23A0HVhlOtZYY3/SVwGzm2ePAaA2vgNdehsrbpLOjOQ6XfZ9cfARdlqB10St37AAAAAElFTkSuQmCC) no-repeat;\n    background-size: contain;\n  }\n}\n\n\n.find-goods{\n  margin: .24rem;\n  border-radius: .16rem;\n  background: #fff;\n}\n\n.home-title{\n  height: .94rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding-left: .26rem;\n  padding-right: .18rem;\n  /* border-top-left-radius: .16rem;\n  border-top-right-radius: .16rem; */\n  span{\n    font-size: .32rem;\n    color: #333;\n    font-weight: bolder;\n  }\n  .more{\n    display: inline-flex;\n    align-items: center;\n    font-size: .24rem;\n    color: #999;\n  }\n}\n\n.goods-time{\n  padding-bottom: .12rem;\n  height: .8rem;\n  background: #fff;\n}\n.goods-time-warp{\n  margin-right: .24rem;\n  padding-left: .3rem; \n  height: 100%;\n  overflow: hidden;\n  overflow-x: auto;\n  white-space: nowrap;\n  -webkit-overflow-scrolling: touch;\n}\n\n.time-item{\n  display: inline-flex;\n  flex-direction: column; \n  justify-content: space-between;\n  align-items: center;\n  vertical-align: top;\n  width: .8rem;\n  margin-right: .6rem;\n  &:last-child{\n    margin-right: 0;\n  }\n  &.active{\n    strong{\n      color: #2283E2;\n    }\n    span{\n      padding: .04rem .08rem;\n      background: #2283E2;\n      color: #fff;\n      font-size: .2rem;\n      border-radius: .16rem;\n    }\n  }\n  strong{\n    margin-bottom: .04rem;\n    font-size: .32rem;\n    font-weight: bolder;\n    color: #333;\n  }\n  span{\n    display: inline-flex;\n    width: .8rem;\n    padding: .04rem .08rem;\n    justify-content: center;\n    align-items: center;\n    font-size: .22rem;\n    color: #333;\n  }\n}\n\n.goods-list{\n  padding-bottom: .2rem;\n}\n\n.goods-list-box{\n  height: 2.8rem;\n  overflow: hidden;\n}\n\n.goods-list-warp{\n  padding: 0 .3rem .24rem;\n  overflow: hidden;\n  overflow-x: auto;\n  white-space: nowrap;\n  -webkit-overflow-scrolling: touch;\n}\n.goods-item{\n  display: inline-block;\n  vertical-align: top;\n  margin-right: .2rem;\n  &:last-child{\n    margin-right: 0;\n  }\n  .van-image{\n    overflow: hidden;\n    border-radius: .1rem;\n  }\n  img{\n    width: 1.92rem;\n    height: 1.92rem;\n    border-radius: .1rem;\n  }\n  div{\n    font-size: .24rem;\n    font-weight: bolder;\n    > p{\n      padding: .08rem 0;\n      font-size: .28rem;\n      color: #ED2856;\n      span{\n        font-size: .24rem;\n        font-weight: normal;\n      }\n    }\n  }\n  del{\n    padding-top: .08rem;\n    font-size: .24rem;\n    font-weight: normal;\n    color: #999;\n  }\n}\n\n.nearby-shop{\n  margin: .24rem;\n  border-radius: .16rem;\n  background: #fff;\n}\n\n.nearby-shop-box{\n  height: 2rem;\n  overflow: hidden;\n}\n\n.nearby-shop-warp{\n  height: 2.5rem;\n  padding: 0 .3rem .24rem;\n  overflow: hidden;\n  overflow-x: auto;\n  white-space: nowrap;\n  -webkit-overflow-scrolling: touch;\n}\n\n.nearby-shop-item{\n  display: inline-block;\n  vertical-align: top;\n  width: 4.94rem;\n  height: 1.78rem;\n  padding: .16rem .14rem;\n  margin-right: .16rem;\n  border: 1px solid #eee;\n  border-radius: .16rem;\n  box-sizing: border-box;\n  &:last-child{\n    margin-right: 0;\n  }\n}\n\n.nearby-title{\n  display: flex;\n  align-items: center;\n  font-size: .32rem;\n  font-weight: bold;\n  overflow: hidden;\n  i{\n    display: inline-block;\n    width: .48rem;\n    height: .48rem;\n    margin-right: .06rem;\n    background: url(data:image/png;base64,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) no-repeat;\n    background-size: cover;\n  }\n}\n\n.nearby-tips{\n  padding-top: .14rem;\n  display: flex;\n  align-items: center;\n  > p{\n    display: inline-flex;\n    align-items: center;\n    font-size: .24rem;\n    color: #666;\n    i{\n      display: inline-block;\n      width: .24rem;\n      height: .28rem;\n      margin-right: .08rem;\n      vertical-align: top;\n    }\n    .icon-auth{\n      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAcCAYAAAB75n/uAAAAAXNSR0IArs4c6QAAA8JJREFUSA2tlU1IVFEUx+db01IHhUiQisBVUPRBi6AWmYGZUeFYUYrOoBRBq8hFZFEE0Sooya9EGylHggiUREuoRQQZuSyLiIFcVDiL0nTGmX7nde/jTY2OhhfuO/ec/zn/c+7ns9mW0AYGBjKkLyHEZl+sc0tLyxa73R4U/0QicaKhoeHNYmLTJhgZGXGNj483QnoRQrcijTocjku5ubnXfT7f3EKJFkwgVRPcQt9G9eRI3BQyxmcZI+yjqIH6+vq3Yk/VUiZobW0thqCRgBq6A6LPSCEaFhLwEkQ7PmuRcfAgM7oSCAQ+CG5tZoJgMJgzNTV1kKDjBOxTFc7ifCsvL6+JpfhhDQyFQisjkchlbGfw9RCDSAwhu91ud39tbW1E/I0ELMUzgJ3iaBjt9l/Ie+jX2cyPYpuvEbuB2PPgJ/HPVH5RbC+Y8R4jAVOOAToBnwPcz8jI6Kupqfk+H2kqe1dXV/7MzEwlPMfAd9FjFOd2iTPGn4icoqKi0rKyshmxLbWpgu5wTzrD4bCswJRwOBSRJLBNTEzkK/2/hYXD4DRmwLJEmMWaWCwmCb4shp1NdrLJt4kr93g8G/WmxuPxAokXTpHGDHAyjpfT6VwnxnQNcs/k5GSIuAZ8J7Ozs81lhViOrrRx+RgJML4TheybRFpbW1ubn0NQrm3d3d3ZVN6Pfpj+iqJ2c4SnNW7heC82vURjVCP6dvlYGwHXKCCfREeQL6anpwfAdzAeyszMPFRdXW2stSXG4AAfE5sxA6oYZBwnSQnnOsvibAM7jl2O8QOSvQQT8odcvvK/ySUWv73CpTj/JKirq/tK0GuALK78AWsCrv9T9KN0N8HF+HVAXsWyyC1Paip2hXAJp4DGDGSAsVUkVcqtTGpcmEfgFfRT3M7AfC+ojtVcQmK+RXIy2LxPVFmI3QdpX1KWNArLU4lLCPIvzHC9nqE5AzFA3qR4mtnU1Wk4TVj5NotBODS56GYCUai6HfGEXoBjb2dnp368BE7ZxIelCQHKBXuiOEzfpARidblcfkSYBLuj0WgvU9d/MTNIDwQTH3R53MIqVsOG/CeB3++Xp6KU/o0kFcjBnp4er+Ft+SjboPL5BlSqYi1elk1OsqJwezcj+iEoZOPkVlbpX6PCesHk2EpB+zXGOKmZpyjJqpSOjo7Cubm5xxBthWiWfkEg9Kt0+YuNcqEqUlWu+RZMIE4c3xU8bDcgOw2p4c+YYaLZ6/We48SY75Amtcq0CbQzy1IC6V3RSVDHkgxrbNkkS7ZK+rIRLgfRb7fivcqKAGhqAAAAAElFTkSuQmCC) no-repeat;\n      background-size: cover;\n    }\n    .icon-address{\n      background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAcCAYAAAB75n/uAAAEdElEQVRIS7VWTWxUVRT+zi2ldOFCiDJqokZRxOBCCwETBBYs1A0Jiop/kdJm7n1tmoitRkMyToyagDQSnHfvaxsmRI1YfxLcqDEqwQjEiImaKPiXEBVaFVxY00ln5h5zxjflzXRoJzGezcy775zz3fOdv0f4n4Wa9Z/L5VJEdCkRtRSLxV/6+vp+b8Z2VoBcLrdEKfUogDsBXF3n8GcAB5n5pSAITl4IrCHA6Ojo/HPnzr3AzAbAvNj4DwC/ElGZma8CsCg+LwKIxsbGBrLZbKEeaAaAtVZoeIeZVwGYApD33ruenp4vAXDVgbX2ZgDdALoAtAI4WiwWN9ZTVwMQRVErM3/EzGuI6Efv/aYgCL6ajWtr7XKhCsA1AI4opdan02mJqiI1AM65XczcD+BUW1vbys7OzqYSKVED+BTAEmYeDILgsRkAURRd6b3/DkALM68OguB48uZRFK3x3gttLUR0TGv9SZKyXC63UiklINza2np9V1fXqZoIrLWDAKRi9htjHklwfQURHRDa6qg66r2/t6enR6qpIs65fcy8lYhe1FqLr/MUWWu/lxABrDLGfCYvBwcH29vb2z8HcCOAcQBvMXOZiO4GcBmAkxMTEx0DAwN/i34Yhh1EJPo/GWOunQbI5/OpQqFwBsBZrfUlRFSpFmttH4A9RHRiampqbbVCRkZGFpZKpUPMfBOAfmPMbtFnZnLOSd4WKaUuT6fTZypJjqLoFu+9cH7cGLMiEfK7zHw7gAeNMa/W5WSz936UiD7UWm9IUCrRr2TmFZLHCkAYhrcR0WEAh40x6xIAx6QfmHltEASS1Glxzt3KzEcAfGGM6UgAfAxgPRGt01ofrgIsFRqEU2PMDQnlVwA8ACBrjHk6CRCG4Q4ieoaIXtda35ewET9LlVLL0un0iQpAPp9fUCgU/pSOLJfLi3t7e8/GkW0gog8AFJRS206fPn1AzlOp1D0A9gFoJ6I7tNbvxfoXA/iNiEqTk5MLt2/fPjndaM65Kt/bjDFiXBFr7V4AvfHjX/HvRZUKIXJaa5lXFXHObWVmsX3fGCO5O1+mURTd772XRP4wNja2LJvNlhKGmpmfSEzUU0S0U2sdVnUymcy8VCr1DYDrkkUxHUEmk1GpVOrruOZ3G2NkZNTI8PDwYjno7u6WnqgR59xOZh5g5m/Hx8eXZ7NZXxNBzOFqIjpERPOZOW2MGa531Og5DMMuIhqS6cvM64MgOFbVmzGuRVkpNcT/SmcQBPtnA3HOPczMeZLubHCphgvHOdfPzLsAyFh4SGv9WiMQ59wWZn45HpCPB0EgNjVywZXpnHuSmZ8DUCKiLVrrN5OWQ0NDd5XLZSlb2Xg7jDHPNrrErDs5iqKM914arEhEm7XWslikHDcy8xvxJpvRhEmgOb8qrLVys6ckgUqpTXFu3gbQRkTPa63l3QVlToD4xtVNV13qCwA0LOWmc1CvaK3dA0DGt3TwXq115f9c0lQEVSdhGFopR2OMjIfpL4z/TFHVgSyUOIKmnIvuP0urETsr927CAAAAAElFTkSuQmCC) no-repeat;\n      background-size: cover;\n    }\n  }\n  > span{\n    margin: 0 .14rem;\n    width: 1px;\n    height: .2rem;\n    display: inline-block;\n    background: #d8d8d8;\n  }\n  > i{\n    display: inline-block;\n    margin-left: .16rem;\n    width: .28rem;\n    height: .28rem;\n    background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABwAAAAcCAYAAAByDd+UAAAEhElEQVRIS71We0xbZRT/ndvevksLDB3b3MAHLT6XkTiIImoMYDQxIi26uCXqZsLcgplRfKCiYXNjvtCpM4Zl0cw5IGpEl21O1OEEmcZsLgKiMZsPGKMUWqAvej9zL7S0t7dAZsJN+ke/8zvnd875zjnfIczxMYBGcp2FDLgDAitgRFcQkCqqMcBNjPWBow4CvrB2N7WTdJz8o9mELpujHER1AGxzOTYt7wVjNem9zS3J8IqEIzZHdpjoQwD58ySSwzpVjK2x9jb/KRckELrtjiIBJHq46ALJImpDHFh5ak/zt7F24ghFMgY6wgDN/yST1AkIElhxLGmUcDqNXfLIKN0CMuohnB2I+qDOs0OVsxyB/UeiZ1zmInBLMzD5Y7fc1yEVY9dH0hsldNmdHUp3ZtxWCdWV2fDcXQ2wqQI07twM1WXL4Cmrjho3vPgw1PYseJxPKyWnM72nqWA6amC6GpuVkLQ4HdZDr2O89j0EPz0mQSxfv43gl13wbdsr/RcdSjmwFWOVOxD67qTybTDmEKuXxD4btjvFPMSVPpkMILNBUtZtuAuC24tgSxvoolRYPqrDeN0ehNp+kuR8aT74lTmY2P6+9J95J8DGJuTEvWk9TbnkznXeJDDEVZKI1D9SDv0mxwXVjm9XM3xvJbYiRygiV25FPRh7PKFfUs2gVHPcMWk1MO99DqGjXfA1fpbUGeb2Qvwl2CTU07Ct4hgjVjifUMTUilGPlFSBDbjmoxLvMKN2ctmd/QAWx0o0ZTeDX311gkH+ljwgEETo+1/mJAv9cBrBj7+R4wbEgvEzQBsr0VbcBv6Ga2eO9DpobrwO4b8HEe6WTSudFprClZg8/QeE/qGoTuj4KQQOHI2PEAgoEsaiKC0Fpt1PgrOY4Fn7PNigOyE640sboV5lg+f+WrDzifKIAomESimNALjsJTC/Uw3wanjX1kL497xyKrU8TG88BnXOcnirXkX41O/JUj6gWDSUYoSusgy6NaUQBl0Qzrkh/JOELGKaV0N1+TKoLl0K/wcH4d/9CdjomKxoWDsN5zp3MIYnohKDDtbDDSCdFhNvNiF4qAPGmgckMbckAyp7FkJtJ+Ij0PDQFK2C56E6iDPVUL0ObNyH0dsfBfzBKJbEtlBqfL4kH5M/9ybcl7F+E7g0C7zrt8YRio5Yv9qF0XtrED7ZB8qwQrUiM2GQS42fbLTJL4G75GJYWl/B+LPvItjaHidWXZUNS8t2jJRWQTgz86rIbEyNNvFwtuEtKem1MDc+I6XZU/4UIAhxtvji1TA3bMFw3jpgIqBcMJHhHZEme564rEyYGraAS7fA+2Adwr+dTTBoqF0PvuAajJZUJavO+OdJRMkfYLH/9Jsd0N5zK8Jn+jG2cSeEv85JBrX3FQNaHggLUNtWQJxM/j2t8L28T4lQ+QEWkbErBpn0MDXWIHjwOAL7DgOT4ZlCfmED+Pyp0cf8QYRO/Arfa/uBcX98G8y2YkSQC7pERUgXdE2MzcmCLcKxpJFVXwDdSQLyGTHZqk99jEMnB/b5fFb9/wAn99A2Lv4f5QAAAABJRU5ErkJggg==) no-repeat;\n    background-size: cover;\n  }\n}\n\n.nearby-info{\n  padding-top: .16rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  > p{\n    font-size: .22rem;\n    color: #999;\n  }\n}\n\n.live-banner{\n  margin: .24rem;\n  img{\n    width: 100%;\n  }\n}\n\n.require-good{\n  margin: .24rem;\n  border-radius: .16rem;\n  background: #fff;\n}\n\n.require-goods-warp{\n  display: flex;\n  justify-content: space-between;\n  padding: .02rem .24rem .24rem;\n}\n\n.require-goods-left{\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 3.2rem;\n  height: 4.16rem;\n  img{\n    width: 100%;\n    width: 3.2rem;\n    height: 4.16rem;\n  }\n}\n.require-goods-right{\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  width: 3.18rem;\n  height: 4.16rem;\n  div{\n    height: 2rem;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n  }\n  img{\n    width: 100%;\n    width: 3.18rem;\n    height: 2rem\n  }\n}\n\n.bottom-category{\n  margin: .24rem;\n  margin-top: 0;\n}\n\n.btm-category-nav{\n  padding-top: .16rem;\n  /* display: flex;\n  align-items: center; */\n  height: .88rem;\n  line-height: .48rem;\n  padding-bottom: .24rem;\n  white-space: nowrap;\n  overflow: hidden;\n  overflow-x: auto;\n  -webkit-overflow-scrolling: touch;\n}\n\n.btm-nav-box{\n  height: .55rem;\n  overflow: hidden;\n}\n\n.btm-nav-warp{\n  height: .8rem;\n  overflow: hidden;\n  overflow-x: auto;\n  white-space: nowrap;\n  -webkit-overflow-scrolling: touch;\n}\n.btm-nav-warp div{\n  display: inline-block;\n  vertical-align: top;\n  font-size: 0;\n  &:last-child{\n    margin-right: .24rem;\n    span{\n      display: none;\n    }\n  }\n  a{\n    font-weight: bold;\n    font-size: .32rem;\n    color: #333;\n    &.active{\n      color: #2283E2;\n      position: relative;\n      font-size: .36rem;\n      &::after{\n        content: '';\n        position: absolute;\n        width: 70%;\n        height: .06rem;\n        background: #2283E2;\n        left: 50%;\n        bottom: -5px;\n        transform: translateX(-50%);\n        border-radius: 6px;\n      }\n    }\n  }\n  span{\n    font-size: .24rem;\n    color: #999;\n    padding: 0 .3rem;\n  }\n}\n\n\n.btm-category-list{\n  display: flex;\n  flex-wrap: wrap;\n  justify-content: space-between;\n}\n\n.category-empty{\n  padding-top: .4rem;\n  text-align: center;\n  img{\n    max-width: 40%;\n  }\n}\n\n.btm-list-item{\n  display: inline-block;\n  margin-bottom: .24rem;\n  width: 3.44rem;\n  padding: .24rem;\n  background: #fff;\n  border-radius: .16rem;\n  box-sizing: border-box;\n  .btm-item-img{\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    width: 3rem;\n    height: 3rem;\n    overflow: hidden;\n    img{\n      max-width: 100%;\n      max-height: 100%;\n    }\n  }\n  .btm-item-name{\n    margin-top: .16rem;\n    margin-bottom: .08rem;\n    height: .7rem;\n    font-size: .24rem;\n    color: #333;\n    overflow : hidden;\n    text-overflow: ellipsis;\n    display: -webkit-box;\n    -webkit-line-clamp: 2;\n    -webkit-box-orient: vertical;\n    word-break: break-all;\n    line-height: .35rem;\n    span{\n      margin-right: 2px;\n      display: inline-flex;\n      justify-content: center;\n      align-items: center;\n      width: .6rem;\n      height: .28rem;\n      border-radius: .04rem;\n      font-size: .22rem;\n      color: #fff;\n      background: #ED2856;\n    }\n  }\n\n  .btm-item-sellpoint{\n    display: flex;\n    align-items: center;\n    flex-wrap: wrap;\n    padding-bottom: .1rem;\n    height: .44rem;\n    span{\n      color: #666666;\n      margin-right: .1rem;\n      margin-bottom: .12rem;\n      background-color: #f6f6f6;\n      border-radius: 0.14rem;\n      padding: .06rem .08rem;\n      font-size: .2rem;\n    }\n  }\n\n  .btm-item-footer{\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    .disabled{\n      color: #999;\n    }\n    p{\n      font-size: .32rem;\n      font-weight: bold;\n      color: #ED2856;\n      span{\n        font-size: .28rem;\n        font-weight: normal;\n      }\n    }\n    strong{\n      font-size: .24rem;\n      font-weight: normal;\n      color:#2283E2;\n    }\n  }\n}\n\n\n.btm-item-commit{\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  span{\n    margin-right: .16rem;\n    padding: .2rem 0;\n    padding-bottom: 0;\n    font-size: .24rem;\n    color: #999;\n  }\n}\n\n\n.bottom-text{\n  padding: .2rem 0;\n  margin-bottom: 1.2rem;\n  text-align: center;\n  font-size: .28rem;\n  color: #969799;\n}\n\n\n.popup-container {\n  position: absolute;\n  top:1.28rem;\n  right: .24rem;\n  width: 2.48rem;\n  padding: 0 0.24rem;\n  background-color: white;\n  border-radius: 4px;\n  z-index: 6;\n  box-shadow: -.2rem .2rem .4rem rgba(0,0,0,.2);\n  box-sizing: border-box;\n  &::before{\n    content: '';\n    display: inline-block;\n    position: absolute;\n    top: -.14rem;\n    right: .2rem;\n    width: 0;\n    height: 0;\n    border-left: .12rem solid transparent;\n    border-right: .12rem solid transparent;\n    border-bottom: .16rem solid #fff;\n  }\n  .text {\n    padding: .24rem 0;\n    color: #333;\n    font-size: 0.28rem;\n    border-bottom: 0.1px solid #eee;\n    display: flex;\n    align-items: center;\n    flex: 1;\n  }\n}\n\n.icon-message{\n  @include icon-common();\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAyCAYAAAAayliMAAAAAXNSR0IArs4c6QAABSpJREFUaAXtmF1oXEUUx5PdfO+u2IZgsyqBPIj4FEzWEBM0Igajm+o+LKL1tfSlIgEfVES3oiWIWEEoSBEf+vGSh/jBEhpFg2QTYxLQIkKlSUiNiTVubU0i+dqNv7PcG24vu3fmZrf4cgeG+Thn/v9zzsydOzNlZV7yIuBFwIuAFwEvAl4E/r8IlJeKOh6PVy0sLDyWyWQOl5eXPwBueHd3Nyz4tJcolmj/4vf7v2hqavp2cHBwS2TFpqIdaG1tbcTANzHkCAaGTIPou0592Wg3Ijtoka1SP0/f2zMzM6aOKXZV7tuB7u7umrW1tTcwoh/GOgxepByi/DwQCKRGR0c3rJaI/vr6eif6z9Afo7yH8l/0TwWDwXfs+taxTvV9OdDZ2Rne3NwcwoiHMOAa5YlQKHQGI3acyEwZzlSsrq4eZexbjL2L8ofq6upYKpWSpeYquXYgEom0ZLPZJCyyvi9g+DEMX3PFaijjSBBHPqb5AnnJ5/M9PTU19aMbLFcOSOQ3NjamIGgkv876HXBDVkiX7+hVZCfJyzU1NRE3M+EvBGrv7+joqN3a2rpI//3kkhkvPMvLy2PhcHiTaoxd7BF2qXOLi4tay9EnADppe3tbPtgIuudLFXkrr4EpO1NEuKwyp7qWA0T/bkD6+dj+qK+vP+YEWIxMsIVDuAxOJZyWAywd2S1qQUuMjIysK1H3qWBgJ4RLOHVglA709vZWA/Q8+Sr79Sc6oMXoGBxXhdPgdoRTOrCysvI4CEHykO4+78ioEBocQ8JpcDuOUDrA6MOCwNr8zBGphEILV47bCVrHAdk2yxoaGiacgETGgc7f1tbWKccGu670iUx07DJ728KV47bLrW2lA3xQYSKSHh4eln3aMc3Nzb2C/hh/14/sitInMtGxy+xt4RJO4bbL7G2lAwyQY7HWGQVS2W5lueVKKxl9cnjLK7PqWepySi2JA1UAVViA91vddTOQoPlwOqMaozQMkN8AyUVPBQbp76JjllZ9s88srbIC9Ub6ZRYck84SmoU01N7efocjEsLm5ub3cbiLE+pLdl3pE5no2GX2tpy76DtAVjqgnAFALpOf4JDVTvkVuWDimihTnsqnwP4uF5zU9PR0PvEtfTs7O49KB4H76RZBnoZyBgD50hj3XJ7xt6WL+0Zu/5f7s4pA6QBT/w1TnwYoxrm9UgVYrFz+F/DFwLlRV1f3nQpP6YDxaz/LTBwE+LgKsFg59+bjcB2CS+uKqnRADALwXYqb5BPyCiF9tyN1dXUdgEtuZzcrKysHdDi0HOCy8RcROQm4PJucpnR1FdUxJJFI+LiuXgC7Hv2BiYmJ6zrjtBwQII65H+DE11Sf5WJ/WgfcjU4ymZQAPcmYi319fe/pjnUVSZliovQ9RPdBMMDMvKZLVEhPDnfz8/OnwJR/x2xtbW1kbGzs70L69n5XDshgov8i29xZqru85RwaHx//0w6q2zbel85g/FPM7s9VVVVR8BZ0x4ueKwfY4u7kNe4ShPcy9hI/pRaIXZ1xhLSnpyeQTqf7qcoHGyCPVFRUxCcnJ/+h7irp/In3ADFePmAxXo67R3SNl/8H0W3itaGF8XGMj4Ihz5HXKF+ORqOf8hFn94hcVLRngKXzMEvHPCbcgOMyBvgxSE6NPkvpN9voiKyaUh539y4y9P1K+xyPWB/yiCUPvftO2jPAb30JB+ReIK/RIQx4kJylnqXMGKXZlmiadTkDjZNn0blCTrp9PmSsl7wIeBHwIuBFwItA3gj8B+0XK6KrbCz/AAAAAElFTkSuQmCC) no-repeat;\n  background-size: contain;\n}\n\n.icon-order{\n  @include icon-common();\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAqJJREFUaAXtWb9v00AUvjubEVRBkqqsTeI4UwYGlkIHBlDV/wABEwszQuLXwICE+C9ADF1ZEVCpI50QiRtXYkBIHeIwVSiQxI/3ol5lpY599jkyw51k2bn73vfd+97FUV8ZM8M4YBwwDhgHNBzgWWLbzfZGCNOHGHQFANayxKZhOedHwNi+4Nbrnt/bS8PLdUs+pN3bzdbjMJy+RZyD1/k0fI514nQAwru1SnUyGAZKSShVwG241wCmu+jSmAF7zs6JN57nHeXY5MIQ13XX2Di8wzh7gdW1hbA3s1RiITEttBrO+1a9CW7deZQILGCRNEiLNFXohAqIzvwMh86r4LUwJxqnmilkSgnIL2zRxyZub1JDasZhonNKCUQD/rdnk0DZFbF1N9DpdFZGo9F6Hh7Lsvrdbvc4T6yM0U7gz/HvL0hWl4RZ7uEEfMTTD2PuoZ0A43wHf9xu5toBZx9zxUWCtBPwDvtPkY+uUoZ5C5Vie0RU+wi1687tkMNGhFP5kXP2yfP9HeWAGKB2AiFnr/BLfDmGW2GKbyOo3ARswW5NQFxV2O1ZiMX3z05mm9GuwLd+/ytK0lXKMG+hUmyPiGofIbfR/ADAbkQ45eOIC3ZP9y0jyRbdtRNA4r9x5Pj3sw2AKSx5aCfgHfpbS95jIv3SHUpUL2DRJFCAiVoUpgJa9hUQrFQBaryS1qz9V4BoEoXUkJpJWFpTSoC6xjMi6l0ue5xonGqm6Cl1p2uV1Z/UNUZXrlcvVsbV1dr3IAi0ugnz+yLnqyuXHlBzF9c4NnfvD4aDH/O4+c9K3WkKchvOE2z3EblS1eaFMnwOhRDPev7BS5UYpQoQUfBruIeV+AwMKliJCzhV6P8IZmee811yvud771Q2bzDGAeOAccA4oO3AP20MrPxj7LRFAAAAAElFTkSuQmCC) left center no-repeat;\n  background-size: contain;\n}\n\n.icon-fav{\n  @include icon-common();\n  background: url(data:image/png;base64,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) no-repeat;\n  background-size: contain;\n}\n\n.icon-quan{\n  @include icon-common();\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAA3tJREFUaAXtWE1oE0EU7uaPCOaguVTbEol68iKmSQjRmxgwCN4ED14UPAg9FVFEzEmsIgoi4kUQKYhHExVEMEIgCSYWhZwEpZjWQwStCeY/9XvTXQ+lszPdbGsPMzA7O/Pe+97f/O2OjKiiIqAioCKgIqAioCLw/yKgyaiORqPjvV5vBrzHUHeSjKZpy2iWqVAfhfX1cdbXaf/ohoxBNGSob9Ags4j3rNfrvZbL5X4SzawIHQiHwxMAnUP1mwHZTYMT36DzULlc/mGG7TIjEg0gN8l4AL5CPZ9MJhcqlQpzvFarafV6XQsGg6zfaDS0ZrOptdtt1u90Oprf79eoJaxut6shk6xS3+fzsfd+v8/o1KKMgec+dB7G+w2wnSNeXmGCPCKNT05OfgfYqMfjGc/n8wtmvHbRYrHYPjj9GXjzyMAeM1yHGZFoZDy1m2U86UokEl+oRZlYafhPoQN80a1B2ZIOGGsMa8DYwbjR2pIOcK1dg2CbA1jsL6iuoWPdQ7S7yQpJO4Dz4IQZKBb7capmPLI0bKM+ndeWKcRABoPB81AodEnWCKt8CNQBnCXvSR4BscWBHVhMF1F7wLyOaRKxapxILh6P74bNb8C3F/rm0B4RyQinEA6SpVKpdAtAd1A1KJgSgVqlt1qtK8AfhfFZt9sdh+6iCEvogAEA4Kf0DvCwMWZ3C+yjhOl0OqdxcDZl8KUdcLlcNIWoCOflCtv6nwiSl6TgyB9ZaWkHsIhPEiiUfJIFt8D3gWRwqWO6ZOSFDqRSKQcW7hkYflkHfCADbJGHYUPXVex4p0m3CEfIkE6nlwD4GJXSexsL660I1Cod2K8xfe5B1zZgzGYymZoIS+gAALYTCIDPQsG0CHBYOna8KYfDcUHHIUdMi4wDDADAj8yQ4OBL0G25SuDb44muy9g4uKqFX2RcyVUEOJhcNWS5i2AwuzCV+iIQmQxs2LbJMw5XCadOE2ZAxgGGhWhI3xB5hsmOYw2wDCATtmRAVq9tfDg0WQYQNFsysEiWRSKRcdssFADh0GTfwsiA8CeCzBR6R/oQjYf4WzAm0D00GT/R9uN74K4OlBUBCuc1GY3/OR/hwGb/2PqKS93BYrH428wJY7VzearVaj0QCMwirbvARE4YX0tcmSEJ85g6z3CdPlUoFH4NiaXEVQRUBFQEVARUBFQENjQCfwE8TlA+F0Ee6wAAAABJRU5ErkJggg==) no-repeat;\n  background-size: cover;\n}\n\n\n.icon-tucao{\n  @include icon-common();\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAMKADAAQAAAABAAAAMAAAAADbN2wMAAACo0lEQVRoBe2YvYsTQRjG82VA7a7yvBPPlDaHhhBiBI+zENFKvSvuD7C2EP8IQdH27GyusbK5zltOTEhICkEEQbA5OTRwton58veGXdhmZzY740ZwFob9eOd9n+d93pmd2c1k3OEUcAo4BZwCTgGnwOIUyMaBrlarq+Px+Ol0Ot2g/3IcH4M+x9ls1svn809ardaRLo42AZ/8R8gv6YLZtJPECUms65Io6EB95ZcIuE/Ah7qAung6uy/YLoLdFmz676h8ciqj2Pxhk0mDvOCJQIIVxpbrqEObAI6zMf+3lQ8TDGFp51ucBMKx/7lrl8CiS+Iq4CpgqIAbQoYCGrtrtxJJESqVyjm2H5fZDkwLhcJnFqcfSWOp/KwnUK/Xzw8Gg5eTyeQewLMhOhqNJuVy+Q33j7rd7rGK0Lw2q3MA1S/0+/0G+6cHqD+AzDvaAde/OW9z/lCr1VbmJanqbzUBiL8A7CJED4vF4hpq36RtsjlbE/LYLw2Hw2cqQvPabCdwFQI9yG81Go2fARkZ/5Df4v4X7Vrw3MbZagIQv067EiYfkJSxTyXKuVzuRvDMxtnqJG42m99VpNrt9jeVPYnNagWSEDD1+S8SmL235VvVVK24/iEs7ZqhrQCvP0+AWVF3Q4Hjcpm7X/BRL44BtiqI8W8V3jqrweRltT0D6GtemfdVoHFsxIn1W0VbAfnA5vW3TsA9gCNL6q+w7y2Qlx9be4IZ+riPzFlbgShP1D7CtiIVYHgt095yL38RvkLgbqfT+RLla/O5tgI6MLYG22zcDukn5A9IqJoWeeFmnABD5jntNLFe0W4xH04kcFqH8UrMcBlD9jGqy0Yu9cMkgU+wPUvbgfx+6sx9wMQJlEqlO71e75Tnef1FkXe4TgGngFNg8Qr8ATAc/NKLg12zAAAAAElFTkSuQmCC) no-repeat;\n  background-size: cover;\n}\n\n.icon-sao{\n  @include icon-common();\n  background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAACcUlEQVRoQ+1YTWsUQRCtwhUDbk6r6HHveupqWMSv/RMqiB6SgxE1/gQP/gSjYnJIDooQ/RMblcBCd5/0vkdF95QVDK48mTCCB7Frtie7LOm5VtV79er1TA3NNOcPz3n/lAXM2sHsQHYgcQL5CCUOMLm8igNsrV0GcA/AOWY+kcz+FwCAfWb+xMzPnXObRAQNvlqAtXYTwJIGNDWHmbecc8saHJUAY8wtZn5JRHtEtDocDrcHg8EPDYE2p91uL7RarRtEtEZEiwBuhxBexeq1AnaZ+QIz33XOrcdAU+LW2hUALwDshhAuxrBUAkRkREQniei09/5bDDQlLiKniOgrEX333jdjWFoBBy+U916VHyONxUVEzadqqApgrDlNvApfFqCZaNWc2h0wxvwqmgghHKvazCT5IrJfvnPRZak9Qo9KwMeTNFS1xhhT7INiYNuxWpWAGMgs41nALKdfcKsdEJH3RHRpSg1/8N5f1nAdHQGaacwiR+3ALJrTcB4NAcaYO+Vi2dBMJTXHWvugwHDOPY1hqRwQkZ/lJj4eA6wjLiLjkq8Rw9MKUP+fxwg18dp/5qoAahqM5VThyw7EpjlJvHYHjDF7zNxsNBpn+/3+l0ma0tZ0Op0z4/H4M4BRCGExVqc6QsaYHWa+wsyrmk9bjPR/8eITCmANwLsQwtUYlkqAiFwjojfFVQczP2w2m697vV6tF1vdbndhNBrdBPCkvMK57r1/W4uAAsRauw7gYKEd9sPMG865FQ2PyoE/QMaYJWa+D+D8IV3ufgTwLISwpWm+yKkkQAs6zbwsYJrT/hdXdiA7kDiBfIQSB5hcPvcO/AY4hPkxyAe9fgAAAABJRU5ErkJggg==) no-repeat;\n  background-size: cover;;\n}\n\n.mask{\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: transparent;\n  z-index: 4;\n}\n\n.go-top{\n  position: absolute;\n  right: .16rem;\n  bottom: .8rem;\n  width: 1.12rem;\n  height: 1.12rem;\n  z-index: 5;\n  img{\n    width: 1.12rem;\n    height: 1.12rem;\n  }\n}\n\n\n.van-swipe  .van-swipe__indicator {\n  background-color: white;\n}\n.van-swipe  .van-swipe__indicator--active {\n  width: 0.2rem;\n  height: 0.1rem;\n  background:rgba(255,255,255,1);\n  border-radius: 0.08rem;\n}\n.van-image{\n  width: 100%;\n  transform: translateZ(0);\n  vertical-align: top;\n}\n\n</style>\n", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=03defe63&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"search-page\"},[_c('div',{staticClass:\"search-header\",style:({'top': _vm.pdtHeight})},[_c('div',{staticClass:\"search-flex\"},[_c('div',{staticClass:\"search-back\",on:{\"click\":_vm.goBack}},[_c('van-icon',{attrs:{\"name\":\"arrow-left\",\"size\":\".4rem\"}})],1),_c('div',{staticClass:\"search-input\"},[_c('img',{attrs:{\"src\":require(\"../../assets/images/icon_search.png\"),\"alt\":\"\"}}),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.keyword),expression:\"keyword\"}],attrs:{\"type\":\"text\",\"placeholder\":_vm.placeholder},domProps:{\"value\":(_vm.keyword)},on:{\"input\":[function($event){if($event.target.composing){ return; }_vm.keyword=$event.target.value},function($event){return _vm.searchInput($event)}],\"focus\":_vm.inputFocus}}),_c('van-icon',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.clear),expression:\"clear\"}],attrs:{\"name\":\"clear\",\"size\":\".4rem\",\"color\":\"#aaa\"},on:{\"click\":_vm.clearFn}})],1),_c('div',{staticClass:\"search-btn\",on:{\"click\":function($event){return _vm.doSearch(_vm.keyword)}}},[_vm._v(\"搜索\")])]),_c('div',{staticClass:\"search-tab\"},_vm._l((_vm.tabType),function(item){return _c('div',{key:item.type,class:{'active': item.type == _vm.curTab},on:{\"click\":function($event){return _vm.changeTab(item.type)}}},[_c('span',[_vm._v(_vm._s(item.name))])])}),0)]),(!_vm.curStatus)?_c('div',{staticClass:\"searching\"},_vm._l((_vm.searchingList),function(item,index){return _c('div',{key:index,staticClass:\"searching-item\",on:{\"click\":function($event){return _vm.doKeySearch(item)}}},[_c('div',{domProps:{\"innerHTML\":_vm._s(_vm.formatHtml(item.key))}}),(item.tag.length)?_c('div',_vm._l((item.tag),function(tag,i){return _c('span',{key:i,staticClass:\"searching-item-tag\",on:{\"click\":function($event){$event.stopPropagation();return _vm.doKeySearch(tag)}}},[_vm._v(_vm._s(tag.tsh))])}),0):_vm._e()])}),0):_c('div',{staticClass:\"search-body\",style:({'top': parseFloat(_vm.pdtHeight) + 1.68 + 'rem'})},[(_vm.curStatus == 1)?_c('div',{staticClass:\"search-list-container\"},[_c('hotSearch',{on:{\"search\":_vm.doSearch}}),_c('recentSearch',{on:{\"search\":_vm.doSearch}})],1):_c('div',{staticClass:\"search-list-container\"},[(_vm.curStatus == 2)?_c('productList',{ref:\"produtRefList\",on:{\"filter\":function($event){_vm.filterShow = true}}}):_vm._e(),(_vm.curStatus == 3)?_c('shopList'):_vm._e()],1)]),_c('filterList',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.filterShow),expression:\"filterShow\"}],style:({'top': _vm.pdtHeight}),on:{\"close\":_vm.closeFilter,\"sure\":_vm.sureFilter}})],1)}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.hotTags.length)?_c('div',{staticClass:\"hot-search\"},[_c('h4',[_vm._v(\"热门搜索\")]),_c('div',{staticClass:\"hot-tags\"},_vm._l((_vm.hotTags),function(item,index){return _c('div',{key:index,on:{\"click\":function($event){return _vm.doSearch(item)}}},[_c('span',[_vm._v(_vm._s(item.hot_word))])])}),0)]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "import UplusApi from \"@uplus/uplus-api\"\nimport qs from 'qs'\nconst config = require('../config')\nimport Store from '../store/index'\n\nconst instance = new UplusApi();\ninstance.initDeviceReady()\n\nexport default {\n  get(url,data,headers){\n    return new Promise((resolve,reject)=>{\n      instance.initDeviceReady().then(()=>{\n        let targetUrl = data ? url + '?' + decodeURIComponent(qs.stringify(data.params)) : url;\n        if(targetUrl.indexOf('http') == -1){\n          targetUrl = config.HOST + targetUrl\n        }\n        console.log(targetUrl,'targetUrl')\n        instance.upHttpModule.get({\n          url: targetUrl,\n          headers: {\n            ...headers,\n            TokenAuthorization: 'Bearer' + Store.state.sessionValue\n          }\n        }).then((result) => {\n          let data = {\n            config: {url,method: \"get\", headers},\n            data: null,\n            headers: null,\n          }\n          if(result.retCode === '000000'){\n            data.headers = result.retData.headers\n            data.data = result.retData.data\n            data.status = 200\n            data.statusText = \"OK\",\n            resolve(data)\n          }else{\n            resolve(data)\n          }\n        }).catch((err) =>{\n          reject(err)\n        });\n      })\n    })\n  },\n  post(url, data = {}, headers = {}, transform = false){\n    return new Promise((resolve,reject)=>{\n      instance.initDeviceReady().then(()=>{\n        console.log('post:' + url , JSON.stringify(data))\n        if(url.indexOf('http') == -1){\n          url = config.HOST + url\n        }\n        instance.upHttpModule.post({\n          url,\n          headers: {\n            ...headers,\n            common: {\n              TokenAuthorization: 'Bearer' + Store.state.sessionValue\n            }\n          },\n          data,\n          transform,\n        }).then((result) => {\n          let data = {\n            config: {url,method: \"post\", headers},\n            data: null,\n            headers: null,\n          }\n          if(result.retCode === '000000'){\n            data.headers = result.retData.headers\n            data.data = result.retData.data\n            data.status = 200\n            data.statusText = \"OK\",\n            resolve(data)\n          }else{\n            resolve(data)\n          }\n        }).catch((err) =>{\n          reject(err)\n        });\n      });\n    })\n  }\n}", "import axios from 'axios';\nimport Store from '../store'\nimport sdkHttp from '../utils/uplushttp'\nconst config = require('../config')\n\nlet httpModule;\n\nif(navigator.userAgent.indexOf('App/Uplus') > -1){ \n  httpModule = sdkHttp\n}else{\n  axios.defaults.baseURL = process.env.NODE_ENV === 'development' ? '' : `${config.HOST}`;\n\n  axios.interceptors.request.use(\n    function (config) {\n      config.headers.common['TokenAuthorization'] = 'Bearer' + Store.state.sessionValue\n      return config\n    },\n    function (error) {\n      // Do something with request error\n      return Promise.reject(error)\n    }\n  )\n  httpModule = axios\n}\n\n\n\n\nexport default httpModule;", "import httpModule from '../../utils/http'\nconst config = require('../../config')\n\n\n\n//默认关键词搜索\nconst defaultSearch = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/search/defaultSearch.html' : config.SHOST + '/search/search/defaultSearch.html'\n  return httpModule.get(url, {\n    params\n  });\n}\n\n//热门搜索\nconst hotSearch = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/search/hotSearch.html' : config.SHOST \n  return httpModule.get(url,{params})\n}\n\n//用户输入关键词搜索\nconst searchDropdown = (params) => {\n  let url = process.env.NODE_ENV === 'development' ? '/search/searchdropdown.json' : config.SHOST \n  return httpModule.get(url,{params})\n}\n\n\nexport default {\n  defaultSearch,\n  hotSearch,\n  searchDropdown,\n}", "<template>\n  <div class=\"hot-search\" v-if=\"hotTags.length\">\n    <h4>热门搜索</h4>\n    <div class=\"hot-tags\">\n      <div v-for=\"(item, index) in hotTags\" :key=\"index\" @click=\"doSearch(item)\"><span>{{item.hot_word}}</span></div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {uplusBuriedUrlPoint} from '../../../utils/utils'\nimport searchService from '../searchService'\n\nconst config = require('../../../config');\n\nexport default {\n  name: 'hotSearch',\n  data(){\n    return {\n      hotTags: []\n    }\n  },\n  mounted(){\n    searchService.hotSearch({platform: 3}).then(({data})=>{\n      this.hotTags = data.data\n    })\n  },\n  methods:{\n    doSearch(item){\n      if (item.linkType === '1') {\n        // 跳商品详情\n        uplusBuriedUrlPoint(`${config.HOST}/sgmobile/goodsDetail?productId=${item.link.split('=')[1]}`);\n        // this.$router.push({\n        //   path: '/goodsDetail',\n        //   query: {\n        //     productId: item.link.split('=')[1],\n        //   }\n        // })\n      } else if (item.linkType === '4') {\n        uplusBuriedUrlPoint(`${config.HOST}/sgmobile/bannerTheme/${item.link.split('&')[1].split('=')[1]}`)\n        // this.$router.push(`/bannerTheme/${item.link.split('&')[1].split('=')[1]}`)\n      } else if (item.linkType === '5') {\n        uplusBuriedUrlPoint(`${config.HOST}/sgmobile/banner/${item.link.split('/').slice(-1)}`)\n        // this.$router.push(`/banner/${item.link.split('/').slice(-1)}`)\n      } else {\n        // 触发搜索\n        this.$emit('search', item.hot_word)\n      }\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.hot-search{\n  overflow: hidden;\n  padding: .24rem;\n  border-top: 1px solid #eee;\n  background: #fff;\n  h4{\n    height: .72rem;\n    line-height: .72rem;\n    font-size: .28rem;\n    color: #333;\n    font-weight: normal;\n  }\n}\n.hot-tags{\n  width: 110%;\n  padding: .24rem 0;\n  height: 2.1rem;\n  overflow: hidden;\n  div{\n    margin-right: .26rem;\n    margin-bottom: .16rem;\n    display: inline-flex;\n    justify-content: center;\n    align-items: center;\n    vertical-align: top;\n    width: 2.16rem;\n    height: .68rem;\n    padding: 0 .15rem;\n    font-size: .28rem;\n    color: #666;\n    border-radius: .4rem;\n    border: 1px solid #eee;\n    background: #fff;\n    box-sizing: border-box;\n    span{\n      text-overflow: ellipsis;\n      white-space: nowrap;\n      overflow: hidden;\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./hotSearch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./hotSearch.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./hotSearch.vue?vue&type=template&id=041f4c7c&scoped=true&\"\nimport script from \"./hotSearch.vue?vue&type=script&lang=js&\"\nexport * from \"./hotSearch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./hotSearch.vue?vue&type=style&index=0&id=041f4c7c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"041f4c7c\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"recent-search\"},[_c('div',{staticClass:\"recent-title\"},[_c('h4',[_vm._v(\"最近搜索\")]),(_vm.recentList.length)?_c('img',{attrs:{\"src\":require(\"../../../assets/images/delete.png\"),\"alt\":\"\"},on:{\"click\":_vm.deleteHistory}}):_vm._e()]),(_vm.recentList.length)?_c('div',{staticClass:\"recent-list\"},_vm._l((_vm.recentList),function(item,index){return _c('div',{key:index,on:{\"click\":function($event){return _vm.doSearch(item)}}},[_vm._v(_vm._s(item))])}),0):_c('div',{staticClass:\"empty-history\"},[_c('img',{attrs:{\"src\":\"https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/noWord.png\",\"alt\":\"\"}}),_c('p',[_vm._v(\"暂无搜索记录\")])])])}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"recent-search\">\n    <div class=\"recent-title\">\n      <h4>最近搜索</h4>\n      <img v-if=\"recentList.length\" src=\"../../../assets/images/delete.png\" alt=\"\" @click=\"deleteHistory\">\n    </div>\n    <div class=\"recent-list\" v-if=\"recentList.length\">\n      <div v-for=\"(item,index) in recentList\" :key=\"index\" @click=\"doSearch(item)\">{{item}}</div>\n    </div>\n    <div v-else class=\"empty-history\">\n      <img src=\"https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/noWord.png\" alt=\"\">\n      <p>暂无搜索记录</p>\n    </div>\n  </div>\n</template>\n\n<script>\nimport {mapMutations,mapGetters} from 'vuex'\n\nexport default {\n  name: 'recentSearch',\n  data(){\n    return {\n      recentList: []\n    }\n  },\n  mounted(){\n    let recentStr = this.getHistorySearch()\n    if(recentStr){\n      console.log(recentStr,'recentStr')\n      this.recentList = recentStr.split(',')\n    }\n  },\n  methods: {\n    ...mapGetters(['getHistorySearch']),\n    ...mapMutations(['deleteHistorySearch']),\n    deleteHistory(){\n      this.$dialog.confirm({\n        message: '确定清空搜索历史记录吗?',\n      }).then(() => {\n        this.deleteHistorySearch()\n        this.recentList = []\n      }).catch(() => {\n        \n      })\n    },\n    doSearch(item){\n      this.$emit('search',item)\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.recent-search{\n  background: #fff;\n}\n.recent-title{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 .24rem;\n  height: .72rem;\n  border-bottom: 1px solid #eee;\n  h4{\n    font-size: .28rem;\n    color: #333;\n    font-weight: normal;\n  }\n  img{\n    width: .42rem;\n    height: .42rem;\n  }\n}\n\n.recent-list{\n  div{\n    display: flex;\n    align-items: center;\n    padding: 0 .24rem;\n    height: .76rem;\n    font-size: .28rem;\n    color: #666;\n  }\n}\n\n\n.empty-history{\n  padding: .6rem 0;\n  text-align: center;\n  img{\n    width: 2.4rem;\n    height: 2.4rem;\n  }\n  p{\n    margin-top: .16rem;\n    font-size: .28rem;\n    color: #999;\n  }\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./recentSearch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./recentSearch.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./recentSearch.vue?vue&type=template&id=57f7910f&scoped=true&\"\nimport script from \"./recentSearch.vue?vue&type=script&lang=js&\"\nexport * from \"./recentSearch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./recentSearch.vue?vue&type=style&index=0&id=57f7910f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57f7910f\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"filter-warp\",on:{\"click\":_vm.closeFilter}},[_c('div',{staticClass:\"filter-container\",on:{\"click\":function($event){$event.stopPropagation();}}},[_c('div',{staticClass:\"filter-title\"},[_c('van-icon',{staticClass:\"close-filter\",attrs:{\"name\":\"cross\",\"size\":\".4rem\"},on:{\"click\":_vm.closeFilter}}),_c('span',[_vm._v(\"筛选\")])],1),_c('div',{staticClass:\"filter-body\"},[_c('div',{staticClass:\"filter-stock\"},_vm._l((_vm.filterStock),function(item){return _c('span',{key:item.id,class:{'active': _vm.curStockStatus == item.id},on:{\"click\":function($event){return _vm.changeFilterStock(item.id)}}},[_vm._v(_vm._s(item.name))])}),0),_c('div',{directives:[{name:\"arrow\",rawName:\"v-arrow\"}],staticClass:\"filter-part-title active\"},[_c('h5',[_vm._v(\"价格\")]),_c('a',{attrs:{\"href\":\"javascript:;\"}})]),_c('div',{staticClass:\"filter-price\"},[_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.lowestPrice),expression:\"lowestPrice\"}],attrs:{\"placeholder\":\"最低价\",\"placeholder-style\":\"color:rgba(153,153,153,1);\",\"type\":\"number\"},domProps:{\"value\":(_vm.lowestPrice)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.lowestPrice=$event.target.value}}}),_c('span'),_c('input',{directives:[{name:\"model\",rawName:\"v-model\",value:(_vm.highestPrice),expression:\"highestPrice\"}],attrs:{\"placeholder\":\"最高价\",\"placeholder-style\":\"color:rgba(153,153,153,1);\",\"type\":\"number\"},domProps:{\"value\":(_vm.highestPrice)},on:{\"input\":function($event){if($event.target.composing){ return; }_vm.highestPrice=$event.target.value}}})]),_c('div',{directives:[{name:\"arrow\",rawName:\"v-arrow\"}],staticClass:\"filter-part-title active\"},[_c('h5',[_vm._v(\"品牌\")]),_c('a',{attrs:{\"href\":\"javascript:;\"}})]),_vm._m(0)]),_c('div',{staticClass:\"filter-footer\"},[_c('a',{attrs:{\"href\":\"javascript:;\"}},[_vm._v(\"重置\")]),_c('a',{staticClass:\"sure\",attrs:{\"href\":\"javascript:;\"},on:{\"click\":_vm.doSure}},[_vm._v(\"确定\")])])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"filter-tags\"},[_c('div',{staticClass:\"filter-item-tag active\"},[_c('span',[_vm._v(\"全部\")])]),_c('div',{staticClass:\"filter-item-tag\"},[_c('span',[_vm._v(\"Haier/海尔\")])]),_c('div',{staticClass:\"filter-item-tag\"},[_c('span',[_vm._v(\"海尔MOOKA/模卡\")])]),_c('div',{staticClass:\"filter-item-tag\"},[_c('span',[_vm._v(\"Haier/海尔\")])]),_c('div',{staticClass:\"filter-item-tag\"},[_c('span',[_vm._v(\"Haier/海尔\")])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"filter-warp\" @click=\"closeFilter\">\n    <div class=\"filter-container\" @click.stop>\n      <div class=\"filter-title\"><van-icon @click=\"closeFilter\" class=\"close-filter\" name=\"cross\" size=\".4rem\"/><span>筛选</span></div>\n      <div class=\"filter-body\">\n        <div class=\"filter-stock\">\n          <span v-for=\"item in filterStock\" :key=\"item.id\" :class=\"{'active': curStockStatus == item.id}\" @click=\"changeFilterStock(item.id)\">{{item.name}}</span>\n        </div>\n        <div class=\"filter-part-title active\" v-arrow>\n          <h5>价格</h5>\n          <a href=\"javascript:;\"></a>\n        </div>\n        <div class=\"filter-price\">\n          <input placeholder=\"最低价\" placeholder-style=\"color:rgba(153,153,153,1);\" type=\"number\" v-model=\"lowestPrice\" />\n          <span></span>\n          <input placeholder=\"最高价\" placeholder-style=\"color:rgba(153,153,153,1);\" type=\"number\" v-model=\"highestPrice\" />\n        </div>\n\n        <div class=\"filter-part-title active\" v-arrow>\n          <h5>品牌</h5>\n          <a href=\"javascript:;\"></a>\n        </div>\n        <div class=\"filter-tags\">\n          <div class=\"filter-item-tag active\"> <span>全部</span> </div>\n          <div class=\"filter-item-tag\"> <span>Haier/海尔</span> </div>\n          <div class=\"filter-item-tag\">  <span>海尔MOOKA/模卡</span>  </div>\n          <div class=\"filter-item-tag\"> <span>Haier/海尔</span> </div>\n          <div class=\"filter-item-tag\"> <span>Haier/海尔</span> </div>\n        </div>\n\n\n      </div>\n      <div class=\"filter-footer\">\n        <a href=\"javascript:;\">重置</a>\n        <a href=\"javascript:;\" class=\"sure\" @click=\"doSure\">确定</a>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'filter',\n  data(){\n    return {\n      curStockStatus: 'all',\n      filterStock: [\n        {id:'all', name: '显示全部商品'},\n        {id:'hasStock', name: '仅看有货'}\n      ],\n      lowestPrice: '',\n      highestPrice: '',\n    }\n  },\n  directives: {\n    arrow: {\n      bind(el){\n        el.addEventListener('click',()=>{\n          el.classList.toggle('active')\n        })\n      }\n    }\n  },\n  methods: {\n    changeFilterStock(id){\n      this.curStockStatus = id\n    },\n    closeFilter(){\n      this.$emit('close')\n    },\n    doSure(){\n      this.$emit('sure', {\n        price: 1,\n        tag: 2\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/minxin.scss';\n\n@mixin block-bg{\n  position: relative;\n  background: #f5f5f5;\n  color: #333;\n  box-sizing: border-box;\n  &.active{\n    background: #fff;\n    border: 1px solid $themeColor;\n    color: $themeColor;\n    &::after{\n      content: '';\n      position: absolute;\n      right: -1px;\n      bottom: -1px;\n      display: block;\n      width: .36rem;\n      height: .32rem;\n      background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/home/<USER>\n      background-size: cover;\n    }\n  }\n}\n\n\n.filter-warp{\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  top: 0;\n  z-index: 30;\n  background: rgba(0,0,0,.7);\n  text-align: right;\n  overflow: hidden;\n}\n.filter-container{\n  position: relative;\n  width: 6.22rem;\n  height: 100%;\n  display: inline-block;\n  background: #fff;\n  text-align: left;\n}\n.filter-title{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: .88rem;\n  line-height: .88rem;\n  font-size: .3rem;\n  color: #2c3e50;\n  text-align: center;\n  z-index: 2;\n  span{\n    margin-left: -.3rem;\n  }\n}\n.close-filter{\n  float: left;\n  margin: .2rem;\n}\n\n.filter-body{\n  position: absolute;\n  top: 1rem;\n  bottom: 1rem;\n  left: 0;\n  right: 0;\n  padding: 0 .22rem;\n  overflow: hidden;\n  overflow-y: auto;\n  box-sizing: border-box;\n}\n\n.filter-stock{\n  display: flex;\n  justify-content: space-between;\n  span{\n    display: inline-flex;\n    width: 2.82rem;\n    height: .58rem;\n    justify-content: center;\n    align-items: center;\n    overflow: hidden;\n    border-radius: .08rem;\n    font-size: .26rem;\n    @include block-bg;\n  }\n}\n\n\n.filter-part-title{\n  margin-top: .4rem;\n  margin-bottom: .28rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  height: .3rem;\n  font-size: .28rem;\n  &.active{\n    a{\n      background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/home/<USER>\n      background-size: cover;\n      transform: rotate(0);\n    }\n  }\n  h5{\n    font-weight: normal;\n    color: #333;\n  }\n  a{\n    display: inline-block;\n    width: .24rem;\n    height: .14rem;\n    background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/home/<USER>\n    background-size: cover;\n    transform: rotate(-90deg);\n    transition: all .1s linear;\n  }\n}\n\n.filter-price{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  span{\n    width: .15rem;\n    height: .02rem;\n    display: inline-block;\n    background: #d9d9d9;\n  }\n  input{\n    width: 2.6rem;\n    height: .54rem;\n    display: inline-block;\n    text-align: center;\n    background: #f5f5f5;\n    color: #333;\n    border-radius: .08rem;\n    padding: 0 .1rem;\n    font-size: .26rem;\n    border: none;\n    outline: none;\n  }\n}\n\n\n.filter-tags{\n  display: flex;\n  justify-content: flex-start;\n  align-items: center;\n  flex-wrap: wrap;\n  width: 105%;\n}\n\n.filter-item-tag{\n  margin-right: .11rem;\n  margin-bottom: .22rem;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 .1rem;\n  width: 1.85rem;\n  height: .58rem;\n  font-size: .26rem;\n  border-radius: .08rem;\n  @include block-bg;\n  span{\n    @include text-overflow(1);\n  }\n}\n\n\n.filter-footer{\n  position: absolute;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  height: .88rem;\n  display: flex;\n  font-size: .34rem;\n  z-index: 2;\n  a{\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    color: #333;\n    border-top: 1px solid #d9d9d9;\n    &.sure{\n      border-top: 0;\n      background: $themeColor;\n      color: #fff;\n    }\n  }\n}\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./filter.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./filter.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./filter.vue?vue&type=template&id=ff1ae770&scoped=true&\"\nimport script from \"./filter.vue?vue&type=script&lang=js&\"\nexport * from \"./filter.vue?vue&type=script&lang=js&\"\nimport style0 from \"./filter.vue?vue&type=style&index=0&id=ff1ae770&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ff1ae770\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"search-list\"},[_c('div',{staticClass:\"search-shop-list\",attrs:{\"id\":\"shopListWarp\"}},[_c('div',{staticClass:\"search-list-box\"},_vm._l((_vm.shopList),function(item){return _c('div',{key:item,staticClass:\"search-shop\"},[_vm._m(0,true),_vm._m(1,true)])}),0),(!_vm.fetchEnd)?_c('div',{directives:[{name:\"scroll\",rawName:\"v-scroll\",value:(_vm.fetchData),expression:\"fetchData\"}],staticClass:\"load-more\"},[_vm._v(\"加载中...\")]):_c('div',{staticClass:\"load-more\"},[_vm._v(\"到底了\")])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"search-shop-item\"},[_c('div',{staticClass:\"search-shop-img\"},[_c('img',{attrs:{\"src\":\"https://cdn09.ehaier.com/shunguang/H5/www/img/awesome/ic_default_avatar.png\",\"alt\":\"\"}})]),_c('div',{staticClass:\"search-shop-name\"},[_c('h4',[_vm._v(\"永宁县闽宁镇海恒达电器店洗衣机\")]),_c('p',[_c('span',[_vm._v(\"官方认证\")])])]),_c('div',{staticClass:\"search-shop-btn\"},[_c('a',{attrs:{\"href\":\"\"}},[_vm._v(\"进店\")])])])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"search-shop-product\"},[_c('div',{staticClass:\"shop-product-img\"},[_c('img',{attrs:{\"src\":\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\",\"alt\":\"\"}}),_c('p',[_vm._v(\"￥1299.00\")])]),_c('div',{staticClass:\"shop-product-img\"},[_c('img',{attrs:{\"src\":\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\",\"alt\":\"\"}}),_c('p',[_vm._v(\"￥1299.00\")])]),_c('div',{staticClass:\"shop-product-img\"},[_c('img',{attrs:{\"src\":\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\",\"alt\":\"\"}}),_c('p',[_vm._v(\"￥1299.00\")])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"search-list\">\n    <div id=\"shopListWarp\" class=\"search-shop-list\">\n      <div class=\"search-list-box\">\n        <div class=\"search-shop\" v-for=\"item in shopList\" :key=\"item\">\n          <div class=\"search-shop-item\">\n            <div class=\"search-shop-img\"><img src=\"https://cdn09.ehaier.com/shunguang/H5/www/img/awesome/ic_default_avatar.png\" alt=\"\"></div>\n            <div class=\"search-shop-name\">\n              <h4>永宁县闽宁镇海恒达电器店洗衣机</h4>\n              <p><span>官方认证</span></p>\n            </div>\n            <div class=\"search-shop-btn\"><a href=\"\">进店</a></div>\n          </div>\n          <div class=\"search-shop-product\">\n            <div class=\"shop-product-img\">\n              <img src=\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\" alt=\"\">\n              <p>￥1299.00</p>\n            </div>\n            <div class=\"shop-product-img\">\n              <img src=\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\" alt=\"\">\n              <p>￥1299.00</p>\n            </div>\n            <div class=\"shop-product-img\">\n              <img src=\"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg\" alt=\"\">\n              <p>￥1299.00</p>\n            </div>\n          </div>\n        </div>\n      </div>\n      <div class=\"load-more\" v-if=\"!fetchEnd\" v-scroll=\"fetchData\">加载中...</div>\n      <div class=\"load-more\" v-else>到底了</div>\n    </div>\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: 'searchList',\n  directives: {\n    scroll: {\n      inserted(el, binding){\n        document.getElementById('shopListWarp').addEventListener('scroll',()=>{\n          if(el.getBoundingClientRect().top < window.innerHeight){\n            binding.value()\n          }\n        })\n      }\n    }\n  },\n  data(){\n    return {\n      shopList: [1,2,3,4,5,6,7,8,9],\n      \n      curPage: 1,\n      fetching: true,\n      fetchEnd: false,\n    }\n  },\n  mounted(){\n    this.fetchData()\n  },\n  methods: {\n    fetchData(){\n      console.log('fetchData shop')\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/minxin.scss';\n\n.search-list{\n  height: 100%;\n}\n\n.search-list-warp{\n  position: relative;\n  height: 100%;\n  overflow: hidden;\n  overflow-y: auto;\n}\n\n.search-filter-tabs{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: .84rem;\n  padding: .24rem;\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid #eee;\n  > span{\n    width: 1px;\n    height: .34rem;\n    background: #d8d8d8;\n  }\n}\n\n.filter-tag-condition{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,.7);\n  z-index: 10;\n}\n\n.tag-condition-item{\n  padding: 0 .24rem;\n  height: .76rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #fff;\n  font-size: .28rem;\n  color: #999;\n  i{\n    display: inline-block;\n    width: .4rem;\n    height: .4rem;\n    background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/grayTrue.png) no-repeat;\n    background-size: cover;\n  }\n  &.active{\n    color: $themeColor;\n    i{\n      background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/true.png) no-repeat;\n      background-size: cover;\n    }\n  }\n}\n\n.search-shop-list{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  overflow: hidden;\n  overflow-y: auto;\n  background: #f4f4f4;\n}\n\n.search-shop{\n  margin-bottom: .16rem;\n  background: #fff;\n}\n\n.search-shop-item{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: .24rem;\n  height: 1.48rem;\n}\n\n.search-shop-img{\n  width: 1rem;\n  height: 1rem;\n  border-radius: .16rem;\n  overflow: hidden;\n  img{\n    width: 1rem;\n    height: 1rem;\n  }\n}\n\n.search-shop-name{\n  width: 5.22rem;\n  height: 1rem;\n  padding: .1rem .24rem;\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  h4{\n    font-weight: normal;\n    font-size: .32rem;\n    margin-bottom: .16rem;\n    @include text-overflow(1);\n  }\n  p{\n    line-height: .3rem;\n    span{\n      display: inline-flex;\n      justify-content: center;\n      align-items: center;\n      vertical-align: top;\n      height: .28rem;\n      background: #ed2856;\n      color: #fff;\n      font-size: .18rem;\n      border-radius: .14rem;\n      padding: 0 .1rem;\n    }\n  }\n}\n\n.search-shop-btn{\n  width: .8rem;\n  a{\n    height: .48rem;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    background: $themeColor;\n    color: #fff;\n    font-size: .24rem;\n    border-radius: .24rem;\n  }\n}\n\n.search-shop-product{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 0 .24rem;\n  padding-bottom: .24rem;\n}\n\n.shop-product-img{\n  position: relative;\n  width: 2.24rem;\n  height: 2.24rem;\n  img{\n    width: 2.24rem;\n    height: 2.24rem;\n  }\n  p{\n    position: absolute;\n    right: 0;\n    bottom: 0;\n    display: inline-flex;\n    height: .44rem;\n    justify-content: center;\n    align-items: center;\n    font-size: .28rem;\n    color: #fff;\n    background: #ed2856;\n  }\n}\n\n.load-more{\n  padding: .4rem 0;\n  text-align: center;\n  font-size: .26rem;\n  color: #999;\n}\n\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shopList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shopList.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./shopList.vue?vue&type=template&id=561881e7&scoped=true&\"\nimport script from \"./shopList.vue?vue&type=script&lang=js&\"\nexport * from \"./shopList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shopList.vue?vue&type=style&index=0&id=561881e7&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"561881e7\",\n  null\n  \n)\n\nexport default component.exports", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"search-list\"},[_c('div',{staticClass:\"search-list-warp\"},[_c('div',{staticClass:\"search-filter-tabs\"},[_vm._l((_vm.productTab),function(item){return _c('div',{key:item.id,staticClass:\"filter-item\",class:{'active': item.id == _vm.curProTab, 'no-filter': item.nofilter, 'up': item.sort, 'down': !item.sort},on:{\"click\":function($event){return _vm.changeProTab(item)}}},[_vm._v(_vm._s(item.name)+\" \"),(item.sort !== undefined)?_c('span'):_c('i')])}),_c('span'),_c('div',{staticClass:\"filter-item item-flex-end\",on:{\"click\":_vm.doFilter}},[_vm._v(\"筛选\"),_c('em')])],2),_c('div',{directives:[{name:\"show\",rawName:\"v-show\",value:(_vm.allFilterShow),expression:\"allFilterShow\"}],staticClass:\"filter-tag-condition\",on:{\"click\":function($event){$event.stopPropagation();return _vm.closeAllFilterShow($event)}}},[_vm._m(0)]),_c('div',{staticClass:\"search-list-box search-product-list\",attrs:{\"id\":\"prodListWarp\"}},[_c('div',{staticClass:\"search-product\"},_vm._l((_vm.productList),function(item){return _c('div',{key:item,staticClass:\"list-item\"},[_vm._m(1,true),_vm._m(2,true)])}),0),(!_vm.fetchEnd)?_c('div',{directives:[{name:\"scroll\",rawName:\"v-scroll\",value:(_vm.fetchData),expression:\"fetchData\"}],staticClass:\"load-more\"},[_vm._v(\"加载中...\")]):_c('div',{staticClass:\"load-more\"},[_vm._v(\"到底了\")])])])])}\nvar staticRenderFns = [function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"tag-condition-item active\"},[_c('span',[_vm._v(\"人气优先\")]),_c('i')])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"list-item-img\"},[_c('img',{attrs:{\"src\":\"https://cdn50.ehaier.com/marketing-center-web/changeactivity/image/2020/06/2b35e8d1bd73418baac3114ac80408cc.jpg\",\"alt\":\"\"}})])},function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return _c('div',{staticClass:\"list-item-body\"},[_c('div',{staticClass:\"list-item-title\"},[_vm._v(\" Haier/海尔 冰箱天网云BCD-218STPS \")]),_c('div',{staticClass:\"list-item-desc\"},[_vm._v(\"BCD-520WICTU1 风冷无霜变频智能自由嵌入式冰箱\")]),_c('div',{staticClass:\"list-item-other\"},[_c('div',[_c('p',[_c('strong',[_vm._v(\"￥1299.00\")])])]),_c('div',[_c('small',[_vm._v(\"有货\")])])])])}]\n\nexport { render, staticRenderFns }", "<template>\n  <div class=\"search-list\">\n    <div class=\"search-list-warp\">\n      <div class=\"search-filter-tabs\">\n        <div \n          v-for=\"item in productTab\" \n          class=\"filter-item\" \n          :key=\"item.id\" \n          :class=\"{'active': item.id == curProTab, 'no-filter': item.nofilter, 'up': item.sort, 'down': !item.sort}\"\n          @click=\"changeProTab(item)\"\n        >{{item.name}} <span v-if=\"item.sort !== undefined\"></span> <i v-else></i></div>\n        <span></span>\n        <div class=\"filter-item item-flex-end\" @click=\"doFilter\">筛选<em></em></div>\n      </div>\n      <div class=\"filter-tag-condition\" v-show=\"allFilterShow\" @click.stop=\"closeAllFilterShow\">\n        <div class=\"tag-condition-item active\"><span>人气优先</span><i></i></div>\n      </div>\n      <div id=\"prodListWarp\" class=\"search-list-box search-product-list\">\n        <div class=\"search-product\">\n          <div class=\"list-item\" v-for=\"item in productList\" :key=\"item\">\n            <div class=\"list-item-img\">\n              <img src=\"https://cdn50.ehaier.com/marketing-center-web/changeactivity/image/2020/06/2b35e8d1bd73418baac3114ac80408cc.jpg\" alt=\"\">\n            </div>\n            <div class=\"list-item-body\">\n              <div class=\"list-item-title\"> Haier/海尔 冰箱天网云BCD-218STPS </div>\n              <div class=\"list-item-desc\">BCD-520WICTU1 风冷无霜变频智能自由嵌入式冰箱</div>\n              <div class=\"list-item-other\">\n                <div>\n                  <p><strong>￥1299.00</strong></p>\n                </div>\n                <div>\n                  <small>有货</small>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        <div class=\"load-more\" v-if=\"!fetchEnd\" v-scroll=\"fetchData\">加载中...</div>\n        <div class=\"load-more\" v-else>到底了</div>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\n\nexport default {\n  name: 'searchList',\n  directives: {\n    scroll: {\n      inserted(el, binding){\n        document.getElementById('prodListWarp').addEventListener('scroll',()=>{\n          if(el.getBoundingClientRect().top < window.innerHeight){\n            binding.value()\n          }\n        })\n      }\n    }\n  },\n  data(){\n    return {\n      curProTab: 'all',\n      productTab: [\n        {id: 'all', name: '综合'},\n        {id: 'sale', name: '销量', nofilter: true},\n        {id: 'cms', name: '佣金', sort: true},\n        {id: 'price', name: '价格', sort: true},\n      ],\n      allFilterShow: false, //综合的筛选方式\n      productList: [1,2,3,4,5,6,7,8,9],\n      \n      curPage: 1,\n      fetching: true,\n      fetchEnd: false,\n    }\n  },\n  mounted(){\n    this.fetchData()\n  },\n  methods: {\n    fetchData(options){\n      console.log('fetchData prod',options)\n    },\n    changeProTab(item){\n      if(this.curProTab == 'all' && item.id == 'all'){\n        this.allFilterShow = true\n      }\n      if(item.sort !== undefined && item.id == this.curProTab){\n        item.sort = !item.sort\n      }\n      this.curProTab = item.id\n      this.fetchData()\n    },\n    doFilter(){\n      this.$emit('filter')\n    },\n    closeAllFilterShow(){\n      this.allFilterShow = false\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/minxin.scss';\n\n.search-list{\n  height: 100%;\n}\n\n.search-list-warp{\n  position: relative;\n  height: 100%;\n  overflow: hidden;\n  overflow-y: auto;\n}\n\n.search-filter-tabs{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  height: .84rem;\n  padding: .24rem;\n  display: flex;\n  align-items: center;\n  border-bottom: 1px solid #eee;\n  background: #fff;\n  > span{\n    width: 1px;\n    height: .34rem;\n    background: #d8d8d8;\n  }\n}\n\n.filter-tag-condition{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: rgba(0,0,0,.7);\n  z-index: 10;\n}\n\n.tag-condition-item{\n  padding: 0 .24rem;\n  height: .76rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  background: #fff;\n  font-size: .28rem;\n  color: #999;\n  i{\n    display: inline-block;\n    width: .4rem;\n    height: .4rem;\n    background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/grayTrue.png) no-repeat;\n    background-size: cover;\n  }\n  &.active{\n    color: $themeColor;\n    i{\n      background: url(https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/true.png) no-repeat;\n      background-size: cover;\n    }\n  }\n}\n\n\n\n.search-product-list{\n  position: absolute;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  top: .8rem;\n  overflow: hidden;\n  overflow-y: auto;\n}\n\n\n.filter-item{\n  flex: 1;\n  height: .64rem;\n  display: inline-flex;\n  justify-content: center;\n  align-items: center;\n  font-size: .28rem;\n  color: #666;\n\n  span{\n    margin: 3px;\n    display: inline-flex;\n    flex-direction: column;\n    width: .12rem;\n    &::before{\n      content: '';\n      display: inline-block;\n      width: .12rem;\n      height: .08rem;\n      @include icon-up-gray;\n    }\n    &::after{\n      margin-top: 2px;\n      content: '';\n      display: inline-block;\n      width: .12rem;\n      height: .08rem;\n      @include icon-down-gray;\n    }\n  }\n\n  i {\n    margin: 3px;\n    display: inline-block;\n    vertical-align: middle;\n    width: .12rem;\n    height: .08rem;\n    @include icon-down-gray;\n  }\n\n  em{\n    margin: 3px;\n    display: inline-block;\n    vertical-align: middle;\n    width: .22rem;\n    height: .22rem;\n    @include icon-filter;\n  }\n\n  &.active{\n    color: $themeColor;\n    &.up{\n      span{\n        &::before{\n          @include icon-up-active;\n        }\n      }\n    }\n    &.down{\n      span{\n        &::after{\n          @include icon-down-active;\n        }\n      }\n    }\n    i{\n      @include icon-down-active;\n    }\n  }\n\n  &.no-filter{\n    i{\n      display: none;\n    }\n  }\n}\n\n.item-flex-end{\n  justify-content: flex-end;\n}\n\n\n.search-list-box{\n  background: #f4f4f4;\n}\n\n.search-product{\n  padding: 0 .24rem;\n  background: #fff;\n}\n\n.list-item{\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: .24rem 0;\n  border-bottom: 1px solid #eee;\n  &:last-child{\n    border-bottom: 0;\n  }\n}\n\n.list-item-img{\n  display: inline-flex;\n  width: 1.8rem;\n  height: 1.8rem;\n  img{\n    max-width: 100%;\n    max-height: 100%;\n  }\n}\n\n.list-item-body{\n  width: 5.1rem;\n  padding-left: .16rem;\n  display: flex;\n  flex-direction: column;\n  justify-content: space-between;\n  align-items: flex-start;\n  \n}\n\n.list-item-title{\n  min-height: .76rem;\n  font-size: .28rem;\n  line-height: .38rem;\n  color: #333;\n  @include text-overflow(2);\n}\n\n.list-item-desc{\n  margin: .08rem 0;\n  font-size: .24rem;\n  line-height: .34rem;\n  color: #999;\n  @include text-overflow(1);\n}\n\n.list-item-other{\n  width: 100%;\n  height: .4rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  > div{\n    display: flex;\n    align-items: center;\n    height: .4rem;\n    p{\n      font-size: .28rem;\n      strong{\n        font-weight: bolder;\n        color: #ed2856;\n      }\n    }\n    small{\n      display: inline-block;\n      vertical-align: top;\n      border: 1px solid $themeColor;\n      color: $themeColor;\n      font-size: .2rem;\n      padding: 1px 4px;\n    }\n  }\n}\n\n\n.load-more{\n  padding: .4rem 0;\n  text-align: center;\n  font-size: .26rem;\n  color: #999;\n}\n\n</style>", "import mod from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./prodList.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../../node_modules/thread-loader/dist/cjs.js!../../../../node_modules/babel-loader/lib/index.js!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./prodList.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./prodList.vue?vue&type=template&id=0e0c35c0&scoped=true&\"\nimport script from \"./prodList.vue?vue&type=script&lang=js&\"\nexport * from \"./prodList.vue?vue&type=script&lang=js&\"\nimport style0 from \"./prodList.vue?vue&type=style&index=0&id=0e0c35c0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0e0c35c0\",\n  null\n  \n)\n\nexport default component.exports", "<template>\n  <div class=\"search-page\">\n    <div class=\"search-header\" :style=\"{'top': pdtHeight}\">\n      <div class=\"search-flex\">\n        <div class=\"search-back\" @click=\"goBack\">\n          <van-icon name=\"arrow-left\" size=\".4rem\"/>\n        </div>\n        <div class=\"search-input\">\n          <img src=\"../../assets/images/icon_search.png\" alt=\"\">\n          <input type=\"text\" @input=\"searchInput($event)\" @focus=\"inputFocus\" v-model=\"keyword\" :placeholder=\"placeholder\" />\n          <van-icon v-show=\"clear\" name=\"clear\" size=\".4rem\" color=\"#aaa\" @click=\"clearFn\"/>\n        </div>\n        <div class=\"search-btn\" @click=\"doSearch(keyword)\">搜索</div>\n      </div>\n      <div class=\"search-tab\">\n        <div v-for=\"item in tabType\" :key=\"item.type\" :class=\"{'active': item.type == curTab}\" @click=\"changeTab(item.type)\">\n          <span>{{item.name}}</span>\n        </div>\n      </div>\n    </div>\n    <div class=\"searching\" v-if=\"!curStatus\">\n        <div class=\"searching-item\" v-for=\"(item,index) in searchingList\" :key=\"index\" @click=\"doKeySearch(item)\">\n          <div v-html=\"formatHtml(item.key)\"></div>\n          <div v-if=\"item.tag.length\">\n            <span class=\"searching-item-tag\" v-for=\"(tag, i) in item.tag\" :key=\"i\" @click.stop=\"doKeySearch(tag)\">{{ tag.tsh }}</span>\n          </div>\n        </div>\n    </div>\n    <div class=\"search-body\" v-else :style=\"{'top': parseFloat(pdtHeight) + 1.68 + 'rem'}\">\n      <div class=\"search-list-container\" v-if=\"curStatus == 1\">\n        <hotSearch @search=\"doSearch\"/>\n        <recentSearch @search=\"doSearch\"/>\n      </div>\n      <div class=\"search-list-container\" v-else>\n        <productList v-if=\"curStatus == 2\" @filter=\"filterShow = true\" ref=\"produtRefList\"/>\n        <shopList v-if=\"curStatus == 3\" />\n      </div>\n    </div>\n    <filterList @close=\"closeFilter\" v-show=\"filterShow\" @sure=\"sureFilter\" :style=\"{'top': pdtHeight}\"/>\n  </div>\n</template>\n\n<script>\nimport {mapMutations} from 'vuex';\n\nimport hotSearch from './components/hotSearch'\nimport recentSearch from './components/recentSearch'\nimport filterList from './components/filter'\nimport shopList from './components/shopList'\nimport productList from './components/prodList'\n\nimport {uplusBuriedUrlPoint} from '../../utils/utils'\nimport searchService from './searchService'\nimport {isIphonex,isAndroid} from '../../utils/utils'\n\n\nconst config = require('../../config');\n\nexport default {\n  name: 'search',\n  components:{\n    hotSearch,\n    recentSearch,\n    filterList,\n    shopList,\n    productList,\n  },\n  data(){\n    return{\n      pdtHeight: '',\n      curStatus: 1, //当前状态 0 搜索中  1 默认   2 商品搜索   3店铺搜索\n      curTab: 'prod',\n      tabType: [\n        {type: 'prod', name: '商品'},\n        {type: 'shop', name: '店铺'}\n      ],\n      keyword: '', //搜索关键词\n      placeholder: '在千万商品中搜索',\n      searchingList: [], //搜索中的返回结果\n      clear: false, //清除icon\n      filterShow: false,\n    }\n  },\n  mounted(){\n    if(isAndroid()){\n      this.pdtHeight = '.64rem'\n    }else if(isIphonex()){\n      this.pdtHeight = '.88rem'\n    }else{\n      this.pdtHeight = '.3rem'\n    }\n    console.log(this.$route.query,'search query')\n    let query = this.$route.query;\n    if(query.keyword){\n      console.log(query)\n    }else{\n      searchService.defaultSearch({ platform: 3 }).then(({data})=>{\n        // console.log('default',data)\n        if(data.success){\n          this.placeholder = data.data.hot_word\n        }\n      })\n    }\n  },\n  methods: {\n    ...mapMutations(['addHistorySearch']),\n    changeTab(type){\n      // console.log(type)\n      this.curTab = type\n      if(this.keyword){\n        if(type == 'prod'){\n          this.curStatus = 2\n        }else if(type == 'shop'){\n          this.curStatus = 3\n        }\n        this.doSearch(this.keyword)\n      }\n      \n    },\n    doSearch(item){\n      this.keyword = item;\n      console.log('doSearch', this.keyword)\n      if(!this.keyword){\n        this.$toast('请填写搜索内容')\n        return;\n      }\n      if(this.keyword.indexOf('店铺') !== -1){\n        this.curTab = 'shop';\n        this.keyword = this.keyword.split('\"')[1]\n      }\n\n      if(this.curTab == 'prod'){\n        this.curStatus = 2\n        console.log('商品',this.curStatus)\n        this.addHistorySearch(this.keyword)\n      }else if(this.curTab == 'shop'){\n        this.curStatus = 3\n        console.log('店铺', this.curStatus)\n        this.addHistorySearch(`\"${this.keyword}\"店铺`)\n      }\n\n    },\n    searchInput(ev){\n      let value = ev.target.value\n      if(value){\n        this.clear = true\n        this.keyword = value\n        let timer = setTimeout(()=>{\n          clearTimeout(timer);\n          searchService.searchDropdown({\n            key: value,\n          }).then(({data})=>{\n            // console.log('searchinput',data)\n            let result = data\n            if(result.success){\n              this.searchingList = result.data\n              this.curStatus = 0\n            }\n          })\n        },200)\n      }else{\n        this.clear = false\n        this.curStatus = 1\n      }\n    },\n    inputFocus(){\n      if(this.keyword){\n        this.clear = true\n      }\n    },\n    clearFn(){\n      this.keyword = ''\n      this.clear = false\n      this.curStatus = 1\n    },\n\n    doKeySearch(item){\n      if (item.key) {\n        // 点击li标签 触发搜索\n        this.keyword = item.key\n        this.doSearch(this.keyword)\n      } else {\n        // 点击tag标签\n        if (item.linkType === '1') {\n          // 跳商品详情\n          uplusBuriedUrlPoint(`${config.HOST}/sgmobile/goodsDetail?productId=${item.link.split('=')[1]}&container_type=3&hidesBottomBarWhenPushed=1`)\n          // this.$router.push({path: '/goodsDetail', query: {productId: item.link.split('=')[1]}})\n        } else if (item.linkType === '4') {\n          uplusBuriedUrlPoint(`${config.HOST}/sgmobile/bannerTheme/${item.link.split('&')[1].split('=')[1]}?container_type=3&hidesBottomBarWhenPushed=1`)\n          // this.$router.push(`/bannerTheme/${item.link.split('&')[1].split('=')[1]}`)\n        } else if (item.linkType === '5') {\n          uplusBuriedUrlPoint(`${config.HOST}/sgmobile/banner/${item.link.split('/').slice(-1)}`)\n          // this.$router.push(`/banner/${item.link.split('/').slice(-1)}`)\n        } else {\n          // 触发搜索\n          this.keyword = item.tse\n          this.doSearch(this.keyword)\n        }\n      }\n    },\n    formatHtml (value) {\n      // 处理关键字\n      return value.split(this.keyword).join(`<span style=\"color:#999\">${this.keyword}</span>`)\n    },\n\n    sureFilter(condition){\n      console.log(condition)\n      this.filterShow = false\n      this.doSearch(this.keyword)\n      this.$refs.produtRefList.fetchData(condition)\n    },\n    filterFn(flag){\n      this.filterShow = flag\n    },\n    closeFilter(){\n      this.filterShow = false\n    },\n    goBack(){\n      this.$router.back()\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n@import '@/assets/scss/minxin.scss';\n\n.search-page{\n  position: relative;\n  height: 100%;\n  background: #fff;\n}\n\n.search-header{\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  background: #fff;\n}\n\n.search-flex{\n  height: .92rem;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  border-bottom: 1px solid #ededed;\n}\n\n.search-back{\n  display: flex;\n  align-items: center;\n  width: .6rem;\n  padding-left: .2rem;\n}\n\n.search-input{\n  flex: 1;\n  display: flex;\n  align-items: center;\n  margin: 0 .2rem;\n  padding: .1rem .2rem;\n  background: #f7f8fa;\n  height: .68rem;\n  border-radius: .4rem;\n  box-sizing: border-box;\n  img{\n    width: .24rem;\n    height: .24rem;\n    margin-right: 0.08rem;\n  }\n  input{\n    width: 4rem;\n    margin-right: .4rem;\n    border: none;\n    font-size: .28rem;\n    color: #323232;\n    background: transparent;\n  }\n}\n\n.search-btn{\n  width: .88rem;\n  font-size: .28rem;\n  color: #333;\n}\n\n\n.search-tab{\n  display: flex;\n  height: .76rem;\n  div{\n    flex: 1;\n    display: flex;\n    justify-content: center;\n    align-items: center;\n    font-size: .28rem;\n    &.active span{\n      height: .76rem;\n      line-height: .76rem;\n      color: $themeColor;\n      border-bottom: 2px solid $themeColor;\n    }\n  }\n}\n\n\n\n.search-body{\n  position: absolute;\n  top: 1.68rem;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  overflow: hidden;\n  overflow-y: auto;\n}\n\n.search-list-container{\n  height: 100%;\n}\n\n.searching{\n  position: absolute;\n  top: .8rem;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  overflow: hidden;\n  overflow-y: auto;\n  background: #fff;\n  .searching-item{\n    height: .86rem;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    padding: 0 .24rem;\n    border-bottom: 1px solid #eee;\n    > div{\n      font-size: .28rem;\n      color: #333;\n      span{\n        margin-left: .3rem;\n        display: inline-flex;\n        justify-content: center;\n        align-items: center;\n        height: .44rem;\n        padding: 0 .24rem;\n        border-radius: .22rem;\n        background: #eee;\n        font-size: .24rem;\n        color: #666;\n      }\n    }\n  }\n}\n\n\n</style>", "import mod from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../node_modules/cache-loader/dist/cjs.js??ref--12-0!../../../node_modules/thread-loader/dist/cjs.js!../../../node_modules/babel-loader/lib/index.js!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=2bbaa86a&scoped=true&\"\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=2bbaa86a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2bbaa86a\",\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport Router from 'vue-router'\nimport Home from './views/home/<USER>'\nimport Search from './views/search'\n\nVue.use(Router)\n\nexport default new Router({\n  mode: 'hash',\n  base: process.env.BASE_URL,\n  routes: [\n    {\n      path: '/',\n      name: 'home',\n      component: Home\n    },\n    {\n      path: '/search',\n      name: 'search',\n      component: Search\n    }\n  ]\n})\n", "import Vue from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport axios from './utils/http'\nimport sdkHttp from './utils/uplushttp'\nimport {windowToRem, isUplus} from './utils/utils'\nimport '@/assets/styles/common.css';\nimport 'vant/lib/index.css';\nimport Vant from 'vant';\n\nVue.use(Vant)\n\nVue.config.productionTip = false\n\nif(isUplus()){ //智家环境 使用 sdk 的 http模块\n  Vue.prototype.$axios = sdkHttp;\n}else{\n  Vue.prototype.$axios = axios\n}\n\nwindowToRem()\n\nnew Vue({\n  router,\n  store,\n  render: h => h(App)\n}).$mount('#app')\n", "module.exports = __webpack_public_path__ + \"img/gotop.4f2c9d8d.png\";", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAALVBMVEVHcEyXl5eZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmWlpaZmZmYmJiUlJSZmZkXrFhnAAAADnRSTlMABYpx6jnTolQqHrqwE/x1RjcAAACtSURBVBjTY2BAB1yhE+HsS3nvnlRC2RJ+7sZq7xQhHD1nAUaGsNcCIDbHswVAkrGuAMS54gWWl34BVmUAMfEZiEwRgGitmwAUeQ41tA+ogvUxlBOngMSZB+RwPkJSxugG5egFgEwJALMZXUCmzksEc5jAepmegy3qA4sx7tsFJIXebQQrEH9XEdnkl/0G4pK2d+/eOQueOwgxdbKSqQCDDFQKYva5AKRgEUUNJQAAKSuahllXMwAAAABJRU5ErkJggg==\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./hotSearch.vue?vue&type=style&index=0&id=041f4c7c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./hotSearch.vue?vue&type=style&index=0&id=041f4c7c&lang=scss&scoped=true&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./prodList.vue?vue&type=style&index=0&id=0e0c35c0&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./prodList.vue?vue&type=style&index=0&id=0e0c35c0&lang=scss&scoped=true&\"", "module.exports = __webpack_public_path__ + \"img/empty.bfc80b2a.png\";", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAGBAMAAABdv/GrAAAAD1BMVEUzMzNHcEwyMjIqKiozMzO2Ay5mAAAABHRSTlOIAO4GsuCDGQAAACZJREFUCNdjMFBiFgQBJJqBxcUBzEaiGVRcnMBsJBqrGDa9WOwAABR/DSc3nNHYAAAAAElFTkSuQmCC\"", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--6-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=0&lang=css&\"", "module.exports = \"data:image/png;base64,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\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shopList.vue?vue&type=style&index=0&id=561881e7&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./shopList.vue?vue&type=style&index=0&id=561881e7&lang=scss&scoped=true&\"", "module.exports = \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAB8UlEQVRYR+2Xz0pbQRSHfwcvdmN8gET6AhF0Zxa6EjeFPoHbMocSFcUHCHkAUdQgM3TbJxDclK7aRbtrwbyAmDyAcaNcOTKQSAyJc+84qFfmLufPmW++OXOGSyjIRwXhRAQNfVLR6Js3qrX+BWB5AuhvZl7x2UTwoy8MqI+tLHOCG82yqM+YCOpj7ak5TqOOyxGaZzTeQ5V4P6CjW9Rai21jZucm8+h2xc29mCtgHrjhsa64ETSv2Rc32q8SNocfvemT2gcbeg3QsZfNBeLqD56jkxZ0gbj6I+honXUZc/VHo9Fo1gLtyqV467OazPqCFMmo/V1+E0/oFYBSmqaler1+nfdExo1vtVozSZL0APSYeXbcGJ86eg6gCqDGzH9DgGqtlwD8AdBm5vlQoPsAtonoSCm1FQLUGHMoIpsADph5JwioMaYqIv8BpERUU0r9ew6sMWZRRKzNhIgWlFLtIKA2iDHmSEQ2AFwS0Wdf2D7kKYA5IjpWSlmrY7/cOWqjNBqN6XK5fAZgFcANERkR+Z6madt1wfoXp0pE6yKiAHwA8LPb7X5qNpu3QUEHsJVKZU9EvgKY8jz+OyI66XQ6u09B2theRoeh+jn7BcAagI+2dDmgbRm6APCDiL5NysnRGM8G9TSZe1oEza3MMaEwRu8BRB5sOvhbpEEAAAAASUVORK5CYII=\"", "module.exports = __webpack_public_path__ + \"img/Jg_02.89d03692.png\";", "module.exports = \"data:image/png;base64,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\"", "module.exports = \"data:image/png;base64,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\"", "// 全局配置文件\nlet HOST = '', ENV = '', SHOST = '', DEBUG = '';\n\n/*\n  ENV: \n    0 生产   \n    1 预发布  \n    2 测试 \n*/\n\nENV = 0;\n//重要： ****  是否开启调试，发版时改成 false ****  //\nDEBUG = false;  \n\nlet HOSTS = [\n  'https://m.ehaier.com',\n  'https://m-pre.ehaier.com',\n  'http://mobile.testehaier.com',\n]\nHOST = HOSTS[ENV]\n\nENV !== 2 ? SHOST = 'https://s.ehaier.com' : SHOST = 'http://s.testehaier.com'\n\nmodule.exports = {\n  HOST: HOST,\n  SHOST: SHOST,\n  DEBUG,\n}\n", "import mod from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./index.vue?vue&type=style&index=1&lang=scss&\"", "import mod from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./recentSearch.vue?vue&type=style&index=0&id=57f7910f&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../node_modules/css-loader/index.js??ref--8-oneOf-1-1!../../../../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../../../../node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-2!../../../../node_modules/sass-loader/dist/cjs.js??ref--8-oneOf-1-3!../../../../node_modules/cache-loader/dist/cjs.js??ref--0-0!../../../../node_modules/vue-loader/lib/index.js??vue-loader-options!./recentSearch.vue?vue&type=style&index=0&id=57f7910f&lang=scss&scoped=true&\""], "sourceRoot": ""}
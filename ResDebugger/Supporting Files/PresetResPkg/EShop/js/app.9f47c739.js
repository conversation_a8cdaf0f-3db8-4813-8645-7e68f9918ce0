(function(t){function e(e){for(var a,o,s=e[0],c=e[1],l=e[2],u=0,h=[];u<s.length;u++)o=s[u],Object.prototype.hasOwnProperty.call(n,o)&&n[o]&&h.push(n[o][0]),n[o]=0;for(a in c)Object.prototype.hasOwnProperty.call(c,a)&&(t[a]=c[a]);d&&d(e);while(h.length)h.shift()();return r.push.apply(r,l||[]),i()}function i(){for(var t,e=0;e<r.length;e++){for(var i=r[e],a=!0,s=1;s<i.length;s++){var c=i[s];0!==n[c]&&(a=!1)}a&&(r.splice(e--,1),t=o(o.s=i[0]))}return t}var a={},n={app:0},r=[];function o(e){if(a[e])return a[e].exports;var i=a[e]={i:e,l:!1,exports:{}};return t[e].call(i.exports,i,i.exports,o),i.l=!0,i.exports}o.m=t,o.c=a,o.d=function(t,e,i){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},o.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(t,e){if(1&e&&(t=o(t)),8&e)return t;if(4&e&&"object"===typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var a in t)o.d(i,a,function(e){return t[e]}.bind(null,a));return i},o.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="";var s=window["webpackJsonp"]=window["webpackJsonp"]||[],c=s.push.bind(s);s.push=e,s=s.slice();for(var l=0;l<s.length;l++)e(s[l]);var d=c;r.push([0,"chunk-vendors"]),i()})({0:function(t,e,i){t.exports=i("56d7")},"034f":function(t,e,i){"use strict";var a=i("64a9"),n=i.n(a);n.a},"045d":function(t,e,i){t.exports=i.p+"img/Jg_05.0c8d7339.png"},"0fc6":function(t,e,i){t.exports=i.p+"img/Jg_03.80a43bdd.png"},1023:function(t,e,i){"use strict";var a=i("ef7a"),n=i.n(a);n.a},"13ce":function(t,e,i){"use strict";var a=i("9c68"),n=i.n(a);n.a},"1afd":function(t,e,i){t.exports=i.p+"img/bg.d85e7ace.png"},"1e20":function(t,e,i){},2333:function(t,e,i){"use strict";var a=i("80ab"),n=i.n(a);n.a},2414:function(t,e,i){t.exports=i.p+"img/Jg_04.c4495afe.png"},2534:function(t,e,i){t.exports=i.p+"img/Jg_01.b34f9923.png"},"288d":function(t,e,i){},"56d7":function(t,e,i){"use strict";i.r(e);i("cadf"),i("551c"),i("f751"),i("097d");var a=i("2b0e"),n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"app"}},[i("router-view")],1)},r=[],o=(i("034f"),i("2877")),s={},c=Object(o["a"])(s,n,r,!1,null,null,null),l=c.exports,d=i("8c4f"),u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"home"},[a("div",{staticClass:"home-header",style:{"padding-top":t.pdtHeight},attrs:{id:"homeHeader"}},[a("div",{staticClass:"header"},[a("div",{staticClass:"title"},[t._v("商城")]),a("div",{staticClass:"search",on:{click:t.toSearch}},[a("img",{attrs:{src:i("6052")}}),a("span",{staticClass:"text"},[t._v(t._s(t.searchWord))])]),a("img",{staticClass:"service",attrs:{src:i("e8eb")},on:{click:function(e){return t.uplusBuriedPoint("https://m.ehaier.com/v3/h5/sg/common/customService.html?container_type=3&show_title_bar=true&hidesBottomBarWhenPushed=1")}}}),a("div",{staticClass:"cart",on:{click:t.toShoppingCart}},[a("img",{attrs:{src:i("a948")}}),t.cartNum?a("span",[t._v(t._s(t.cartNum))]):t._e()]),a("img",{staticClass:"more",attrs:{src:i("a02e")},on:{click:t.showModal}})]),a("div",{staticClass:"header-nav-warp",attrs:{id:"headerNavWarp"}},[a("div",{staticClass:"header-nav-top-box"},t._l(t.btmCategoryNav,(function(e){return a("div",{key:e.productCateId,attrs:{id:"headNav-"+e.productCateId},on:{click:function(i){return t.changeCate(e,"scroll",i)}}},[a("a",{class:{active:t.curproductCateId==e.productCateId},attrs:{href:"javascript:;"}},[t._v(t._s(e.name))]),a("span",[t._v("|")])])})),0)])]),a("div",{staticClass:"home-container",style:{top:t.pdtHeight},attrs:{id:"homeContainer"}},[a("van-pull-refresh",{staticClass:"flex-1",on:{refresh:t.onRefresh},scopedSlots:t._u([{key:"pulling",fn:function(){return[a("refreshLoading",{attrs:{text:"下拉刷新"}})]},proxy:!0},{key:"loosing",fn:function(){return[a("refreshLoading",{attrs:{text:"下拉刷新"}})]},proxy:!0},{key:"loading",fn:function(){return[a("refreshLoading",{attrs:{text:"加载中"}})]},proxy:!0},{key:"success",fn:function(){return[a("refreshLoading",{attrs:{text:"加载完成"}})]},proxy:!0}]),model:{value:t.isLoading,callback:function(e){t.isLoading=e},expression:"isLoading"}},[a("div",{staticClass:"header-top"},[t.isOnline||t.bannerList.length?a("van-swipe",{staticClass:"banner",attrs:{autoplay:3e3,"indicator-color":"white"}},t._l(t.bannerList,(function(e,i){return a("van-swipe-item",{key:i},[a("img",{attrs:{src:t._f("imageToHttps")(e.imageUrl),lazy:"loading"},on:{click:function(i){return t.bannerClick(e)}}})])})),1):a("div",{staticClass:"banner"},[a("img",{attrs:{src:i("1afd")}})]),t.middleBannerImg?a("div",{staticClass:"middle-banner"},[a("img",{attrs:{src:t._f("imageToHttps")(t.middleBannerImg.pic),lazy:"loading"},on:{click:function(e){return t.bannerClick({hyperLinkType:""+t.middleBannerImg.linkType,hyperLink:t.middleBannerImg.link})}}})]):t._e(),a("div",{staticClass:"tab-row"},t._l(t.tabList,(function(e,i){return a("div",{key:i,staticClass:"tab",on:{click:function(i){return t.goPage(e.path)}}},[a("img",{attrs:{src:e.img}}),a("span",[t._v(t._s(e.title))])])})),0)],1),t.categoryList.length?a("div",{staticClass:"home-category"},[t._l(t.categoryList,(function(e){return a("div",{key:e.keywordId,staticClass:"category-item",on:{click:function(i){return t.toSearch(e)}}},[a("van-image",{attrs:{round:"",cover:"",src:t._f("imageToHttps")(e.iconUrl)}}),a("p",[t._v(t._s(e.keywordValue))])],1)})),a("div",{staticClass:"category-item",on:{click:function(e){return t.goPage("/category")}}},[a("img",{attrs:{src:i("d76f"),alt:""}}),a("p",[t._v("更多")])])],2):t._e(),t.programTabs.length?a("div",{staticClass:"zj-program"},[a("div",{staticClass:"home-title"},[a("span",[t._v("智家场景超市")]),a("div",{staticClass:"more",on:{click:function(e){return t.goPage("/scene")}}},[t._v("更多"),a("van-icon",{attrs:{name:"arrow",color:"#999"}})],1)]),a("div",{staticClass:"program-tab-warp"},[a("div",{staticClass:"program-tab"},t._l(t.programTabs,(function(e,i){return a("span",{key:e.tabId,class:{active:i==t.curProgramIndex},attrs:{id:"ptab-"+e.tabId},on:{click:function(a){return t.changeProgram(e.tabId,i)}}},[t._v(t._s(e.tabName))])})),0)]),t.programLists[t.curProgramTab]?a("div",[a("van-swipe",{ref:"proSwiper",staticClass:"program-swiper",attrs:{"show-indicators":!1},on:{change:t.proTabChange}},t._l(t.programTabs,(function(e,i){return a("van-swipe-item",{key:i,staticClass:"program-product"},t._l(e.plans,(function(e,i){return a("div",{key:i,staticClass:"program-product-item",on:{click:function(i){return t.goPage(e.planDetailsRelativeUrl,!1,!0)}}},[a("div",{staticClass:"program-product-img"},[e.hasVideo?t._e():a("div",{staticClass:"program-play"},[a("span")]),a("img",{attrs:{src:t._f("imageToHttps")(e.planThumbImg),alt:"",lazy:"loading"}})]),a("p",[t._v(t._s(e.salesHighlights))])])})),0)})),1)],1):t._e()]):t._e(),t.saleList.length>0?a("div",{staticClass:"find-goods"},[a("div",{staticClass:"home-title"},[a("span",[t._v("发现好货")]),a("div",{staticClass:"more",on:{click:t.toNextPage}},[t._v("更多"),a("van-icon",{attrs:{name:"arrow",color:"#999"}})],1)]),a("div",{staticClass:"goods-time"},[a("div",{staticClass:"goods-time-warp"},t._l(t.saleList,(function(e,i){return a("div",{key:i,staticClass:"time-item",class:{active:t.activeTab==i},on:{click:function(a){return t.changeTab(e,i)}}},[a("strong",[t._v(t._s(e.title))]),a("span",[t._v(t._s(e.timeTip))])])})),0)]),a("div",{staticClass:"goods-list"},[a("div",{staticClass:"goods-list-box"},[a("div",{staticClass:"goods-list-warp"},t._l(t.saleProductList,(function(e){return a("div",{key:e.productId,staticClass:"goods-item",on:{click:function(i){return t.toProduct(e)}}},[a("img",{attrs:{src:t._f("imageToHttps")(e.imageUrl),lazy:"loading"}}),a("div",[a("p",{attrs:{pd:e.flashsalePrice}},[a("span",[t._v("￥")]),t._v(t._s(t._f("toFixedTwo")(e.flashsalePrice)))]),a("del",[t._v("￥"+t._s(t._f("toFixedTwo")(e.miniPrice)))])])])})),0)])])]):t._e(),t.nearbyList.length?a("div",{staticClass:"nearby-shop"},[a("div",{staticClass:"home-title"},[a("span",[t._v("附近好店")]),a("div",{staticClass:"more",on:{click:function(e){return t.goPage("/shopNearby")}}},[t._v("更多"),a("van-icon",{attrs:{name:"arrow",color:"#999"}})],1)]),a("div",{staticClass:"nearby-shop-box"},[a("div",{staticClass:"nearby-shop-warp"},t._l(t.nearbyList,(function(e){return a("div",{key:e.id,staticClass:"nearby-shop-item",on:{click:function(i){return t.goNearby(e)}}},[a("div",{staticClass:"nearby-title"},[a("i"),t._v(t._s(e.name))]),a("div",{staticClass:"nearby-tips"},[a("p",[a("i",{staticClass:"icon-auth"}),t._v("官方认证")]),a("span"),a("p",[a("i",{staticClass:"icon-address"}),t._v("距您"+t._s(e.distance))]),e.couponStatus?a("i"):t._e()]),a("div",{staticClass:"nearby-info"},[a("p",[t._v("商品描述 "+t._s(t._f("toFixedTwo")(e.pg,1)))]),a("p",[t._v("卖家服务 "+t._s(t._f("toFixedTwo")(e.sg,1)))]),a("p",[t._v("物流服务 "+t._s(t._f("toFixedTwo")(e.lg,1)))])])])})),0)])]):t._e(),a("div",{staticClass:"require-good"},[a("div",{staticClass:"home-title"},[a("span",[t._v("必选单品")])]),a("div",{staticClass:"require-goods-warp"},[a("div",{staticClass:"require-goods-left",on:{click:function(e){return t.bannerClick({hyperLinkType:""+t.requireGoods.one.linkType,hyperLink:t.requireGoods.one.link,relationId:t.requireGoods.one.relationId})}}},[a("img",{attrs:{src:t._f("imageToHttps")(t.requireGoods.one.pic),lazy:"loading"}})]),a("div",{staticClass:"require-goods-right"},[a("div",{on:{click:function(e){return t.bannerClick({hyperLinkType:""+t.requireGoods.two.linkType,hyperLink:t.requireGoods.two.link,relationId:t.requireGoods.two.relationId})}}},[a("img",{attrs:{src:t._f("imageToHttps")(t.requireGoods.two.pic),lazy:"loading"}})]),a("div",{on:{click:function(e){return t.bannerClick({hyperLinkType:""+t.requireGoods.three.linkType,hyperLink:t.requireGoods.three.link,relationId:t.requireGoods.three.relationId})}}},[a("img",{attrs:{src:t._f("imageToHttps")(t.requireGoods.three.pic),lazy:"loading"}})])])])]),a("div",{staticClass:"bottom-category"},[a("div",{staticClass:"btm-category-nav",attrs:{id:"btmCategoryNav"}},[a("div",{staticClass:"btm-nav-box"},[a("div",{staticClass:"btm-nav-warp"},t._l(t.btmCategoryNav,(function(e){return a("div",{key:e.productCateId,attrs:{id:"btmNav-"+e.productCateId},on:{click:function(i){return t.changeCate(e,null,i)}}},[a("a",{class:{active:t.curproductCateId==e.productCateId},attrs:{href:"javascript:;"}},[t._v(t._s(e.name))]),a("span",[t._v("|")])])})),0)])]),!t.productCateList.length&&t.fetchEnd?a("div",{staticClass:"category-empty"},[a("img",{attrs:{src:i("8f86"),alt:""}})]):a("div",{staticClass:"btm-category-list"},t._l(t.productCateList,(function(e){return a("div",{key:e.productId,staticClass:"btm-list-item",on:{click:function(i){return t.toProduct(e)}}},[a("div",{staticClass:"btm-item-img"},[a("img",{attrs:{src:t._f("imageToHttps")(e.defaultImageUrl),lazy:"loading"}})]),a("div",{staticClass:"btm-item-name"},[e.recommend?a("span",[t._v("推荐")]):t._e(),t._v(t._s(e.productFullName))]),a("div",{staticClass:"btm-item-sellpoint"},t._l(e.sellPoint,(function(e,i){return a("span",{key:i},[t._v(t._s(e))])})),0),a("div",{staticClass:"btm-item-footer"},[a("p",[a("span",[t._v("￥")]),t._v(t._s(t._f("toFixedTwo")(e.finalPrice)))]),a("strong",{class:{disabled:"无货"===e.hasStock}},[t._v(t._s(e.hasStock))])]),a("div",{staticClass:"btm-item-commit"},[a("span",[t._v(t._s("暂无评价"==e.comments?e.comments:e.comments+"条评价"))]),"暂无评价"!==e.comments?a("span",[t._v(t._s(e.goodRate)+"好评")]):t._e()])])})),0)]),a("div",{directives:[{name:"scroll",rawName:"v-scroll",value:t.fetchData,expression:"fetchData"}],staticClass:"bottom-text"},[t.fetchEnd?a("div",[t._v("没有更多了")]):a("van-loading",{attrs:{size:".4rem"}},[t._v("加载中...")])],1)])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowModal,expression:"isShowModal"}],staticClass:"mask",on:{click:function(e){t.isShowModal=!1}}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.isShowModal,expression:"isShowModal"}],staticClass:"popup-container",style:{top:t.maskHeight},on:{click:function(e){t.isShowModal=!1}}},[a("div",{staticClass:"text",on:{click:function(e){return t.goPage("/messageCenter",!0)}}},[a("i",{staticClass:"icon-message"}),t._v("平台消息")]),a("div",{staticClass:"text",on:{click:function(e){return t.goPage("/orderlist",!0)}}},[a("i",{staticClass:"icon-order"}),t._v("我的订单")]),a("div",{staticClass:"text",on:{click:function(e){return t.goPage("/myCollection",!0)}}},[a("i",{staticClass:"icon-fav"}),t._v("我的收藏")]),a("div",{staticClass:"text",on:{click:function(e){return t.goPage("/coupon",!0)}}},[a("i",{staticClass:"icon-quan"}),t._v("优惠券")]),a("div",{staticClass:"text",on:{click:function(e){return t.goPage("https://uplus.haier.com/uplusapp/problemFeedBack/feedback.html",!0,!0)}}},[a("i",{staticClass:"icon-tucao"}),t._v("我要吐槽")]),a("div",{staticClass:"text",on:{click:function(e){return t.goPage("https://uplus.haier.com/uplusapp/scan/homescanpage.html",!1,!0)}}},[a("i",{staticClass:"icon-sao"}),t._v("扫码购物")])]),a("div",{directives:[{name:"show",rawName:"v-show",value:t.showTotop,expression:"showTotop"}],staticClass:"go-top",on:{click:t.toGotop}},[a("img",{attrs:{src:i("59e0")}})])])},h=[],p=(i("8e6e"),i("456d"),i("3b2b"),i("6b54"),i("28a5"),i("20d6"),i("75fc")),m=(i("5df3"),i("ac6a"),i("a481"),i("c5f6"),i("bd86")),g=i("21b1"),f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"refresh-load"},[t._m(0),t.text?i("div",{staticClass:"spinner-text"},[t._v(t._s(t.text))]):t._e()])},v=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"spinner"},[i("div",{staticClass:"rect1"}),i("div",{staticClass:"rect2"}),i("div",{staticClass:"rect3"}),i("div",{staticClass:"rect4"}),i("div",{staticClass:"rect5"})])}],b={name:"refreshLoading",props:["text"]},y=b,A=(i("1023"),Object(o["a"])(y,f,v,!1,null,"dc6d3272",null)),w=A.exports,C=i("bc3a"),S=i.n(C),k=(i("96cf"),i("3b8d")),I=(i("4f7f"),i("2f62")),O=i("4328"),P=i.n(O),T=i("f121"),x=new g["a"];x.initDeviceReady(),a["a"].use(I["a"]);var B,L={region:"山东 青岛 崂山区 中韩街道",provinceId:16,province:"山东",cityId:173,city:"青岛",districtId:2450,district:"崂山区",townshipId:12036596,township:"中韩街道",latitude:"36.12784",longitude:"120.41803"},_=new I["a"].Store({state:{latitude:L.latitude,longitude:L.longitude,provinceId:L.provinceId,cityId:L.cityId,districtId:L.districtId,townshipId:L.townshipId,locationInfo:L,sessionValue:"",historySearch:localStorage.getItem("historySearch")||""},getters:{getlocationInfo:function(t){return t.locationInfo},getHistorySearch:function(t){return t.historySearch},getLatLong:function(t){return{latitude:t.latitude,longitude:t.longitude}}},mutations:{setSessionValue:function(t,e){return t.sessionValue=e},setLatLong:function(t,e){t.latitude=e.latitude,t.longitude=e.longitude},addHistorySearch:function(t,e){var i=localStorage.getItem("historySearch"),a=i?i.split(","):[];a.unshift(e),a=Object(p["a"])(new Set(a)),a.length>10&&(a=a.splice(0,10)),t.historySearch=a.join(","),localStorage.setItem("historySearch",a.join(","))},deleteHistorySearch:function(t){t.historySearch="",localStorage.setItem("historySearch","")}},actions:{doLocation:function(t){var e=t.commit;return new Promise((function(t){x.initDeviceReady().then((function(){x.upLocationModule.getLocation().then((function(i){if(i.retData){var a=i.retData.longitude,n=i.retData.latitude;console.log("uplus lat long",n,a),e("setLatLong",{latitude:n,longitude:a}),t({latitude:n,longitude:a})}else t({latitude:L.latitude,longitude:L.longitude})})).catch((function(){t({latitude:L.latitude,longitude:L.longitude})}))}))}))},getAuthorize:function(){var t=Object(k["a"])(regeneratorRuntime.mark((function t(e,i){var a,n;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.dispatch,n=T.HOST+"/sg/h5/web/auth/mobile/customer/authorize.json?platform=2&callbackUrl="+encodeURIComponent("https://m.ehaier.com"),t.abrupt("return",S.a.get(n,{headers:{Authorization:i}}).then((function(t){var e=t.data;if(e.success){var n=e.data.mId;return a("getMember",{sdToken:i,mId:n})}return 400})));case 3:case"end":return t.stop()}}),t)})));function e(e,i){return t.apply(this,arguments)}return e}(),getMember:function(){var t=Object(k["a"])(regeneratorRuntime.mark((function t(e,i){var a,n,r,o;return regeneratorRuntime.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=e.commit,n=i.sdToken,r=i.mId,o=T.HOST+"/v3/platform/web/member/getMember.json",t.abrupt("return",S.a.post(o,P.a.stringify({token:r}),{headers:{Authorization:n,"Content-Type":"application/x-www-form-urlencoded"}}).then((function(t){var e=t.data;if(e.success){var i=e.data,n=i.mid,r=i.sessionValue,o={};return o.username=i.userName,o.avatarImage=i.avatarImageFileId,o.loginName=i.loginName,o.birthday=i.birthday,o.phone=i.mobile,o.email=i.email,o.cartNumber=i.cartNumber,o.gender=i.gender,o.isNewUser=i.isNewUser,o.isStoreMember=i.isStoreMember,o.nickName=i.nickName,o.rankName=i.rankName,o.promotionCode=i.promotionCode,o.resultSign=i.resultSign,a("setmId",n),a("setSessionValue",r),i}return 400})));case 4:case"end":return t.stop()}}),t)})));function e(e,i){return t.apply(this,arguments)}return e}()}}),D=i("f121");S.a.defaults.baseURL="".concat(D.HOST),S.a.interceptors.request.use((function(t){return t.headers.common["TokenAuthorization"]="Bearer"+_.state.sessionValue,t}),(function(t){return Promise.reject(t)})),B=S.a;var j=function(){return B.get("/v3/h5/scart/num.json")},E="/sg/cms/home/<USER>",H=function(t){return B.get(E,{params:t})},R=function(t){return B.post("/v3/mstore/sg/getSgMessageLimit.json?messageType=".concat(t))},N=function(){return B.get("/sg/cms/home/<USER>/getTabsByPageInfo.json")},M=function(){return B.post("/sg/cms/home/<USER>")},U=function(){return B.get("/sg/cms/middleImageConfig.json")},q=function(t){return B.get("/sg/cms/flashSales/fourth/indexsale.json",{params:t})},F=function(t){return B.post("/sg/cms/home/<USER>",t)},W=function(t){return B.get("/sg/cms/electricalSecond.json",{params:t})},Z=function(t){var e=D.SHOST+"/search/search/defaultSearch.html";return B.get(e,{params:t})},z=function(t){var e=D.SHOST+"/search/commonLoadItemNew.html";return B.get(e,{params:t})},V=function(t){var e=D.SHOST+"/search/getPriceByProductList.html";return B.get(e,{params:t})},G={getCartNumber:j,getBanner:H,getMessageList:R,getProgramData:N,getRecommondKeyWord:M,getMiddleBanner:U,getFlashSale:q,getNearbyShop:F,getRequireGoods:W,getDefaultSearchWord:Z,getCommonLoadItemNew:z,getPriceByProduct:V},Y=new g["a"];Y.initDeviceReady();var J=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:750;(function(e,i){var a=e.documentElement,n="orientationchange"in window?"orientationchange":"resize",r=function(){var e=a.clientWidth;e&&(a.style.fontSize=e/t*100+"px")};e.addEventListener&&(i.addEventListener(n,r,!1),e.addEventListener("DOMContentLoaded",r,!1))})(document,window)},Q=function(){return!("undefined"===typeof window||!window)&&(/iphone/gi.test(window.navigator.userAgent)&&window.screen.height>=812)},X=function(){return navigator.userAgent.indexOf("Android")>-1},K=function(){return navigator.userAgent.indexOf("App/Uplus")>-1},$=function(t,e){window.addEventListener("online",(function(){t&&t()}),!1),window.addEventListener("offline",(function(){e&&e()}),!1)},tt=function(t){Y.initDeviceReady().then((function(){Y.upTraceModule.reportSelfPageChange({type:"GOTO",selfPageUrl:t,selfPageTitle:""}).then((function(e){console.log(e,"utils"),Y.upVdnModule.goToPage({url:t})}))}))};function et(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function it(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?et(Object(i),!0).forEach((function(e){Object(m["a"])(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):et(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var at,nt,rt=i("f121"),ot=new g["a"];ot.initDeviceReady();var st={name:"home",components:{refreshLoading:w},data:function(){return{searchWord:"在千万商品中搜索",pdtHeight:"",maskHeight:"",isLoading:!0,bannerList:[],middleBannerImg:{},textIndex:0,cartNum:"",textList:[],categoryList:[],curProgramTab:"",curProgramIndex:0,programTabs:[],programLists:{},tabList:[{img:i("2534"),title:"领券中心",path:"/voucher"},{img:i("bef1"),title:"分期购",path:"/banner/554"},{img:i("0fc6"),title:"海尔智选",path:"/specialtyStore"},{img:i("2414"),title:"场景超市",path:"/scene"},{img:i("045d"),title:"智家魔方",path:"/banner/1971"}],isShowModal:!1,nearbyList:[],activeTab:0,isSaling:!1,saleList:[],saleProductList:[],requireGoods:{one:{},two:{},three:{}},curproductCateId:2729,btmCategoryNav:[{productCateId:2729,name:"空调"},{productCateId:2725,name:"洗衣机"},{productCateId:2723,name:"冰箱"},{productCateId:2743,name:"彩电"},{productCateId:2741,name:"热水器"},{productCateId:2742,name:"厨房电器"},{productCateId:2726,name:"冷柜"},{productCateId:2973,name:"智能产品"},{productCateId:4255,name:"生活电器"},{productCateId:2774,name:"水家电"},{productCateId:2811,name:"家用中央空调"}],productCateList:[],fetching:!1,fetchEnd:!1,curpage:1,pageSize:10,isOnline:!0,showTotop:!1}},directives:{scroll:{inserted:function(t,e){var i;document.getElementById("homeContainer").addEventListener("scroll",(function(){i=i||document.getElementById("homeHeader").offsetHeight,t.getBoundingClientRect().top<window.innerHeight+200&&e.value(),i>document.getElementById("btmCategoryNav").getBoundingClientRect().top?(document.getElementById("headerNavWarp").classList.add("active"),at&&document.getElementById(at).scrollIntoView({inline:"center",block:"nearest"}),at=""):(nt&&document.getElementById(nt).scrollIntoView({inline:"center",block:"nearest"}),nt="",document.getElementById("headerNavWarp").classList.remove("active"))}))}}},computed:it(it({},Object(I["b"])({location:"getlocationInfo"})),{},{textSelect:function(){return{index:this.textIndex,id:this.textList[this.textIndex]&&this.textList[this.textIndex].id,val:this.textList[this.textIndex]&&this.textList[this.textIndex].title,con:this.textList[this.textIndex]&&this.textList[this.textIndex].content,relationId:this.textList[this.textIndex]&&this.textList[this.textIndex].relationId}}}),mounted:function(){var t=this;X()?(this.pdtHeight=".64rem",this.maskHeight="1.74rem"):Q()?(this.pdtHeight=".88rem",this.maskHeight="1.98rem"):(this.pdtHeight=".3rem",this.maskHeight="1.45rem"),K()?(this.onRefresh(),this.fetchUserCartInfo(),ot.initDeviceReady().then((function(){ot.addResumeEventListener((function(){console.log("回到前台"),t.fetchUserCartInfo()}),"homeShow"),ot.upNetworkModule.addNetStateEventListener((function(e){console.log("ret online",e),"online"==e?(t.isOnline=!0,t.fetching=!1,t.fetchEnd=!1,t.onRefresh(),t.fetchUserCartInfo()):(t.isOnline=!1,t.fetching=!1,t.fetchEnd=!0)}),"itsFirst")}))):($((function(){t.isOnline=!0,t.fetching=!1,t.fetchEnd=!1,t.onRefresh(),t.fetchUserCartInfo()}),(function(){t.isOnline=!1,t.fetching=!1,t.fetchEnd=!0})),this.onRefresh()),document.getElementById("homeContainer").addEventListener("scroll",(function(e){e.target.scrollTop>200?t.showTotop=!0:t.showTotop=!1}))},filters:{toFixedTwo:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2,i=t?Number(t).toFixed(e):Number(0).toFixed(e);return i},imageToHttps:function(t){return t&&-1!==t.indexOf("http:")&&(t=t.replace("http:","https:")),t}},methods:{getDeviceList:function(){return new Promise((function(t){ot.upDeviceModule.getDeviceList().then((function(e){"000000"==e.retCode?t(e.retData):t(400)})).catch((function(){t(400)}))}))},fetchUserCartInfo:function(){var t=this;this.checkIsLogin().then((function(e){e?t.$store.dispatch("getAuthorize",e).then((function(e){400!=e&&G.getCartNumber().then((function(e){var i=e.data,a=i.data;t.cartNum=a>99?"99+":a}))})):t.cartNum=""}))},fetchData:function(t){var e=this;this.fetching||this.fetchEnd||(this.fetching=!0,G.getCommonLoadItemNew({pholder:1,productCateId:this.curproductCateId,provinceId:this.location.provinceId,cityId:this.location.cityId,districtId:this.location.districtId,streetId:this.location.townshipId,fromType:1,memberId:"",qs:"isHotDesc",filterData:"",pageIndex:this.curpage,pageSize:this.pageSize}).then((function(i){var a=i.data;if(a.success){var n=a.data.productList,r=a.data.traceId;n.length&&r?G.getPriceByProduct({traceId:r}).then((function(i){var a=i.data;if(a.success){var r=a.products;n.forEach((function(t){r.forEach((function(e){e.sku==t.sku&&(t.finalPrice=e.finalPrice,t.commission=e.commission)}))})),t&&(e.productCateList=[]),e.productCateList=e.productCateList.concat(n),e.fetching=!1,e.curpage+=1,n.length<e.pageSize&&(e.fetchEnd=!0)}})):(t&&(e.productCateList=[]),e.fetchEnd=!0)}})).catch((function(){e.fetchEnd=!0})))},changeProgram:function(t,e){this.curProgramTab=t,this.curProgramIndex=e,this.$refs.proSwiper.swipeTo(e),X()||document.getElementById("ptab-".concat(t)).scrollIntoView({behavior:"smooth",inline:"center",block:"nearest"})},proTabChange:function(t){this.curProgramIndex=t;var e=this.programTabs[t].tabId;console.log("ptab-".concat(e)),document.getElementById("ptab-".concat(e)).scrollIntoView({behavior:"smooth",inline:"center",block:"nearest"})},changeCate:function(t,e,i){this.curproductCateId=t.productCateId,this.fetching=!1,this.fetchEnd=!1,this.curpage=1,this.fetchData(!0),e&&document.getElementById("btmCategoryNav").scrollIntoView(),i.target.scrollIntoView({behavior:"smooth",inline:"center",block:"nearest"}),at="headNav-"+t.productCateId,nt="btmNav-"+t.productCateId},textStartMove:function(){var t=this;setTimeout((function(){t.textIndex===t.textList.length-1?t.textIndex=0:t.textIndex+=1,t.textStartMove()}),2e3)},uplusBuriedPoint:function(t){console.log(t),tt(t)},onRefresh:function(){var t=this,e=G.getBanner({itemsId:100,provinceId:this.location.provinceId,cityId:this.location.cityId,regionId:this.location.districtId,street:this.location.townshipId}).then((function(e){return t.getBanner(e),Promise.resolve(1)})),i=G.getMessageList(3).then((function(e){console.log(e,"axios response");var i=e.data;return t.textList=i.data||[],t.textStartMove(),Promise.resolve(1)})),a=G.getProgramData().then((function(e){var i=e.data,a=i.data.tabs.splice(1);a.forEach((function(e){e.plans=e.plans.splice(0,2),t.programLists[e.tabId]=e.plans})),t.programTabs=a,t.curProgramTab=a[0].tabId,console.log(t.programTabs,"programTabs"),console.log(t.programLists)})),n=G.getRecommondKeyWord().then((function(e){console.log(e,"category");var i=e.data;return t.categoryList=i.data.splice(0,7),Promise.resolve(1)})),r=G.getMiddleBanner().then((function(e){var i=e.data;return i.success&&(t.middleBannerImg=i.data.middleImagePart1),Promise.resolve(1)})),o=this.$store.dispatch("doLocation").then((function(e){var i=e.latitude,a=e.longitude;return console.log(i,a,"latitude,longitude"),G.getNearbyShop({latitude:i,longitude:a}).then((function(e){var i=e.data;return t.nearbyList=i.data,Promise.resolve(1)}))})),s=G.getRequireGoods({provinceId:this.location.provinceId,cityId:this.location.cityId,regionId:this.location.districtId,street:this.location.townshipId}).then((function(e){var i=e.data;if(i.success)return t.requireGoods.one=i.data.midActivtyList[0][0],t.requireGoods.two=i.data.midActivtyList[0][1],t.requireGoods.three=i.data.midActivtyList[0][2],Promise.resolve(1)})),c=G.getFlashSale({provinceId:this.location.provinceId,cityId:this.location.cityId,districtId:this.location.districtId,streetId:this.location.townshipId}).then((function(e){return t.getFalshSale(e),Promise.resolve(1)})),l=G.getDefaultSearchWord({platform:3}).then((function(e){return t.getDefaultSearch(e),Promise.resolve(1)}));Promise.race([e,i,a,n,r,o,s,c,l]).then((function(e){var i=setTimeout((function(){clearTimeout(i),t.isLoading=!1}),1e3);t.isOnline=!0,console.log(e,"===============")})).catch((function(){t.isLoading=!1}))},toSearch:function(t){t.keywordValue?this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/goodsSearch?keyword=").concat(encodeURIComponent(t.keywordValue),"&container_type=3&hidesBottomBarWhenPushed=1")):this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/goodsSearch?container_type=3&hidesBottomBarWhenPushed=1"))},toShoppingCart:function(){var t=this;console.log("".concat(rt.HOST,"/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1")),this.checkIsLogin().then((function(e){e?t.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1")):ot.initDeviceReady().then((function(){ot.upVdnModule.goToPageForResult({url:"apicloud://usercenter"}).then((function(e){(e.retData.loginByH5||e.retData.loginSuccess)&&t.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/cart?container_type=3&hidesBottomBarWhenPushed=1"))})).catch((function(){}))}))}))},showModal:function(){ot.initDeviceReady().then((function(){ot.upTraceModule.reportPageClickEvent({actionCode:"right-top-more",extentInfo:""})})),this.isShowModal=!this.isShowModal},getBanner:function(t){var e=t.data,i=e.data,a=e.success;a&&(this.bannerList=i)},getDefaultSearch:function(t){t.data.success&&(this.searchWord=t.data.data.hot_word)},getFalshSale:function(t){if(t.data.success){var e=t.data.data,i=Object(p["a"])(e.list).reverse(),a=e.systemTime,n=0,r=i.length;i.findIndex((function(t,e){if(new Date(t.startTime).getDate()===new Date(a).getDate()&&a<t.endTime&&a>t.startTime)return n=e>-1?r-e-1:0,!0})),e.list&&e.list.map((function(t){a>t.endTime?(t.promotionState=2,t.isSaling=!1):a<t.startTime?(t.promotionState=0,t.isSaling=!1):(t.promotionState=1,t.isSaling=!0),t.title=t.timeStr,console.log(t.title)})),this.activeTab=n,this.isSaling=e.list[n].isSaling,this.saleList=Object(p["a"])(e.list),this.saleProductList=this.saleList[this.activeTab].products,console.log(this.saleProductList,"init")}},bannerClick:function(t){console.log("bannerClick:",t);var e=t.hyperLinkType,i=t.hyperLink,a=t.relationId;console.log("bannerClick-hyperLink:",i,e);var n=i.toString().split("&");switch(e){case"-1":break;case"0":this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/bannerTheme/").concat(a,"?container_type=3&hidesBottomBarWhenPushed=1"));break;case"1":var r=n[0].slice(n[0].indexOf("=")+1);this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/goodsDetail?productId=").concat(r,"&container_type=3&hidesBottomBarWhenPushed=1"));break;case"2":if(i){var o=i.split("=")[1].split(",")[0],s=i.split("=")[1].split(",")[1];this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/couponDetail/").concat(o,"/").concat(s,"?container_type=3&hidesBottomBarWhenPushed=1"))}else this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/voucher?container_type=3&hidesBottomBarWhenPushed=1"));break;case"3":this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/games/luckyWheel?container_type=3&hidesBottomBarWhenPushed=1&").concat(i));break;case"4":if("主题活动"===n[0].slice(n[0].indexOf("=")+1)){var c=n[1].slice(n[1].indexOf("=")+1);this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/bannerTheme/").concat(c,"?container_type=3&hidesBottomBarWhenPushed=1"))}else"日常活动"===n[0].slice(n[0].indexOf("=")+1)&&this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/bannerDaily?container_type=3&hidesBottomBarWhenPushed=1"));break;case"5":var l=i.split("/");if(l.indexOf("CustomPage")>=0){var d=l.slice(-1);this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/banner/").concat(d,"?container_type=3&hidesBottomBarWhenPushed=1"))}else if(l.indexOf("couponsDetail")>=0){var u=i.split("couponsDetail")[1];this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/couponDetail").concat(u,"?container_type=3&hidesBottomBarWhenPushed=1"))}else if(l.indexOf("itSoluteDetail")>=0){var h=l.split("itSoluteDetail")[1].replace(new RegExp("/","g"),"");this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/itSoluteDetail?id=").concat(h,"&container_type=3&hidesBottomBarWhenPushed=1"))}else l.indexOf("fullHouseSolution")>=0||this.uplusBuriedPoint("".concat(i));break;case"7":if(i){var p=i;this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/goodsDetail?productId=").concat(p,"&container_type=3&hidesBottomBarWhenPushed=1"))}break;case"8":break}},goPage:function(t,e,i){var a=this;if(-1===t.indexOf("appVdnPage"))-1!==t.indexOf("?")?t+="&container_type=3&hidesBottomBarWhenPushed=1":t+="?container_type=3&hidesBottomBarWhenPushed=1",e?(console.log(i?t:"".concat(rt.HOST,"/sgmobile").concat(t)),this.checkIsLogin().then((function(e){e?a.uplusBuriedPoint(i?t:"".concat(rt.HOST,"/sgmobile").concat(t)):ot.initDeviceReady().then((function(){ot.upVdnModule.goToPageForResult({url:"apicloud://usercenter"}).then((function(e){(e.retData.loginByH5||e.retData.loginSuccess)&&a.uplusBuriedPoint(i?t:"".concat(rt.HOST,"/sgmobile").concat(t))})).catch((function(){}))}))}))):this.uplusBuriedPoint(i?t:"".concat(rt.HOST,"/sgmobile").concat(t));else{var n=t.split("=")[1];this.uplusBuriedPoint(n)}},toNextPage:function(){this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/flashSale?container_type=3&hidesBottomBarWhenPushed=1"))},goNearby:function(t){this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/nearshop?nearStoreId=").concat(t.id,"&container_type=3&hidesBottomBarWhenPushed=1"))},toProduct:function(t){this.uplusBuriedPoint("".concat(rt.HOST,"/sgmobile/goodsDetail?productId=").concat(t.productId,"&container_type=3&hidesBottomBarWhenPushed=1"))},changeTab:function(t,e){this.saleProductList=[],this.activeTab=e,this.saleProductList=this.saleList[this.activeTab].products,console.log(this.saleProductList,"change")},checkIsLogin:function(){return new Promise((function(t){ot.initDeviceReady().then((function(){ot.upUserModule.getUserInfo().then((function(e){e.retData.user_center_access_token?t(e.retData.user_center_access_token):t(!1)})).catch((function(){t(!1)}))}))}))},toGotop:function(){this.scrollSmoothTo(document.getElementById("homeContainer"),0)},scrollSmoothTo:function(t,e){window.requestAnimationFrame||(window.requestAnimationFrame=function(t){return setTimeout(t,17)});var i=t.scrollTop,a=function a(){var n=e-i;i+=n/5,Math.abs(n)<1?t.scrollTo(0,e):(t.scrollTo(0,i),requestAnimationFrame(a))};a()}}},ct=st,lt=(i("a4f2"),i("fca8"),Object(o["a"])(ct,u,h,!1,null,null,null)),dt=lt.exports,ut=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"search-page"},[a("div",{staticClass:"search-header",style:{top:t.pdtHeight}},[a("div",{staticClass:"search-flex"},[a("div",{staticClass:"search-back",on:{click:t.goBack}},[a("van-icon",{attrs:{name:"arrow-left",size:".4rem"}})],1),a("div",{staticClass:"search-input"},[a("img",{attrs:{src:i("6052"),alt:""}}),a("input",{directives:[{name:"model",rawName:"v-model",value:t.keyword,expression:"keyword"}],attrs:{type:"text",placeholder:t.placeholder},domProps:{value:t.keyword},on:{input:[function(e){e.target.composing||(t.keyword=e.target.value)},function(e){return t.searchInput(e)}],focus:t.inputFocus}}),a("van-icon",{directives:[{name:"show",rawName:"v-show",value:t.clear,expression:"clear"}],attrs:{name:"clear",size:".4rem",color:"#aaa"},on:{click:t.clearFn}})],1),a("div",{staticClass:"search-btn",on:{click:function(e){return t.doSearch(t.keyword)}}},[t._v("搜索")])]),a("div",{staticClass:"search-tab"},t._l(t.tabType,(function(e){return a("div",{key:e.type,class:{active:e.type==t.curTab},on:{click:function(i){return t.changeTab(e.type)}}},[a("span",[t._v(t._s(e.name))])])})),0)]),t.curStatus?a("div",{staticClass:"search-body",style:{top:parseFloat(t.pdtHeight)+1.68+"rem"}},[1==t.curStatus?a("div",{staticClass:"search-list-container"},[a("hotSearch",{on:{search:t.doSearch}}),a("recentSearch",{on:{search:t.doSearch}})],1):a("div",{staticClass:"search-list-container"},[2==t.curStatus?a("productList",{ref:"produtRefList",on:{filter:function(e){t.filterShow=!0}}}):t._e(),3==t.curStatus?a("shopList"):t._e()],1)]):a("div",{staticClass:"searching"},t._l(t.searchingList,(function(e,i){return a("div",{key:i,staticClass:"searching-item",on:{click:function(i){return t.doKeySearch(e)}}},[a("div",{domProps:{innerHTML:t._s(t.formatHtml(e.key))}}),e.tag.length?a("div",t._l(e.tag,(function(e,i){return a("span",{key:i,staticClass:"searching-item-tag",on:{click:function(i){return i.stopPropagation(),t.doKeySearch(e)}}},[t._v(t._s(e.tsh))])})),0):t._e()])})),0),a("filterList",{directives:[{name:"show",rawName:"v-show",value:t.filterShow,expression:"filterShow"}],style:{top:t.pdtHeight},on:{close:t.closeFilter,sure:t.sureFilter}})],1)},ht=[],pt=(i("b54a"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return t.hotTags.length?i("div",{staticClass:"hot-search"},[i("h4",[t._v("热门搜索")]),i("div",{staticClass:"hot-tags"},t._l(t.hotTags,(function(e,a){return i("div",{key:a,on:{click:function(i){return t.doSearch(e)}}},[i("span",[t._v(t._s(e.hot_word))])])})),0)]):t._e()}),mt=[];function gt(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function ft(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?gt(Object(i),!0).forEach((function(e){Object(m["a"])(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):gt(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var vt=i("f121"),bt=new g["a"];bt.initDeviceReady();var yt,At={get:function(t,e,i){return new Promise((function(a,n){bt.initDeviceReady().then((function(){var r=e?t+"?"+decodeURIComponent(P.a.stringify(e.params)):t;-1==r.indexOf("http")&&(r=vt.HOST+r),console.log(r,"targetUrl"),bt.upHttpModule.get({url:r,headers:ft(ft({},i),{},{TokenAuthorization:"Bearer"+_.state.sessionValue})}).then((function(e){var n={config:{url:t,method:"get",headers:i},data:null,headers:null};"000000"===e.retCode?(n.headers=e.retData.headers,n.data=e.retData.data,n.status=200,n.statusText="OK",a(n)):a(n)})).catch((function(t){n(t)}))}))}))},post:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return new Promise((function(n,r){bt.initDeviceReady().then((function(){console.log("post:"+t,JSON.stringify(e)),-1==t.indexOf("http")&&(t=vt.HOST+t),bt.upHttpModule.post({url:t,headers:ft(ft({},i),{},{common:{TokenAuthorization:"Bearer"+_.state.sessionValue}}),data:e,transform:a}).then((function(e){var a={config:{url:t,method:"post",headers:i},data:null,headers:null};"000000"===e.retCode?(a.headers=e.retData.headers,a.data=e.retData.data,a.status=200,a.statusText="OK",n(a)):n(a)})).catch((function(t){r(t)}))}))}))}},wt=i("f121");navigator.userAgent.indexOf("App/Uplus")>-1?yt=At:(S.a.defaults.baseURL="".concat(wt.HOST),S.a.interceptors.request.use((function(t){return t.headers.common["TokenAuthorization"]="Bearer"+_.state.sessionValue,t}),(function(t){return Promise.reject(t)})),yt=S.a);var Ct=yt,St=i("f121"),kt=function(t){var e=St.SHOST+"/search/search/defaultSearch.html";return Ct.get(e,{params:t})},It=function(t){var e=St.SHOST;return Ct.get(e,{params:t})},Ot=function(t){var e=St.SHOST;return Ct.get(e,{params:t})},Pt={defaultSearch:kt,hotSearch:It,searchDropdown:Ot},Tt=i("f121"),xt={name:"hotSearch",data:function(){return{hotTags:[]}},mounted:function(){var t=this;Pt.hotSearch({platform:3}).then((function(e){var i=e.data;t.hotTags=i.data}))},methods:{doSearch:function(t){"1"===t.linkType?tt("".concat(Tt.HOST,"/sgmobile/goodsDetail?productId=").concat(t.link.split("=")[1])):"4"===t.linkType?tt("".concat(Tt.HOST,"/sgmobile/bannerTheme/").concat(t.link.split("&")[1].split("=")[1])):"5"===t.linkType?tt("".concat(Tt.HOST,"/sgmobile/banner/").concat(t.link.split("/").slice(-1))):this.$emit("search",t.hot_word)}}},Bt=xt,Lt=(i("6fee"),Object(o["a"])(Bt,pt,mt,!1,null,"041f4c7c",null)),_t=Lt.exports,Dt=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"recent-search"},[a("div",{staticClass:"recent-title"},[a("h4",[t._v("最近搜索")]),t.recentList.length?a("img",{attrs:{src:i("b32a"),alt:""},on:{click:t.deleteHistory}}):t._e()]),t.recentList.length?a("div",{staticClass:"recent-list"},t._l(t.recentList,(function(e,i){return a("div",{key:i,on:{click:function(i){return t.doSearch(e)}}},[t._v(t._s(e))])})),0):a("div",{staticClass:"empty-history"},[a("img",{attrs:{src:"https://cdn09.ehaier.com/shunguang/H5/www/img/sgmobile/search/noWord.png",alt:""}}),a("p",[t._v("暂无搜索记录")])])])},jt=[];function Et(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function Ht(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?Et(Object(i),!0).forEach((function(e){Object(m["a"])(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):Et(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var Rt={name:"recentSearch",data:function(){return{recentList:[]}},mounted:function(){var t=this.getHistorySearch();t&&(console.log(t,"recentStr"),this.recentList=t.split(","))},methods:Ht(Ht(Ht({},Object(I["b"])(["getHistorySearch"])),Object(I["c"])(["deleteHistorySearch"])),{},{deleteHistory:function(){var t=this;this.$dialog.confirm({message:"确定清空搜索历史记录吗?"}).then((function(){t.deleteHistorySearch(),t.recentList=[]})).catch((function(){}))},doSearch:function(t){this.$emit("search",t)}})},Nt=Rt,Mt=(i("ff8b"),Object(o["a"])(Nt,Dt,jt,!1,null,"57f7910f",null)),Ut=Mt.exports,qt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"filter-warp",on:{click:t.closeFilter}},[i("div",{staticClass:"filter-container",on:{click:function(t){t.stopPropagation()}}},[i("div",{staticClass:"filter-title"},[i("van-icon",{staticClass:"close-filter",attrs:{name:"cross",size:".4rem"},on:{click:t.closeFilter}}),i("span",[t._v("筛选")])],1),i("div",{staticClass:"filter-body"},[i("div",{staticClass:"filter-stock"},t._l(t.filterStock,(function(e){return i("span",{key:e.id,class:{active:t.curStockStatus==e.id},on:{click:function(i){return t.changeFilterStock(e.id)}}},[t._v(t._s(e.name))])})),0),i("div",{directives:[{name:"arrow",rawName:"v-arrow"}],staticClass:"filter-part-title active"},[i("h5",[t._v("价格")]),i("a",{attrs:{href:"javascript:;"}})]),i("div",{staticClass:"filter-price"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.lowestPrice,expression:"lowestPrice"}],attrs:{placeholder:"最低价","placeholder-style":"color:rgba(153,153,153,1);",type:"number"},domProps:{value:t.lowestPrice},on:{input:function(e){e.target.composing||(t.lowestPrice=e.target.value)}}}),i("span"),i("input",{directives:[{name:"model",rawName:"v-model",value:t.highestPrice,expression:"highestPrice"}],attrs:{placeholder:"最高价","placeholder-style":"color:rgba(153,153,153,1);",type:"number"},domProps:{value:t.highestPrice},on:{input:function(e){e.target.composing||(t.highestPrice=e.target.value)}}})]),i("div",{directives:[{name:"arrow",rawName:"v-arrow"}],staticClass:"filter-part-title active"},[i("h5",[t._v("品牌")]),i("a",{attrs:{href:"javascript:;"}})]),t._m(0)]),i("div",{staticClass:"filter-footer"},[i("a",{attrs:{href:"javascript:;"}},[t._v("重置")]),i("a",{staticClass:"sure",attrs:{href:"javascript:;"},on:{click:t.doSure}},[t._v("确定")])])])])},Ft=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"filter-tags"},[i("div",{staticClass:"filter-item-tag active"},[i("span",[t._v("全部")])]),i("div",{staticClass:"filter-item-tag"},[i("span",[t._v("Haier/海尔")])]),i("div",{staticClass:"filter-item-tag"},[i("span",[t._v("海尔MOOKA/模卡")])]),i("div",{staticClass:"filter-item-tag"},[i("span",[t._v("Haier/海尔")])]),i("div",{staticClass:"filter-item-tag"},[i("span",[t._v("Haier/海尔")])])])}],Wt={name:"filter",data:function(){return{curStockStatus:"all",filterStock:[{id:"all",name:"显示全部商品"},{id:"hasStock",name:"仅看有货"}],lowestPrice:"",highestPrice:""}},directives:{arrow:{bind:function(t){t.addEventListener("click",(function(){t.classList.toggle("active")}))}}},methods:{changeFilterStock:function(t){this.curStockStatus=t},closeFilter:function(){this.$emit("close")},doSure:function(){this.$emit("sure",{price:1,tag:2})}}},Zt=Wt,zt=(i("13ce"),Object(o["a"])(Zt,qt,Ft,!1,null,"ff1ae770",null)),Vt=zt.exports,Gt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"search-list"},[i("div",{staticClass:"search-shop-list",attrs:{id:"shopListWarp"}},[i("div",{staticClass:"search-list-box"},t._l(t.shopList,(function(e){return i("div",{key:e,staticClass:"search-shop"},[t._m(0,!0),t._m(1,!0)])})),0),t.fetchEnd?i("div",{staticClass:"load-more"},[t._v("到底了")]):i("div",{directives:[{name:"scroll",rawName:"v-scroll",value:t.fetchData,expression:"fetchData"}],staticClass:"load-more"},[t._v("加载中...")])])])},Yt=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"search-shop-item"},[i("div",{staticClass:"search-shop-img"},[i("img",{attrs:{src:"https://cdn09.ehaier.com/shunguang/H5/www/img/awesome/ic_default_avatar.png",alt:""}})]),i("div",{staticClass:"search-shop-name"},[i("h4",[t._v("永宁县闽宁镇海恒达电器店洗衣机")]),i("p",[i("span",[t._v("官方认证")])])]),i("div",{staticClass:"search-shop-btn"},[i("a",{attrs:{href:""}},[t._v("进店")])])])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"search-shop-product"},[i("div",{staticClass:"shop-product-img"},[i("img",{attrs:{src:"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg",alt:""}}),i("p",[t._v("￥1299.00")])]),i("div",{staticClass:"shop-product-img"},[i("img",{attrs:{src:"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg",alt:""}}),i("p",[t._v("￥1299.00")])]),i("div",{staticClass:"shop-product-img"},[i("img",{attrs:{src:"https://cdn50.ehaier.com/mobilemall-admin-web/picmanagecontroller/image/2020/01/22270c62dc9d4c3d868187a706dc5bd3.jpg",alt:""}}),i("p",[t._v("￥1299.00")])])])}],Jt={name:"searchList",directives:{scroll:{inserted:function(t,e){document.getElementById("shopListWarp").addEventListener("scroll",(function(){t.getBoundingClientRect().top<window.innerHeight&&e.value()}))}}},data:function(){return{shopList:[1,2,3,4,5,6,7,8,9],curPage:1,fetching:!0,fetchEnd:!1}},mounted:function(){this.fetchData()},methods:{fetchData:function(){console.log("fetchData shop")}}},Qt=Jt,Xt=(i("b265"),Object(o["a"])(Qt,Gt,Yt,!1,null,"561881e7",null)),Kt=Xt.exports,$t=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"search-list"},[i("div",{staticClass:"search-list-warp"},[i("div",{staticClass:"search-filter-tabs"},[t._l(t.productTab,(function(e){return i("div",{key:e.id,staticClass:"filter-item",class:{active:e.id==t.curProTab,"no-filter":e.nofilter,up:e.sort,down:!e.sort},on:{click:function(i){return t.changeProTab(e)}}},[t._v(t._s(e.name)+" "),void 0!==e.sort?i("span"):i("i")])})),i("span"),i("div",{staticClass:"filter-item item-flex-end",on:{click:t.doFilter}},[t._v("筛选"),i("em")])],2),i("div",{directives:[{name:"show",rawName:"v-show",value:t.allFilterShow,expression:"allFilterShow"}],staticClass:"filter-tag-condition",on:{click:function(e){return e.stopPropagation(),t.closeAllFilterShow(e)}}},[t._m(0)]),i("div",{staticClass:"search-list-box search-product-list",attrs:{id:"prodListWarp"}},[i("div",{staticClass:"search-product"},t._l(t.productList,(function(e){return i("div",{key:e,staticClass:"list-item"},[t._m(1,!0),t._m(2,!0)])})),0),t.fetchEnd?i("div",{staticClass:"load-more"},[t._v("到底了")]):i("div",{directives:[{name:"scroll",rawName:"v-scroll",value:t.fetchData,expression:"fetchData"}],staticClass:"load-more"},[t._v("加载中...")])])])])},te=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"tag-condition-item active"},[i("span",[t._v("人气优先")]),i("i")])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"list-item-img"},[i("img",{attrs:{src:"https://cdn50.ehaier.com/marketing-center-web/changeactivity/image/2020/06/2b35e8d1bd73418baac3114ac80408cc.jpg",alt:""}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"list-item-body"},[i("div",{staticClass:"list-item-title"},[t._v(" Haier/海尔 冰箱天网云BCD-218STPS ")]),i("div",{staticClass:"list-item-desc"},[t._v("BCD-520WICTU1 风冷无霜变频智能自由嵌入式冰箱")]),i("div",{staticClass:"list-item-other"},[i("div",[i("p",[i("strong",[t._v("￥1299.00")])])]),i("div",[i("small",[t._v("有货")])])])])}],ee=(i("55dd"),{name:"searchList",directives:{scroll:{inserted:function(t,e){document.getElementById("prodListWarp").addEventListener("scroll",(function(){t.getBoundingClientRect().top<window.innerHeight&&e.value()}))}}},data:function(){return{curProTab:"all",productTab:[{id:"all",name:"综合"},{id:"sale",name:"销量",nofilter:!0},{id:"cms",name:"佣金",sort:!0},{id:"price",name:"价格",sort:!0}],allFilterShow:!1,productList:[1,2,3,4,5,6,7,8,9],curPage:1,fetching:!0,fetchEnd:!1}},mounted:function(){this.fetchData()},methods:{fetchData:function(t){console.log("fetchData prod",t)},changeProTab:function(t){"all"==this.curProTab&&"all"==t.id&&(this.allFilterShow=!0),void 0!==t.sort&&t.id==this.curProTab&&(t.sort=!t.sort),this.curProTab=t.id,this.fetchData()},doFilter:function(){this.$emit("filter")},closeAllFilterShow:function(){this.allFilterShow=!1}}}),ie=ee,ae=(i("7486"),Object(o["a"])(ie,$t,te,!1,null,"0e0c35c0",null)),ne=ae.exports;function re(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);e&&(a=a.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,a)}return i}function oe(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?re(Object(i),!0).forEach((function(e){Object(m["a"])(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):re(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}var se=i("f121"),ce={name:"search",components:{hotSearch:_t,recentSearch:Ut,filterList:Vt,shopList:Kt,productList:ne},data:function(){return{pdtHeight:"",curStatus:1,curTab:"prod",tabType:[{type:"prod",name:"商品"},{type:"shop",name:"店铺"}],keyword:"",placeholder:"在千万商品中搜索",searchingList:[],clear:!1,filterShow:!1}},mounted:function(){var t=this;X()?this.pdtHeight=".64rem":Q()?this.pdtHeight=".88rem":this.pdtHeight=".3rem",console.log(this.$route.query,"search query");var e=this.$route.query;e.keyword?console.log(e):Pt.defaultSearch({platform:3}).then((function(e){var i=e.data;i.success&&(t.placeholder=i.data.hot_word)}))},methods:oe(oe({},Object(I["c"])(["addHistorySearch"])),{},{changeTab:function(t){this.curTab=t,this.keyword&&("prod"==t?this.curStatus=2:"shop"==t&&(this.curStatus=3),this.doSearch(this.keyword))},doSearch:function(t){this.keyword=t,console.log("doSearch",this.keyword),this.keyword?(-1!==this.keyword.indexOf("店铺")&&(this.curTab="shop",this.keyword=this.keyword.split('"')[1]),"prod"==this.curTab?(this.curStatus=2,console.log("商品",this.curStatus),this.addHistorySearch(this.keyword)):"shop"==this.curTab&&(this.curStatus=3,console.log("店铺",this.curStatus),this.addHistorySearch('"'.concat(this.keyword,'"店铺')))):this.$toast("请填写搜索内容")},searchInput:function(t){var e=this,i=t.target.value;if(i){this.clear=!0,this.keyword=i;var a=setTimeout((function(){clearTimeout(a),Pt.searchDropdown({key:i}).then((function(t){var i=t.data,a=i;a.success&&(e.searchingList=a.data,e.curStatus=0)}))}),200)}else this.clear=!1,this.curStatus=1},inputFocus:function(){this.keyword&&(this.clear=!0)},clearFn:function(){this.keyword="",this.clear=!1,this.curStatus=1},doKeySearch:function(t){t.key?(this.keyword=t.key,this.doSearch(this.keyword)):"1"===t.linkType?tt("".concat(se.HOST,"/sgmobile/goodsDetail?productId=").concat(t.link.split("=")[1],"&container_type=3&hidesBottomBarWhenPushed=1")):"4"===t.linkType?tt("".concat(se.HOST,"/sgmobile/bannerTheme/").concat(t.link.split("&")[1].split("=")[1],"?container_type=3&hidesBottomBarWhenPushed=1")):"5"===t.linkType?tt("".concat(se.HOST,"/sgmobile/banner/").concat(t.link.split("/").slice(-1))):(this.keyword=t.tse,this.doSearch(this.keyword))},formatHtml:function(t){return t.split(this.keyword).join('<span style="color:#999">'.concat(this.keyword,"</span>"))},sureFilter:function(t){console.log(t),this.filterShow=!1,this.doSearch(this.keyword),this.$refs.produtRefList.fetchData(t)},filterFn:function(t){this.filterShow=t},closeFilter:function(){this.filterShow=!1},goBack:function(){this.$router.back()}})},le=ce,de=(i("2333"),Object(o["a"])(le,ut,ht,!1,null,"2bbaa86a",null)),ue=de.exports;a["a"].use(d["a"]);var he=new d["a"]({mode:"hash",base:"",routes:[{path:"/",name:"home",component:dt},{path:"/search",name:"search",component:ue}]}),pe=(i("1e20"),i("157a"),i("b970"));a["a"].use(pe["a"]),a["a"].config.productionTip=!1,K()?a["a"].prototype.$axios=At:a["a"].prototype.$axios=Ct,J(),new a["a"]({router:he,store:_,render:function(t){return t(l)}}).$mount("#app")},5841:function(t,e,i){},"59e0":function(t,e,i){t.exports=i.p+"img/gotop.4f2c9d8d.png"},6052:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYBAMAAAASWSDLAAAALVBMVEVHcEyXl5eZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmZmWlpaZmZmYmJiUlJSZmZkXrFhnAAAADnRSTlMABYpx6jnTolQqHrqwE/x1RjcAAACtSURBVBjTY2BAB1yhE+HsS3nvnlRC2RJ+7sZq7xQhHD1nAUaGsNcCIDbHswVAkrGuAMS54gWWl34BVmUAMfEZiEwRgGitmwAUeQ41tA+ogvUxlBOngMSZB+RwPkJSxugG5egFgEwJALMZXUCmzksEc5jAepmegy3qA4sx7tsFJIXebQQrEH9XEdnkl/0G4pK2d+/eOQueOwgxdbKSqQCDDFQKYva5AKRgEUUNJQAAKSuahllXMwAAAABJRU5ErkJggg=="},6233:function(t,e,i){},"64a9":function(t,e,i){},"6fee":function(t,e,i){"use strict";var a=i("8feb"),n=i.n(a);n.a},7486:function(t,e,i){"use strict";var a=i("288d"),n=i.n(a);n.a},"80ab":function(t,e,i){},"8f86":function(t,e,i){t.exports=i.p+"img/empty.bfc80b2a.png"},"8feb":function(t,e,i){},"9c68":function(t,e,i){},a02e:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACYAAAAGBAMAAABdv/GrAAAAD1BMVEUzMzNHcEwyMjIqKiozMzO2Ay5mAAAABHRSTlOIAO4GsuCDGQAAACZJREFUCNdjMFBiFgQBJJqBxcUBzEaiGVRcnMBsJBqrGDa9WOwAABR/DSc3nNHYAAAAAElFTkSuQmCC"},a427:function(t,e,i){},a4f2:function(t,e,i){"use strict";var a=i("a427"),n=i.n(a);n.a},a948:function(t,e){t.exports="data:image/png;base64,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"},aaf0:function(t,e,i){},b265:function(t,e,i){"use strict";var a=i("aaf0"),n=i.n(a);n.a},b32a:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACoAAAAqCAYAAADFw8lbAAAB8UlEQVRYR+2Xz0pbQRSHfwcvdmN8gET6AhF0Zxa6EjeFPoHbMocSFcUHCHkAUdQgM3TbJxDclK7aRbtrwbyAmDyAcaNcOTKQSAyJc+84qFfmLufPmW++OXOGSyjIRwXhRAQNfVLR6Js3qrX+BWB5AuhvZl7x2UTwoy8MqI+tLHOCG82yqM+YCOpj7ak5TqOOyxGaZzTeQ5V4P6CjW9Rai21jZucm8+h2xc29mCtgHrjhsa64ETSv2Rc32q8SNocfvemT2gcbeg3QsZfNBeLqD56jkxZ0gbj6I+honXUZc/VHo9Fo1gLtyqV467OazPqCFMmo/V1+E0/oFYBSmqaler1+nfdExo1vtVozSZL0APSYeXbcGJ86eg6gCqDGzH9DgGqtlwD8AdBm5vlQoPsAtonoSCm1FQLUGHMoIpsADph5JwioMaYqIv8BpERUU0r9ew6sMWZRRKzNhIgWlFLtIKA2iDHmSEQ2AFwS0Wdf2D7kKYA5IjpWSlmrY7/cOWqjNBqN6XK5fAZgFcANERkR+Z6madt1wfoXp0pE6yKiAHwA8LPb7X5qNpu3QUEHsJVKZU9EvgKY8jz+OyI66XQ6u09B2theRoeh+jn7BcAagI+2dDmgbRm6APCDiL5NysnRGM8G9TSZe1oEza3MMaEwRu8BRB5sOvhbpEEAAAAASUVORK5CYII="},bef1:function(t,e,i){t.exports=i.p+"img/Jg_02.89d03692.png"},d76f:function(t,e){t.exports="data:image/png;base64,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"},e8eb:function(t,e){t.exports="data:image/png;base64,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"},ef7a:function(t,e,i){},f121:function(t,e){var i="",a="",n="",r="";a=0,r=!1;var o=["https://m.ehaier.com","https://m-pre.ehaier.com","http://mobile.testehaier.com"];i=o[a],n=2!==a?"https://s.ehaier.com":"http://s.testehaier.com",t.exports={HOST:i,SHOST:n,DEBUG:r}},fca8:function(t,e,i){"use strict";var a=i("5841"),n=i.n(a);n.a},ff8b:function(t,e,i){"use strict";var a=i("6233"),n=i.n(a);n.a}});
//# sourceMappingURL=app.9f47c739.js.map
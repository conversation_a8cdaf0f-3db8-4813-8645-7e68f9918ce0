(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[31],{554:function(e,t,n){"use strict";n.r(t);var o=n(46),s=n.n(o),i=n(0),a=n.n(i),u=n(1),c=n.n(u),r=n(2),h=n.n(r),p=n(3),v=n.n(p),l=n(5),d=n.n(l),f=n(504),g=n.n(f),M=function(e){function t(){a()(this,t);var e=h()(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.state={active:!1},e.onTouchStart=function(t){e.triggerEvent("TouchStart",!0,t)},e.onTouchMove=function(t){e.triggerEvent("TouchMove",!1,t)},e.onTouchEnd=function(t){e.triggerEvent("TouchEnd",!1,t)},e.onTouchCancel=function(t){e.triggerEvent("TouchCancel",!1,t)},e.onMouseDown=function(t){e.triggerEvent("MouseDown",!0,t)},e.onMouseUp=function(t){e.triggerEvent("MouseUp",!1,t)},e.onMouseLeave=function(t){e.triggerEvent("MouseLeave",!1,t)},e}return v()(t,e),c()(t,[{key:"componentDidUpdate",value:function(){this.props.disabled&&this.state.active&&this.setState({active:!1})}},{key:"triggerEvent",value:function(e,t,n){var o="on"+e,s=this.props.children;s.props[o]&&s.props[o](n),t!==this.state.active&&this.setState({active:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.disabled,o=e.activeClassName,i=e.activeStyle,a=n?void 0:{onTouchStart:this.onTouchStart,onTouchMove:this.onTouchMove,onTouchEnd:this.onTouchEnd,onTouchCancel:this.onTouchCancel,onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onMouseLeave:this.onMouseLeave},u=d.a.Children.only(t);if(!n&&this.state.active){var c=u.props,r=c.style,h=c.className;return!1!==i&&(i&&(r=s()({},r,i)),h=g()(h,o)),d.a.cloneElement(u,s()({className:h,style:r},a))}return d.a.cloneElement(u,a)}}]),t}(d.a.Component),T=M;M.defaultProps={disabled:!1},n.d(t,"default",(function(){return T}))}}]);
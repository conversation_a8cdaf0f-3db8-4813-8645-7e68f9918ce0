(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[24,31],{1411:function(e,t,n){"use strict";n(514),n(1412)},1412:function(e,t,n){},1413:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=g(n(46)),o=g(n(512)),r=g(n(0)),i=g(n(1)),c=g(n(2)),s=g(n(3)),u=g(n(504)),l=v(n(5)),h=v(n(76)),f=g(n(554)),p=g(n(1414)),d=n(1415),m=n(1416);function v(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t.default=e,t}function g(e){return e&&e.__esModule?e:{default:e}}var C=function(e){function t(e){(0,r.default)(this,t);var n=(0,c.default)(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));n.onSubmit=function(e){e.preventDefault(),n.props.onSubmit&&n.props.onSubmit(n.state.value||""),n.inputRef&&n.inputRef.blur()},n.onChange=function(e){n.state.focus||n.setState({focus:!0});var t=e.target.value;"value"in n.props||n.setState({value:t}),n.props.onChange&&n.props.onChange(t)},n.onFocus=function(){n.setState({focus:!0}),n.firstFocus=!0,n.props.onFocus&&n.props.onFocus()},n.onBlur=function(){var e;n.onBlurTimeout=(e=function(){n.blurFromOnClear||document.activeElement!==n.inputRef&&n.setState({focus:!1}),n.blurFromOnClear=!1},window.requestAnimationFrame?window.requestAnimationFrame(e):window.setTimeout(e,1)),n.props.onBlur&&(setTimeout((function(){document.body&&(document.body.scrollTop=document.body.scrollTop)}),100),n.props.onBlur())},n.onClear=function(){n.doClear()},n.doClear=function(){var e=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];n.blurFromOnClear=e,"value"in n.props||n.setState({value:""}),n.props.onClear&&n.props.onClear(""),n.props.onChange&&n.props.onChange(""),e&&n.focus()},n.onCancel=function(){n.props.onCancel?n.props.onCancel(n.state.value||""):n.doClear(!1)},n.focus=function(){n.inputRef&&n.inputRef.focus()};var a=void 0;return a="value"in e?e.value||"":"defaultValue"in e?e.defaultValue:"",n.state={value:a,focus:!1},n}return(0,s.default)(t,e),(0,i.default)(t,[{key:"componentDidMount",value:function(){if(this.rightBtnRef){var e=window.getComputedStyle(this.rightBtnRef);this.rightBtnInitMarginleft=e.marginLeft}this.componentDidUpdate()}},{key:"componentDidUpdate",value:function(){if(this.syntheticPhRef)if(this.inputContainerRef&&this.inputContainerRef.className.indexOf(this.props.prefixCls+"-start")>-1){if(this.syntheticPhContainerRef){var e=this.syntheticPhContainerRef.getBoundingClientRect().width;this.syntheticPhRef.style.width=Math.ceil(e)+"px"}!this.props.showCancelButton&&this.rightBtnRef&&(this.rightBtnRef.style.marginRight="0")}else this.syntheticPhRef.style.width="100%",!this.props.showCancelButton&&this.rightBtnRef&&(this.rightBtnRef.style.marginRight="-"+(this.rightBtnRef.offsetWidth+(null!=this.rightBtnInitMarginleft?parseInt(this.rightBtnInitMarginleft,10):0))+"px")}},{key:"componentWillReceiveProps",value:function(e){"value"in e&&e.value!==this.state.value&&this.setState({value:e.value})}},{key:"componentWillUnmount",value:function(){var e;this.onBlurTimeout&&(e=this.onBlurTimeout,window.cancelAnimationFrame?window.cancelAnimationFrame(e):window.clearTimeout(e),this.onBlurTimeout=null)}},{key:"render",value:function(){var e,t=this,r=this.props,i=r.prefixCls,c=r.showCancelButton,s=r.disabled,h=r.placeholder,m=r.className,v=r.style,g=r.maxLength,C=(0,d.getComponentLocale)(this.props,this.context,"SearchBar",(function(){return n(1417)})).cancelText,y=this.state,b=y.value,_=y.focus,E=(0,u.default)(i,m,(0,o.default)({},i+"-start",!!(_||b&&b.length>0))),w=(0,u.default)(i+"-clear",(0,o.default)({},i+"-clear-show",!!(_&&b&&b.length>0))),M=(0,u.default)(i+"-cancel",(e={},(0,o.default)(e,i+"-cancel-show",!!(c||_||b&&b.length>0)),(0,o.default)(e,i+"-cancel-anim",this.firstFocus),e));return l.createElement("form",{onSubmit:this.onSubmit,className:E,style:v,ref:function(e){return t.inputContainerRef=e},action:"#"},l.createElement("div",{className:i+"-input"},l.createElement("div",{className:i+"-synthetic-ph",ref:function(e){return t.syntheticPhRef=e}},l.createElement("span",{className:i+"-synthetic-ph-container",ref:function(e){return t.syntheticPhContainerRef=e}},l.createElement("i",{className:i+"-synthetic-ph-icon"}),l.createElement("span",{className:i+"-synthetic-ph-placeholder",style:{visibility:h&&!b?"visible":"hidden"}},h))),l.createElement("input",(0,a.default)({type:"search",className:i+"-value",value:b,disabled:s,placeholder:h,onChange:this.onChange,onFocus:this.onFocus,onBlur:this.onBlur,ref:function(e){return t.inputRef=e},maxLength:g},(0,p.default)(this.props))),l.createElement(f.default,{activeClassName:i+"-clear-active"},l.createElement("a",{onClick:this.onClear,className:w}))),l.createElement("div",{className:M,onClick:this.onCancel,ref:function(e){return t.rightBtnRef=e}},this.props.cancelText||C))}}]),t}(l.Component);t.default=C,C.defaultProps=m.defaultProps,C.contextTypes={antLocale:h.object},e.exports=t.default},1414:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return Object.keys(e).reduce((function(t,n){return"aria-"!==n.substr(0,5)&&"data-"!==n.substr(0,5)&&"role"!==n||(t[n]=e[n]),t}),{})},e.exports=t.default},1415:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a,o=n(46),r=(a=o)&&a.__esModule?a:{default:a};t.getComponentLocale=function(e,t,n,a){var o={};if(t&&t.antLocale&&t.antLocale[n])o=t.antLocale[n];else{var i=a();o=i.default||i}var c=(0,r.default)({},o);e.locale&&(c=(0,r.default)({},c,e.locale),e.locale.lang&&(c.lang=(0,r.default)({},o.lang,e.locale.lang)));return c},t.getLocaleCode=function(e){var t=e.antLocale&&e.antLocale.locale;if(e.antLocale&&e.antLocale.exist&&!t)return"zh-cn";return t}},1416:function(e,t,n){"use strict";function a(){}Object.defineProperty(t,"__esModule",{value:!0});t.defaultProps={prefixCls:"am-search",placeholder:"",onSubmit:a,onChange:a,onFocus:a,onBlur:a,onClear:a,showCancelButton:!1,disabled:!1}},1417:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default={cancelText:"\u53d6\u6d88"},e.exports=t.default},1418:function(e,t,n){e.exports={"search-wrap":"search_search-wrap__22CvU","search-page":"search_search-page__3Y4O5","icon-left":"search_icon-left__19gXB","no-search-content":"search_no-search-content__9Lo9A","no-search":"search_no-search__2sxbD",contentWrap:"search_contentWrap__20W1d",choiceList:"search_choiceList__78dzm"}},1452:function(e,t,n){"use strict";n.r(t);n(1411);var a,o=n(1413),r=n.n(o),i=n(6),c=n.n(i),s=n(9),u=n(163),l=n(5),h=n.n(l),f=n(1418),p=n.n(f),d=n(508),m=n(23),v=n(19),g=n(760),C=function(){return h.a.createElement("div",{className:p.a["no-search-content"]},h.a.createElement("div",{className:p.a["no-search"]}),"\u672a\u627e\u5230\u76f8\u5173\u5185\u5bb9")};t.default=function(){var e=Object(l.useState)(""),t=Object(u.a)(e,2),n=t[0],o=t[1],i=Object(l.useState)(),f=Object(u.a)(i,2),y=f[0],b=f[1],_=Object(l.useState)(!1),E=Object(u.a)(_,2),w=E[0],M=E[1],T=Object(l.useState)(!1),R=Object(u.a)(T,2),O=R[0],B=R[1],S=Object(l.useCallback)(function(){var e=Object(s.a)(c.a.mark((function e(t,n){var a;return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,Object(d.e)(t,10,"",n);case 2:a=e.sent,B(!1),a&&a.retCode===m.v&&a.data&&a.data.contents&&0===a.data.contents.length&&0===t&&B(!0),a&&a.retCode===m.v&&a.data&&a.data.contents&&(a.data.contents.length===m.t?M(!0):M(!1),b((function(e){return 0===t?a.data.contents:e.concat(a.data.contents)}))),window.console.log("getChoiceContent",a);case 7:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}(),[]);Object(l.useEffect)((function(){return a.focus(),function(){a&&a.inputContainerRef&&a.inputContainerRef.blur()}}),[]);return h.a.createElement("div",null,h.a.createElement("div",{className:p.a["search-wrap"]},h.a.createElement("div",{className:p.a["icon-left"],onClick:function(){return a&&a.inputContainerRef&&(a.inputContainerRef.blur(),console.log(1111111)),void setTimeout((function(){Object(v.f)()}),300)}}),h.a.createElement("div",{className:p.a["search-page"]},h.a.createElement(r.a,{placeholder:"\u53d1\u73b0\u66f4\u591a\u667a\u6167\u751f\u6d3b",ref:function(e){return a=e},cancelText:"",onChange:function(e){return function(e){o(e)}(e)},onSubmit:function(){return Object(v.u)({actionCode:"9011",dataType:"cms_page",extentInfo:{title:"\u641c\u7d22\u6309\u94ae",content_area_id:"3",content_area:"\u4f17\u64ad",search_keyword:n}}),console.log(n),void S(0,n)}}))),h.a.createElement("div",{className:p.a.contentWrap},y&&y.length>0&&h.a.createElement("div",{className:p.a.choiceList},h.a.createElement(g.a,{loadMore:function(){S(y.length,n)},hasMore:w,contentList:y,listType:"contentList"})),O&&h.a.createElement(C,null)))}},554:function(e,t,n){"use strict";n.r(t);var a=n(46),o=n.n(a),r=n(0),i=n.n(r),c=n(1),s=n.n(c),u=n(2),l=n.n(u),h=n(3),f=n.n(h),p=n(5),d=n.n(p),m=n(504),v=n.n(m),g=function(e){function t(){i()(this,t);var e=l()(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments));return e.state={active:!1},e.onTouchStart=function(t){e.triggerEvent("TouchStart",!0,t)},e.onTouchMove=function(t){e.triggerEvent("TouchMove",!1,t)},e.onTouchEnd=function(t){e.triggerEvent("TouchEnd",!1,t)},e.onTouchCancel=function(t){e.triggerEvent("TouchCancel",!1,t)},e.onMouseDown=function(t){e.triggerEvent("MouseDown",!0,t)},e.onMouseUp=function(t){e.triggerEvent("MouseUp",!1,t)},e.onMouseLeave=function(t){e.triggerEvent("MouseLeave",!1,t)},e}return f()(t,e),s()(t,[{key:"componentDidUpdate",value:function(){this.props.disabled&&this.state.active&&this.setState({active:!1})}},{key:"triggerEvent",value:function(e,t,n){var a="on"+e,o=this.props.children;o.props[a]&&o.props[a](n),t!==this.state.active&&this.setState({active:t})}},{key:"render",value:function(){var e=this.props,t=e.children,n=e.disabled,a=e.activeClassName,r=e.activeStyle,i=n?void 0:{onTouchStart:this.onTouchStart,onTouchMove:this.onTouchMove,onTouchEnd:this.onTouchEnd,onTouchCancel:this.onTouchCancel,onMouseDown:this.onMouseDown,onMouseUp:this.onMouseUp,onMouseLeave:this.onMouseLeave},c=d.a.Children.only(t);if(!n&&this.state.active){var s=c.props,u=s.style,l=s.className;return!1!==r&&(r&&(u=o()({},u,r)),l=v()(l,a)),d.a.cloneElement(c,o()({className:l,style:u},i))}return d.a.cloneElement(c,i)}}]),t}(d.a.Component),C=g;g.defaultProps={disabled:!1},n.d(t,"default",(function(){return C}))},760:function(e,t,n){"use strict";var a=n(5),o=n.n(a),r=n(594),i=n.n(r),c=n(553),s=n.n(c),u=n(547),l=n(548),h=n.n(l),f={transitionDuration:0};t.a=function(e){var t=e.loadMore,n=e.hasMore,a=e.contentList,r=e.listType,c=e.owmUserId,l=e.userId;return o.a.createElement(s.a,{pageStart:0,loadMore:function(){console.log("in load mmm!"),t()},hasMore:n,loader:o.a.createElement("div",{key:"msg-list-loader",className:"loader"})},o.a.createElement(i.a,{options:f},a.map((function(e,t){return o.a.createElement("div",{key:t,className:"".concat(h.a.masonry," masonry")},o.a.createElement("div",{style:{overflow:"hidden",height:"auto"}},o.a.createElement(u.a,{contents:e,index:t,listType:r,owmUserId:c,userId:l})))}))))}}}]);
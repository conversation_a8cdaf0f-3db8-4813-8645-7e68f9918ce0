(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[22],{1459:function(e,t,n){"use strict";n.r(t);var r=n(536),a=n(163),o=n(5),c=n.n(o),i=n(916),u=n.n(i),s=n(530),A=n(758),l=n(508),d=n(502),f=n(572),v=n.n(f),p=function(e){var t=e.sceneData,r=e.hasToggle,i=Object(o.useState)(!1),s=Object(a.a)(i,2),A=s[0],l=s[1];return c.a.createElement("div",{className:u.a.sceneItemWrap},c.a.createElement("p",{className:u.a.itemName},t.tabName),c.a.createElement("div",{style:{height:r&&!A&&t.plans&&t.plans.length>4?"5.6rem":"auto",overflow:"hidden"}},t&&t.plans&&t.plans.length>0&&t.plans.map((function(e,t){return c.a.createElement("div",{className:u.a.sceneItem,key:e.planThumbImg+t,onClick:function(){return t=e.planDetailsUrl,void Object(d.d)({url:t,noNeedAuth:!0});var t}},c.a.createElement(v.a,{src:e.planThumbImg,placeholder:n(600)},(function(t){return c.a.createElement("img",{src:t,className:u.a.pic,alt:e.title})})),c.a.createElement("span",{className:u.a.titleName},e.salesHighlights))}))),r&&t.plans&&t.plans.length>4&&c.a.createElement("div",{className:u.a.toggle,onClick:function(){return l(!A)}},c.a.createElement("span",null,A?"\u6536\u8d77":"\u5c55\u793a\u5168\u90e8\u5185\u5bb9"),c.a.createElement("span",{className:"".concat(A?u.a["arrow-up"]:u.a["arrow-down"])})))},g=n(707);t.default=function(){var e=Object(o.useState)([]),t=Object(a.a)(e,2),n=t[0],i=t[1],d=Object(o.useState)([{tabId:"all",tabName:"\u5168\u90e8"}]),f=Object(a.a)(d,2),v=f[0],h=f[1],b=Object(o.useState)([]),m=Object(a.a)(b,2),E=m[0],y=m[1],O=Object(o.useState)("all"),w=Object(a.a)(O,2),C=w[0],j=w[1];Object(o.useEffect)((function(){Object(l.k)("https://m.ehaier.com/sg/cms/home/<USER>/getTabsByPageInfo.json").then((function(e){if(e&&e.data&&e.data.tabs.length>0){var t=e.data.tabs.filter((function(e){return"-1"!==String(e.tabId)}));if(t&&t.length>0){window.console.log("wyf \u63a8\u8350\u6570\u636e --",JSON.stringify(t)),i(t);var n=t.map((function(e){return{tabName:e.tabName,tabId:e.tabId}}));h([{tabId:"all",tabName:"\u5168\u90e8"}].concat(Object(r.a)(n)))}}}))}),[]);return Object(o.useEffect)((function(){if(n&&n.length>0&&C){var e="all"===C?n:n.filter((function(e){return e.tabId===C}));y(e),window.console.log("333",e)}}),[C,n]),c.a.createElement(c.a.Fragment,null,c.a.createElement(s.a,{title:"\u667a\u5bb6\u573a\u666f\u5546\u57ce"}),c.a.createElement("div",{className:"".concat(u.a.pageWrap)},c.a.createElement("div",{className:"".concat(u.a.content)},c.a.createElement(g.a,{adLocation:"1031"}),E.length>0&&c.a.createElement("div",null,c.a.createElement(A.a,{onPress:function(e){j(e.tabId)},currentTabId:C,tabList:v}),c.a.createElement("div",{className:u.a.contentWrap},E.map((function(e,t){return c.a.createElement(p,{key:t,sceneData:e,hasToggle:"all"===C})})))))))}},502:function(e,t,n){"use strict";n.d(t,"d",(function(){return f})),n.d(t,"g",(function(){return v})),n.d(t,"f",(function(){return p})),n.d(t,"k",(function(){return g})),n.d(t,"b",(function(){return h})),n.d(t,"c",(function(){return b})),n.d(t,"e",(function(){return m})),n.d(t,"j",(function(){return E})),n.d(t,"h",(function(){return y})),n.d(t,"i",(function(){return O})),n.d(t,"a",(function(){return w}));var r=n(6),a=n.n(r),o=(n(506),n(507)),c=n.n(o),i=n(9),u=n(19),s=n(23),A=n(510);function l(e,t,n){var r=e.split("?")[0],a="";return e.indexOf("?")>0&&(a=e.split("?")[1]),a&&(a="&"+a),e="".concat(r,"?").concat(t,"=").concat(n).concat(a)}function d(e,t,n){var r=e;return t&&r.indexOf("container_type")<0&&(r=l(r,"container_type",t)),r.indexOf("hidesBottomBarWhenPushed")<0&&(r=l(r,"hidesBottomBarWhenPushed","1")),n&&r.indexOf("needAuthLogin")<0&&(r=l(r,"needAuthLogin","1")),function(e){var t=e.match(/#.*\?/);return t&&t[0]&&(e=e.replace(/#.*\?/g,"?")+t[0].split("?")[0]),e}(r)}var f=function(){var e=Object(i.a)(a.a.mark((function e(t){var n,r;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(u.o)();case 3:if(!(n=e.sent)){e.next=9;break}r=d(t.url,t.containerType,!t.noNeedAuth),Object(u.n)(r),e.next=10;break;case 9:throw Error(n);case 10:e.next=15;break;case 12:e.prev=12,e.t0=e.catch(0),c.a.info("\u7f51\u7edc\u4e0d\u53ef\u7528",3);case 15:case"end":return e.stop()}}),e,null,[[0,12]])})));return function(t){return e.apply(this,arguments)}}();function v(e){e&&e.retData&&/\u65ad\u5f00\u4e0e\u4e92\u8054\u7f51\u7684\u8fde\u63a5|unable to resolve host/gi.test(e.retData)?Object(A.c)(s.m):e&&e.retData&&/failed to connect|\u672a\u80fd\u8fde\u63a5\u5230\u670d\u52a1\u5668|timeout|\u8d85\u65f6/gi.test(e.retData)?Object(A.c)(s.y):Object(A.c)(s.u)}function p(e){return e&&e.retData&&/\u65ad\u5f00\u4e0e\u4e92\u8054\u7f51\u7684\u8fde\u63a5|unable to resolve host/gi.test(e.retData)?(Object(A.c)(s.m),!0):!!(e&&e.retData&&/failed to connect|\u672a\u80fd\u8fde\u63a5\u5230\u670d\u52a1\u5668|timeout|\u8d85\u65f6/gi.test(e.retData))&&(Object(A.c)(s.y),!0)}function g(e,t){var n=e,r=null,a=!0;return function(){for(var e=this,o=arguments.length,c=new Array(o),i=0;i<o;i++)c[i]=arguments[i];if(a)return n.apply(this,c),void(a=!1);r||(r=setTimeout((function(){clearTimeout(r),r=null,n.apply(e,c)}),t))}}function h(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=0;return function(){for(var a=arguments.length,o=new Array(a),c=0;c<a;c++)o[c]=arguments[c];if(r&&clearTimeout(r),n){var i=!r;r=setTimeout((function(){r=null}),t),i&&e.apply(void 0,o)}else r=setTimeout((function(){e.apply(void 0,o)}),t)}}function b(e){var t={};if(-1!==e.indexOf("?")){var n=e.substr(e.indexOf("?")+1,e.length);-1!==n.indexOf("#")&&(n=n.substr(0,n.indexOf("#")));for(var r=n.split("&"),a=0;a<r.length;a++)t[r[a].split("=")[0]]=decodeURIComponent(r[a].split("=")[1])}return t}function m(e,t){e.target.src=t,console.log("~~~~~~~~~~~~~~~~~~",t)}function E(e){return Object.keys(e).sort().reduce((function(t,n){return t[n]=e[n],t}),{})}var y=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.href;return new Promise((function(t){new window.OpenInstall({appKey:"vghhgm",onready:function(){this.wakeupOrInstall()}},{targetUrl:e,type:"launchToPage"})}))};function O(e){var t=e.targetUrl,n=void 0===t?window.location.href:t,r=e.userId,a=void 0===r?"":r,o=e.fn,c=void 0===o?function(){}:o;if(/apicloud|uphybrid|uplus/i.test(window.navigator.userAgent.toLowerCase()))a?c():f({url:"apicloud://usercenter",noNeedAuth:!0});else{var i=window.location.href;i&&i.indexOf("outOrigin")<0&&y(n)}}function w(e,t){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return e&&e.indexOf("zjrs.haier.net")>-1&&e.indexOf("oss-process")<0?!n&&/\.gif$/i.test(e)?e:e.indexOf("?")>-1?e.split("?")[0]+"?"+t+"&"+e.split("?")[1]:e+"?"+t:e}},508:function(e,t,n){"use strict";n.d(t,"o",(function(){return l})),n.d(t,"d",(function(){return d})),n.d(t,"g",(function(){return f})),n.d(t,"k",(function(){return v})),n.d(t,"l",(function(){return p})),n.d(t,"t",(function(){return g})),n.d(t,"x",(function(){return h})),n.d(t,"s",(function(){return b})),n.d(t,"w",(function(){return m})),n.d(t,"r",(function(){return E})),n.d(t,"y",(function(){return y})),n.d(t,"n",(function(){return O})),n.d(t,"m",(function(){return w})),n.d(t,"p",(function(){return j})),n.d(t,"q",(function(){return S})),n.d(t,"e",(function(){return I})),n.d(t,"b",(function(){return V})),n.d(t,"f",(function(){return P})),n.d(t,"i",(function(){return B})),n.d(t,"c",(function(){return D})),n.d(t,"h",(function(){return M})),n.d(t,"j",(function(){return U})),n.d(t,"a",(function(){return x})),n.d(t,"u",(function(){return k})),n.d(t,"v",(function(){return _}));var r=n(6),a=n.n(r),o=n(9),c=n(92),i=n(23),u=n(531),s=n.n(u),A=n(19),l=function(e,t){return Object(c.d)({url:t?"/omsappapi/special/v3/test/list":"/omsappapi/special/v3/list",data:{suuid:e}})},d=function(e){return Object(c.d)({url:"/omsappapi/ad/v1/rotation",data:{adLocation:e}})},f=function(e){var t=e.province,n=e.city;return Object(c.d)({url:"/rcs/weather/current-forecast",data:{province:t,city:n}})},v=function(e){return Object(c.b)({url:e})},p=function(){var e=Object(o.a)(a.a.mark((function e(t){return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,A.A.initDeviceReady();case 2:return window.console.log("ppppppp",t),e.abrupt("return",s()({method:"post",url:"https://haier.vhallyun.com/api/livecenter/live-center-list",data:{page:t},headers:{"Content-Type":"application/x-www-form-urlencoded"},transformRequest:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.entries(e).map((function(e){return e.join("=")})).join("&")||""}}));case 4:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}(),g=function(e){var t=e.title,n=e.content,r=e.videoUrl,a=e.classCode,o=e.coverUrls;return Object(c.d)({url:"/scs/contents/v1/video",data:{title:t,content:n,videoUrl:r,classCode:a,coverUrls:o}})},h=function(e){var t=e.contentId,n=e.title,r=e.content,a=e.videoUrl,o=e.classCode,i=e.coverUrls;return Object(c.e)({url:"/scs/contents/v1/video",data:{contentId:t,title:n,content:r,videoUrl:a,classCode:o,coverType:3,coverUrls:i}})},b=function(e){var t=e.title,n=e.content,r=e.imageUrls,a=e.classCode,o=e.coverType,u=void 0===o?i.j:o,s=e.coverUrls;return Object(c.d)({url:"/scs/contents/v1/microPost",data:{title:t,content:n,imageUrls:r,classCode:a,coverType:u,coverUrls:s}})},m=function(e){var t=e.contentId,n=e.title,r=e.content,a=e.imageUrls,o=e.classCode,u=e.coverType,s=void 0===u?i.j:u,A=e.coverUrls;return Object(c.e)({url:"/scs/contents/v1/microPost",data:{contentId:t,title:n,content:r,imageUrls:a,classCode:o,coverType:s,coverUrls:A}})},E=function(e){var t=e.title,n=e.content,r=e.coverType,a=e.coverUrls,o=e.classCode;return Object(c.d)({url:"/scs/contents/v1/article",data:{title:t,content:n,coverType:r,coverUrls:a,classCode:o}})},y=function(e){var t=e.contentId,n=e.title,r=e.content,a=e.coverType,o=e.coverUrls,i=e.classCode;return Object(c.e)({url:"/scs/contents/v1/article",data:{contentId:t,title:n,content:r,coverType:a,coverUrls:o,classCode:i}})},O=function(e){return Object(c.d)({url:"/scs/contents/v1/safeDetail",data:{contentId:e}})},w=function(e){var t="/scs/contents/v1/detail";if(/apicloud|uphybrid|uplus/i.test(window.navigator.userAgent.toLowerCase()))return Object(c.d)({url:t,data:{contentId:e}});var n=Object(c.c)(t);return s()({method:"post",url:n,data:{contentId:e},headers:{"Content-Type":"application/json;charset=utf-8"}}).then((function(e){return e&&200===e.status&&e.data?Promise.resolve(e.data):Promise.reject(e)})).catch((function(e){return Promise.reject(e)}))},C=null,j=function(){return C?(setTimeout((function(){T()}),2e3),Promise.resolve(C)):T()},T=function(){var e=Object(o.a)(a.a.mark((function e(){var t;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(c.b)({url:"/scs/commons/v1/classes"});case 3:return(t=e.sent)&&t.retCode===i.v&&t.data&&t.data.classes&&t.data.classes.length>0&&(C=t),e.abrupt("return",t);case 8:return e.prev=8,e.t0=e.catch(0),e.abrupt("return",Promise.reject(e.t0));case 11:case"end":return e.stop()}}),e,null,[[0,8]])})));return function(){return e.apply(this,arguments)}}(),S=function(){var e=Object(o.a)(a.a.mark((function e(t){var n;return a.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(c.d)({url:"/scs/users/v1/calsses",data:{userId:t}});case 3:return n=e.sent,e.abrupt("return",n);case 7:return e.prev=7,e.t0=e.catch(0),e.abrupt("return",Promise.reject(e.t0));case 10:case"end":return e.stop()}}),e,null,[[0,7]])})));return function(t){return e.apply(this,arguments)}}(),I=function(e,t,n,r){return Object(c.d)({url:"/scs/contents/v1/contents",data:{index:e,count:t,classCode:n,keyWords:r}})},V=function(e){return Object(c.d)({url:"/scs/contents/v1/destroy",data:{contentId:e}})},P=function(e){var t=e.index,n=e.count,r=e.contentType;return Object(c.d)({url:"/scs/users/v1/contents",data:{index:t,count:n,contentType:r}})},B=function(e){var t=e.index,n=e.count;return Object(c.d)({url:"/scs/users/v1/favorites",data:{index:t,count:n}})},D=function(e){var t=e.index,n=e.count,r=e.userId,a=e.contentType;return Object(c.d)({url:"/scs/users/v1/author/contents",data:{index:t,count:n,userId:r,contentType:a}})},M=function(){return Object(c.b)({url:"/scs/users/v1/fans"})},U=function(){return Object(c.b)({url:"/scs/users/v1/followers"})},x=function(e){return Object(c.d)({url:"/scs/users/v1/follow",data:{userId:e}})},k=function(e){return Object(c.b)({url:"/scs/users/v1/detail?userId=".concat(e),data:{}})},_=function(e){return Object(c.b)({url:"/scs/users/v1/author/detail?userId=".concat(e),data:{}})}},510:function(e,t,n){"use strict";n.d(t,"c",(function(){return o})),n.d(t,"b",(function(){return c})),n.d(t,"a",(function(){return i}));n(506);var r=n(507),a=n.n(r),o=function(e){a.a.info(e||"\u63d0\u793a\u5185\u5bb9",2)},c=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;a.a.loading("\u52a0\u8f7d\u4e2d",e)},i=function(){return setTimeout((function(){return a.a.hide()}),0)}},524:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAAFVBMVEUAAAA2NjYzMzM1NTU0NDQ0NDQzMzNPh4ptAAAABnRSTlMAMO1cYthR96LlAAAAWElEQVRIx2MYBcMGMAsQochNkbAalrQkAcIGpaUpEjYoLS2VsEEgkwgblCQwatCoQUPJIGZiFDGYga0bNWrUqKFvVCoDEUYpEq7JQG4ibJQicZXiKBh4AACF/kiRuQZwhQAAAABJRU5ErkJggg=="},526:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAASKADAAQAAAABAAAASAAAAACQMUbvAAABVUlEQVR4Ae3aQUoDQRCF4UFyGjdZeJhsQhaeOlcQxAMI2inFdubNPB/jMvTfUHRXVTIwH9WbkGliIYAAAggggAACCCCAAAIIDCvQWrtUHIcFSC9eMM8VnxWvIK2kFjh1/F4gdaPi6JPzY/O7gRRwutK1Dg8dc6h9B864EwROuAvggBMEQovJAScIhBaTA04QCC0mB5wgEFpMDjhBILSYHHCCQGgxOeAEgdBicsAJAqHF5ICzEfjPj+Lv9e22ecJc+HrWYU4HPNUVO1d8VPy13qrxNCDN/MoFANLM4U8geRepgiQcPgHJu0gVJOHwCUjeRaogCYdPQPIuUgVJOHwCkneRKkjC4ROQvItUQRIOn4DkXaQKknD4BCTvIlWQhMMnIHkXqe5AGveP5F0qIL1U77F/bujdIIGznogFEjhrnJ4X0olr1TXYEUAAAQQQQAABBBC4d4EbAZy47u7W9DkAAAAASUVORK5CYII="},530:function(e,t,n){"use strict";n(521);var r=n(522),a=n.n(r),o=n(5),c=n.n(o),i=n(532),u=n.n(i),s=n(19);t.a=function(e){return c.a.createElement(a.a,{className:"".concat(u.a["my-nav-bar"]," ").concat(e.mode&&"dark"===e.mode?u.a.dark:""),mode:"light",leftContent:e.leftContent||[c.a.createElement("span",{key:"left-icon",className:u.a["nav-bar-span"],role:"button",tabIndex:0,onClick:function(){"function"===typeof e.onClose?e.onClose():Object(s.f)()}},c.a.createElement("img",{className:u.a["nav-bar-icon"],src:e&&"dark"===e.mode?n(526):n(524),alt:""}))],rightContent:e.rightContent},e.title)}},532:function(e,t,n){e.exports={"my-nav-bar":"MyNavBar_my-nav-bar__B7cJG",dark:"MyNavBar_dark__11_W2","nav-bar-span":"MyNavBar_nav-bar-span__1_CkP","nav-bar-icon":"MyNavBar_nav-bar-icon__3egr0"}},536:function(e,t,n){"use strict";var r=n(116);var a=n(165);function o(e){return function(e){if(Array.isArray(e))return Object(r.a)(e)}(e)||function(e){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||Object(a.a)(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(t,"a",(function(){return o}))},539:function(e,t,n){"use strict";var r=n(46),a=n.n(r),o=n(0),c=n.n(o),i=n(1),u=n.n(i),s=n(2),A=n.n(s),l=n(3),d=n.n(l),f=n(5),v=n.n(f),p=251,g=10,h=.3;function b(e,t){return Math.sqrt(e*e+t*t)}function m(e,t){var n=Math.atan2(t,e);return 180/(Math.PI/n)}function E(){return Date.now()}function y(e){if(!(e.length<2)){var t=e[0],n=t.x,r=t.y,a=e[1],o=a.x-n,c=a.y-r;return{x:o,y:c,z:b(o,c),angle:m(o,c)}}}function O(e,t){return e+t[0].toUpperCase()+t.slice(1)}function w(e){var t=void 0;switch(e){case 1:break;case 2:t="left";break;case 4:t="right";break;case 8:t="up";break;case 16:t="down"}return t}var C={all:30,vertical:24,horizontal:6},j=function(e){function t(e){c()(this,t);var n=A()(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.state={},n.triggerEvent=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];var o=n.props[e];"function"===typeof o&&o.apply(void 0,[n.getGestureState()].concat(r))},n.triggerCombineEvent=function(e,t){for(var r=arguments.length,a=Array(r>2?r-2:0),o=2;o<r;o++)a[o-2]=arguments[o];n.triggerEvent.apply(n,[e].concat(a)),n.triggerSubEvent.apply(n,[e,t].concat(a))},n.triggerSubEvent=function(e,t){for(var r=arguments.length,a=Array(r>2?r-2:0),o=2;o<r;o++)a[o-2]=arguments[o];if(t){var c=O(e,t);n.triggerEvent.apply(n,[c].concat(a))}},n.triggerPinchEvent=function(e,t){for(var r=arguments.length,a=Array(r>2?r-2:0),o=2;o<r;o++)a[o-2]=arguments[o];var c=n.gesture.scale;"move"===t&&"number"===typeof c&&(c>1&&n.triggerEvent("onPinchOut"),c<1&&n.triggerEvent("onPinchIn")),n.triggerCombineEvent.apply(n,[e,t].concat(a))},n.initPressTimer=function(){n.cleanPressTimer(),n.pressTimer=setTimeout((function(){n.setGestureState({press:!0}),n.triggerEvent("onPress")}),p)},n.cleanPressTimer=function(){n.pressTimer&&clearTimeout(n.pressTimer)},n.setGestureState=function(e){n.gesture||(n.gesture={}),n.gesture.touches&&(n.gesture.preTouches=n.gesture.touches),n.gesture=a()({},n.gesture,e)},n.getGestureState=function(){return n.gesture?a()({},n.gesture):n.gesture},n.cleanGestureState=function(){delete n.gesture},n.getTouches=function(e){return Array.prototype.slice.call(e.touches).map((function(e){return{x:e.screenX,y:e.screenY}}))},n.triggerUserCb=function(e,t){var r=O("onTouch",e);r in n.props&&n.props[r](t)},n._handleTouchStart=function(e){n.triggerUserCb("start",e),n.event=e,e.touches.length>1&&e.preventDefault(),n.initGestureStatus(e),n.initPressTimer(),n.checkIfMultiTouchStart()},n.initGestureStatus=function(e){n.cleanGestureState();var t=n.getTouches(e),r=E(),a=y(t);n.setGestureState({startTime:r,startTouches:t,startMutliFingerStatus:a,time:r,touches:t,mutliFingerStatus:a,srcEvent:n.event})},n.checkIfMultiTouchStart=function(){var e=n.props,t=e.enablePinch,r=e.enableRotate,a=n.gesture.touches;if(a.length>1&&(t||r)){if(t){var o=y(a);n.setGestureState({startMutliFingerStatus:o,pinch:!0,scale:1}),n.triggerCombineEvent("onPinch","start")}r&&(n.setGestureState({rotate:!0,rotation:0}),n.triggerCombineEvent("onRotate","start"))}},n._handleTouchMove=function(e){n.triggerUserCb("move",e),n.event=e,n.gesture&&(n.cleanPressTimer(),n.updateGestureStatus(e),n.checkIfSingleTouchMove(),n.checkIfMultiTouchMove())},n.checkIfMultiTouchMove=function(){var e=n.gesture,t=e.pinch,r=e.rotate,a=e.touches,o=e.startMutliFingerStatus,c=e.mutliFingerStatus;if(t||r){if(a.length<2)return n.setGestureState({pinch:!1,rotate:!1}),t&&n.triggerCombineEvent("onPinch","cancel"),void(r&&n.triggerCombineEvent("onRotate","cancel"));if(t){var i=c.z/o.z;n.setGestureState({scale:i}),n.triggerPinchEvent("onPinch","move")}if(r){var u=function(e,t){var n=e.angle;return t.angle-n}(o,c);n.setGestureState({rotation:u}),n.triggerCombineEvent("onRotate","move")}}},n.allowGesture=function(){return e=n.gesture.direction,!!(n.directionSetting&e);var e},n.checkIfSingleTouchMove=function(){var e=n.gesture,t=e.pan,r=e.touches,a=e.moveStatus,o=e.preTouches,c=e.availablePan,i=void 0===c||c;if(r.length>1)return n.setGestureState({pan:!1}),void(t&&n.triggerCombineEvent("onPan","cancel"));if(a&&i){var u=function(e,t){var n=e.x,r=e.y,a=t.x-n,o=t.y-r;return 0===a&&0===o?1:Math.abs(a)>=Math.abs(o)?a<0?2:4:o<0?8:16}(o[0],r[0]);n.setGestureState({direction:u});var s=w(u);if(!n.allowGesture())return void(t||n.setGestureState({availablePan:!1}));t?(n.triggerCombineEvent("onPan",s),n.triggerSubEvent("onPan","move")):(n.triggerCombineEvent("onPan","start"),n.setGestureState({pan:!0,availablePan:!0}))}},n.checkIfMultiTouchEnd=function(e){var t=n.gesture,r=t.pinch,a=t.rotate;r&&n.triggerCombineEvent("onPinch",e),a&&n.triggerCombineEvent("onRotate",e)},n.updateGestureStatus=function(e){var t=E();if(n.setGestureState({time:t}),e.touches&&e.touches.length){var r=n.gesture,a=r.startTime,o=r.startTouches,c=r.pinch,i=r.rotate,u=n.getTouches(e),s=function(e,t,n){var r=e[0],a=r.x,o=r.y,c=t[0],i=c.x-a,u=c.y-o,s=b(i,u);return{x:i,y:u,z:s,time:n,velocity:s/n,angle:m(i,u)}}(o,u,t-a),A=void 0;(c||i)&&(A=y(u)),n.setGestureState({touches:u,mutliFingerStatus:A,moveStatus:s})}},n._handleTouchEnd=function(e){n.triggerUserCb("end",e),n.event=e,n.gesture&&(n.cleanPressTimer(),n.updateGestureStatus(e),n.doSingleTouchEnd("end"),n.checkIfMultiTouchEnd("end"))},n._handleTouchCancel=function(e){n.triggerUserCb("cancel",e),n.event=e,n.gesture&&(n.cleanPressTimer(),n.updateGestureStatus(e),n.doSingleTouchEnd("cancel"),n.checkIfMultiTouchEnd("cancel"))},n.triggerAllowEvent=function(e,t){n.allowGesture()?n.triggerCombineEvent(e,t):n.triggerSubEvent(e,t)},n.doSingleTouchEnd=function(e){var t=n.gesture,r=t.moveStatus,a=t.pinch,o=t.rotate,c=t.press,i=t.pan,u=t.direction;if(!a&&!o){if(r){var s=function(e,t){return Math.abs(e)>=g&&Math.abs(t)>h}(r.z,r.velocity);if(n.setGestureState({swipe:s}),i&&n.triggerAllowEvent("onPan",e),s){var A=w(u);return void n.triggerAllowEvent("onSwipe",A)}}c?n.triggerEvent("onPressUp"):n.triggerEvent("onTap")}},n.getTouchAction=function(){var e=n.props,t=e.enablePinch,r=e.enableRotate,a=n.directionSetting;return t||r||30===a?"pan-x pan-y":24===a?"pan-x":6===a?"pan-y":"auto"},n.directionSetting=C[e.direction],n}return d()(t,e),u()(t,[{key:"componentWillUnmount",value:function(){this.cleanPressTimer()}},{key:"render",value:function(){var e=this.props.children,t=v.a.Children.only(e),n=this.getTouchAction(),r={onTouchStart:this._handleTouchStart,onTouchMove:this._handleTouchMove,onTouchCancel:this._handleTouchCancel,onTouchEnd:this._handleTouchEnd};return v.a.cloneElement(t,a()({},r,{style:a()({touchAction:n},t.props.style||{})}))}}]),t}(f.Component);t.a=j;j.defaultProps={enableRotate:!1,enablePinch:!1,direction:"all"}},600:function(e,t){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAeAAAAEOCAMAAABmRDRVAAAAaVBMVEXd3d3////5+fni4uL09PTq6urg4ODj4+Pf39/7+/vr6+vu7u78/Pz6+vrm5ub19fXh4eH9/f3z8/Pk5OTs7Ozt7e34+Pjv7+/n5+f29vbp6eny8vLw8PD39/fl5eX+/v7e3t7o6Ojx8fFw+clAAAAE30lEQVR4XuzASREAMAgEsDVCL/8iq4IPk6QPAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAACst+uezMRn526XE4WhAAyfQyB8CMiXAiom9P4vcme7TRMNGqoL7jrn+cmU4Mw7IcXQFlv8bZ/DOyKFh3+IDlZFmBKyMIJJEdM4PKRCJeawJiIMI0wKhFbBI0KB3xJYE0HDrcCo+fCIxBrhrVDgBrUB3ggFtkeo4I1QYHsNDuDtUGA4ouJF8H4oMB/wj7SGN0SBgX+UiCi2IbwVCqyxQxLU8JdIDiuhwOvj4zHLInhPFJidYkQUDNZDgSXLx8OhOxcSHiTZuQuCMWeOmRltUvVZV0KBi8DPBH4qvePIAYrNNw6fktY8Yo9QqRFE5jcMbjp7+GUDq6DA9bHEC/EH6/BbCL/JGL8xK1p1NULp5zAtKNf9QowCs0qgpRxQEV+BPX3kKnDo44T+DBN2qO1heRQ4SPEed2DZlDhJtPLulgWmHBZGgeUH4nOBowpv6gu4NOKFGpZFgaWPTwbmA96RMTCxFC9sYCEU2N3XHdjua8sKMGzxkgeLosAnfDKw3KLDEN26Qe+bkMNPUeAgmpRMBR7RlG7bJEnavvxB4A2avNPhXOdJFaPhCIr0UEsDCQ+gwGU8qZwIzM0QWaKmU9FkcwPXArW+U8l4E08tGh1qXgizEZzDDtwaEdsINP4xM3BvjLCToPEK7dcDfGtpXhIFLkpUyhEuBWJO4M7oq0ewlvfEup44w+Io8MauowVzAm/vvaVVobKX1yv+ERZHgWV293H05A7MBCofYIn0+PnViiBqWBwFPpszzMYzZ+AGlTgCW4fKSc13fcHFUeCd49G5cQbWxfp8wphebip4q+4iUeBqcvppRekILGOcqeSXS0ILy6PAe9d86h2BmcC5agDg6d/4CpoCe9tJ3nXgKMYvDUw7OQKfcbZx6cC02XC4Dqwn1AGmbRyBO5wtWT0wbTbw0hV49z8HpsDuW3T7P9+iKbDu9QHT/Nm/ZMVJk9wT8NUDU2D9FOtJmBLFjsD6HpBG4LB+YArcOr75H52bDQMqh38vMAXujOBTBmfgFpXh3wtMgfWMEt3kCc7A5/tX3fibMeSvCkyB4YhKzOBaXboDyz0qGb9xi0/31e78ksAU+CxQ8dh13wzdgSHBb310PYLu2a4dmAJb+/VxDqZDinMCR9nNd9zzGJW0eE1gClwLo+IxBKX25750d0AtM64cbYyhG2sN3sEaKDC0aBDbJmdFmO8GgXMDg4+GISgkAMh6k5lH5asCU2DZ4yVRqrgzA/MMTaXX+8PloZTBqwJTYCiyZ/90pU4dJ+fwusAUGFj2ZGDI7xYWI7wyMAUGtn80sP1EZUtzeG1gCgy8ejIwFD7e0DN4VWAKrB2yyazOwFowOULaSHhdYAqs8cYKtA8q65+wGBUZXOI7K3HcFnCBl7TZ8CjPkMOkztNOcC0aqxgVkR07CTtP2X+V8jPFK+6PgLF/4NY1Bk8J4EeINMANrh+J6t1noT4J5dUJs0cItp+zv6mj+5/gVztwTAAAAIAwyP6pTbEPFgEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA4feBhB9dshxcAAAAASUVORK5CYII="},623:function(e,t){e.exports="data:image/png;base64,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"},675:function(e,t,n){e.exports={header:"Header_header__32P-i","header-top-fake":"Header_header-top-fake__29FcB","header-bar":"Header_header-bar__2xvPQ",text:"Header_text__ODwb2",weather:"Header_weather__vY1Il",city:"Header_city__1WeTu","angle-down":"Header_angle-down__1Nzmp",dark:"Header_dark__S06_c",serviceWrap:"Header_serviceWrap__1A593",bindingWrap:"Header_bindingWrap__1kILQ",carousel:"Header_carousel__1e5x1"}},707:function(e,t,n){"use strict";n(583);var r=n(584),a=n.n(r),o=n(6),c=n.n(o),i=n(9),u=n(163),s=n(5),A=n.n(s),l=n(19),d=n(675),f=n.n(d),v=n(502),p=n(115),g=n(508);t.a=function(e){var t=e.adLocation,r=Object(s.useContext)(p.a).state.appOnlineStatus,o=Object(s.useState)("117"),d=Object(u.a)(o,2),h=d[0],b=d[1],m=Object(s.useState)(!0),E=Object(u.a)(m,2),y=E[0],O=E[1],w=Object(s.useState)({describe:"",slideList:[],slideTime:5,title:""}),C=Object(u.a)(w,2),j=C[0],T=C[1],S=Object(s.useCallback)((function(){O(!1),Object(g.d)(t).then((function(e){e&&e.data&&(T((function(t){return{describe:e.data.describe?e.data.describe:t.describe,slideList:e.data.slideList&&e.data.slideList.length?e.data.slideList:t.slideList,slideTime:e.data.slideTime?e.data.slideTime:t.slideTime,title:e.data.title?e.data.title:t.title}})),O(!0))}))}),[t]);Object(s.useEffect)((function(){r&&S()}),[r,S]),Object(s.useEffect)((function(){return Object(l.c)(S,"resumeCarousel"),function(){Object(l.t)("resumeCarousel")}}),[S]);var I=function(){var e=Object(i.a)(c.a.mark((function e(t,n,r,a){return c.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:Object(l.u)({actionCode:"000".concat(a),extentInfo:{title:n}}),Object(v.d)({url:t,containerType:r,noNeedAuth:!0});case 2:case"end":return e.stop()}}),e)})));return function(t,n,r,a){return e.apply(this,arguments)}}();return A.a.createElement("div",{className:f.a.carousel},A.a.createElement(a.a,{autoplay:y,infinite:!0,dots:j.slideList&&j.slideList.length>1,key:j.slideList&&j.slideList.length,dotStyle:{width:"5px",height:"5px",backgroundColor:"rgba(255,255,255,0.5)"},dotActiveStyle:{backgroundColor:"#fff",width:"10px",height:"5px",borderRadius:"2.5px"},autoplayInterval:1e3*j.slideTime},j.slideList&&j.slideList.map((function(e,t){return A.a.createElement("div",{key:e.pictureUrl,onClick:e.detailsUrl?function(){return I(e.detailsUrl,e.title,e.containerType,t+1)}:void 0,style:{display:"inline-block",width:"100%",height:h}},A.a.createElement("img",{key:e.pictureUrl,src:e.pictureUrl||n(623),alt:"",style:{width:"100%",verticalAlign:"top",height:h,borderRadius:".2rem"},onError:function(e){return Object(v.e)(e,n(623))},onLoad:function(){window.dispatchEvent(new Event("resize")),b((function(){return h}))}}))}))))}},758:function(e,t,n){"use strict";n(886);var r=n(887),a=n.n(r),o=n(50),c=n(5),i=n.n(c),u=n(759),s=n.n(u);t.a=function(e){var t=e.tabList,n=void 0===t?[]:t,r=e.currentTabId,c=void 0===r?0:r,u=e.onPress,A=void 0===u?function(){return null}:u,l=n.map((function(e){return Object(o.a)({},e,{title:e.tabName,key:e.tabId})}));return i.a.createElement("div",{className:s.a.wrapper},i.a.createElement(a.a,{tabs:l,prefixCls:"myTab",renderTabBar:function(e){return i.a.createElement(a.a.DefaultTabBar,Object.assign({},e,{page:5}))},swipeable:!0,initialPage:c,onTabClick:function(e){return A(function(e){var t=n.filter((function(t){return t.tabId===e}));return t&&t.length>0?t[0]:{tabName:"",tabId:""}}(e.key||""))}}))}},759:function(e,t,n){e.exports={tabBarWrapper:"MyTab_tabBarWrapper__19y4-",tabBarImg:"MyTab_tabBarImg__2Owdi"}},916:function(e,t,n){e.exports={content:"SceneShop_content__1timm","content-list":"SceneShop_content-list__3u-BS","content-list-banner":"SceneShop_content-list-banner__1_CEb",pageWrap:"SceneShop_pageWrap__1pvzR",contentWrap:"SceneShop_contentWrap__3Wxus",sceneItem:"SceneShop_sceneItem__17w7T",pic:"SceneShop_pic__1okiY",titleName:"SceneShop_titleName__6gucb",sceneItemWrap:"SceneShop_sceneItemWrap__2I5i2",itemName:"SceneShop_itemName__3hssF",toggle:"SceneShop_toggle__qhN-u","arrow-up":"SceneShop_arrow-up__1fuOe","arrow-down":"SceneShop_arrow-down__1ZKjF"}}}]);
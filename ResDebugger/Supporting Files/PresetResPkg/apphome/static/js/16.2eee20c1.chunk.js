(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[16,17],{1447:function(t,e,n){"use strict";n.r(e);n(518);var r=n(519),o=n.n(r),i=(n(506),n(507)),a=n.n(i),l=n(6),s=n.n(l),u=n(9),c=n(163),f=n(5),p=n.n(f),h=(n(960),n(160)),d=n.n(h),A=n(961),y=n.n(A),g=(n(966),n(602)),v=n(603),b=n(617),m=n(601),E=n.n(m),O=n(502),w=n(115),C=n(23),k=n(510),x=n(508),_=n(19),N=n(918),j=n(92),B={toolbar:{container:"#toolbar",handlers:{openAlbum:_.p}}};function I(t,e){var n=t.split("<img "),r=0;return n&&n.length>1?n.map((function(t,n){return t&&t.indexOf("src=")>-1&&t.indexOf("file://")>-1?(r+=1,/\.gif">/i.test(t)?t.replace(/src="(.+?)">/g,'src="'.concat(e[r-1],'">')):t.replace(/src="(.+?)">/g,'src="'.concat(e[r-1],"?").concat(C.o,'">'))):t})).join("<img "):t}function S(t){return t&&t.length?t.reduce((function(t,e,n){return e&&e.insert&&e.insert.image&&e.insert.image.indexOf("file://")>-1&&t.push(e.insert.image),t}),[]):[]}e.default=function(){var t=Object(f.useContext)(w.a).state.appOnlineStatus,e=Object(f.useState)(""),r=Object(c.a)(e,2),i=r[0],l=r[1],h=Object(f.useState)(""),A=Object(c.a)(h,2),m=A[0],T=A[1],P=Object(f.useState)([]),Q=Object(c.a)(P,2),q=Q[0],R=Q[1],L=Object(f.useState)(!1),M=Object(c.a)(L,2),U=M[0],D=M[1],F=Object(f.useState)(!0),K=Object(c.a)(F,2),H=K[0],Y=K[1],V=Object(f.useRef)(null),G=Object(f.useRef)(null),J=function(t){},z=function(){var t=Object(u.a)(s.a.mark((function t(e){var n,r;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!e||!e.length){t.next=7;break}return n=e.map((function(t,e){return Object(j.g)({url:"/scs/commons/v1/file/upload",filePath:t,data:{type:"1",sizeLimit:"1"},uploadId:"".concat(e),progress:J})})),t.next=5,Promise.all(n);case 5:return r=t.sent,t.abrupt("return",r);case 7:return t.abrupt("return",null);case 10:return t.prev=10,t.t0=t.catch(0),console.log(t.t0),t.abrupt("return",null);case 14:case"end":return t.stop()}}),t,null,[[0,10]])})));return function(e){return t.apply(this,arguments)}}(),Z=function(){var t=Object(u.a)(s.a.mark((function t(e){var n,r,o,i,a;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e&&e.length&&(n=V.current)&&(r=n.getSelection(),o=r?r.index:0,e.forEach((function(t){t&&t.path&&n.insertEmbed(o,"image",t.path,"user"),o+=1,n.setSelection(o)}))),/ipod|iphone|ipad/gi.test(navigator.userAgent)&&(i=Date.now(),a=function t(){var e=Math.max(document.documentElement.clientHeight,document.body.clientHeight);window.scrollTo(0,e),Date.now()-i<200&&window.requestAnimationFrame(t)},window.requestAnimationFrame(a));case 2:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),X=Object(O.b)(function(){var t=Object(u.a)(s.a.mark((function t(e){var n,r,o,i;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,e.stopPropagation(),(n=V.current)&&((r=n.getSelection())?V.current.setSelection(r.index):G&&G.current&&V.current.setSelection(G.current)),t.next=6,Object(_.w)(["storage"]);case 6:if(!((o=t.sent)&&o.retCode===C.B&&o.retData&&o.retData.length)){t.next=13;break}if(!o.retData.find((function(t){return"storage"===t.name&&t.granted}))){t.next=13;break}return t.next=11,Object(_.p)({max:9});case 11:(i=t.sent)&&i.length&&Z(i);case 13:t.next=18;break;case 15:t.prev=15,t.t0=t.catch(0),console.log(t.t0);case 18:case"end":return t.stop()}}),t,null,[[0,15]])})));return function(e){return t.apply(this,arguments)}}(),2e3),W=function(){return p.a.createElement("div",{id:"toolbar",className:E.a.toolbar},p.a.createElement("div",{className:"ql-img",onClick:X},p.a.createElement("img",{src:n(967),alt:""})))},$=function(){var t=Object(u.a)(s.a.mark((function t(e,n){var r,o,i,a;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e||!e.length){t.next=17;break}return t.next=3,z(e);case 3:if(r=t.sent,console.log("updateArticel ossPics:",r),!r||r.length!==e.length){t.next=16;break}if(!(o=Object(N.getValidUrl)(r))||o.length!==e.length){t.next=13;break}return i=o[0],a=I(n,o),t.abrupt("return",{html:a,coverUrl:i});case 13:return t.abrupt("return",{html:"",coverUrl:""});case 14:t.next=17;break;case 16:return t.abrupt("return",{html:"",coverUrl:""});case 17:return t.abrupt("return",{html:n,coverUrl:""});case 18:case"end":return t.stop()}}),t)})));return function(e,n){return t.apply(this,arguments)}}(),tt=function(){var t=Object(u.a)(s.a.mark((function t(){var e,n,r,o,a,l,u,c,f,p;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,Object(k.b)(300),e=V.current,n=e.getContents(),r=e.root.innerHTML,!(n&&n.ops&&n.ops.length)){t.next=23;break}return o=S(n.ops),t.next=9,$(o,r);case 9:if(a=t.sent,l=a.html,u=a.coverUrl,!l){t.next=22;break}return(c=l.match(/<img src="(.+?)">/))&&c.length>1&&(f=c[1].match(/https:\/\/.+?\.(jpg|png|gif|bmp|jpeg)/i))&&f.length&&(u=f[0]),t.next=17,Object(x.y)({title:m,contentId:i,content:l,coverType:u?C.j:C.i,coverUrls:[u],classCode:q.map((function(t){return t.code}))});case 17:p=t.sent,console.log("updateArticel res:",p),Object(N.publishAlert)(p),t.next=23;break;case 22:Object(N.publishAlert)(!1);case 23:Object(k.a)(),t.next=29;break;case 26:t.prev=26,t.t0=t.catch(0),Object(O.g)(t.t0);case 29:case"end":return t.stop()}}),t,null,[[0,26]])})));return function(){return t.apply(this,arguments)}}(),et=function(){var t=Object(u.a)(s.a.mark((function t(){var e,n,r,o,i,a,l,u;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,Object(k.b)(300),e=V.current,n=e.getContents(),r=e.root.innerHTML,!(n&&n.ops&&n.ops.length)){t.next=21;break}return o=S(n.ops),t.next=9,$(o,r);case 9:if(i=t.sent,a=i.html,l=i.coverUrl,!a){t.next=20;break}return t.next=15,Object(x.r)({title:m,content:a,coverType:l?C.j:C.i,coverUrls:[l],classCode:q.map((function(t){return t.code}))});case 15:u=t.sent,console.log("publish res:",u),Object(N.publishAlert)(u),t.next=21;break;case 20:Object(N.publishAlert)(!1);case 21:Object(k.a)(),t.next=27;break;case 24:t.prev=24,t.t0=t.catch(0),Object(O.g)(t.t0);case 27:case"end":return t.stop()}}),t,null,[[0,24]])})));return function(){return t.apply(this,arguments)}}(),nt=Object(O.b)((function(){try{t?i?tt():et():a.a.info(C.m,2)}catch(e){console.log(e)}}),3e3,!0),rt=function(t,e,n){"user"===n&&D(!0),t&&t.ops&&t.ops.length&&t.ops.find((function(t){return t.insert&&t.insert.image}))||V.current.getLength()>1?Y(!1):Y(!0);var r=V.current.getLength();r>1e4&&V.current.deleteText(1e4,r)},ot=function(t,e,n){!t&&e&&(G.current=e?e.index:0)},it=(!m||!m.trim())&&H&&(!q||!q.length),at=!!(m&&m.trim()&&!H&&q&&q.length&&q.find((function(t){return t&&t.name&&t.code}))),lt=Object(f.useCallback)((function(){!U||it||H?Object(_.f)():o.a.alert("","\u60a8\u7684\u5185\u5bb9\u8fd8\u672a\u53d1\u5e03\uff0c\u9000\u51fa\u540e\u5185\u5bb9\u5c06\u4e0d\u4f1a\u88ab\u4fdd\u5b58\uff0c\u662f\u5426\u9000\u51fa\uff1f",[{text:"\u53d6\u6d88",onPress:function(){return console.log("cancel")},style:{fontSize:"17px",color:"#aaa"}},{text:"\u7ee7\u7eed\u9000\u51fa",onPress:function(){return Object(_.f)()},style:{fontSize:"17px",color:"#EE4040"}}])}),[it,U,H]);return Object(f.useEffect)((function(){function t(){return(t=Object(u.a)(s.a.mark((function t(e){var n,r,o,i,a,l,u,c;return s.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(x.n)(e);case 3:n=t.sent,console.log("detail:",n),n&&n.data&&n.data.title&&n.data.content&&n.data.imageUrls&&(r=n.data,o=r.title,i=r.content,a=r.classList,T(o),l=i,(u=i.split("<img "))&&u.length>1&&(c=u.map((function(t){return t&&t.indexOf("zjrs.haier.net")>-1&&t.indexOf("oss-process")<0&&!/\.gif">/i.test(t)?t.match(/src="(.+\?.+?)">/g)?t.replace(/src="(.+?)">/g,'src="$1&'.concat(C.o,'">')):t.replace(/src="(.+?)">/g,'src="$1?'.concat(C.o,'">')):t})),l=c.join("<img ")),V.current.clipboard.dangerouslyPasteHTML(l,"api"),R(a.filter((function(t){return t&&t.name&&t.code})))),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.log("getSafeDetail err:",t.t0),Object(O.g)(t.t0);case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))).apply(this,arguments)}var e=Object(O.c)(window.location.href);if(e&&e.contentId){var n=e.contentId.replace(/\//g,"");n&&(l(n),function(e){t.apply(this,arguments)}(n))}}),[]),Object(f.useEffect)((function(){var t=document.title;return document.title="\u56fe\u6587\u6df7\u6392\u53d1\u5e03",function(){document.title=t}}),[]),Object(f.useEffect)((function(){if(/android/gi.test(navigator.userAgent))return Object(_.d)(lt,!0),function(){Object(_.d)(lt,!1)}}),[lt]),Object(f.useEffect)((function(){if(/iphone|ipod|ipad/gi.test(navigator.userAgent))return Object(_.d)((function(){}),!0),function(){Object(_.d)((function(){}),!1)}}),[]),Object(f.useEffect)((function(){return window._fcInstance&&window._fcInstance.destroy(),function(){window._fcInstance=d.a.attach(document.body)}}),[]),Object(f.useEffect)((function(){return V.current=new y.a("#editor",{theme:"snow",modules:B,placeholder:"\u6dfb\u52a0\u6b63\u6587"}),V.current.on("text-change",rt),V.current.on("selection-change",ot),function(){V.current.off("text-change",rt),V.current.off("selection-change",ot)}}),[]),Object(f.useEffect)((function(){y.a.import("formats/image").sanitize=function(t){return t}}),[]),Object(f.useEffect)((function(){if(/iphone|ipod|mac/gi.test(window.navigator.userAgent)&&window.screen.height>=812){var t=document.querySelector(".art-content");t&&t.classList.add("ix")}}),[]),p.a.createElement(p.a.Fragment,null,p.a.createElement("div",{className:E.a.main},p.a.createElement(g.a,{isModified:U,isEmpty:it&&H,canPub:U&&at&&!H,handleConfirm:nt}),p.a.createElement("div",{className:"".concat(E.a.content," art-content")},p.a.createElement(v.a,{data:m,handleChange:function(t){"string"===typeof t&&(T(t),D(!0))}}),p.a.createElement("div",{id:"editor",ref:V}),p.a.createElement(b.a,{selectedClass:q,onSelectClassification:function(t){R(t&&t.length?t.map((function(t){return{code:t.code,name:t.name}})):[]),D(!0)}},p.a.createElement(W,null)))))}},502:function(t,e,n){"use strict";n.d(e,"d",(function(){return h})),n.d(e,"g",(function(){return d})),n.d(e,"f",(function(){return A})),n.d(e,"k",(function(){return y})),n.d(e,"b",(function(){return g})),n.d(e,"c",(function(){return v})),n.d(e,"e",(function(){return b})),n.d(e,"j",(function(){return m})),n.d(e,"h",(function(){return E})),n.d(e,"i",(function(){return O})),n.d(e,"a",(function(){return w}));var r=n(6),o=n.n(r),i=(n(506),n(507)),a=n.n(i),l=n(9),s=n(19),u=n(23),c=n(510);function f(t,e,n){var r=t.split("?")[0],o="";return t.indexOf("?")>0&&(o=t.split("?")[1]),o&&(o="&"+o),t="".concat(r,"?").concat(e,"=").concat(n).concat(o)}function p(t,e,n){var r=t;return e&&r.indexOf("container_type")<0&&(r=f(r,"container_type",e)),r.indexOf("hidesBottomBarWhenPushed")<0&&(r=f(r,"hidesBottomBarWhenPushed","1")),n&&r.indexOf("needAuthLogin")<0&&(r=f(r,"needAuthLogin","1")),function(t){var e=t.match(/#.*\?/);return e&&e[0]&&(t=t.replace(/#.*\?/g,"?")+e[0].split("?")[0]),t}(r)}var h=function(){var t=Object(l.a)(o.a.mark((function t(e){var n,r;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(s.o)();case 3:if(!(n=t.sent)){t.next=9;break}r=p(e.url,e.containerType,!e.noNeedAuth),Object(s.n)(r),t.next=10;break;case 9:throw Error(n);case 10:t.next=15;break;case 12:t.prev=12,t.t0=t.catch(0),a.a.info("\u7f51\u7edc\u4e0d\u53ef\u7528",3);case 15:case"end":return t.stop()}}),t,null,[[0,12]])})));return function(e){return t.apply(this,arguments)}}();function d(t){t&&t.retData&&/\u65ad\u5f00\u4e0e\u4e92\u8054\u7f51\u7684\u8fde\u63a5|unable to resolve host/gi.test(t.retData)?Object(c.c)(u.m):t&&t.retData&&/failed to connect|\u672a\u80fd\u8fde\u63a5\u5230\u670d\u52a1\u5668|timeout|\u8d85\u65f6/gi.test(t.retData)?Object(c.c)(u.y):Object(c.c)(u.u)}function A(t){return t&&t.retData&&/\u65ad\u5f00\u4e0e\u4e92\u8054\u7f51\u7684\u8fde\u63a5|unable to resolve host/gi.test(t.retData)?(Object(c.c)(u.m),!0):!!(t&&t.retData&&/failed to connect|\u672a\u80fd\u8fde\u63a5\u5230\u670d\u52a1\u5668|timeout|\u8d85\u65f6/gi.test(t.retData))&&(Object(c.c)(u.y),!0)}function y(t,e){var n=t,r=null,o=!0;return function(){for(var t=this,i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];if(o)return n.apply(this,a),void(o=!1);r||(r=setTimeout((function(){clearTimeout(r),r=null,n.apply(t,a)}),e))}}function g(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],r=0;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];if(r&&clearTimeout(r),n){var l=!r;r=setTimeout((function(){r=null}),e),l&&t.apply(void 0,i)}else r=setTimeout((function(){t.apply(void 0,i)}),e)}}function v(t){var e={};if(-1!==t.indexOf("?")){var n=t.substr(t.indexOf("?")+1,t.length);-1!==n.indexOf("#")&&(n=n.substr(0,n.indexOf("#")));for(var r=n.split("&"),o=0;o<r.length;o++)e[r[o].split("=")[0]]=decodeURIComponent(r[o].split("=")[1])}return e}function b(t,e){t.target.src=e,console.log("~~~~~~~~~~~~~~~~~~",e)}function m(t){return Object.keys(t).sort().reduce((function(e,n){return e[n]=t[n],e}),{})}var E=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:window.location.href;return new Promise((function(e){new window.OpenInstall({appKey:"vghhgm",onready:function(){this.wakeupOrInstall()}},{targetUrl:t,type:"launchToPage"})}))};function O(t){var e=t.targetUrl,n=void 0===e?window.location.href:e,r=t.userId,o=void 0===r?"":r,i=t.fn,a=void 0===i?function(){}:i;if(/apicloud|uphybrid|uplus/i.test(window.navigator.userAgent.toLowerCase()))o?a():h({url:"apicloud://usercenter",noNeedAuth:!0});else{var l=window.location.href;l&&l.indexOf("outOrigin")<0&&E(n)}}function w(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return t&&t.indexOf("zjrs.haier.net")>-1&&t.indexOf("oss-process")<0?!n&&/\.gif$/i.test(t)?t:t.indexOf("?")>-1?t.split("?")[0]+"?"+e+"&"+t.split("?")[1]:t+"?"+e:t}},508:function(t,e,n){"use strict";n.d(e,"o",(function(){return f})),n.d(e,"d",(function(){return p})),n.d(e,"g",(function(){return h})),n.d(e,"k",(function(){return d})),n.d(e,"l",(function(){return A})),n.d(e,"t",(function(){return y})),n.d(e,"x",(function(){return g})),n.d(e,"s",(function(){return v})),n.d(e,"w",(function(){return b})),n.d(e,"r",(function(){return m})),n.d(e,"y",(function(){return E})),n.d(e,"n",(function(){return O})),n.d(e,"m",(function(){return w})),n.d(e,"p",(function(){return k})),n.d(e,"q",(function(){return _})),n.d(e,"e",(function(){return N})),n.d(e,"b",(function(){return j})),n.d(e,"f",(function(){return B})),n.d(e,"i",(function(){return I})),n.d(e,"c",(function(){return S})),n.d(e,"h",(function(){return T})),n.d(e,"j",(function(){return P})),n.d(e,"a",(function(){return Q})),n.d(e,"u",(function(){return q})),n.d(e,"v",(function(){return R}));var r=n(6),o=n.n(r),i=n(9),a=n(92),l=n(23),s=n(531),u=n.n(s),c=n(19),f=function(t,e){return Object(a.d)({url:e?"/omsappapi/special/v3/test/list":"/omsappapi/special/v3/list",data:{suuid:t}})},p=function(t){return Object(a.d)({url:"/omsappapi/ad/v1/rotation",data:{adLocation:t}})},h=function(t){var e=t.province,n=t.city;return Object(a.d)({url:"/rcs/weather/current-forecast",data:{province:e,city:n}})},d=function(t){return Object(a.b)({url:t})},A=function(){var t=Object(i.a)(o.a.mark((function t(e){return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,c.A.initDeviceReady();case 2:return window.console.log("ppppppp",e),t.abrupt("return",u()({method:"post",url:"https://haier.vhallyun.com/api/livecenter/live-center-list",data:{page:e},headers:{"Content-Type":"application/x-www-form-urlencoded"},transformRequest:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return Object.entries(t).map((function(t){return t.join("=")})).join("&")||""}}));case 4:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),y=function(t){var e=t.title,n=t.content,r=t.videoUrl,o=t.classCode,i=t.coverUrls;return Object(a.d)({url:"/scs/contents/v1/video",data:{title:e,content:n,videoUrl:r,classCode:o,coverUrls:i}})},g=function(t){var e=t.contentId,n=t.title,r=t.content,o=t.videoUrl,i=t.classCode,l=t.coverUrls;return Object(a.e)({url:"/scs/contents/v1/video",data:{contentId:e,title:n,content:r,videoUrl:o,classCode:i,coverType:3,coverUrls:l}})},v=function(t){var e=t.title,n=t.content,r=t.imageUrls,o=t.classCode,i=t.coverType,s=void 0===i?l.j:i,u=t.coverUrls;return Object(a.d)({url:"/scs/contents/v1/microPost",data:{title:e,content:n,imageUrls:r,classCode:o,coverType:s,coverUrls:u}})},b=function(t){var e=t.contentId,n=t.title,r=t.content,o=t.imageUrls,i=t.classCode,s=t.coverType,u=void 0===s?l.j:s,c=t.coverUrls;return Object(a.e)({url:"/scs/contents/v1/microPost",data:{contentId:e,title:n,content:r,imageUrls:o,classCode:i,coverType:u,coverUrls:c}})},m=function(t){var e=t.title,n=t.content,r=t.coverType,o=t.coverUrls,i=t.classCode;return Object(a.d)({url:"/scs/contents/v1/article",data:{title:e,content:n,coverType:r,coverUrls:o,classCode:i}})},E=function(t){var e=t.contentId,n=t.title,r=t.content,o=t.coverType,i=t.coverUrls,l=t.classCode;return Object(a.e)({url:"/scs/contents/v1/article",data:{contentId:e,title:n,content:r,coverType:o,coverUrls:i,classCode:l}})},O=function(t){return Object(a.d)({url:"/scs/contents/v1/safeDetail",data:{contentId:t}})},w=function(t){var e="/scs/contents/v1/detail";if(/apicloud|uphybrid|uplus/i.test(window.navigator.userAgent.toLowerCase()))return Object(a.d)({url:e,data:{contentId:t}});var n=Object(a.c)(e);return u()({method:"post",url:n,data:{contentId:t},headers:{"Content-Type":"application/json;charset=utf-8"}}).then((function(t){return t&&200===t.status&&t.data?Promise.resolve(t.data):Promise.reject(t)})).catch((function(t){return Promise.reject(t)}))},C=null,k=function(){return C?(setTimeout((function(){x()}),2e3),Promise.resolve(C)):x()},x=function(){var t=Object(i.a)(o.a.mark((function t(){var e;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(a.b)({url:"/scs/commons/v1/classes"});case 3:return(e=t.sent)&&e.retCode===l.v&&e.data&&e.data.classes&&e.data.classes.length>0&&(C=e),t.abrupt("return",e);case 8:return t.prev=8,t.t0=t.catch(0),t.abrupt("return",Promise.reject(t.t0));case 11:case"end":return t.stop()}}),t,null,[[0,8]])})));return function(){return t.apply(this,arguments)}}(),_=function(){var t=Object(i.a)(o.a.mark((function t(e){var n;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(a.d)({url:"/scs/users/v1/calsses",data:{userId:e}});case 3:return n=t.sent,t.abrupt("return",n);case 7:return t.prev=7,t.t0=t.catch(0),t.abrupt("return",Promise.reject(t.t0));case 10:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(e){return t.apply(this,arguments)}}(),N=function(t,e,n,r){return Object(a.d)({url:"/scs/contents/v1/contents",data:{index:t,count:e,classCode:n,keyWords:r}})},j=function(t){return Object(a.d)({url:"/scs/contents/v1/destroy",data:{contentId:t}})},B=function(t){var e=t.index,n=t.count,r=t.contentType;return Object(a.d)({url:"/scs/users/v1/contents",data:{index:e,count:n,contentType:r}})},I=function(t){var e=t.index,n=t.count;return Object(a.d)({url:"/scs/users/v1/favorites",data:{index:e,count:n}})},S=function(t){var e=t.index,n=t.count,r=t.userId,o=t.contentType;return Object(a.d)({url:"/scs/users/v1/author/contents",data:{index:e,count:n,userId:r,contentType:o}})},T=function(){return Object(a.b)({url:"/scs/users/v1/fans"})},P=function(){return Object(a.b)({url:"/scs/users/v1/followers"})},Q=function(t){return Object(a.d)({url:"/scs/users/v1/follow",data:{userId:t}})},q=function(t){return Object(a.b)({url:"/scs/users/v1/detail?userId=".concat(t),data:{}})},R=function(t){return Object(a.b)({url:"/scs/users/v1/author/detail?userId=".concat(t),data:{}})}},510:function(t,e,n){"use strict";n.d(e,"c",(function(){return i})),n.d(e,"b",(function(){return a})),n.d(e,"a",(function(){return l}));n(506);var r=n(507),o=n.n(r),i=function(t){o.a.info(t||"\u63d0\u793a\u5185\u5bb9",2)},a=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:30;o.a.loading("\u52a0\u8f7d\u4e2d",t)},l=function(){return setTimeout((function(){return o.a.hide()}),0)}},524:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABIBAMAAACnw650AAAAFVBMVEUAAAA2NjYzMzM1NTU0NDQ0NDQzMzNPh4ptAAAABnRSTlMAMO1cYthR96LlAAAAWElEQVRIx2MYBcMGMAsQochNkbAalrQkAcIGpaUpEjYoLS2VsEEgkwgblCQwatCoQUPJIGZiFDGYga0bNWrUqKFvVCoDEUYpEq7JQG4ibJQicZXiKBh4AACF/kiRuQZwhQAAAABJRU5ErkJggg=="},526:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAASKADAAQAAAABAAAASAAAAACQMUbvAAABVUlEQVR4Ae3aQUoDQRCF4UFyGjdZeJhsQhaeOlcQxAMI2inFdubNPB/jMvTfUHRXVTIwH9WbkGliIYAAAggggAACCCCAAAIIDCvQWrtUHIcFSC9eMM8VnxWvIK2kFjh1/F4gdaPi6JPzY/O7gRRwutK1Dg8dc6h9B864EwROuAvggBMEQovJAScIhBaTA04QCC0mB5wgEFpMDjhBILSYHHCCQGgxOeAEgdBicsAJAqHF5ICzEfjPj+Lv9e22ecJc+HrWYU4HPNUVO1d8VPy13qrxNCDN/MoFANLM4U8geRepgiQcPgHJu0gVJOHwCUjeRaogCYdPQPIuUgVJOHwCkneRKkjC4ROQvItUQRIOn4DkXaQKknD4BCTvIlWQhMMnIHkXqe5AGveP5F0qIL1U77F/bujdIIGznogFEjhrnJ4X0olr1TXYEUAAAQQQQAABBBC4d4EbAZy47u7W9DkAAAAASUVORK5CYII="},530:function(t,e,n){"use strict";n(521);var r=n(522),o=n.n(r),i=n(5),a=n.n(i),l=n(532),s=n.n(l),u=n(19);e.a=function(t){return a.a.createElement(o.a,{className:"".concat(s.a["my-nav-bar"]," ").concat(t.mode&&"dark"===t.mode?s.a.dark:""),mode:"light",leftContent:t.leftContent||[a.a.createElement("span",{key:"left-icon",className:s.a["nav-bar-span"],role:"button",tabIndex:0,onClick:function(){"function"===typeof t.onClose?t.onClose():Object(u.f)()}},a.a.createElement("img",{className:s.a["nav-bar-icon"],src:t&&"dark"===t.mode?n(526):n(524),alt:""}))],rightContent:t.rightContent},t.title)}},532:function(t,e,n){t.exports={"my-nav-bar":"MyNavBar_my-nav-bar__B7cJG",dark:"MyNavBar_dark__11_W2","nav-bar-span":"MyNavBar_nav-bar-span__1_CkP","nav-bar-icon":"MyNavBar_nav-bar-icon__3egr0"}},601:function(t,e,n){t.exports={main:"TeletextPublish_main__10AnR",content:"TeletextPublish_content__3ptW1",hidden:"TeletextPublish_hidden__8RuZv",defaultVideo:"TeletextPublish_defaultVideo__28Q_P",defaultVideoPng:"TeletextPublish_defaultVideoPng__1cMRf",toolbar:"TeletextPublish_toolbar__cUZ5Q"}},602:function(t,e,n){"use strict";n(518);var r=n(519),o=n.n(r),i=n(5),a=n.n(i),l=n(19),s=n(624),u=n.n(s);e.a=function(t){var e=t.canPub,n=t.isModified,r=t.isEmpty,i=t.handleConfirm,s=t.onCancel,c=function(){setTimeout((function(){"function"===typeof s?s():Object(l.f)()}),300)};return a.a.createElement("div",{className:e?"".concat(u.a.content):"".concat(u.a.content," ").concat(u.a.disabled),onClick:function(t){t.target instanceof HTMLElement&&t.target.dataset&&t.target.dataset.event&&("cancel"===t.target.dataset.event?(console.log("isModified, isEmpty",n,r),n&&!r?o.a.alert("","\u60a8\u7684\u5185\u5bb9\u8fd8\u672a\u53d1\u5e03\uff0c\u9000\u51fa\u540e\u5185\u5bb9\u5c06\u4e0d\u4f1a\u88ab\u4fdd\u5b58\uff0c\u662f\u5426\u9000\u51fa\uff1f",[{text:"\u53d6\u6d88",onPress:function(){return console.log("cancel")},style:{fontSize:"17px",color:"#aaa"}},{text:"\u7ee7\u7eed\u9000\u51fa",onPress:function(){return c()},style:{fontSize:"17px",color:"#EE4040"}}]):c()):"publish"===t.target.dataset.event&&i())}},a.a.createElement("span",{"data-event":"cancel"},"\u53d6\u6d88"),a.a.createElement("span",{"data-event":"publish",className:u.a.pub},"\u53d1\u5e03"))}},603:function(t,e,n){"use strict";var r=n(5),o=n.n(r),i=n(625),a=n.n(i);e.a=function(t){var e=t.data,n=t.handleChange;return o.a.createElement("input",{className:a.a.title,placeholder:"\u8bf7\u8f93\u5165\u6807\u9898",value:e,maxLength:20,onChange:function(t){t.target instanceof HTMLInputElement&&n(t.target.value.slice(0,20))}})}},617:function(t,e,n){"use strict";var r=n(6),o=n.n(r),i=n(9),a=n(5),l=n.n(a),s=(n(521),n(522)),u=n.n(s),c=n(536),f=n(163),p=(n(518),n(519)),h=n.n(p),d=n(114),A=n(628),y=n.n(A),g=n(508),v=n(23),b=n(502),m=n(629);function E(t){var e=document.createElement("div");function n(){d.unmountComponentAtNode(e),e&&e.parentNode&&e.parentNode.removeChild(e)}return document.body.appendChild(e),new Promise((function(r,o){d.render(l.a.createElement(h.a,{visible:!0,popup:!0,animationType:"slide-up",transparent:!1,transitionName:"am-zoom",closable:!1,maskClosable:!1,maskTransitionName:"am-fade",className:"am-modal-privacy"},l.a.createElement(O,{defaultLabelList:t,onOk:function(t){r(t),n()},onCancel:function(t){o(t),n()}})),e)}))}function O(t){var e=Object(a.useState)([]),n=Object(f.a)(e,2),r=n[0],o=n[1],i=Object(a.useState)([]),s=Object(f.a)(i,2),p=s[0],h=s[1],d=Object(a.useState)({name:"",code:"",classesTwo:[]}),A=Object(f.a)(d,2),E=A[0],O=A[1],w=Object(a.useState)({name:"",code:""}),C=Object(f.a)(w,2),k=C[0],x=C[1];Object(a.useEffect)((function(){t&&t.defaultLabelList&&t.defaultLabelList.length&&h(t.defaultLabelList),Object(g.p)().then((function(t){t&&t.retCode===v.v&&t.data&&t.data.classes&&t.data.classes.length>0&&(O(t.data.classes[0]),o(t.data.classes.reduce((function(t,e){return e&&e.code&&e.name&&e.classesTwo&&e.classesTwo.length?[].concat(Object(c.a)(t),[{code:e.code,name:e.name,classesTwo:e.classesTwo}]):t}),[])))})).catch((function(t){console.log(t),Object(b.g)(t)}))}),[t]);var _=Object(a.useCallback)((function(){"function"===typeof t.onCancel&&t.onCancel(p)}),[t,p]),N=Object(a.useCallback)((function(){"function"===typeof t.onOk&&t.onOk(p)}),[t,p]),j=Object(a.useCallback)((function(t){p&&p.length>0&&h(p.filter((function(e){return e.code!==t.code})))}),[p]),B=Object(a.useCallback)((function(t){O(t)}),[]),I=Object(a.useCallback)((function(t){h((function(e){return!function(t,e){return!(!e||!e.length)&&e.filter((function(e){return e&&e.code===t.code})).length>0}(t,e)&&e.length<3?[].concat(Object(c.a)(e),[t]):e})),x(t)}),[]);return l.a.createElement(l.a.Fragment,null,l.a.createElement(u.a,{mode:"light",leftContent:l.a.createElement("span",{style:{color:"#AAAAAA"},onClick:_},"\u53d6\u6d88"),rightContent:l.a.createElement("span",{onClick:N},"\u5b8c\u6210")},"\u9009\u62e9\u5206\u7c7b"),l.a.createElement("div",{className:y.a["selected-label-wraper"]},p&&p.map((function(t,e){return t&&l.a.createElement("div",{key:e,className:y.a["selected-label"]},l.a.createElement("span",null,t.name),l.a.createElement("img",{onClick:function(){return j(t)},className:y.a["selected-label-img"],src:m,alt:""}))}))),l.a.createElement("div",{className:y.a["label-list-wraper"]},l.a.createElement("div",{className:y.a["label-list-left"]},r&&r.map((function(t,e){return t&&l.a.createElement("div",{key:e,className:"".concat(y.a["label-list-item"]," ").concat(String(t.code)===String(E.code)?"active":""),onClick:function(){return B(t)}},t.name)}))),l.a.createElement("div",{className:y.a["label-list-right"]},E&&E.classesTwo&&E.classesTwo.map((function(t,e){return t&&l.a.createElement("div",{key:e,className:"".concat(y.a["label-list-item"]," ").concat(String(t.code)===String(k.code)?"active":""),onClick:function(){return I(t)}},t.name)})))))}var w=n(630),C=n.n(w),k=n(631),x=n.n(k),_=function(t){return l.a.createElement("span",{"data-value":t.code},t.name)},N=function(t){var e=t.list;return e&&e.length?l.a.createElement("div",{className:x.a.tags},e.map((function(t){return t&&l.a.createElement(_,{key:t&&t.code,name:t&&t.name,code:t&&t.code})}))):null};e.a=function(t){var e=t.selectedClass,r=t.onSelectClassification,a=t.children,s=function(){var t=Object(i.a)(o.a.mark((function t(){var n;return o.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,E(e);case 3:n=t.sent,r(n),t.next=10;break;case 7:t.prev=7,t.t0=t.catch(0),console.log(t.t0);case 10:case"end":return t.stop()}}),t,null,[[0,7]])})));return function(){return t.apply(this,arguments)}}();return l.a.createElement("div",{className:"".concat(C.a.content)},l.a.createElement("div",{onClick:function(){return s()},className:C.a.left},l.a.createElement("img",{className:C.a.icon,src:n(632),alt:"\u9009\u62e9\u5206\u7c7b"}),e&&e.length&&e.find((function(t){return t&&t.code&&t.name}))?l.a.createElement(N,{list:e}):l.a.createElement("span",null,"\u9009\u62e9\u5206\u7c7b")),a)}},618:function(t,e,n){"use strict";n(514),n(619)},619:function(t,e,n){},620:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=i(n(621)),o=i(n(622));function i(t){return t&&t.__esModule?t:{default:t}}r.default.Item=o.default,e.default=r.default,t.exports=e.default},621:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=f(n(46)),o=f(n(512)),i=f(n(0)),a=f(n(1)),l=f(n(2)),s=f(n(3)),u=f(n(504)),c=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}(n(5));function f(t){return t&&t.__esModule?t:{default:t}}var p=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&(n[r[o]]=t[r[o]])}return n},h=function(t){function e(){return(0,i.default)(this,e),(0,l.default)(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return(0,s.default)(e,t),(0,a.default)(e,[{key:"render",value:function(){var t,e=this.props,n=e.direction,i=e.wrap,a=e.justify,l=e.align,s=e.alignContent,f=e.className,h=e.children,d=e.prefixCls,A=e.style,y=p(e,["direction","wrap","justify","align","alignContent","className","children","prefixCls","style"]),g=(0,u.default)(d,f,(t={},(0,o.default)(t,d+"-dir-row","row"===n),(0,o.default)(t,d+"-dir-row-reverse","row-reverse"===n),(0,o.default)(t,d+"-dir-column","column"===n),(0,o.default)(t,d+"-dir-column-reverse","column-reverse"===n),(0,o.default)(t,d+"-nowrap","nowrap"===i),(0,o.default)(t,d+"-wrap","wrap"===i),(0,o.default)(t,d+"-wrap-reverse","wrap-reverse"===i),(0,o.default)(t,d+"-justify-start","start"===a),(0,o.default)(t,d+"-justify-end","end"===a),(0,o.default)(t,d+"-justify-center","center"===a),(0,o.default)(t,d+"-justify-between","between"===a),(0,o.default)(t,d+"-justify-around","around"===a),(0,o.default)(t,d+"-align-start","start"===l),(0,o.default)(t,d+"-align-center","center"===l),(0,o.default)(t,d+"-align-end","end"===l),(0,o.default)(t,d+"-align-baseline","baseline"===l),(0,o.default)(t,d+"-align-stretch","stretch"===l),(0,o.default)(t,d+"-align-content-start","start"===s),(0,o.default)(t,d+"-align-content-end","end"===s),(0,o.default)(t,d+"-align-content-center","center"===s),(0,o.default)(t,d+"-align-content-between","between"===s),(0,o.default)(t,d+"-align-content-around","around"===s),(0,o.default)(t,d+"-align-content-stretch","stretch"===s),t));return c.createElement("div",(0,r.default)({className:g,style:A},y),h)}}]),e}(c.Component);e.default=h,h.defaultProps={prefixCls:"am-flexbox",align:"center"},t.exports=e.default},622:function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=c(n(46)),o=c(n(0)),i=c(n(1)),a=c(n(2)),l=c(n(3)),s=c(n(504)),u=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e.default=t,e}(n(5));function c(t){return t&&t.__esModule?t:{default:t}}var f=function(t,e){var n={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(t);o<r.length;o++)e.indexOf(r[o])<0&&(n[r[o]]=t[r[o]])}return n},p=function(t){function e(){return(0,o.default)(this,e),(0,a.default)(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return(0,l.default)(e,t),(0,i.default)(e,[{key:"render",value:function(){var t=this.props,e=t.children,n=t.className,o=t.prefixCls,i=t.style,a=f(t,["children","className","prefixCls","style"]),l=(0,s.default)(o+"-item",n);return u.createElement("div",(0,r.default)({className:l,style:i},a),e)}}]),e}(u.Component);e.default=p,p.defaultProps={prefixCls:"am-flexbox"},t.exports=e.default},624:function(t,e,n){t.exports={content:"Nav_content__1fd2i",pub:"Nav_pub__1Bl0c",disabled:"Nav_disabled__KW6w9"}},625:function(t,e,n){t.exports={title:"Title_title__6fqSc"}},626:function(t,e,n){"use strict";var r=n(5),o=n.n(r),i=n(627),a=n.n(i);e.a=function(t){var e=t.data,n=t.handleChange;return o.a.createElement("textarea",{className:"".concat(a.a.content," no-scroll-bar"),placeholder:"\u6dfb\u52a0\u6b63\u6587",value:e,maxLength:1e3,onChange:function(t){t.target instanceof HTMLTextAreaElement&&n(t.target.value.slice(0,1e3))}})}},627:function(t,e,n){t.exports={content:"Content_content__3aF6R"}},628:function(t,e,n){t.exports={"selected-label-wraper":"SelectLabel_selected-label-wraper__8YaMF","selected-label":"SelectLabel_selected-label__FhBXH","selected-label-img":"SelectLabel_selected-label-img__3eDfz","label-list-wraper":"SelectLabel_label-list-wraper__DqJZC","label-list-left":"SelectLabel_label-list-left__3sLK-","label-list-right":"SelectLabel_label-list-right__24teW","label-list-item":"SelectLabel_label-list-item__2jlCB"}},629:function(t,e){t.exports="data:image/png;base64,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"},630:function(t,e,n){t.exports={content:"Footer_content__AAhlw",left:"Footer_left__1n49o",icon:"Footer_icon__3yj9l"}},631:function(t,e,n){t.exports={tags:"Tags_tags__3UKAg"}},632:function(t,e){t.exports="data:image/png;base64,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"},716:function(t,e,n){t.exports={item:"MyGrid_item__1vKwY"}},717:function(t,e){t.exports="data:image/png;base64,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"},718:function(t,e){t.exports="data:image/png;base64,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"},719:function(t,e,n){t.exports={content:"UGCPhotoPreview_content__3jr02",del:"UGCPhotoPreview_del__305Pe",pic:"UGCPhotoPreview_pic__2P_HV",carousel:"UGCPhotoPreview_carousel__3Qb3x"}},720:function(t,e){t.exports="data:image/png;base64,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"},721:function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAEgAAABICAYAAABV7bNHAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAASKADAAQAAAABAAAASAAAAACQMUbvAAACjElEQVR4Ae2bz0rEMBDG1QWfy4NvpCh415M38W2E9QHWk3dfQsE/IOs3uhOyJcu3TTN1q9/AkDTTTCa/TtpSmr09iQiIwNQJLJfLGfQMuoC+Q2vF+poP8zWbOpfv+G0i0Dm0tczhcPqQMAm72lFyNvksAhlbEi4XqBzWTsr6Qs2Hy6LW1870w0zye041HJ8Q/Bkkl3dvjyr3oxy7X5uJ1/chXh9SRvjcFM/BJoPafwiEAsKVzpfUR0PoyVdnjIZDBANaBX6aRfyY1YdWc1+n0ZBSsBjoCprfVHHYTJo9khFR1KvDK3yfJCDdCozPzXCsO5rjsNlLnfmCms8Iecq5dO9BNzCm9Z2fWFE3Pw/Qc+gxHmCfFT6KXVa+jle+bYxWMb/B121xUDWKgAiIgAiIwN8ngBePS6i9L11OZbajxozB/E07/FNDqwswJObenx8wWPPPF61AbPIzJObum/SmMZq0I9Dq5Tmkb5Pgt3ViV8Nl2z5+HvpVL8+BfT3klP0eEytHzSAE49+HvGTx5Xbv42VuC6uPDShsIlGOBYiQFSABIgSIWRkkQIQAMSuDBIgQIGZlkAARAsSsDBIgQoCYlUECRAgQszJIgAgBYlYGCRAhQMzKIAEiBIhZGSRAhAAxK4MEiBAgZmWQABECxKwMEiBCgJiVQQJECBCzMmjHAPmeCi9JeGtm7+PlmjHqYOwMusZEXqBW9pUhffuOlc7XT5wJRbkydgaVo9jhVgEiF0eAAgClpwj+rR31j1Myl6K5E2OKvXhyobEmg35nx3EheNa0ghO187o8PAaN2nEM1+HSe+d1zWPedi/fQY/KCHe29R6R9d5c3HuJBe44jiBr95yQndcRwcqnCPxDAl9gfVZnPjhvMQAAAABJRU5ErkJggg=="},918:function(t,e,n){"use strict";n.r(e);n(506);var r=n(507),o=n.n(r),i=n(6),a=n.n(i),l=n(9),s=n(163),u=n(536),c=(n(518),n(519)),f=n.n(c),p=n(5),h=n.n(p),d=n(114),A=n.n(d),y=(n(618),n(620)),g=n.n(y),v=n(19),b=n(716),m=n.n(b),E=n(23),O=n(502),w=function(t){var e=t.data,r=t.handleGetPhotos,o=t.previewPhoto,i=function(t){var e=t.data,r=t.index;return h.a.createElement(h.a.Fragment,null,h.a.createElement("div",{id:"img-pub-".concat(e),className:m.a.item,onClick:function(){return o(r)}},h.a.createElement("img",{src:Object(O.a)(e,E.p),alt:"",onError:function(t){return Object(O.e)(t,n(717))}})))},s=Object(O.b)(Object(l.a)(a.a.mark((function t(){var n,o;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(v.w)(["storage"]);case 3:if(!((n=t.sent)&&n.retCode===E.B&&n.retData&&n.retData.length)){t.next=10;break}if(!n.retData.find((function(t){return"storage"===t.name&&t.granted}))){t.next=10;break}return t.next=8,Object(v.p)({max:9-e.length});case 8:(o=t.sent)&&o.length&&r(o);case 10:t.next=15;break;case 12:t.prev=12,t.t0=t.catch(0),console.log(t.t0);case 15:case"end":return t.stop()}}),t,null,[[0,12]])}))),2e3);return h.a.createElement(h.a.Fragment,null,h.a.createElement(g.a,{wrap:"wrap"},e&&e.length?e.map((function(t,e){return t&&h.a.createElement(i,{key:t+e,data:t,index:e})})):null,e&&e.length<9&&h.a.createElement("img",{className:m.a.item,src:n(718),alt:"\u6dfb\u52a0\u56fe\u7247",onClick:function(){return s()}})))},C=n(602),k=n(603),x=n(626),_=(n(583),n(584)),N=n.n(_),j=n(719),B=n.n(j),I=n(530),S=function(t){var e=t.imgUrls,r=t.selectedIndex,i=void 0===r?0:r,a=t.onDel,l=t.onClose,u=Object(p.useState)(i),c=Object(s.a)(u,2),f=c[0],d=c[1],A=Object(p.useState)(e),y=Object(s.a)(A,2),g=y[0],v=y[1],b=Object(O.k)((function(){g&&1===g.length?o.a.info("\u81f3\u5c11\u53d1\u5e03\u4e00\u5f20\u56fe\u7247\u54e6~"):(v((function(t){return t.filter((function(t,e){return e!==f}))})),a(f),f+1===g.length&&d(0))}),300),m=function(t){t.target.classList.add("error"),Object(O.e)(t,n(720))};return h.a.createElement("div",{className:"".concat(B.a.content)},h.a.createElement(I.a,{title:"".concat(f+1,"/").concat(g.length),mode:"dark",onClose:function(){return l(g)},rightContent:[h.a.createElement("img",{key:"rightcontent",className:B.a.del,src:n(721),alt:"\u5220\u9664",onClick:b})]}),g&&g.length&&h.a.createElement(N.a,{className:B.a.carousel,selectedIndex:f,dots:!1,autoplay:!1,infinite:!0,swipeSpeed:30,afterChange:function(t){return d(t)}},g.map((function(t,e){return h.a.createElement("img",{key:t+e,src:Object(O.a)(t,E.o,!1),alt:"",style:{width:"auto",verticalAlign:"top",height:"auto"},onError:m,onLoad:function(){window.dispatchEvent(new Event("resize"))}})}))))},T=n(617),P=n(508),Q=n(601),q=n.n(Q),R=n(115),L=n(92),M=n(510);function U(t){t&&t.retCode===E.v?f.a.alert("","\u5185\u5bb9\u53d1\u5e03\u6210\u529f\uff0c\u6b63\u5728\u5ba1\u6838\u4e2d...",[{text:"\u597d\u7684",onPress:function(){return Object(v.f)()},style:{fontSize:"17px",color:"#2283E2"}}]):f.a.alert("","\u5185\u5bb9\u53d1\u5e03\u5931\u8d25\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5...",[{text:"\u597d\u7684",onPress:function(){return console.log("ok")},style:{fontSize:"17px",color:"#2283E2"}}])}function D(t){return t&&t.length?t.reduce((function(t,e){return e.retData&&e.retData.data&&e.retData.data.data&&e.retData.data.data.url?[].concat(Object(u.a)(t),[e.retData.data.data.url]):t}),[]):[]}n.d(e,"publishAlert",(function(){return U})),n.d(e,"getValidUrl",(function(){return D}));e.default=function(){var t=Object(p.useContext)(R.a).state.appOnlineStatus,e=Object(p.useState)(""),n=Object(s.a)(e,2),r=n[0],i=n[1],c=Object(p.useState)(""),d=Object(s.a)(c,2),y=d[0],g=d[1],b=Object(p.useState)(""),m=Object(s.a)(b,2),_=m[0],N=m[1],j=Object(p.useState)([]),B=Object(s.a)(j,2),I=B[0],Q=B[1],F=Object(p.useState)([]),K=Object(s.a)(F,2),H=K[0],Y=K[1],V=Object(p.useState)(!1),G=Object(s.a)(V,2),J=G[0],z=G[1],Z=Object(p.useState)(!1),X=Object(s.a)(Z,2),W=X[0],$=X[1];Object(p.useEffect)((function(){function t(){return(t=Object(l.a)(a.a.mark((function t(e){var n,r,o,i,l,s;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(P.n)(e);case 3:n=t.sent,console.log("detail:",n),n&&n.data&&n.data.title&&n.data.content&&n.data.imageUrls&&(r=n.data,o=r.title,i=r.content,l=r.imageUrls,s=r.classList,g(o),N(i),Q(l.filter((function(t){return t}))),Y(s.filter((function(t){return t&&t.name&&t.code})))),t.next=12;break;case 8:t.prev=8,t.t0=t.catch(0),console.log("getSafeDetail err:",t.t0),Object(O.g)(t.t0);case 12:case"end":return t.stop()}}),t,null,[[0,8]])})))).apply(this,arguments)}var e=Object(O.c)(window.location.href);if(e&&e.contentId){var n=e.contentId.replace(/\//g,"");n&&(i(n),function(e){t.apply(this,arguments)}(n))}}),[]);var tt=Object(p.useCallback)((function(){console.log("click back!!!");var t=document.querySelector("#preview-wrap");if(t)A.a.unmountComponentAtNode(t),t&&t.parentNode&&t.parentNode.removeChild(t),z(!1);else{var e=(!y||!y.trim())&&(!_||!_.trim())&&(!I||!I.length)&&(!H||!H.length);W&&!e?f.a.alert("","\u60a8\u7684\u5185\u5bb9\u8fd8\u672a\u53d1\u5e03\uff0c\u9000\u51fa\u540e\u5185\u5bb9\u5c06\u4e0d\u4f1a\u88ab\u4fdd\u5b58\uff0c\u662f\u5426\u9000\u51fa\uff1f",[{text:"\u53d6\u6d88",onPress:function(){return console.log("cancel")},style:{fontSize:"17px",color:"#aaa"}},{text:"\u7ee7\u7eed\u9000\u51fa",onPress:function(){return Object(v.f)()},style:{fontSize:"17px",color:"#EE4040"}}]):Object(v.f)()}}),[y,_,I,H,W]);Object(p.useEffect)((function(){if(/android/gi.test(navigator.userAgent))return Object(v.d)(tt,!0),function(){Object(v.d)(tt,!1)}}),[tt]),Object(p.useEffect)((function(){if(/iphone|ipod|ipad/gi.test(navigator.userAgent))return Object(v.d)((function(){}),!0),function(){Object(v.d)((function(){}),!1)}}),[]);var et=function(t){Q((function(e){return e.filter((function(e,n){return n!==t}))})),$(!0)},nt=Object(O.b)((function(){try{console.log("click handlePub"),t?r?it():at():o.a.info(E.m,2)}catch(e){console.log(e)}}),3e3,!0),rt=function(t){},ot=function(){var t=Object(l.a)(a.a.mark((function t(e){var n,r;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,!e||!e.length){t.next=8;break}return n=e.map((function(t,e){return Object(L.g)({url:"/scs/commons/v1/file/upload",filePath:t,data:{type:"1",sizeLimit:"1"},uploadId:"".concat(e),progress:rt})})),console.log("promises:",n),t.next=6,Promise.all(n);case 6:return r=t.sent,t.abrupt("return",r);case 8:return t.abrupt("return",null);case 11:return t.prev=11,t.t0=t.catch(0),console.log(t.t0),t.abrupt("return",null);case 15:case"end":return t.stop()}}),t,null,[[0,11]])})));return function(e){return t.apply(this,arguments)}}(),it=function(){var t=Object(l.a)(a.a.mark((function t(){var e,n,o,i;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(t.prev=0,Object(M.b)(300),e=I.filter((function(t){return t.indexOf("file://")>-1})),n=null,o=[],!e||!e.length){t.next=13;break}return t.next=8,ot(e);case 8:n=t.sent,console.log("updateTeletext ossPics:",n),n&&n.length===e.length&&(o=D(n))&&o.length===e.length&&(o=[].concat(Object(u.a)(I.filter((function(t){return-1===t.indexOf("file://")}))),Object(u.a)(o))),t.next=14;break;case 13:o=I;case 14:if(!o||o.length!==I.length){t.next=22;break}return t.next=17,Object(P.w)({contentId:r,title:y,content:_,imageUrls:o,coverType:E.j,coverUrls:[o[0]],classCode:H.map((function(t){return t.code}))});case 17:i=t.sent,console.log("publish res:",i),U(i),t.next=23;break;case 22:U(!1);case 23:Object(M.a)(),t.next=29;break;case 26:t.prev=26,t.t0=t.catch(0),Object(O.g)(t.t0);case 29:case"end":return t.stop()}}),t,null,[[0,26]])})));return function(){return t.apply(this,arguments)}}(),at=function(){var t=Object(l.a)(a.a.mark((function t(){var e,n,r;return a.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,Object(M.b)(300),t.next=4,ot(Object(u.a)(I));case 4:if(e=t.sent,console.log("publish ossPics:",e),!e||!e.length){t.next=19;break}if(!(n=D(e))||n.length!==e.length){t.next=16;break}return t.next=11,Object(P.s)({title:y,content:_,imageUrls:n,coverType:E.j,coverUrls:[n[0]],classCode:H.map((function(t){return t.code}))});case 11:r=t.sent,console.log("publish res:",r),U(r),t.next=17;break;case 16:U(!1);case 17:t.next=20;break;case 19:U(!1);case 20:Object(M.a)(),t.next=26;break;case 23:t.prev=23,t.t0=t.catch(0),Object(O.g)(t.t0);case 26:case"end":return t.stop()}}),t,null,[[0,23]])})));return function(){return t.apply(this,arguments)}}();return Object(p.useEffect)((function(){var t=document.title;return document.title="\u56fe\u6587\u53d1\u5e03",function(){document.title=t}}),[]),h.a.createElement(h.a.Fragment,null,h.a.createElement("div",{className:J?"".concat(q.a.hidden," ").concat(q.a.main):"".concat(q.a.main)},h.a.createElement(C.a,{isModified:W,isEmpty:(!y||!y.trim())&&(!_||!_.trim())&&(!I||!I.length)&&(!H||!H.length),canPub:W&&!!(y&&y.trim()&&_&&_.trim()&&I&&I.length&&H&&H.length&&H.find((function(t){return t&&t.name&&t.code}))),handleConfirm:nt}),h.a.createElement("div",{className:q.a.content},h.a.createElement(k.a,{data:y,handleChange:function(t){"string"===typeof t&&(g(t),$(!0))}}),h.a.createElement(x.a,{data:_,handleChange:function(t){"string"===typeof t&&(N(t),$(!0))}}),h.a.createElement(w,{data:I,handleGetPhotos:function(t){if(t&&t.length){var e=t.map((function(t){return t.path}));e&&e.length&&(Q((function(t){return[].concat(Object(u.a)(t),Object(u.a)(e))})),$(!0))}},previewPhoto:function(t){z(!0),function(t){var e=t.imgUrls,n=t.selectedIndex,r=void 0===n?0:n,o=t.onClose,i=t.onDel,a=document.createElement("div");a.id="preview-wrap";var l=document.getElementById("root");l&&l.appendChild(a),A.a.render(h.a.createElement(S,{imgUrls:e,selectedIndex:r,onDel:i,onClose:function(){A.a.unmountComponentAtNode(a),a&&a.parentNode&&a.parentNode.removeChild(a),o()}}),a)}({selectedIndex:t,imgUrls:I,onDel:et,onClose:function(){return z(!1)}})}}),h.a.createElement(T.a,{selectedClass:H,onSelectClassification:function(t){Y(t&&t.length?t.map((function(t){return{code:t.code,name:t.name}})):[]),$(!0)}}))))}},960:function(t,e,n){n(676).polyfill()},961:function(t,e,n){(function(e){var n;"undefined"!==typeof self&&self,n=function(){return function(t){var e={};function n(r){if(e[r])return e[r].exports;var o=e[r]={i:r,l:!1,exports:{}};return t[r].call(o.exports,o,o.exports,n),o.l=!0,o.exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{configurable:!1,enumerable:!0,get:r})},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=109)}([function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(17),o=n(18),i=n(19),a=n(45),l=n(46),s=n(47),u=n(48),c=n(49),f=n(12),p=n(32),h=n(33),d=n(31),A=n(1),y={Scope:A.Scope,create:A.create,find:A.find,query:A.query,register:A.register,Container:r.default,Format:o.default,Leaf:i.default,Embed:u.default,Scroll:a.default,Block:s.default,Inline:l.default,Text:c.default,Attributor:{Attribute:f.default,Class:p.default,Style:h.default,Store:d.default}};e.default=y},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(e){var n=this;return e="[Parchment] "+e,(n=t.call(this,e)||this).message=e,n.name=n.constructor.name,n}return r(e,t),e}(Error);e.ParchmentError=o;var i,a={},l={},s={},u={};function c(t,e){var n;if(void 0===e&&(e=i.ANY),"string"===typeof t)n=u[t]||a[t];else if(t instanceof Text||t.nodeType===Node.TEXT_NODE)n=u.text;else if("number"===typeof t)t&i.LEVEL&i.BLOCK?n=u.block:t&i.LEVEL&i.INLINE&&(n=u.inline);else if(t instanceof HTMLElement){var r=(t.getAttribute("class")||"").split(/\s+/);for(var o in r)if(n=l[r[o]])break;n=n||s[t.tagName]}return null==n?null:e&i.LEVEL&n.scope&&e&i.TYPE&n.scope?n:null}e.DATA_KEY="__blot",function(t){t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY"}(i=e.Scope||(e.Scope={})),e.create=function(t,e){var n=c(t);if(null==n)throw new o("Unable to create "+t+" blot");var r=n,i=t instanceof Node||t.nodeType===Node.TEXT_NODE?t:r.create(e);return new r(i,e)},e.find=function t(n,r){return void 0===r&&(r=!1),null==n?null:null!=n[e.DATA_KEY]?n[e.DATA_KEY].blot:r?t(n.parentNode,r):null},e.query=c,e.register=function t(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(e.length>1)return e.map((function(e){return t(e)}));var r=e[0];if("string"!==typeof r.blotName&&"string"!==typeof r.attrName)throw new o("Invalid definition");if("abstract"===r.blotName)throw new o("Cannot register abstract class");if(u[r.blotName||r.attrName]=r,"string"===typeof r.keyName)a[r.keyName]=r;else if(null!=r.className&&(l[r.className]=r),null!=r.tagName){Array.isArray(r.tagName)?r.tagName=r.tagName.map((function(t){return t.toUpperCase()})):r.tagName=r.tagName.toUpperCase();var i=Array.isArray(r.tagName)?r.tagName:[r.tagName];i.forEach((function(t){null!=s[t]&&null!=r.className||(s[t]=r)}))}return r}},function(t,e,n){var r=n(51),o=n(11),i=n(3),a=n(20),l=String.fromCharCode(0),s=function(t){Array.isArray(t)?this.ops=t:null!=t&&Array.isArray(t.ops)?this.ops=t.ops:this.ops=[]};s.prototype.insert=function(t,e){var n={};return 0===t.length?this:(n.insert=t,null!=e&&"object"===typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n))},s.prototype.delete=function(t){return t<=0?this:this.push({delete:t})},s.prototype.retain=function(t,e){if(t<=0)return this;var n={retain:t};return null!=e&&"object"===typeof e&&Object.keys(e).length>0&&(n.attributes=e),this.push(n)},s.prototype.push=function(t){var e=this.ops.length,n=this.ops[e-1];if(t=i(!0,{},t),"object"===typeof n){if("number"===typeof t.delete&&"number"===typeof n.delete)return this.ops[e-1]={delete:n.delete+t.delete},this;if("number"===typeof n.delete&&null!=t.insert&&(e-=1,"object"!==typeof(n=this.ops[e-1])))return this.ops.unshift(t),this;if(o(t.attributes,n.attributes)){if("string"===typeof t.insert&&"string"===typeof n.insert)return this.ops[e-1]={insert:n.insert+t.insert},"object"===typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"===typeof t.retain&&"number"===typeof n.retain)return this.ops[e-1]={retain:n.retain+t.retain},"object"===typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this},s.prototype.chop=function(){var t=this.ops[this.ops.length-1];return t&&t.retain&&!t.attributes&&this.ops.pop(),this},s.prototype.filter=function(t){return this.ops.filter(t)},s.prototype.forEach=function(t){this.ops.forEach(t)},s.prototype.map=function(t){return this.ops.map(t)},s.prototype.partition=function(t){var e=[],n=[];return this.forEach((function(r){(t(r)?e:n).push(r)})),[e,n]},s.prototype.reduce=function(t,e){return this.ops.reduce(t,e)},s.prototype.changeLength=function(){return this.reduce((function(t,e){return e.insert?t+a.length(e):e.delete?t-e.delete:t}),0)},s.prototype.length=function(){return this.reduce((function(t,e){return t+a.length(e)}),0)},s.prototype.slice=function(t,e){t=t||0,"number"!==typeof e&&(e=1/0);for(var n=[],r=a.iterator(this.ops),o=0;o<e&&r.hasNext();){var i;o<t?i=r.next(t-o):(i=r.next(e-o),n.push(i)),o+=a.length(i)}return new s(n)},s.prototype.compose=function(t){var e=a.iterator(this.ops),n=a.iterator(t.ops),r=[],i=n.peek();if(null!=i&&"number"===typeof i.retain&&null==i.attributes){for(var l=i.retain;"insert"===e.peekType()&&e.peekLength()<=l;)l-=e.peekLength(),r.push(e.next());i.retain-l>0&&n.next(i.retain-l)}for(var u=new s(r);e.hasNext()||n.hasNext();)if("insert"===n.peekType())u.push(n.next());else if("delete"===e.peekType())u.push(e.next());else{var c=Math.min(e.peekLength(),n.peekLength()),f=e.next(c),p=n.next(c);if("number"===typeof p.retain){var h={};"number"===typeof f.retain?h.retain=c:h.insert=f.insert;var d=a.attributes.compose(f.attributes,p.attributes,"number"===typeof f.retain);if(d&&(h.attributes=d),u.push(h),!n.hasNext()&&o(u.ops[u.ops.length-1],h)){var A=new s(e.rest());return u.concat(A).chop()}}else"number"===typeof p.delete&&"number"===typeof f.retain&&u.push(p)}return u.chop()},s.prototype.concat=function(t){var e=new s(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e},s.prototype.diff=function(t,e){if(this.ops===t.ops)return new s;var n=[this,t].map((function(e){return e.map((function(n){if(null!=n.insert)return"string"===typeof n.insert?n.insert:l;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")})).join("")})),i=new s,u=r(n[0],n[1],e),c=a.iterator(this.ops),f=a.iterator(t.ops);return u.forEach((function(t){for(var e=t[1].length;e>0;){var n=0;switch(t[0]){case r.INSERT:n=Math.min(f.peekLength(),e),i.push(f.next(n));break;case r.DELETE:n=Math.min(e,c.peekLength()),c.next(n),i.delete(n);break;case r.EQUAL:n=Math.min(c.peekLength(),f.peekLength(),e);var l=c.next(n),s=f.next(n);o(l.insert,s.insert)?i.retain(n,a.attributes.diff(l.attributes,s.attributes)):i.push(s).delete(n)}e-=n}})),i.chop()},s.prototype.eachLine=function(t,e){e=e||"\n";for(var n=a.iterator(this.ops),r=new s,o=0;n.hasNext();){if("insert"!==n.peekType())return;var i=n.peek(),l=a.length(i)-n.peekLength(),u="string"===typeof i.insert?i.insert.indexOf(e,l)-l:-1;if(u<0)r.push(n.next());else if(u>0)r.push(n.next(u));else{if(!1===t(r,n.next(1).attributes||{},o))return;o+=1,r=new s}}r.length()>0&&t(r,{},o)},s.prototype.transform=function(t,e){if(e=!!e,"number"===typeof t)return this.transformPosition(t,e);for(var n=a.iterator(this.ops),r=a.iterator(t.ops),o=new s;n.hasNext()||r.hasNext();)if("insert"!==n.peekType()||!e&&"insert"===r.peekType())if("insert"===r.peekType())o.push(r.next());else{var i=Math.min(n.peekLength(),r.peekLength()),l=n.next(i),u=r.next(i);if(l.delete)continue;u.delete?o.push(u):o.retain(i,a.attributes.transform(l.attributes,u.attributes,e))}else o.retain(a.length(n.next()));return o.chop()},s.prototype.transformPosition=function(t,e){e=!!e;for(var n=a.iterator(this.ops),r=0;n.hasNext()&&r<=t;){var o=n.peekLength(),i=n.peekType();n.next(),"delete"!==i?("insert"===i&&(r<t||!e)&&(t+=o),r+=o):t-=Math.min(o,t-r)}return t},t.exports=s},function(t,e){"use strict";var n=Object.prototype.hasOwnProperty,r=Object.prototype.toString,o=Object.defineProperty,i=Object.getOwnPropertyDescriptor,a=function(t){return"function"===typeof Array.isArray?Array.isArray(t):"[object Array]"===r.call(t)},l=function(t){if(!t||"[object Object]"!==r.call(t))return!1;var e,o=n.call(t,"constructor"),i=t.constructor&&t.constructor.prototype&&n.call(t.constructor.prototype,"isPrototypeOf");if(t.constructor&&!o&&!i)return!1;for(e in t);return"undefined"===typeof e||n.call(t,e)},s=function(t,e){o&&"__proto__"===e.name?o(t,e.name,{enumerable:!0,configurable:!0,value:e.newValue,writable:!0}):t[e.name]=e.newValue},u=function(t,e){if("__proto__"===e){if(!n.call(t,e))return;if(i)return i(t,e).value}return t[e]};t.exports=function t(){var e,n,r,o,i,c,f=arguments[0],p=1,h=arguments.length,d=!1;for("boolean"===typeof f&&(d=f,f=arguments[1]||{},p=2),(null==f||"object"!==typeof f&&"function"!==typeof f)&&(f={});p<h;++p)if(null!=(e=arguments[p]))for(n in e)r=u(f,n),f!==(o=u(e,n))&&(d&&o&&(l(o)||(i=a(o)))?(i?(i=!1,c=r&&a(r)?r:[]):c=r&&l(r)?r:{},s(f,{name:n,newValue:t(d,c,o)})):"undefined"!==typeof o&&s(f,{name:n,newValue:o}));return f}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BlockEmbed=e.bubbleFormats=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=f(n(3)),a=f(n(2)),l=f(n(0)),s=f(n(16)),u=f(n(6)),c=f(n(7));function f(t){return t&&t.__esModule?t:{default:t}}function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function d(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var A=function(t){function e(){return p(this,e),h(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),r(e,[{key:"attach",value:function(){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"attach",this).call(this),this.attributes=new l.default.Attributor.Store(this.domNode)}},{key:"delta",value:function(){return(new a.default).insert(this.value(),(0,i.default)(this.formats(),this.attributes.values()))}},{key:"format",value:function(t,e){var n=l.default.query(t,l.default.Scope.BLOCK_ATTRIBUTE);null!=n&&this.attributes.attribute(n,e)}},{key:"formatAt",value:function(t,e,n,r){this.format(n,r)}},{key:"insertAt",value:function(t,n,r){if("string"===typeof n&&n.endsWith("\n")){var i=l.default.create(y.blotName);this.parent.insertBefore(i,0===t?this:this.next),i.insertAt(0,n.slice(0,-1))}else o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,n,r)}}]),e}(l.default.Embed);A.scope=l.default.Scope.BLOCK_BLOT;var y=function(t){function e(t){p(this,e);var n=h(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return n.cache={},n}return d(e,t),r(e,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=this.descendants(l.default.Leaf).reduce((function(t,e){return 0===e.length()?t:t.insert(e.value(),g(e))}),new a.default).insert("\n",g(this))),this.cache.delta}},{key:"deleteAt",value:function(t,n){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"deleteAt",this).call(this,t,n),this.cache={}}},{key:"formatAt",value:function(t,n,r,i){n<=0||(l.default.query(r,l.default.Scope.BLOCK)?t+n===this.length()&&this.format(r,i):o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,Math.min(n,this.length()-t-1),r,i),this.cache={})}},{key:"insertAt",value:function(t,n,r){if(null!=r)return o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,n,r);if(0!==n.length){var i=n.split("\n"),a=i.shift();a.length>0&&(t<this.length()-1||null==this.children.tail?o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,Math.min(t,this.length()-1),a):this.children.tail.insertAt(this.children.tail.length(),a),this.cache={});var l=this;i.reduce((function(t,e){return(l=l.split(t,!0)).insertAt(0,e),e.length}),t+a.length)}}},{key:"insertBefore",value:function(t,n){var r=this.children.head;o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,n),r instanceof s.default&&r.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(t,n){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"moveChildren",this).call(this,t,n),this.cache={}}},{key:"optimize",value:function(t){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.cache={}}},{key:"path",value:function(t){return o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"path",this).call(this,t,!0)}},{key:"removeChild",value:function(t){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"removeChild",this).call(this,t),this.cache={}}},{key:"split",value:function(t){var n=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(n&&(0===t||t>=this.length()-1)){var r=this.clone();return 0===t?(this.parent.insertBefore(r,this),this):(this.parent.insertBefore(r,this.next),r)}var i=o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"split",this).call(this,t,n);return this.cache={},i}}]),e}(l.default.Block);function g(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return null==t?e:("function"===typeof t.formats&&(e=(0,i.default)(e,t.formats())),null==t.parent||"scroll"==t.parent.blotName||t.parent.statics.scope!==t.statics.scope?e:g(t.parent,e))}y.blotName="block",y.tagName="P",y.defaultChild="break",y.allowedChildren=[u.default,l.default.Embed,c.default],e.bubbleFormats=g,e.BlockEmbed=A,e.default=y},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.overload=e.expandConfig=void 0;var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();n(50);var a=y(n(2)),l=y(n(14)),s=y(n(8)),u=y(n(9)),c=y(n(0)),f=n(15),p=y(f),h=y(n(3)),d=y(n(10)),A=y(n(34));function y(t){return t&&t.__esModule?t:{default:t}}function g(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function v(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var b=(0,d.default)("quill"),m=function(){function t(e){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(v(this,t),this.options=E(e,r),this.container=this.options.container,null==this.container)return b.error("Invalid Quill container",e);this.options.debug&&t.debug(this.options.debug);var o=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",this.container.__quill=this,this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.root.setAttribute("data-gramm",!1),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new s.default,this.scroll=c.default.create(this.root,{emitter:this.emitter,whitelist:this.options.formats}),this.editor=new l.default(this.scroll),this.selection=new p.default(this.scroll,this.emitter),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.theme.init(),this.emitter.on(s.default.events.EDITOR_CHANGE,(function(t){t===s.default.events.TEXT_CHANGE&&n.root.classList.toggle("ql-blank",n.editor.isBlank())})),this.emitter.on(s.default.events.SCROLL_UPDATE,(function(t,e){var r=n.selection.lastRange,o=r&&0===r.length?r.index:void 0;O.call(n,(function(){return n.editor.update(null,e,o)}),t)}));var i=this.clipboard.convert("<div class='ql-editor' style=\"white-space: normal;\">"+o+"<p><br></p></div>");this.setContents(i),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable()}return i(t,null,[{key:"debug",value:function(t){!0===t&&(t="log"),d.default.level(t)}},{key:"find",value:function(t){return t.__quill||c.default.find(t)}},{key:"import",value:function(t){return null==this.imports[t]&&b.error("Cannot import "+t+". Are you sure it was registered?"),this.imports[t]}},{key:"register",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!==typeof t){var o=t.attrName||t.blotName;"string"===typeof o?this.register("formats/"+o,t,e):Object.keys(t).forEach((function(r){n.register(r,t[r],e)}))}else null==this.imports[t]||r||b.warn("Overwriting "+t+" with",e),this.imports[t]=e,(t.startsWith("blots/")||t.startsWith("formats/"))&&"abstract"!==e.blotName?c.default.register(e):t.startsWith("modules")&&"function"===typeof e.register&&e.register()}}]),i(t,[{key:"addContainer",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"===typeof t){var n=t;(t=document.createElement("div")).classList.add(n)}return this.container.insertBefore(t,e),t}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(t,e,n){var r=this,i=w(t,e,n),a=o(i,4);return t=a[0],e=a[1],n=a[3],O.call(this,(function(){return r.editor.deleteText(t,e)}),n,t,-1*e)}},{key:"disable",value:function(){this.enable(!1)}},{key:"enable",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}},{key:"focus",value:function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()}},{key:"format",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.default.sources.API;return O.call(this,(function(){var r=n.getSelection(!0),o=new a.default;if(null==r)return o;if(c.default.query(t,c.default.Scope.BLOCK))o=n.editor.formatLine(r.index,r.length,g({},t,e));else{if(0===r.length)return n.selection.format(t,e),o;o=n.editor.formatText(r.index,r.length,g({},t,e))}return n.setSelection(r,s.default.sources.SILENT),o}),r)}},{key:"formatLine",value:function(t,e,n,r,i){var a,l=this,s=w(t,e,n,r,i),u=o(s,4);return t=u[0],e=u[1],a=u[2],i=u[3],O.call(this,(function(){return l.editor.formatLine(t,e,a)}),i,t,0)}},{key:"formatText",value:function(t,e,n,r,i){var a,l=this,s=w(t,e,n,r,i),u=o(s,4);return t=u[0],e=u[1],a=u[2],i=u[3],O.call(this,(function(){return l.editor.formatText(t,e,a)}),i,t,0)}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=void 0;n="number"===typeof t?this.selection.getBounds(t,e):this.selection.getBounds(t.index,t.length);var r=this.container.getBoundingClientRect();return{bottom:n.bottom-r.top,height:n.height,left:n.left-r.left,right:n.right-r.left,top:n.top-r.top,width:n.width}}},{key:"getContents",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,n=w(t,e),r=o(n,2);return t=r[0],e=r[1],this.editor.getContents(t,e)}},{key:"getFormat",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return"number"===typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length)}},{key:"getIndex",value:function(t){return t.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(t){return this.scroll.leaf(t)}},{key:"getLine",value:function(t){return this.scroll.line(t)}},{key:"getLines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!==typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}},{key:"getModule",value:function(t){return this.theme.modules[t]}},{key:"getSelection",value:function(){var t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return t&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getText",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,n=w(t,e),r=o(n,2);return t=r[0],e=r[1],this.editor.getText(t,e)}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(e,n,r){var o=this,i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.sources.API;return O.call(this,(function(){return o.editor.insertEmbed(e,n,r)}),i,e)}},{key:"insertText",value:function(t,e,n,r,i){var a,l=this,s=w(t,0,n,r,i),u=o(s,4);return t=u[0],a=u[2],i=u[3],O.call(this,(function(){return l.editor.insertText(t,e,a)}),i,t,e.length)}},{key:"isEnabled",value:function(){return!this.container.classList.contains("ql-disabled")}},{key:"off",value:function(){return this.emitter.off.apply(this.emitter,arguments)}},{key:"on",value:function(){return this.emitter.on.apply(this.emitter,arguments)}},{key:"once",value:function(){return this.emitter.once.apply(this.emitter,arguments)}},{key:"pasteHTML",value:function(t,e,n){this.clipboard.dangerouslyPasteHTML(t,e,n)}},{key:"removeFormat",value:function(t,e,n){var r=this,i=w(t,e,n),a=o(i,4);return t=a[0],e=a[1],n=a[3],O.call(this,(function(){return r.editor.removeFormat(t,e)}),n,t)}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API;return O.call(this,(function(){t=new a.default(t);var n=e.getLength(),r=e.editor.deleteText(0,n),o=e.editor.applyDelta(t),i=o.ops[o.ops.length-1];return null!=i&&"string"===typeof i.insert&&"\n"===i.insert[i.insert.length-1]&&(e.editor.deleteText(e.getLength()-1,1),o.delete(1)),r.compose(o)}),n)}},{key:"setSelection",value:function(e,n,r){if(null==e)this.selection.setRange(null,n||t.sources.API);else{var i=w(e,n,r),a=o(i,4);e=a[0],n=a[1],r=a[3],this.selection.setRange(new f.Range(e,n),r),r!==s.default.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API,n=(new a.default).insert(t);return this.setContents(n,e)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.default.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}},{key:"updateContents",value:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:s.default.sources.API;return O.call(this,(function(){return t=new a.default(t),e.editor.applyDelta(t,n)}),n,!0)}}]),t}();function E(t,e){if((e=(0,h.default)(!0,{container:t,modules:{clipboard:!0,keyboard:!0,history:!0}},e)).theme&&e.theme!==m.DEFAULTS.theme){if(e.theme=m.import("themes/"+e.theme),null==e.theme)throw new Error("Invalid theme "+e.theme+". Did you register it?")}else e.theme=A.default;var n=(0,h.default)(!0,{},e.theme.DEFAULTS);[n,e].forEach((function(t){t.modules=t.modules||{},Object.keys(t.modules).forEach((function(e){!0===t.modules[e]&&(t.modules[e]={})}))}));var r=Object.keys(n.modules).concat(Object.keys(e.modules)).reduce((function(t,e){var n=m.import("modules/"+e);return null==n?b.error("Cannot load "+e+" module. Are you sure you registered it?"):t[e]=n.DEFAULTS||{},t}),{});return null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar}),e=(0,h.default)(!0,{},m.DEFAULTS,{modules:r},n,e),["bounds","container","scrollingContainer"].forEach((function(t){"string"===typeof e[t]&&(e[t]=document.querySelector(e[t]))})),e.modules=Object.keys(e.modules).reduce((function(t,n){return e.modules[n]&&(t[n]=e.modules[n]),t}),{}),e}function O(t,e,n,r){if(this.options.strict&&!this.isEnabled()&&e===s.default.sources.USER)return new a.default;var o=null==n?null:this.getSelection(),i=this.editor.delta,l=t();if(null!=o&&(!0===n&&(n=o.index),null==r?o=C(o,l,e):0!==r&&(o=C(o,n,r,e)),this.setSelection(o,s.default.sources.SILENT)),l.length()>0){var u,c,f=[s.default.events.TEXT_CHANGE,l,i,e];(u=this.emitter).emit.apply(u,[s.default.events.EDITOR_CHANGE].concat(f)),e!==s.default.sources.SILENT&&(c=this.emitter).emit.apply(c,f)}return l}function w(t,e,n,o,i){var a={};return"number"===typeof t.index&&"number"===typeof t.length?"number"!==typeof e?(i=o,o=n,n=e,e=t.length,t=t.index):(e=t.length,t=t.index):"number"!==typeof e&&(i=o,o=n,n=e,e=0),"object"===("undefined"===typeof n?"undefined":r(n))?(a=n,i=o):"string"===typeof n&&(null!=o?a[n]=o:i=n),[t,e,a,i=i||s.default.sources.API]}function C(t,e,n,r){if(null==t)return null;var i=void 0,l=void 0;if(e instanceof a.default){var u=[t.index,t.index+t.length].map((function(t){return e.transformPosition(t,r!==s.default.sources.USER)})),c=o(u,2);i=c[0],l=c[1]}else{var p=[t.index,t.index+t.length].map((function(t){return t<e||t===e&&r===s.default.sources.USER?t:n>=0?t+n:Math.max(e,t+n)})),h=o(p,2);i=h[0],l=h[1]}return new f.Range(i,l-i)}m.DEFAULTS={bounds:null,formats:null,modules:{},placeholder:"",readOnly:!1,scrollingContainer:null,strict:!0,theme:"default"},m.events=s.default.events,m.sources=s.default.sources,m.version="1.3.7",m.imports={delta:a.default,parchment:c.default,"core/module":u.default,"core/theme":A.default},e.expandConfig=E,e.overload=w,e.default=m},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=l(n(7)),a=l(n(0));function l(t){return t&&t.__esModule?t:{default:t}}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var c=function(t){function e(){return s(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),r(e,[{key:"formatAt",value:function(t,n,r,i){if(e.compare(this.statics.blotName,r)<0&&a.default.query(r,a.default.Scope.BLOT)){var l=this.isolate(t,n);i&&l.wrap(r,i)}else o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,n,r,i)}},{key:"optimize",value:function(t){if(o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.parent instanceof e&&e.compare(this.statics.blotName,this.parent.statics.blotName)>0){var n=this.parent.isolate(this.offset(),this.length());this.moveChildren(n),n.wrap(this)}}}],[{key:"compare",value:function(t,n){var r=e.order.indexOf(t),o=e.order.indexOf(n);return r>=0||o>=0?r-o:t===n?0:t<n?-1:1}}]),e}(a.default.Inline);c.allowedChildren=[c,a.default.Embed,i.default],c.order=["cursor","inline","underline","strike","italic","bold","script","link","code"],e.default=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=n(0);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var l=function(t){function e(){return i(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((r=o)&&r.__esModule?r:{default:r}).default.Text);e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=a(n(54));function a(t){return t&&t.__esModule?t:{default:t}}var l=(0,a(n(10)).default)("quill:events");["selectionchange","mousedown","mouseup","click"].forEach((function(t){document.addEventListener(t,(function(){for(var t=arguments.length,e=Array(t),n=0;n<t;n++)e[n]=arguments[n];[].slice.call(document.querySelectorAll(".ql-container")).forEach((function(t){var n;t.__quill&&t.__quill.emitter&&(n=t.__quill.emitter).handleDOM.apply(n,e)}))}))}));var s=function(t){function e(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var t=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return t.listeners={},t.on("error",l.error),t}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),r(e,[{key:"emit",value:function(){l.log.apply(l,arguments),o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"emit",this).apply(this,arguments)}},{key:"handleDOM",value:function(t){for(var e=arguments.length,n=Array(e>1?e-1:0),r=1;r<e;r++)n[r-1]=arguments[r];(this.listeners[t.type]||[]).forEach((function(e){var r=e.node,o=e.handler;(t.target===r||r.contains(t.target))&&o.apply(void 0,[t].concat(n))}))}},{key:"listenDOM",value:function(t,e,n){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:n})}}]),e}(i.default);s.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change"},s.sources={API:"api",SILENT:"silent",USER:"user"},e.default=s},function(t,e,n){"use strict";function r(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var o=function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};r(this,t),this.quill=e,this.options=n};o.DEFAULTS={},e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=["error","warn","log","info"],o="warn";function i(t){if(r.indexOf(t)<=r.indexOf(o)){for(var e,n=arguments.length,i=Array(n>1?n-1:0),a=1;a<n;a++)i[a-1]=arguments[a];(e=console)[t].apply(e,i)}}function a(t){return r.reduce((function(e,n){return e[n]=i.bind(console,n,t),e}),{})}i.level=a.level=function(t){o=t},e.default=a},function(t,e,n){var r=Array.prototype.slice,o=n(52),i=n(53),a=t.exports=function(t,e,n){return n||(n={}),t===e||(t instanceof Date&&e instanceof Date?t.getTime()===e.getTime():!t||!e||"object"!=typeof t&&"object"!=typeof e?n.strict?t===e:t==e:function(t,e,n){var u,c;if(l(t)||l(e))return!1;if(t.prototype!==e.prototype)return!1;if(i(t))return!!i(e)&&(t=r.call(t),e=r.call(e),a(t,e,n));if(s(t)){if(!s(e))return!1;if(t.length!==e.length)return!1;for(u=0;u<t.length;u++)if(t[u]!==e[u])return!1;return!0}try{var f=o(t),p=o(e)}catch(h){return!1}if(f.length!=p.length)return!1;for(f.sort(),p.sort(),u=f.length-1;u>=0;u--)if(f[u]!=p[u])return!1;for(u=f.length-1;u>=0;u--)if(c=f[u],!a(t[c],e[c],n))return!1;return typeof t===typeof e}(t,e,n))};function l(t){return null===t||void 0===t}function s(t){return!(!t||"object"!==typeof t||"number"!==typeof t.length)&&"function"===typeof t.copy&&"function"===typeof t.slice&&!(t.length>0&&"number"!==typeof t[0])}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(1),o=function(){function t(t,e,n){void 0===n&&(n={}),this.attrName=t,this.keyName=e;var o=r.Scope.TYPE&r.Scope.ATTRIBUTE;null!=n.scope?this.scope=n.scope&r.Scope.LEVEL|o:this.scope=r.Scope.ATTRIBUTE,null!=n.whitelist&&(this.whitelist=n.whitelist)}return t.keys=function(t){return[].map.call(t.attributes,(function(t){return t.name}))},t.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},t.prototype.canAdd=function(t,e){return null!=r.query(t,r.Scope.BLOT&(this.scope|r.Scope.TYPE))&&(null==this.whitelist||("string"===typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1))},t.prototype.remove=function(t){t.removeAttribute(this.keyName)},t.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},t}();e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Code=void 0;var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=f(n(2)),l=f(n(0)),s=f(n(4)),u=f(n(6)),c=f(n(7));function f(t){return t&&t.__esModule?t:{default:t}}function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function d(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var A=function(t){function e(){return p(this,e),h(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),e}(u.default);A.blotName="code",A.tagName="CODE";var y=function(t){function e(){return p(this,e),h(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return d(e,t),o(e,[{key:"delta",value:function(){var t=this,e=this.domNode.textContent;return e.endsWith("\n")&&(e=e.slice(0,-1)),e.split("\n").reduce((function(e,n){return e.insert(n).insert("\n",t.formats())}),new a.default)}},{key:"format",value:function(t,n){if(t!==this.statics.blotName||!n){var o=this.descendant(c.default,this.length()-1),a=r(o,1)[0];null!=a&&a.deleteAt(a.length()-1,1),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n)}}},{key:"formatAt",value:function(t,n,r,o){if(0!==n&&null!=l.default.query(r,l.default.Scope.BLOCK)&&(r!==this.statics.blotName||o!==this.statics.formats(this.domNode))){var i=this.newlineIndex(t);if(!(i<0||i>=t+n)){var a=this.newlineIndex(t,!0)+1,s=i-a+1,u=this.isolate(a,s),c=u.next;u.format(r,o),c instanceof e&&c.formatAt(0,t-a+n-s,r,o)}}}},{key:"insertAt",value:function(t,e,n){if(null==n){var o=this.descendant(c.default,t),i=r(o,2),a=i[0],l=i[1];a.insertAt(l,e)}}},{key:"length",value:function(){var t=this.domNode.textContent.length;return this.domNode.textContent.endsWith("\n")?t:t+1}},{key:"newlineIndex",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)return this.domNode.textContent.slice(0,t).lastIndexOf("\n");var n=this.domNode.textContent.slice(t).indexOf("\n");return n>-1?t+n:-1}},{key:"optimize",value:function(t){this.domNode.textContent.endsWith("\n")||this.appendChild(l.default.create("text","\n")),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t);var n=this.next;null!=n&&n.prev===this&&n.statics.blotName===this.statics.blotName&&this.statics.formats(this.domNode)===n.statics.formats(n.domNode)&&(n.optimize(t),n.moveChildren(this),n.remove())}},{key:"replace",value:function(t){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replace",this).call(this,t),[].slice.call(this.domNode.querySelectorAll("*")).forEach((function(t){var e=l.default.find(t);null==e?t.parentNode.removeChild(t):e instanceof l.default.Embed?e.remove():e.unwrap()}))}}],[{key:"create",value:function(t){var n=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return n.setAttribute("spellcheck",!1),n}},{key:"formats",value:function(){return!0}}]),e}(s.default);y.blotName="code-block",y.tagName="PRE",y.TAB="  ",e.Code=A,e.default=y},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),a=g(n(2)),l=g(n(20)),s=g(n(0)),u=g(n(13)),c=g(n(24)),f=n(4),p=g(f),h=g(n(16)),d=g(n(21)),A=g(n(11)),y=g(n(3));function g(t){return t&&t.__esModule?t:{default:t}}var v=/^[ -~]*$/,b=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scroll=e,this.delta=this.getDelta()}return i(t,[{key:"applyDelta",value:function(t){var e=this,n=!1;this.scroll.update();var i=this.scroll.length();return this.scroll.batchStart(),(t=function(t){return t.reduce((function(t,e){if(1===e.insert){var n=(0,d.default)(e.attributes);return delete n.image,t.insert({image:e.attributes.image},n)}if(null==e.attributes||!0!==e.attributes.list&&!0!==e.attributes.bullet||((e=(0,d.default)(e)).attributes.list?e.attributes.list="ordered":(e.attributes.list="bullet",delete e.attributes.bullet)),"string"===typeof e.insert){var r=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(r,e.attributes)}return t.push(e)}),new a.default)}(t)).reduce((function(t,a){var u=a.retain||a.delete||a.insert.length||1,c=a.attributes||{};if(null!=a.insert){if("string"===typeof a.insert){var h=a.insert;h.endsWith("\n")&&n&&(n=!1,h=h.slice(0,-1)),t>=i&&!h.endsWith("\n")&&(n=!0),e.scroll.insertAt(t,h);var d=e.scroll.line(t),A=o(d,2),g=A[0],v=A[1],b=(0,y.default)({},(0,f.bubbleFormats)(g));if(g instanceof p.default){var m=g.descendant(s.default.Leaf,v),E=o(m,1)[0];b=(0,y.default)(b,(0,f.bubbleFormats)(E))}c=l.default.attributes.diff(b,c)||{}}else if("object"===r(a.insert)){var O=Object.keys(a.insert)[0];if(null==O)return t;e.scroll.insertAt(t,O,a.insert[O])}i+=u}return Object.keys(c).forEach((function(n){e.scroll.formatAt(t,u,n,c[n])})),t+u}),0),t.reduce((function(t,n){return"number"===typeof n.delete?(e.scroll.deleteAt(t,n.delete),t):t+(n.retain||n.insert.length||1)}),0),this.scroll.batchEnd(),this.update(t)}},{key:"deleteText",value:function(t,e){return this.scroll.deleteAt(t,e),this.update((new a.default).retain(t).delete(e))}},{key:"formatLine",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.scroll.update(),Object.keys(r).forEach((function(o){if(null==n.scroll.whitelist||n.scroll.whitelist[o]){var i=n.scroll.lines(t,Math.max(e,1)),a=e;i.forEach((function(e){var i=e.length();if(e instanceof u.default){var l=t-e.offset(n.scroll),s=e.newlineIndex(l+a)-l+1;e.formatAt(l,s,o,r[o])}else e.format(o,r[o]);a-=i}))}})),this.scroll.optimize(),this.update((new a.default).retain(t).retain(e,(0,d.default)(r)))}},{key:"formatText",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return Object.keys(r).forEach((function(o){n.scroll.formatAt(t,e,o,r[o])})),this.update((new a.default).retain(t).retain(e,(0,d.default)(r)))}},{key:"getContents",value:function(t,e){return this.delta.slice(t,t+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce((function(t,e){return t.concat(e.delta())}),new a.default)}},{key:"getFormat",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=[],r=[];0===e?this.scroll.path(t).forEach((function(t){var e=o(t,1)[0];e instanceof p.default?n.push(e):e instanceof s.default.Leaf&&r.push(e)})):(n=this.scroll.lines(t,e),r=this.scroll.descendants(s.default.Leaf,t,e));var i=[n,r].map((function(t){if(0===t.length)return{};for(var e=(0,f.bubbleFormats)(t.shift());Object.keys(e).length>0;){var n=t.shift();if(null==n)return e;e=m((0,f.bubbleFormats)(n),e)}return e}));return y.default.apply(y.default,i)}},{key:"getText",value:function(t,e){return this.getContents(t,e).filter((function(t){return"string"===typeof t.insert})).map((function(t){return t.insert})).join("")}},{key:"insertEmbed",value:function(t,e,n){return this.scroll.insertAt(t,e,n),this.update((new a.default).retain(t).insert(function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}({},e,n)))}},{key:"insertText",value:function(t,e){var n=this,r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(r).forEach((function(o){n.scroll.formatAt(t,e.length,o,r[o])})),this.update((new a.default).retain(t).insert(e,(0,d.default)(r)))}},{key:"isBlank",value:function(){if(0==this.scroll.children.length)return!0;if(this.scroll.children.length>1)return!1;var t=this.scroll.children.head;return t.statics.blotName===p.default.blotName&&!(t.children.length>1)&&t.children.head instanceof h.default}},{key:"removeFormat",value:function(t,e){var n=this.getText(t,e),r=this.scroll.line(t+e),i=o(r,2),l=i[0],s=i[1],c=0,f=new a.default;null!=l&&(c=l instanceof u.default?l.newlineIndex(s)-s+1:l.length()-s,f=l.delta().slice(s,s+c-1).insert("\n"));var p=this.getContents(t,e+c).diff((new a.default).insert(n).concat(f)),h=(new a.default).retain(t).concat(p);return this.applyDelta(h)}},{key:"update",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,r=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(v)&&s.default.find(e[0].target)){var o=s.default.find(e[0].target),i=(0,f.bubbleFormats)(o),l=o.offset(this.scroll),u=e[0].oldValue.replace(c.default.CONTENTS,""),p=(new a.default).insert(u),h=(new a.default).insert(o.value()),d=(new a.default).retain(l).concat(p.diff(h,n));t=d.reduce((function(t,e){return e.insert?t.insert(e.insert,i):t.push(e)}),new a.default),this.delta=r.compose(t)}else this.delta=this.getDelta(),t&&(0,A.default)(r.compose(t),this.delta)||(t=r.diff(this.delta,n));return t}}]),t}();function m(t,e){return Object.keys(e).reduce((function(n,r){return null==t[r]||(e[r]===t[r]?n[r]=e[r]:Array.isArray(e[r])?e[r].indexOf(t[r])<0&&(n[r]=e[r].concat([t[r]])):n[r]=[e[r],t[r]]),n}),{})}e.default=b},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.Range=void 0;var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=u(n(0)),a=u(n(21)),l=u(n(11)),s=u(n(8));function u(t){return t&&t.__esModule?t:{default:t}}function c(t){if(Array.isArray(t)){for(var e=0,n=Array(t.length);e<t.length;e++)n[e]=t[e];return n}return Array.from(t)}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var p=(0,u(n(10)).default)("quill:selection"),h=function t(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;f(this,t),this.index=e,this.length=n},d=function(){function t(e,n){var r=this;f(this,t),this.emitter=n,this.scroll=e,this.composing=!1,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=i.default.create("cursor",this),this.lastRange=this.savedRange=new h(0,0),this.handleComposition(),this.handleDragging(),this.emitter.listenDOM("selectionchange",document,(function(){r.mouseDown||setTimeout(r.update.bind(r,s.default.sources.USER),1)})),this.emitter.on(s.default.events.EDITOR_CHANGE,(function(t,e){t===s.default.events.TEXT_CHANGE&&e.length()>0&&r.update(s.default.sources.SILENT)})),this.emitter.on(s.default.events.SCROLL_BEFORE_UPDATE,(function(){if(r.hasFocus()){var t=r.getNativeRange();null!=t&&t.start.node!==r.cursor.textNode&&r.emitter.once(s.default.events.SCROLL_UPDATE,(function(){try{r.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset)}catch(e){}}))}})),this.emitter.on(s.default.events.SCROLL_OPTIMIZE,(function(t,e){if(e.range){var n=e.range,o=n.startNode,i=n.startOffset,a=n.endNode,l=n.endOffset;r.setNativeRange(o,i,a,l)}})),this.update(s.default.sources.SILENT)}return o(t,[{key:"handleComposition",value:function(){var t=this;this.root.addEventListener("compositionstart",(function(){t.composing=!0})),this.root.addEventListener("compositionend",(function(){if(t.composing=!1,t.cursor.parent){var e=t.cursor.restore();if(!e)return;setTimeout((function(){t.setNativeRange(e.startNode,e.startOffset,e.endNode,e.endOffset)}),1)}}))}},{key:"handleDragging",value:function(){var t=this;this.emitter.listenDOM("mousedown",document.body,(function(){t.mouseDown=!0})),this.emitter.listenDOM("mouseup",document.body,(function(){t.mouseDown=!1,t.update(s.default.sources.USER)}))}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(t,e){if(null==this.scroll.whitelist||this.scroll.whitelist[t]){this.scroll.update();var n=this.getNativeRange();if(null!=n&&n.native.collapsed&&!i.default.query(t,i.default.Scope.BLOCK)){if(n.start.node!==this.cursor.textNode){var r=i.default.find(n.start.node,!1);if(null==r)return;if(r instanceof i.default.Leaf){var o=r.split(n.start.offset);r.parent.insertBefore(this.cursor,o)}else r.insertBefore(this.cursor,n.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}}},{key:"getBounds",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();t=Math.min(t,n-1),e=Math.min(t+e,n-1)-t;var o=void 0,i=this.scroll.leaf(t),a=r(i,2),l=a[0],s=a[1];if(null==l)return null;var u=l.position(s,!0),c=r(u,2);o=c[0],s=c[1];var f=document.createRange();if(e>0){f.setStart(o,s);var p=this.scroll.leaf(t+e),h=r(p,2);if(l=h[0],s=h[1],null==l)return null;var d=l.position(s,!0),A=r(d,2);return o=A[0],s=A[1],f.setEnd(o,s),f.getBoundingClientRect()}var y="left",g=void 0;return o instanceof Text?(s<o.data.length?(f.setStart(o,s),f.setEnd(o,s+1)):(f.setStart(o,s-1),f.setEnd(o,s),y="right"),g=f.getBoundingClientRect()):(g=l.domNode.getBoundingClientRect(),s>0&&(y="right")),{bottom:g.top+g.height,height:g.height,left:g[y],right:g[y],top:g.top,width:0}}},{key:"getNativeRange",value:function(){var t=document.getSelection();if(null==t||t.rangeCount<=0)return null;var e=t.getRangeAt(0);if(null==e)return null;var n=this.normalizeNative(e);return p.info("getNativeRange",n),n}},{key:"getRange",value:function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){return document.activeElement===this.root}},{key:"normalizedToRange",value:function(t){var e=this,n=[[t.start.node,t.start.offset]];t.native.collapsed||n.push([t.end.node,t.end.offset]);var o=n.map((function(t){var n=r(t,2),o=n[0],a=n[1],l=i.default.find(o,!0),s=l.offset(e.scroll);return 0===a?s:l instanceof i.default.Container?s+l.length():s+l.index(o,a)})),a=Math.min(Math.max.apply(Math,c(o)),this.scroll.length()-1),l=Math.min.apply(Math,[a].concat(c(o)));return new h(l,a-l)}},{key:"normalizeNative",value:function(t){if(!A(this.root,t.startContainer)||!t.collapsed&&!A(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach((function(t){for(var e=t.node,n=t.offset;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>n)e=e.childNodes[n],n=0;else{if(e.childNodes.length!==n)break;n=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length+1}t.node=e,t.offset=n})),e}},{key:"rangeToNative",value:function(t){var e=this,n=t.collapsed?[t.index]:[t.index,t.index+t.length],o=[],i=this.scroll.length();return n.forEach((function(t,n){t=Math.min(i-1,t);var a,l=e.scroll.leaf(t),s=r(l,2),u=s[0],c=s[1],f=u.position(c,0!==n),p=r(f,2);a=p[0],c=p[1],o.push(a,c)})),o.length<2&&(o=o.concat(o)),o}},{key:"scrollIntoView",value:function(t){var e=this.lastRange;if(null!=e){var n=this.getBounds(e.index,e.length);if(null!=n){var o=this.scroll.length()-1,i=this.scroll.line(Math.min(e.index,o)),a=r(i,1)[0],l=a;if(e.length>0){var s=this.scroll.line(Math.min(e.index+e.length,o));l=r(s,1)[0]}if(null!=a&&null!=l){var u=t.getBoundingClientRect();n.top<u.top?t.scrollTop-=u.top-n.top:n.bottom>u.bottom&&(t.scrollTop+=n.bottom-u.bottom)}}}}},{key:"setNativeRange",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(p.info("setNativeRange",t,e,n,r),null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=n.parentNode){var i=document.getSelection();if(null!=i)if(null!=t){this.hasFocus()||this.root.focus();var a=(this.getNativeRange()||{}).native;if(null==a||o||t!==a.startContainer||e!==a.startOffset||n!==a.endContainer||r!==a.endOffset){"BR"==t.tagName&&(e=[].indexOf.call(t.parentNode.childNodes,t),t=t.parentNode),"BR"==n.tagName&&(r=[].indexOf.call(n.parentNode.childNodes,n),n=n.parentNode);var l=document.createRange();l.setStart(t,e),l.setEnd(n,r),i.removeAllRanges(),i.addRange(l)}}else i.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:s.default.sources.API;if("string"===typeof e&&(n=e,e=!1),p.info("setRange",t),null!=t){var r=this.rangeToNative(t);this.setNativeRange.apply(this,c(r).concat([e]))}else this.setNativeRange(null);this.update(n)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.default.sources.USER,e=this.lastRange,n=this.getRange(),o=r(n,2),i=o[0],u=o[1];if(this.lastRange=i,null!=this.lastRange&&(this.savedRange=this.lastRange),!(0,l.default)(e,this.lastRange)){var c;!this.composing&&null!=u&&u.native.collapsed&&u.start.node!==this.cursor.textNode&&this.cursor.restore();var f,p=[s.default.events.SELECTION_CHANGE,(0,a.default)(this.lastRange),(0,a.default)(e),t];(c=this.emitter).emit.apply(c,[s.default.events.EDITOR_CHANGE].concat(p)),t!==s.default.sources.SILENT&&(f=this.emitter).emit.apply(f,p)}}}]),t}();function A(t,e){try{e.parentNode}catch(n){return!1}return e instanceof Text&&(e=e.parentNode),t.contains(e)}e.Range=h,e.default=d},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(0);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var s=function(t){function e(){return a(this,e),l(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"insertInto",value:function(t,n){0===t.children.length?function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertInto",this).call(this,t,n):this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}],[{key:"value",value:function(){}}]),e}(((r=i)&&r.__esModule?r:{default:r}).default.Embed);s.blotName="break",s.tagName="BR",e.default=s},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(44),i=n(30),a=n(1),l=function(t){function e(e){var n=t.call(this,e)||this;return n.build(),n}return r(e,t),e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){t.prototype.attach.call(this),this.children.forEach((function(t){t.attach()}))},e.prototype.build=function(){var t=this;this.children=new o.default,[].slice.call(this.domNode.childNodes).reverse().forEach((function(e){try{var n=s(e);t.insertBefore(n,t.children.head||void 0)}catch(r){if(r instanceof a.ParchmentError)return;throw r}}))},e.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,(function(t,e,n){t.deleteAt(e,n)}))},e.prototype.descendant=function(t,n){var r=this.children.find(n),o=r[0],i=r[1];return null==t.blotName&&t(o)||null!=t.blotName&&o instanceof t?[o,i]:o instanceof e?o.descendant(t,i):[null,-1]},e.prototype.descendants=function(t,n,r){void 0===n&&(n=0),void 0===r&&(r=Number.MAX_VALUE);var o=[],i=r;return this.children.forEachAt(n,r,(function(n,r,a){(null==t.blotName&&t(n)||null!=t.blotName&&n instanceof t)&&o.push(n),n instanceof e&&(o=o.concat(n.descendants(t,r,i))),i-=a})),o},e.prototype.detach=function(){this.children.forEach((function(t){t.detach()})),t.prototype.detach.call(this)},e.prototype.formatAt=function(t,e,n,r){this.children.forEachAt(t,e,(function(t,e,o){t.formatAt(e,o,n,r)}))},e.prototype.insertAt=function(t,e,n){var r=this.children.find(t),o=r[0],i=r[1];if(o)o.insertAt(i,e,n);else{var l=null==n?a.create("text",e):a.create(e,n);this.appendChild(l)}},e.prototype.insertBefore=function(t,e){if(null!=this.statics.allowedChildren&&!this.statics.allowedChildren.some((function(e){return t instanceof e})))throw new a.ParchmentError("Cannot insert "+t.statics.blotName+" into "+this.statics.blotName);t.insertInto(this,e)},e.prototype.length=function(){return this.children.reduce((function(t,e){return t+e.length()}),0)},e.prototype.moveChildren=function(t,e){this.children.forEach((function(n){t.insertBefore(n,e)}))},e.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),0===this.children.length)if(null!=this.statics.defaultChild){var n=a.create(this.statics.defaultChild);this.appendChild(n),n.optimize(e)}else this.remove()},e.prototype.path=function(t,n){void 0===n&&(n=!1);var r=this.children.find(t,n),o=r[0],i=r[1],a=[[this,t]];return o instanceof e?a.concat(o.path(i,n)):(null!=o&&a.push([o,i]),a)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replace=function(n){n instanceof e&&n.moveChildren(this),t.prototype.replace.call(this,n)},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var n=this.clone();return this.parent.insertBefore(n,this.next),this.children.forEachAt(t,this.length(),(function(t,r,o){t=t.split(r,e),n.appendChild(t)})),n},e.prototype.unwrap=function(){this.moveChildren(this.parent,this.next),this.remove()},e.prototype.update=function(t,e){var n=this,r=[],o=[];t.forEach((function(t){t.target===n.domNode&&"childList"===t.type&&(r.push.apply(r,t.addedNodes),o.push.apply(o,t.removedNodes))})),o.forEach((function(t){if(!(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var e=a.find(t);null!=e&&(null!=e.domNode.parentNode&&e.domNode.parentNode!==n.domNode||e.detach())}})),r.filter((function(t){return t.parentNode==n.domNode})).sort((function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1})).forEach((function(t){var e=null;null!=t.nextSibling&&(e=a.find(t.nextSibling));var r=s(t);r.next==e&&null!=r.next||(null!=r.parent&&r.parent.removeChild(n),n.insertBefore(r,e||void 0))}))},e}(i.default);function s(t){var e=a.find(t);if(null==e)try{e=a.create(t)}catch(n){e=a.create(a.Scope.INLINE),[].slice.call(t.childNodes).forEach((function(t){e.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(e.domNode,t),e.attach()}return e}e.default=l},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(12),i=n(31),a=n(17),l=n(1),s=function(t){function e(e){var n=t.call(this,e)||this;return n.attributes=new i.default(n.domNode),n}return r(e,t),e.formats=function(t){return"string"===typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,e){var n=l.query(t);n instanceof o.default?this.attributes.attribute(n,e):e&&(null==n||t===this.statics.blotName&&this.formats()[t]===e||this.replaceWith(t,e))},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.replaceWith=function(e,n){var r=t.prototype.replaceWith.call(this,e,n);return this.attributes.copy(r),r},e.prototype.update=function(e,n){var r=this;t.prototype.update.call(this,e,n),e.some((function(t){return t.target===r.domNode&&"attributes"===t.type}))&&this.attributes.build()},e.prototype.wrap=function(n,r){var o=t.prototype.wrap.call(this,n,r);return o instanceof e&&o.statics.scope===this.statics.scope&&this.attributes.move(o),o},e}(a.default);e.default=s},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(30),i=n(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.value=function(t){return!0},e.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},e.prototype.position=function(t,e){var n=[].indexOf.call(this.parent.domNode.childNodes,this.domNode);return t>0&&(n+=1),[this.parent.domNode,n]},e.prototype.value=function(){var t;return(t={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},e.scope=i.Scope.INLINE_BLOT,e}(o.default);e.default=a},function(t,e,n){var r=n(11),o=n(3),i={attributes:{compose:function(t,e,n){"object"!==typeof t&&(t={}),"object"!==typeof e&&(e={});var r=o(!0,{},e);for(var i in n||(r=Object.keys(r).reduce((function(t,e){return null!=r[e]&&(t[e]=r[e]),t}),{})),t)void 0!==t[i]&&void 0===e[i]&&(r[i]=t[i]);return Object.keys(r).length>0?r:void 0},diff:function(t,e){"object"!==typeof t&&(t={}),"object"!==typeof e&&(e={});var n=Object.keys(t).concat(Object.keys(e)).reduce((function(n,o){return r(t[o],e[o])||(n[o]=void 0===e[o]?null:e[o]),n}),{});return Object.keys(n).length>0?n:void 0},transform:function(t,e,n){if("object"!==typeof t)return e;if("object"===typeof e){if(!n)return e;var r=Object.keys(e).reduce((function(n,r){return void 0===t[r]&&(n[r]=e[r]),n}),{});return Object.keys(r).length>0?r:void 0}}},iterator:function(t){return new a(t)},length:function(t){return"number"===typeof t.delete?t.delete:"number"===typeof t.retain?t.retain:"string"===typeof t.insert?t.insert.length:1}};function a(t){this.ops=t,this.index=0,this.offset=0}a.prototype.hasNext=function(){return this.peekLength()<1/0},a.prototype.next=function(t){t||(t=1/0);var e=this.ops[this.index];if(e){var n=this.offset,r=i.length(e);if(t>=r-n?(t=r-n,this.index+=1,this.offset=0):this.offset+=t,"number"===typeof e.delete)return{delete:t};var o={};return e.attributes&&(o.attributes=e.attributes),"number"===typeof e.retain?o.retain=t:"string"===typeof e.insert?o.insert=e.insert.substr(n,t):o.insert=e.insert,o}return{retain:1/0}},a.prototype.peek=function(){return this.ops[this.index]},a.prototype.peekLength=function(){return this.ops[this.index]?i.length(this.ops[this.index])-this.offset:1/0},a.prototype.peekType=function(){return this.ops[this.index]?"number"===typeof this.ops[this.index].delete?"delete":"number"===typeof this.ops[this.index].retain?"retain":"insert":"retain"},a.prototype.rest=function(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);var t=this.offset,e=this.index,n=this.next(),r=this.ops.slice(this.index);return this.offset=t,this.index=e,[n].concat(r)}return[]},t.exports=i},function(t,n){var r=function(){"use strict";function t(t,e){return null!=e&&t instanceof e}var n,r,o;try{n=Map}catch(s){n=function(){}}try{r=Set}catch(s){r=function(){}}try{o=Promise}catch(s){o=function(){}}function i(a,s,u,c,f){"object"===typeof s&&(u=s.depth,c=s.prototype,f=s.includeNonEnumerable,s=s.circular);var p=[],h=[],d="undefined"!=typeof e;return"undefined"==typeof s&&(s=!0),"undefined"==typeof u&&(u=1/0),function a(u,A){if(null===u)return null;if(0===A)return u;var y,g;if("object"!=typeof u)return u;if(t(u,n))y=new n;else if(t(u,r))y=new r;else if(t(u,o))y=new o((function(t,e){u.then((function(e){t(a(e,A-1))}),(function(t){e(a(t,A-1))}))}));else if(i.__isArray(u))y=[];else if(i.__isRegExp(u))y=new RegExp(u.source,l(u)),u.lastIndex&&(y.lastIndex=u.lastIndex);else if(i.__isDate(u))y=new Date(u.getTime());else{if(d&&e.isBuffer(u))return y=e.allocUnsafe?e.allocUnsafe(u.length):new e(u.length),u.copy(y),y;t(u,Error)?y=Object.create(u):"undefined"==typeof c?(g=Object.getPrototypeOf(u),y=Object.create(g)):(y=Object.create(c),g=c)}if(s){var v=p.indexOf(u);if(-1!=v)return h[v];p.push(u),h.push(y)}for(var b in t(u,n)&&u.forEach((function(t,e){var n=a(e,A-1),r=a(t,A-1);y.set(n,r)})),t(u,r)&&u.forEach((function(t){var e=a(t,A-1);y.add(e)})),u){var m;g&&(m=Object.getOwnPropertyDescriptor(g,b)),m&&null==m.set||(y[b]=a(u[b],A-1))}if(Object.getOwnPropertySymbols){var E=Object.getOwnPropertySymbols(u);for(b=0;b<E.length;b++){var O=E[b];(!(C=Object.getOwnPropertyDescriptor(u,O))||C.enumerable||f)&&(y[O]=a(u[O],A-1),C.enumerable||Object.defineProperty(y,O,{enumerable:!1}))}}if(f){var w=Object.getOwnPropertyNames(u);for(b=0;b<w.length;b++){var C,k=w[b];(C=Object.getOwnPropertyDescriptor(u,k))&&C.enumerable||(y[k]=a(u[k],A-1),Object.defineProperty(y,k,{enumerable:!1}))}}return y}(a,u)}function a(t){return Object.prototype.toString.call(t)}function l(t){var e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),e}return i.clonePrototype=function(t){if(null===t)return null;var e=function(){};return e.prototype=t,new e},i.__objToStr=a,i.__isDate=function(t){return"object"===typeof t&&"[object Date]"===a(t)},i.__isArray=function(t){return"object"===typeof t&&"[object Array]"===a(t)},i.__isRegExp=function(t){return"object"===typeof t&&"[object RegExp]"===a(t)},i.__getRegExpFlags=l,i}();"object"===typeof t&&t.exports&&(t.exports=r)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=h(n(0)),l=h(n(8)),s=n(4),u=h(s),c=h(n(16)),f=h(n(13)),p=h(n(25));function h(t){return t&&t.__esModule?t:{default:t}}function d(t){return t instanceof u.default||t instanceof s.BlockEmbed}var A=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return r.emitter=n.emitter,Array.isArray(n.whitelist)&&(r.whitelist=n.whitelist.reduce((function(t,e){return t[e]=!0,t}),{})),r.domNode.addEventListener("DOMNodeInserted",(function(){})),r.optimize(),r.enable(),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"batchStart",value:function(){this.batch=!0}},{key:"batchEnd",value:function(){this.batch=!1,this.optimize()}},{key:"deleteAt",value:function(t,n){var o=this.line(t),a=r(o,2),l=a[0],u=a[1],p=this.line(t+n),h=r(p,1)[0];if(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"deleteAt",this).call(this,t,n),null!=h&&l!==h&&u>0){if(l instanceof s.BlockEmbed||h instanceof s.BlockEmbed)return void this.optimize();if(l instanceof f.default){var d=l.newlineIndex(l.length(),!0);if(d>-1&&(l=l.split(d+1))===h)return void this.optimize()}else if(h instanceof f.default){var A=h.newlineIndex(0);A>-1&&h.split(A+1)}var y=h.children.head instanceof c.default?null:h.children.head;l.moveChildren(h,y),l.remove()}this.optimize()}},{key:"enable",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.domNode.setAttribute("contenteditable",t)}},{key:"formatAt",value:function(t,n,r,o){(null==this.whitelist||this.whitelist[r])&&(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"formatAt",this).call(this,t,n,r,o),this.optimize())}},{key:"insertAt",value:function(t,n,r){if(null==r||null==this.whitelist||this.whitelist[n]){if(t>=this.length())if(null==r||null==a.default.query(n,a.default.Scope.BLOCK)){var o=a.default.create(this.statics.defaultChild);this.appendChild(o),null==r&&n.endsWith("\n")&&(n=n.slice(0,-1)),o.insertAt(0,n,r)}else{var l=a.default.create(n,r);this.appendChild(l)}else i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertAt",this).call(this,t,n,r);this.optimize()}}},{key:"insertBefore",value:function(t,n){if(t.statics.scope===a.default.Scope.INLINE_BLOT){var r=a.default.create(this.statics.defaultChild);r.appendChild(t),t=r}i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,n)}},{key:"leaf",value:function(t){return this.path(t).pop()||[null,-1]}},{key:"line",value:function(t){return t===this.length()?this.line(t-1):this.descendant(d,t)}},{key:"lines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE,n=function t(e,n,r){var o=[],i=r;return e.children.forEachAt(n,r,(function(e,n,r){d(e)?o.push(e):e instanceof a.default.Container&&(o=o.concat(t(e,n,i))),i-=r})),o};return n(this,t,e)}},{key:"optimize",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!0!==this.batch&&(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t,n),t.length>0&&this.emitter.emit(l.default.events.SCROLL_OPTIMIZE,t,n))}},{key:"path",value:function(t){return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"path",this).call(this,t).slice(1)}},{key:"update",value:function(t){if(!0!==this.batch){var n=l.default.sources.USER;"string"===typeof t&&(n=t),Array.isArray(t)||(t=this.observer.takeRecords()),t.length>0&&this.emitter.emit(l.default.events.SCROLL_BEFORE_UPDATE,n,t),i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"update",this).call(this,t.concat([])),t.length>0&&this.emitter.emit(l.default.events.SCROLL_UPDATE,n,t)}}}]),e}(a.default.Scroll);A.blotName="scroll",A.className="ql-editor",A.tagName="DIV",A.defaultChild="block",A.allowedChildren=[u.default,s.BlockEmbed,p.default],e.default=A},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SHORTKEY=e.default=void 0;var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),a=A(n(21)),l=A(n(11)),s=A(n(3)),u=A(n(2)),c=A(n(20)),f=A(n(0)),p=A(n(5)),h=A(n(10)),d=A(n(9));function A(t){return t&&t.__esModule?t:{default:t}}function y(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var g=(0,h.default)("quill:keyboard"),v=/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",b=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.bindings={},Object.keys(r.options.bindings).forEach((function(e){("list autofill"!==e||null==t.scroll.whitelist||t.scroll.whitelist.list)&&r.options.bindings[e]&&r.addBinding(r.options.bindings[e])})),r.addBinding({key:e.keys.ENTER,shiftKey:null},C),r.addBinding({key:e.keys.ENTER,metaKey:null,ctrlKey:null,altKey:null},(function(){})),/Firefox/i.test(navigator.userAgent)?(r.addBinding({key:e.keys.BACKSPACE},{collapsed:!0},E),r.addBinding({key:e.keys.DELETE},{collapsed:!0},O)):(r.addBinding({key:e.keys.BACKSPACE},{collapsed:!0,prefix:/^.?$/},E),r.addBinding({key:e.keys.DELETE},{collapsed:!0,suffix:/^.?$/},O)),r.addBinding({key:e.keys.BACKSPACE},{collapsed:!1},w),r.addBinding({key:e.keys.DELETE},{collapsed:!1},w),r.addBinding({key:e.keys.BACKSPACE,altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},E),r.listen(),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,null,[{key:"match",value:function(t,e){return e=_(e),!["altKey","ctrlKey","metaKey","shiftKey"].some((function(n){return!!e[n]!==t[n]&&null!==e[n]}))&&e.key===(t.which||t.keyCode)}}]),i(e,[{key:"addBinding",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=_(t);if(null==r||null==r.key)return g.warn("Attempted to add invalid keyboard binding",r);"function"===typeof e&&(e={handler:e}),"function"===typeof n&&(n={handler:n}),r=(0,s.default)(r,e,n),this.bindings[r.key]=this.bindings[r.key]||[],this.bindings[r.key].push(r)}},{key:"listen",value:function(){var t=this;this.quill.root.addEventListener("keydown",(function(n){if(!n.defaultPrevented){var i=n.which||n.keyCode,a=(t.bindings[i]||[]).filter((function(t){return e.match(n,t)}));if(0!==a.length){var s=t.quill.getSelection();if(null!=s&&t.quill.hasFocus()){var u=t.quill.getLine(s.index),c=o(u,2),p=c[0],h=c[1],d=t.quill.getLeaf(s.index),A=o(d,2),y=A[0],g=A[1],v=0===s.length?[y,g]:t.quill.getLeaf(s.index+s.length),b=o(v,2),m=b[0],E=b[1],O=y instanceof f.default.Text?y.value().slice(0,g):"",w=m instanceof f.default.Text?m.value().slice(E):"",C={collapsed:0===s.length,empty:0===s.length&&p.length()<=1,format:t.quill.getFormat(s),offset:h,prefix:O,suffix:w};a.some((function(e){if(null!=e.collapsed&&e.collapsed!==C.collapsed)return!1;if(null!=e.empty&&e.empty!==C.empty)return!1;if(null!=e.offset&&e.offset!==C.offset)return!1;if(Array.isArray(e.format)){if(e.format.every((function(t){return null==C.format[t]})))return!1}else if("object"===r(e.format)&&!Object.keys(e.format).every((function(t){return!0===e.format[t]?null!=C.format[t]:!1===e.format[t]?null==C.format[t]:(0,l.default)(e.format[t],C.format[t])})))return!1;return!(null!=e.prefix&&!e.prefix.test(C.prefix))&&!(null!=e.suffix&&!e.suffix.test(C.suffix))&&!0!==e.handler.call(t,s,C)}))&&n.preventDefault()}}}}))}}]),e}(d.default);function m(t,e){var n,r=t===b.keys.LEFT?"prefix":"suffix";return y(n={key:t,shiftKey:e,altKey:null},r,/^$/),y(n,"handler",(function(n){var r=n.index;t===b.keys.RIGHT&&(r+=n.length+1);var i=this.quill.getLeaf(r);return!(o(i,1)[0]instanceof f.default.Embed)||(t===b.keys.LEFT?e?this.quill.setSelection(n.index-1,n.length+1,p.default.sources.USER):this.quill.setSelection(n.index-1,p.default.sources.USER):e?this.quill.setSelection(n.index,n.length+1,p.default.sources.USER):this.quill.setSelection(n.index+n.length+1,p.default.sources.USER),!1)})),n}function E(t,e){if(!(0===t.index||this.quill.getLength()<=1)){var n=this.quill.getLine(t.index),r=o(n,1)[0],i={};if(0===e.offset){var a=this.quill.getLine(t.index-1),l=o(a,1)[0];if(null!=l&&l.length()>1){var s=r.formats(),u=this.quill.getFormat(t.index-1,1);i=c.default.attributes.diff(s,u)||{}}}var f=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;this.quill.deleteText(t.index-f,f,p.default.sources.USER),Object.keys(i).length>0&&this.quill.formatLine(t.index-f,f,i,p.default.sources.USER),this.quill.focus()}}function O(t,e){var n=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(t.index>=this.quill.getLength()-n)){var r={},i=0,a=this.quill.getLine(t.index),l=o(a,1)[0];if(e.offset>=l.length()-1){var s=this.quill.getLine(t.index+1),u=o(s,1)[0];if(u){var f=l.formats(),h=this.quill.getFormat(t.index,1);r=c.default.attributes.diff(f,h)||{},i=u.length()}}this.quill.deleteText(t.index,n,p.default.sources.USER),Object.keys(r).length>0&&this.quill.formatLine(t.index+i-1,n,r,p.default.sources.USER)}}function w(t){var e=this.quill.getLines(t),n={};if(e.length>1){var r=e[0].formats(),o=e[e.length-1].formats();n=c.default.attributes.diff(o,r)||{}}this.quill.deleteText(t,p.default.sources.USER),Object.keys(n).length>0&&this.quill.formatLine(t.index,1,n,p.default.sources.USER),this.quill.setSelection(t.index,p.default.sources.SILENT),this.quill.focus()}function C(t,e){var n=this;t.length>0&&this.quill.scroll.deleteAt(t.index,t.length);var r=Object.keys(e.format).reduce((function(t,n){return f.default.query(n,f.default.Scope.BLOCK)&&!Array.isArray(e.format[n])&&(t[n]=e.format[n]),t}),{});this.quill.insertText(t.index,"\n",r,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.focus(),Object.keys(e.format).forEach((function(t){null==r[t]&&(Array.isArray(e.format[t])||"link"!==t&&n.quill.format(t,e.format[t],p.default.sources.USER))}))}function k(t){return{key:b.keys.TAB,shiftKey:!t,format:{"code-block":!0},handler:function(e){var n=f.default.query("code-block"),r=e.index,i=e.length,a=this.quill.scroll.descendant(n,r),l=o(a,2),s=l[0],u=l[1];if(null!=s){var c=this.quill.getIndex(s),h=s.newlineIndex(u,!0)+1,d=s.newlineIndex(c+u+i),A=s.domNode.textContent.slice(h,d).split("\n");u=0,A.forEach((function(e,o){t?(s.insertAt(h+u,n.TAB),u+=n.TAB.length,0===o?r+=n.TAB.length:i+=n.TAB.length):e.startsWith(n.TAB)&&(s.deleteAt(h+u,n.TAB.length),u-=n.TAB.length,0===o?r-=n.TAB.length:i-=n.TAB.length),u+=e.length+1})),this.quill.update(p.default.sources.USER),this.quill.setSelection(r,i,p.default.sources.SILENT)}}}}function x(t){return{key:t[0].toUpperCase(),shortKey:!0,handler:function(e,n){this.quill.format(t,!n.format[t],p.default.sources.USER)}}}function _(t){if("string"===typeof t||"number"===typeof t)return _({key:t});if("object"===("undefined"===typeof t?"undefined":r(t))&&(t=(0,a.default)(t,!1)),"string"===typeof t.key)if(null!=b.keys[t.key.toUpperCase()])t.key=b.keys[t.key.toUpperCase()];else{if(1!==t.key.length)return null;t.key=t.key.toUpperCase().charCodeAt(0)}return t.shortKey&&(t[v]=t.shortKey,delete t.shortKey),t}b.keys={BACKSPACE:8,TAB:9,ENTER:13,ESCAPE:27,LEFT:37,UP:38,RIGHT:39,DOWN:40,DELETE:46},b.DEFAULTS={bindings:{bold:x("bold"),italic:x("italic"),underline:x("underline"),indent:{key:b.keys.TAB,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","+1",p.default.sources.USER)}},outdent:{key:b.keys.TAB,shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){if(e.collapsed&&0!==e.offset)return!0;this.quill.format("indent","-1",p.default.sources.USER)}},"outdent backspace":{key:b.keys.BACKSPACE,collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){null!=e.format.indent?this.quill.format("indent","-1",p.default.sources.USER):null!=e.format.list&&this.quill.format("list",!1,p.default.sources.USER)}},"indent code-block":k(!0),"outdent code-block":k(!1),"remove tab":{key:b.keys.TAB,shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,p.default.sources.USER)}},tab:{key:b.keys.TAB,handler:function(t){this.quill.history.cutoff();var e=(new u.default).retain(t.index).delete(t.length).insert("\t");this.quill.updateContents(e,p.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,p.default.sources.SILENT)}},"list empty enter":{key:b.keys.ENTER,collapsed:!0,format:["list"],empty:!0,handler:function(t,e){this.quill.format("list",!1,p.default.sources.USER),e.format.indent&&this.quill.format("indent",!1,p.default.sources.USER)}},"checklist enter":{key:b.keys.ENTER,collapsed:!0,format:{list:"checked"},handler:function(t){var e=this.quill.getLine(t.index),n=o(e,2),r=n[0],i=n[1],a=(0,s.default)({},r.formats(),{list:"checked"}),l=(new u.default).retain(t.index).insert("\n",a).retain(r.length()-i-1).retain(1,{list:"unchecked"});this.quill.updateContents(l,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:b.keys.ENTER,collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var n=this.quill.getLine(t.index),r=o(n,2),i=r[0],a=r[1],l=(new u.default).retain(t.index).insert("\n",e.format).retain(i.length()-a-1).retain(1,{header:null});this.quill.updateContents(l,p.default.sources.USER),this.quill.setSelection(t.index+1,p.default.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:" ",collapsed:!0,format:{list:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){var n=e.prefix.length,r=this.quill.getLine(t.index),i=o(r,2),a=i[0],l=i[1];if(l>n)return!0;var s=void 0;switch(e.prefix.trim()){case"[]":case"[ ]":s="unchecked";break;case"[x]":s="checked";break;case"-":case"*":s="bullet";break;default:s="ordered"}this.quill.insertText(t.index," ",p.default.sources.USER),this.quill.history.cutoff();var c=(new u.default).retain(t.index-l).delete(n+1).retain(a.length()-2-l).retain(1,{list:s});this.quill.updateContents(c,p.default.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-n,p.default.sources.SILENT)}},"code exit":{key:b.keys.ENTER,collapsed:!0,format:["code-block"],prefix:/\n\n$/,suffix:/^\s+$/,handler:function(t){var e=this.quill.getLine(t.index),n=o(e,2),r=n[0],i=n[1],a=(new u.default).retain(t.index+r.length()-i-2).retain(1,{"code-block":null}).delete(1);this.quill.updateContents(a,p.default.sources.USER)}},"embed left":m(b.keys.LEFT,!1),"embed left shift":m(b.keys.LEFT,!0),"embed right":m(b.keys.RIGHT,!1),"embed right shift":m(b.keys.RIGHT,!0)}},e.default=b,e.SHORTKEY=v},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),a=s(n(0)),l=s(n(7));function s(t){return t&&t.__esModule?t:{default:t}}var u=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return r.selection=n,r.textNode=document.createTextNode(e.CONTENTS),r.domNode.appendChild(r.textNode),r._length=0,r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,null,[{key:"value",value:function(){}}]),i(e,[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(t,n){if(0!==this._length)return o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n);for(var r=this,i=0;null!=r&&r.statics.scope!==a.default.Scope.BLOCK_BLOT;)i+=r.offset(r.parent),r=r.parent;null!=r&&(this._length=e.CONTENTS.length,r.optimize(),r.formatAt(i,e.CONTENTS.length,t,n),this._length=0)}},{key:"index",value:function(t,n){return t===this.textNode?0:o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"index",this).call(this,t,n)}},{key:"length",value:function(){return this._length}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(!this.selection.composing&&null!=this.parent){var t=this.textNode,n=this.selection.getNativeRange(),o=void 0,i=void 0,s=void 0;if(null!=n&&n.start.node===t&&n.end.node===t){var u=[t,n.start.offset,n.end.offset];o=u[0],i=u[1],s=u[2]}for(;null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);if(this.textNode.data!==e.CONTENTS){var c=this.textNode.data.split(e.CONTENTS).join("");this.next instanceof l.default?(o=this.next.domNode,this.next.insertAt(0,c),this.textNode.data=e.CONTENTS):(this.textNode.data=c,this.parent.insertBefore(a.default.create(this.textNode),this),this.textNode=document.createTextNode(e.CONTENTS),this.domNode.appendChild(this.textNode))}if(this.remove(),null!=i){var f=[i,s].map((function(t){return Math.max(0,Math.min(o.data.length,t-1))})),p=r(f,2);return i=p[0],s=p[1],{startNode:o,startOffset:i,endNode:o,endOffset:s}}}}},{key:"update",value:function(t,e){var n=this;if(t.some((function(t){return"characterData"===t.type&&t.target===n.textNode}))){var r=this.restore();r&&(e.range=r)}}},{key:"value",value:function(){return""}}]),e}(a.default.Embed);u.blotName="cursor",u.className="ql-cursor",u.tagName="span",u.CONTENTS="\ufeff",e.default=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=a(n(0)),o=n(4),i=a(o);function a(t){return t&&t.__esModule?t:{default:t}}function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var u=function(t){function e(){return l(this,e),s(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(r.default.Container);u.allowedChildren=[i.default,o.BlockEmbed,u],e.default=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ColorStyle=e.ColorClass=e.ColorAttributor=void 0;var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(0),a=(r=i)&&r.__esModule?r:{default:r};function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var u=function(t){function e(){return l(this,e),s(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"value",value:function(t){var n=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t);return n.startsWith("rgb(")?(n=n.replace(/^[^\d]+/,"").replace(/[^\d]+$/,""),"#"+n.split(",").map((function(t){return("00"+parseInt(t).toString(16)).slice(-2)})).join("")):n}}]),e}(a.default.Attributor.Style),c=new a.default.Attributor.Class("color","ql-color",{scope:a.default.Scope.INLINE}),f=new u("color","color",{scope:a.default.Scope.INLINE});e.ColorAttributor=u,e.ColorClass=c,e.ColorStyle=f},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.sanitize=e.default=void 0;var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(6);function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var u=function(t){function e(){return l(this,e),s(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"format",value:function(t,n){if(t!==this.statics.blotName||!n)return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n);n=this.constructor.sanitize(n),this.domNode.setAttribute("href",n)}}],[{key:"create",value:function(t){var n=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return t=this.sanitize(t),n.setAttribute("href",t),n.setAttribute("rel","noopener noreferrer"),n.setAttribute("target","_blank"),n}},{key:"formats",value:function(t){return t.getAttribute("href")}},{key:"sanitize",value:function(t){return c(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}}]),e}(((r=a)&&r.__esModule?r:{default:r}).default);function c(t,e){var n=document.createElement("a");n.href=t;var r=n.href.slice(0,n.href.indexOf(":"));return e.indexOf(r)>-1}u.blotName="link",u.tagName="A",u.SANITIZED_URL="about:blank",u.PROTOCOL_WHITELIST=["http","https","mailto","tel"],e.default=u,e.sanitize=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=l(n(23)),a=l(n(107));function l(t){return t&&t.__esModule?t:{default:t}}var s=0;function u(t,e){t.setAttribute(e,!("true"===t.getAttribute(e)))}var c=function(){function t(e){var n=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.select=e,this.container=document.createElement("span"),this.buildPicker(),this.select.style.display="none",this.select.parentNode.insertBefore(this.container,this.select),this.label.addEventListener("mousedown",(function(){n.togglePicker()})),this.label.addEventListener("keydown",(function(t){switch(t.keyCode){case i.default.keys.ENTER:n.togglePicker();break;case i.default.keys.ESCAPE:n.escape(),t.preventDefault()}})),this.select.addEventListener("change",this.update.bind(this))}return o(t,[{key:"togglePicker",value:function(){this.container.classList.toggle("ql-expanded"),u(this.label,"aria-expanded"),u(this.options,"aria-hidden")}},{key:"buildItem",value:function(t){var e=this,n=document.createElement("span");return n.tabIndex="0",n.setAttribute("role","button"),n.classList.add("ql-picker-item"),t.hasAttribute("value")&&n.setAttribute("data-value",t.getAttribute("value")),t.textContent&&n.setAttribute("data-label",t.textContent),n.addEventListener("click",(function(){e.selectItem(n,!0)})),n.addEventListener("keydown",(function(t){switch(t.keyCode){case i.default.keys.ENTER:e.selectItem(n,!0),t.preventDefault();break;case i.default.keys.ESCAPE:e.escape(),t.preventDefault()}})),n}},{key:"buildLabel",value:function(){var t=document.createElement("span");return t.classList.add("ql-picker-label"),t.innerHTML=a.default,t.tabIndex="0",t.setAttribute("role","button"),t.setAttribute("aria-expanded","false"),this.container.appendChild(t),t}},{key:"buildOptions",value:function(){var t=this,e=document.createElement("span");e.classList.add("ql-picker-options"),e.setAttribute("aria-hidden","true"),e.tabIndex="-1",e.id="ql-picker-options-"+s,s+=1,this.label.setAttribute("aria-controls",e.id),this.options=e,[].slice.call(this.select.options).forEach((function(n){var r=t.buildItem(n);e.appendChild(r),!0===n.selected&&t.selectItem(r)})),this.container.appendChild(e)}},{key:"buildPicker",value:function(){var t=this;[].slice.call(this.select.attributes).forEach((function(e){t.container.setAttribute(e.name,e.value)})),this.container.classList.add("ql-picker"),this.label=this.buildLabel(),this.buildOptions()}},{key:"escape",value:function(){var t=this;this.close(),setTimeout((function(){return t.label.focus()}),1)}},{key:"close",value:function(){this.container.classList.remove("ql-expanded"),this.label.setAttribute("aria-expanded","false"),this.options.setAttribute("aria-hidden","true")}},{key:"selectItem",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=this.container.querySelector(".ql-selected");if(t!==n&&(null!=n&&n.classList.remove("ql-selected"),null!=t&&(t.classList.add("ql-selected"),this.select.selectedIndex=[].indexOf.call(t.parentNode.children,t),t.hasAttribute("data-value")?this.label.setAttribute("data-value",t.getAttribute("data-value")):this.label.removeAttribute("data-value"),t.hasAttribute("data-label")?this.label.setAttribute("data-label",t.getAttribute("data-label")):this.label.removeAttribute("data-label"),e))){if("function"===typeof Event)this.select.dispatchEvent(new Event("change"));else if("object"===("undefined"===typeof Event?"undefined":r(Event))){var o=document.createEvent("Event");o.initEvent("change",!0,!0),this.select.dispatchEvent(o)}this.close()}}},{key:"update",value:function(){var t=void 0;if(this.select.selectedIndex>-1){var e=this.container.querySelector(".ql-picker-options").children[this.select.selectedIndex];t=this.select.options[this.select.selectedIndex],this.selectItem(e)}else this.selectItem(null);var n=null!=t&&t!==this.select.querySelector("option[selected]");this.label.classList.toggle("ql-active",n)}}]),t}();e.default=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=g(n(0)),o=g(n(5)),i=n(4),a=g(i),l=g(n(16)),s=g(n(25)),u=g(n(24)),c=g(n(35)),f=g(n(6)),p=g(n(22)),h=g(n(7)),d=g(n(55)),A=g(n(42)),y=g(n(23));function g(t){return t&&t.__esModule?t:{default:t}}o.default.register({"blots/block":a.default,"blots/block/embed":i.BlockEmbed,"blots/break":l.default,"blots/container":s.default,"blots/cursor":u.default,"blots/embed":c.default,"blots/inline":f.default,"blots/scroll":p.default,"blots/text":h.default,"modules/clipboard":d.default,"modules/history":A.default,"modules/keyboard":y.default}),r.default.register(a.default,l.default,u.default,f.default,p.default,h.default),e.default=o.default},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(1),o=function(){function t(t){this.domNode=t,this.domNode[r.DATA_KEY]={blot:this}}return Object.defineProperty(t.prototype,"statics",{get:function(){return this.constructor},enumerable:!0,configurable:!0}),t.create=function(t){if(null==this.tagName)throw new r.ParchmentError("Blot definition missing tagName");var e;return Array.isArray(this.tagName)?("string"===typeof t&&(t=t.toUpperCase(),parseInt(t).toString()===t&&(t=parseInt(t))),e="number"===typeof t?document.createElement(this.tagName[t-1]):this.tagName.indexOf(t)>-1?document.createElement(t):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e},t.prototype.attach=function(){null!=this.parent&&(this.scroll=this.parent.scroll)},t.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return r.create(t)},t.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),delete this.domNode[r.DATA_KEY]},t.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},t.prototype.formatAt=function(t,e,n,o){var i=this.isolate(t,e);if(null!=r.query(n,r.Scope.BLOT)&&o)i.wrap(n,o);else if(null!=r.query(n,r.Scope.ATTRIBUTE)){var a=r.create(this.statics.scope);i.wrap(a),a.format(n,o)}},t.prototype.insertAt=function(t,e,n){var o=null==n?r.create("text",e):r.create(e,n),i=this.split(t);this.parent.insertBefore(o,i)},t.prototype.insertInto=function(t,e){void 0===e&&(e=null),null!=this.parent&&this.parent.children.remove(this);var n=null;t.children.insertBefore(this,e),null!=e&&(n=e.domNode),this.domNode.parentNode==t.domNode&&this.domNode.nextSibling==n||t.domNode.insertBefore(this.domNode,n),this.parent=t,this.attach()},t.prototype.isolate=function(t,e){var n=this.split(t);return n.split(e),n},t.prototype.length=function(){return 1},t.prototype.offset=function(t){return void 0===t&&(t=this.parent),null==this.parent||this==t?0:this.parent.children.offset(this)+this.parent.offset(t)},t.prototype.optimize=function(t){null!=this.domNode[r.DATA_KEY]&&delete this.domNode[r.DATA_KEY].mutations},t.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},t.prototype.replace=function(t){null!=t.parent&&(t.parent.insertBefore(this,t.next),t.remove())},t.prototype.replaceWith=function(t,e){var n="string"===typeof t?r.create(t,e):t;return n.replace(this),n},t.prototype.split=function(t,e){return 0===t?this:this.next},t.prototype.update=function(t,e){},t.prototype.wrap=function(t,e){var n="string"===typeof t?r.create(t,e):t;return null!=this.parent&&this.parent.insertBefore(n,this.next),n.appendChild(this),n},t.blotName="abstract",t}();e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(12),o=n(32),i=n(33),a=n(1),l=function(){function t(t){this.attributes={},this.domNode=t,this.build()}return t.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},t.prototype.build=function(){var t=this;this.attributes={};var e=r.default.keys(this.domNode),n=o.default.keys(this.domNode),l=i.default.keys(this.domNode);e.concat(n).concat(l).forEach((function(e){var n=a.query(e,a.Scope.ATTRIBUTE);n instanceof r.default&&(t.attributes[n.attrName]=n)}))},t.prototype.copy=function(t){var e=this;Object.keys(this.attributes).forEach((function(n){var r=e.attributes[n].value(e.domNode);t.format(n,r)}))},t.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach((function(t){e.attributes[t].remove(e.domNode)})),this.attributes={}},t.prototype.values=function(){var t=this;return Object.keys(this.attributes).reduce((function(e,n){return e[n]=t.attributes[n].value(t.domNode),e}),{})},t}();e.default=l},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();function o(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter((function(t){return 0===t.indexOf(e+"-")}))}Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map((function(t){return t.split("-").slice(0,-1).join("-")}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add(this.keyName+"-"+e),!0)},e.prototype.remove=function(t){o(t,this.keyName).forEach((function(e){t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute("class")},e.prototype.value=function(t){var e=(o(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},e}(n(12).default);e.default=i},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();function o(t){var e=t.split("-"),n=e.slice(1).map((function(t){return t[0].toUpperCase()+t.slice(1)})).join("");return e[0]+n}Object.defineProperty(e,"__esModule",{value:!0});var i=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.keys=function(t){return(t.getAttribute("style")||"").split(";").map((function(t){return t.split(":")[0].trim()}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[o(this.keyName)]=e,!0)},e.prototype.remove=function(t){t.style[o(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},e.prototype.value=function(t){var e=t.style[o(this.keyName)];return this.canAdd(t,e)?e:""},e}(n(12).default);e.default=i},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function(){function t(e,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.options=n,this.modules={}}return r(t,[{key:"init",value:function(){var t=this;Object.keys(this.options.modules).forEach((function(e){null==t.modules[e]&&t.addModule(e)}))}},{key:"addModule",value:function(t){var e=this.quill.constructor.import("modules/"+t);return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}]),t}();o.DEFAULTS={modules:{}},o.themes={default:o},e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=a(n(0)),i=a(n(7));function a(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function e(t){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var n=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return n.contentNode=document.createElement("span"),n.contentNode.setAttribute("contenteditable",!1),[].slice.call(n.domNode.childNodes).forEach((function(t){n.contentNode.appendChild(t)})),n.leftGuard=document.createTextNode("\ufeff"),n.rightGuard=document.createTextNode("\ufeff"),n.domNode.appendChild(n.leftGuard),n.domNode.appendChild(n.contentNode),n.domNode.appendChild(n.rightGuard),n}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),r(e,[{key:"index",value:function(t,n){return t===this.leftGuard?0:t===this.rightGuard?1:function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"index",this).call(this,t,n)}},{key:"restore",value:function(t){var e=void 0,n=void 0,r=t.data.split("\ufeff").join("");if(t===this.leftGuard)if(this.prev instanceof i.default){var a=this.prev.length();this.prev.insertAt(a,r),e={startNode:this.prev.domNode,startOffset:a+r.length}}else n=document.createTextNode(r),this.parent.insertBefore(o.default.create(n),this),e={startNode:n,startOffset:r.length};else t===this.rightGuard&&(this.next instanceof i.default?(this.next.insertAt(0,r),e={startNode:this.next.domNode,startOffset:r.length}):(n=document.createTextNode(r),this.parent.insertBefore(o.default.create(n),this.next),e={startNode:n,startOffset:r.length}));return t.data="\ufeff",e}},{key:"update",value:function(t,e){var n=this;t.forEach((function(t){if("characterData"===t.type&&(t.target===n.leftGuard||t.target===n.rightGuard)){var r=n.restore(t.target);r&&(e.range=r)}}))}}]),e}(o.default.Embed);e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AlignStyle=e.AlignClass=e.AlignAttribute=void 0;var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r},a={scope:i.default.Scope.BLOCK,whitelist:["right","center","justify"]},l=new i.default.Attributor.Attribute("align","align",a),s=new i.default.Attributor.Class("align","ql-align",a),u=new i.default.Attributor.Style("align","text-align",a);e.AlignAttribute=l,e.AlignClass=s,e.AlignStyle=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.BackgroundStyle=e.BackgroundClass=void 0;var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r},a=n(26),l=new i.default.Attributor.Class("background","ql-bg",{scope:i.default.Scope.INLINE}),s=new a.ColorAttributor("background","background-color",{scope:i.default.Scope.INLINE});e.BackgroundClass=l,e.BackgroundStyle=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.DirectionStyle=e.DirectionClass=e.DirectionAttribute=void 0;var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r},a={scope:i.default.Scope.BLOCK,whitelist:["rtl"]},l=new i.default.Attributor.Attribute("direction","dir",a),s=new i.default.Attributor.Class("direction","ql-direction",a),u=new i.default.Attributor.Style("direction","direction",a);e.DirectionAttribute=l,e.DirectionClass=s,e.DirectionStyle=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.FontClass=e.FontStyle=void 0;var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(0),a=(r=i)&&r.__esModule?r:{default:r};function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var u={scope:a.default.Scope.INLINE,whitelist:["serif","monospace"]},c=new a.default.Attributor.Class("font","ql-font",u),f=new(function(t){function e(){return l(this,e),s(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"value",value:function(t){return function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t).replace(/["']/g,"")}}]),e}(a.default.Attributor.Style))("font","font-family",u);e.FontStyle=f,e.FontClass=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.SizeStyle=e.SizeClass=void 0;var r,o=n(0),i=(r=o)&&r.__esModule?r:{default:r},a=new i.default.Attributor.Class("size","ql-size",{scope:i.default.Scope.INLINE,whitelist:["small","large","huge"]}),l=new i.default.Attributor.Style("size","font-size",{scope:i.default.Scope.INLINE,whitelist:["10px","18px","32px"]});e.SizeClass=a,e.SizeStyle=l},function(t,e,n){"use strict";t.exports={align:{"":n(76),center:n(77),right:n(78),justify:n(79)},background:n(80),blockquote:n(81),bold:n(82),clean:n(83),code:n(58),"code-block":n(58),color:n(84),direction:{"":n(85),rtl:n(86)},float:{center:n(87),full:n(88),left:n(89),right:n(90)},formula:n(91),header:{1:n(92),2:n(93)},italic:n(94),image:n(95),indent:{"+1":n(96),"-1":n(97)},link:n(98),list:{ordered:n(99),bullet:n(100),check:n(101)},script:{sub:n(102),super:n(103)},strike:n(104),underline:n(105),video:n(106)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.getLastChangeIndex=e.default=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=a(n(0)),i=a(n(5));function a(t){return t&&t.__esModule?t:{default:t}}var l=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(i.default.events.EDITOR_CHANGE,(function(t,e,n,o){t!==i.default.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&o!==i.default.sources.USER?r.transform(e):r.record(e,n))})),r.quill.keyboard.addBinding({key:"Z",shortKey:!0},r.undo.bind(r)),r.quill.keyboard.addBinding({key:"Z",shortKey:!0,shiftKey:!0},r.redo.bind(r)),/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"Y",shortKey:!0},r.redo.bind(r)),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),r(e,[{key:"change",value:function(t,e){if(0!==this.stack[t].length){var n=this.stack[t].pop();this.stack[e].push(n),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(n[t],i.default.sources.USER),this.ignoreChange=!1;var r=s(n[t]);this.quill.setSelection(r)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(t,e){if(0!==t.ops.length){this.stack.redo=[];var n=this.quill.getContents().diff(e),r=Date.now();if(this.lastRecorded+this.options.delay>r&&this.stack.undo.length>0){var o=this.stack.undo.pop();n=n.compose(o.undo),t=o.redo.compose(t)}else this.lastRecorded=r;this.stack.undo.push({redo:t,undo:n}),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift()}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(t){this.stack.undo.forEach((function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)})),this.stack.redo.forEach((function(e){e.undo=t.transform(e.undo,!0),e.redo=t.transform(e.redo,!0)}))}},{key:"undo",value:function(){this.change("undo","redo")}}]),e}(a(n(9)).default);function s(t){var e=t.reduce((function(t,e){return t+=e.delete||0}),0),n=t.length()-e;return function(t){var e=t.ops[t.ops.length-1];return null!=e&&(null!=e.insert?"string"===typeof e.insert&&e.insert.endsWith("\n"):null!=e.attributes&&Object.keys(e.attributes).some((function(t){return null!=o.default.query(t,o.default.Scope.BLOCK)})))}(t)&&(n-=1),n}l.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1},e.default=l,e.getLastChangeIndex=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BaseTooltip=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=h(n(3)),i=h(n(2)),a=h(n(8)),l=h(n(23)),s=h(n(34)),u=h(n(59)),c=h(n(60)),f=h(n(28)),p=h(n(61));function h(t){return t&&t.__esModule?t:{default:t}}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function A(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function y(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var g=[!1,"center","right","justify"],v=["#000000","#e60000","#ff9900","#ffff00","#008a00","#0066cc","#9933ff","#ffffff","#facccc","#ffebcc","#ffffcc","#cce8cc","#cce0f5","#ebd6ff","#bbbbbb","#f06666","#ffc266","#ffff66","#66b966","#66a3e0","#c285ff","#888888","#a10000","#b26b00","#b2b200","#006100","#0047b2","#6b24b2","#444444","#5c0000","#663d00","#666600","#003700","#002966","#3d1466"],b=[!1,"serif","monospace"],m=["1","2","3",!1],E=["small",!1,"large","huge"],O=function(t){function e(t,n){d(this,e);var r=A(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return t.emitter.listenDOM("click",document.body,(function e(n){if(!document.body.contains(t.root))return document.body.removeEventListener("click",e);null==r.tooltip||r.tooltip.root.contains(n.target)||document.activeElement===r.tooltip.textbox||r.quill.hasFocus()||r.tooltip.hide(),null!=r.pickers&&r.pickers.forEach((function(t){t.container.contains(n.target)||t.close()}))})),r}return y(e,t),r(e,[{key:"addModule",value:function(t){var n=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"addModule",this).call(this,t);return"toolbar"===t&&this.extendToolbar(n),n}},{key:"buildButtons",value:function(t,e){t.forEach((function(t){(t.getAttribute("class")||"").split(/\s+/).forEach((function(n){if(n.startsWith("ql-")&&(n=n.slice("ql-".length),null!=e[n]))if("direction"===n)t.innerHTML=e[n][""]+e[n].rtl;else if("string"===typeof e[n])t.innerHTML=e[n];else{var r=t.value||"";null!=r&&e[n][r]&&(t.innerHTML=e[n][r])}}))}))}},{key:"buildPickers",value:function(t,e){var n=this;this.pickers=t.map((function(t){if(t.classList.contains("ql-align"))return null==t.querySelector("option")&&C(t,g),new c.default(t,e.align);if(t.classList.contains("ql-background")||t.classList.contains("ql-color")){var n=t.classList.contains("ql-background")?"background":"color";return null==t.querySelector("option")&&C(t,v,"background"===n?"#ffffff":"#000000"),new u.default(t,e[n])}return null==t.querySelector("option")&&(t.classList.contains("ql-font")?C(t,b):t.classList.contains("ql-header")?C(t,m):t.classList.contains("ql-size")&&C(t,E)),new f.default(t)})),this.quill.on(a.default.events.EDITOR_CHANGE,(function(){n.pickers.forEach((function(t){t.update()}))}))}}]),e}(s.default);O.DEFAULTS=(0,o.default)(!0,{},s.default.DEFAULTS,{modules:{toolbar:{handlers:{formula:function(){this.quill.theme.tooltip.edit("formula")},image:function(){var t=this,e=this.container.querySelector("input.ql-image[type=file]");null==e&&((e=document.createElement("input")).setAttribute("type","file"),e.setAttribute("accept","image/png, image/gif, image/jpeg, image/bmp, image/x-icon"),e.classList.add("ql-image"),e.addEventListener("change",(function(){if(null!=e.files&&null!=e.files[0]){var n=new FileReader;n.onload=function(n){var r=t.quill.getSelection(!0);t.quill.updateContents((new i.default).retain(r.index).delete(r.length).insert({image:n.target.result}),a.default.sources.USER),t.quill.setSelection(r.index+1,a.default.sources.SILENT),e.value=""},n.readAsDataURL(e.files[0])}})),this.container.appendChild(e)),e.click()},video:function(){this.quill.theme.tooltip.edit("video")}}}}});var w=function(t){function e(t,n){d(this,e);var r=A(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.textbox=r.root.querySelector('input[type="text"]'),r.listen(),r}return y(e,t),r(e,[{key:"listen",value:function(){var t=this;this.textbox.addEventListener("keydown",(function(e){l.default.match(e,"enter")?(t.save(),e.preventDefault()):l.default.match(e,"escape")&&(t.cancel(),e.preventDefault())}))}},{key:"cancel",value:function(){this.hide()}},{key:"edit",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"link",e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.root.classList.remove("ql-hidden"),this.root.classList.add("ql-editing"),null!=e?this.textbox.value=e:t!==this.root.getAttribute("data-mode")&&(this.textbox.value=""),this.position(this.quill.getBounds(this.quill.selection.savedRange)),this.textbox.select(),this.textbox.setAttribute("placeholder",this.textbox.getAttribute("data-"+t)||""),this.root.setAttribute("data-mode",t)}},{key:"restoreFocus",value:function(){var t=this.quill.scrollingContainer.scrollTop;this.quill.focus(),this.quill.scrollingContainer.scrollTop=t}},{key:"save",value:function(){var t=this.textbox.value;switch(this.root.getAttribute("data-mode")){case"link":var e=this.quill.root.scrollTop;this.linkRange?(this.quill.formatText(this.linkRange,"link",t,a.default.sources.USER),delete this.linkRange):(this.restoreFocus(),this.quill.format("link",t,a.default.sources.USER)),this.quill.root.scrollTop=e;break;case"video":t=function(t){var e=t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtube\.com\/watch.*v=([a-zA-Z0-9_-]+)/)||t.match(/^(?:(https?):\/\/)?(?:(?:www|m)\.)?youtu\.be\/([a-zA-Z0-9_-]+)/);return e?(e[1]||"https")+"://www.youtube.com/embed/"+e[2]+"?showinfo=0":(e=t.match(/^(?:(https?):\/\/)?(?:www\.)?vimeo\.com\/(\d+)/))?(e[1]||"https")+"://player.vimeo.com/video/"+e[2]+"/":t}(t);case"formula":if(!t)break;var n=this.quill.getSelection(!0);if(null!=n){var r=n.index+n.length;this.quill.insertEmbed(r,this.root.getAttribute("data-mode"),t,a.default.sources.USER),"formula"===this.root.getAttribute("data-mode")&&this.quill.insertText(r+1," ",a.default.sources.USER),this.quill.setSelection(r+2,a.default.sources.USER)}}this.textbox.value="",this.hide()}}]),e}(p.default);function C(t,e){var n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];e.forEach((function(e){var r=document.createElement("option");e===n?r.setAttribute("selected","selected"):r.setAttribute("value",e),t.appendChild(r)}))}e.BaseTooltip=w,e.default=O},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(){this.head=this.tail=null,this.length=0}return t.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];this.insertBefore(t[0],null),t.length>1&&this.append.apply(this,t.slice(1))},t.prototype.contains=function(t){for(var e,n=this.iterator();e=n();)if(e===t)return!0;return!1},t.prototype.insertBefore=function(t,e){t&&(t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},t.prototype.offset=function(t){for(var e=0,n=this.head;null!=n;){if(n===t)return e;e+=n.length(),n=n.next}return-1},t.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)},t.prototype.iterator=function(t){return void 0===t&&(t=this.head),function(){var e=t;return null!=t&&(t=t.next),e}},t.prototype.find=function(t,e){void 0===e&&(e=!1);for(var n,r=this.iterator();n=r();){var o=n.length();if(t<o||e&&t===o&&(null==n.next||0!==n.next.length()))return[n,t];t-=o}return[null,0]},t.prototype.forEach=function(t){for(var e,n=this.iterator();e=n();)t(e)},t.prototype.forEachAt=function(t,e,n){if(!(e<=0))for(var r,o=this.find(t),i=o[0],a=t-o[1],l=this.iterator(i);(r=l())&&a<t+e;){var s=r.length();t>a?n(r,t-a,Math.min(e,a+s-t)):n(r,0,Math.min(s,t+e-a)),a+=s}},t.prototype.map=function(t){return this.reduce((function(e,n){return e.push(t(n)),e}),[])},t.prototype.reduce=function(t,e){for(var n,r=this.iterator();n=r();)e=t(e,n);return e},t}();e.default=r},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(17),i=n(1),a={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},l=function(t){function e(e){var n=t.call(this,e)||this;return n.scroll=n,n.observer=new MutationObserver((function(t){n.update(t)})),n.observer.observe(n.domNode,a),n.attach(),n}return r(e,t),e.prototype.detach=function(){t.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(e,n){this.update(),0===e&&n===this.length()?this.children.forEach((function(t){t.remove()})):t.prototype.deleteAt.call(this,e,n)},e.prototype.formatAt=function(e,n,r,o){this.update(),t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.insertAt=function(e,n,r){this.update(),t.prototype.insertAt.call(this,e,n,r)},e.prototype.optimize=function(e,n){var r=this;void 0===e&&(e=[]),void 0===n&&(n={}),t.prototype.optimize.call(this,n);for(var a=[].slice.call(this.observer.takeRecords());a.length>0;)e.push(a.pop());for(var l=function t(e,n){void 0===n&&(n=!0),null!=e&&e!==r&&null!=e.domNode.parentNode&&(null==e.domNode[i.DATA_KEY].mutations&&(e.domNode[i.DATA_KEY].mutations=[]),n&&t(e.parent))},s=function t(e){null!=e.domNode[i.DATA_KEY]&&null!=e.domNode[i.DATA_KEY].mutations&&(e instanceof o.default&&e.children.forEach(t),e.optimize(n))},u=e,c=0;u.length>0;c+=1){if(c>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(u.forEach((function(t){var e=i.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(l(i.find(t.previousSibling,!1)),[].forEach.call(t.addedNodes,(function(t){var e=i.find(t,!1);l(e,!1),e instanceof o.default&&e.children.forEach((function(t){l(t,!1)}))}))):"attributes"===t.type&&l(e.prev)),l(e))})),this.children.forEach(s),a=(u=[].slice.call(this.observer.takeRecords())).slice();a.length>0;)e.push(a.pop())}},e.prototype.update=function(e,n){var r=this;void 0===n&&(n={}),(e=e||this.observer.takeRecords()).map((function(t){var e=i.find(t.target,!0);return null==e?null:null==e.domNode[i.DATA_KEY].mutations?(e.domNode[i.DATA_KEY].mutations=[t],e):(e.domNode[i.DATA_KEY].mutations.push(t),null)})).forEach((function(t){null!=t&&t!==r&&null!=t.domNode[i.DATA_KEY]&&t.update(t.domNode[i.DATA_KEY].mutations||[],n)})),null!=this.domNode[i.DATA_KEY].mutations&&t.prototype.update.call(this,this.domNode[i.DATA_KEY].mutations,n),this.optimize(e,n)},e.blotName="scroll",e.defaultChild="block",e.scope=i.Scope.BLOCK_BLOT,e.tagName="DIV",e}(o.default);e.default=l},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(18),i=n(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.formats=function(n){if(n.tagName!==e.tagName)return t.formats.call(this,n)},e.prototype.format=function(n,r){var i=this;n!==this.statics.blotName||r?t.prototype.format.call(this,n,r):(this.children.forEach((function(t){t instanceof o.default||(t=t.wrap(e.blotName,!0)),i.attributes.copy(t)})),this.unwrap())},e.prototype.formatAt=function(e,n,r,o){null!=this.formats()[r]||i.query(r,i.Scope.ATTRIBUTE)?this.isolate(e,n).format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.optimize=function(n){t.prototype.optimize.call(this,n);var r=this.formats();if(0===Object.keys(r).length)return this.unwrap();var o=this.next;o instanceof e&&o.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var n in t)if(t[n]!==e[n])return!1;return!0}(r,o.formats())&&(o.moveChildren(this),o.remove())},e.blotName="inline",e.scope=i.Scope.INLINE_BLOT,e.tagName="SPAN",e}(o.default);e.default=a},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(18),i=n(1),a=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.formats=function(n){var r=i.query(e.blotName).tagName;if(n.tagName!==r)return t.formats.call(this,n)},e.prototype.format=function(n,r){null!=i.query(n,i.Scope.BLOCK)&&(n!==this.statics.blotName||r?t.prototype.format.call(this,n,r):this.replaceWith(e.blotName))},e.prototype.formatAt=function(e,n,r,o){null!=i.query(r,i.Scope.BLOCK)?this.format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.insertAt=function(e,n,r){if(null==r||null!=i.query(n,i.Scope.INLINE))t.prototype.insertAt.call(this,e,n,r);else{var o=this.split(e),a=i.create(n,r);o.parent.insertBefore(a,o)}},e.prototype.update=function(e,n){navigator.userAgent.match(/Trident/)?this.build():t.prototype.update.call(this,e,n)},e.blotName="block",e.scope=i.Scope.BLOCK_BLOT,e.tagName="P",e}(o.default);e.default=a},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.formats=function(t){},e.prototype.format=function(e,n){t.prototype.formatAt.call(this,0,this.length(),e,n)},e.prototype.formatAt=function(e,n,r,o){0===e&&n===this.length()?this.format(r,o):t.prototype.formatAt.call(this,e,n,r,o)},e.prototype.formats=function(){return this.statics.formats(this.domNode)},e}(n(19).default);e.default=o},function(t,e,n){"use strict";var r=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var o=n(19),i=n(1),a=function(t){function e(e){var n=t.call(this,e)||this;return n.text=n.statics.value(n.domNode),n}return r(e,t),e.create=function(t){return document.createTextNode(t)},e.value=function(t){var e=t.data;return e.normalize&&(e=e.normalize()),e},e.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},e.prototype.index=function(t,e){return this.domNode===t?e:-1},e.prototype.insertAt=function(e,n,r){null==r?(this.text=this.text.slice(0,e)+n+this.text.slice(e),this.domNode.data=this.text):t.prototype.insertAt.call(this,e,n,r)},e.prototype.length=function(){return this.text.length},e.prototype.optimize=function(n){t.prototype.optimize.call(this,n),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof e&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},e.prototype.position=function(t,e){return void 0===e&&(e=!1),[this.domNode,t]},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var n=i.create(this.domNode.splitText(t));return this.parent.insertBefore(n,this.next),this.text=this.statics.value(this.domNode),n},e.prototype.update=function(t,e){var n=this;t.some((function(t){return"characterData"===t.type&&t.target===n.domNode}))&&(this.text=this.statics.value(this.domNode))},e.prototype.value=function(){return this.text},e.blotName="text",e.scope=i.Scope.INLINE_BLOT,e}(o.default);e.default=a},function(t,e,n){"use strict";var r=document.createElement("div");if(r.classList.toggle("test-class",!1),r.classList.contains("test-class")){var o=DOMTokenList.prototype.toggle;DOMTokenList.prototype.toggle=function(t,e){return arguments.length>1&&!this.contains(t)===!e?e:o.call(this,t)}}String.prototype.startsWith||(String.prototype.startsWith=function(t,e){return e=e||0,this.substr(e,t.length)===t}),String.prototype.endsWith||(String.prototype.endsWith=function(t,e){var n=this.toString();("number"!==typeof e||!isFinite(e)||Math.floor(e)!==e||e>n.length)&&(e=n.length),e-=t.length;var r=n.indexOf(t,e);return-1!==r&&r===e}),Array.prototype.find||Object.defineProperty(Array.prototype,"find",{value:function(t){if(null===this)throw new TypeError("Array.prototype.find called on null or undefined");if("function"!==typeof t)throw new TypeError("predicate must be a function");for(var e,n=Object(this),r=n.length>>>0,o=arguments[1],i=0;i<r;i++)if(e=n[i],t.call(o,e,i,n))return e}}),document.addEventListener("DOMContentLoaded",(function(){document.execCommand("enableObjectResizing",!1,!1),document.execCommand("autoUrlDetect",!1,!1)}))},function(t,e){function n(t,e,a){if(t==e)return t?[[0,t]]:[];(a<0||t.length<a)&&(a=null);var s=o(t,e),u=t.substring(0,s);s=i(t=t.substring(s),e=e.substring(s));var c=t.substring(t.length-s),f=function(t,e){var a;if(!t)return[[1,e]];if(!e)return[[-1,t]];var l=t.length>e.length?t:e,s=t.length>e.length?e:t,u=l.indexOf(s);if(-1!=u)return a=[[1,l.substring(0,u)],[0,s],[1,l.substring(u+s.length)]],t.length>e.length&&(a[0][0]=a[2][0]=-1),a;if(1==s.length)return[[-1,t],[1,e]];var c=function(t,e){var n=t.length>e.length?t:e,r=t.length>e.length?e:t;if(n.length<4||2*r.length<n.length)return null;function a(t,e,n){for(var r,a,l,s,u=t.substring(n,n+Math.floor(t.length/4)),c=-1,f="";-1!=(c=e.indexOf(u,c+1));){var p=o(t.substring(n),e.substring(c)),h=i(t.substring(0,n),e.substring(0,c));f.length<h+p&&(f=e.substring(c-h,c)+e.substring(c,c+p),r=t.substring(0,n-h),a=t.substring(n+p),l=e.substring(0,c-h),s=e.substring(c+p))}return 2*f.length>=t.length?[r,a,l,s,f]:null}var l,s,u,c,f,p=a(n,r,Math.ceil(n.length/4)),h=a(n,r,Math.ceil(n.length/2));if(!p&&!h)return null;l=h?p&&p[4].length>h[4].length?p:h:p,t.length>e.length?(s=l[0],u=l[1],c=l[2],f=l[3]):(c=l[0],f=l[1],s=l[2],u=l[3]);var d=l[4];return[s,u,c,f,d]}(t,e);if(c){var f=c[0],p=c[1],h=c[2],d=c[3],A=c[4],y=n(f,h),g=n(p,d);return y.concat([[0,A]],g)}return function(t,e){for(var n=t.length,o=e.length,i=Math.ceil((n+o)/2),a=i,l=2*i,s=new Array(l),u=new Array(l),c=0;c<l;c++)s[c]=-1,u[c]=-1;s[a+1]=0,u[a+1]=0;for(var f=n-o,p=f%2!=0,h=0,d=0,A=0,y=0,g=0;g<i;g++){for(var v=-g+h;v<=g-d;v+=2){for(var b=a+v,m=(k=v==-g||v!=g&&s[b-1]<s[b+1]?s[b+1]:s[b-1]+1)-v;k<n&&m<o&&t.charAt(k)==e.charAt(m);)k++,m++;if(s[b]=k,k>n)d+=2;else if(m>o)h+=2;else if(p&&(w=a+f-v)>=0&&w<l&&-1!=u[w]){var E=n-u[w];if(k>=E)return r(t,e,k,m)}}for(var O=-g+A;O<=g-y;O+=2){for(var w=a+O,C=(E=O==-g||O!=g&&u[w-1]<u[w+1]?u[w+1]:u[w-1]+1)-O;E<n&&C<o&&t.charAt(n-E-1)==e.charAt(o-C-1);)E++,C++;if(u[w]=E,E>n)y+=2;else if(C>o)A+=2;else if(!p&&(b=a+f-O)>=0&&b<l&&-1!=s[b]){var k=s[b];if(m=a+k-b,k>=(E=n-E))return r(t,e,k,m)}}}return[[-1,t],[1,e]]}(t,e)}(t=t.substring(0,t.length-s),e=e.substring(0,e.length-s));return u&&f.unshift([0,u]),c&&f.push([0,c]),function t(e){e.push([0,""]);for(var n,r=0,a=0,l=0,s="",u="";r<e.length;)switch(e[r][0]){case 1:l++,u+=e[r][1],r++;break;case-1:a++,s+=e[r][1],r++;break;case 0:a+l>1?(0!==a&&0!==l&&(0!==(n=o(u,s))&&(r-a-l>0&&0==e[r-a-l-1][0]?e[r-a-l-1][1]+=u.substring(0,n):(e.splice(0,0,[0,u.substring(0,n)]),r++),u=u.substring(n),s=s.substring(n)),0!==(n=i(u,s))&&(e[r][1]=u.substring(u.length-n)+e[r][1],u=u.substring(0,u.length-n),s=s.substring(0,s.length-n))),0===a?e.splice(r-l,a+l,[1,u]):0===l?e.splice(r-a,a+l,[-1,s]):e.splice(r-a-l,a+l,[-1,s],[1,u]),r=r-a-l+(a?1:0)+(l?1:0)+1):0!==r&&0==e[r-1][0]?(e[r-1][1]+=e[r][1],e.splice(r,1)):r++,l=0,a=0,s="",u=""}""===e[e.length-1][1]&&e.pop();var c=!1;for(r=1;r<e.length-1;)0==e[r-1][0]&&0==e[r+1][0]&&(e[r][1].substring(e[r][1].length-e[r-1][1].length)==e[r-1][1]?(e[r][1]=e[r-1][1]+e[r][1].substring(0,e[r][1].length-e[r-1][1].length),e[r+1][1]=e[r-1][1]+e[r+1][1],e.splice(r-1,1),c=!0):e[r][1].substring(0,e[r+1][1].length)==e[r+1][1]&&(e[r-1][1]+=e[r+1][1],e[r][1]=e[r][1].substring(e[r+1][1].length)+e[r+1][1],e.splice(r+1,1),c=!0)),r++;c&&t(e)}(f),null!=a&&(f=function(t,e){var n=function(t,e){if(0===e)return[0,t];for(var n=0,r=0;r<t.length;r++){var o=t[r];if(-1===o[0]||0===o[0]){var i=n+o[1].length;if(e===i)return[r+1,t];if(e<i){t=t.slice();var a=e-n,l=[o[0],o[1].slice(0,a)],s=[o[0],o[1].slice(a)];return t.splice(r,1,l,s),[r+1,t]}n=i}}throw new Error("cursor_pos is out of bounds!")}(t,e),r=n[1],o=n[0],i=r[o],a=r[o+1];if(null==i)return t;if(0!==i[0])return t;if(null!=a&&i[1]+a[1]===a[1]+i[1])return r.splice(o,2,a,i),l(r,o,2);if(null!=a&&0===a[1].indexOf(i[1])){r.splice(o,2,[a[0],i[1]],[0,i[1]]);var s=a[1].slice(i[1].length);return s.length>0&&r.splice(o+2,0,[a[0],s]),l(r,o,3)}return t}(f,a)),f=function(t){for(var e=!1,n=function(t){return t.charCodeAt(0)>=56320&&t.charCodeAt(0)<=57343},r=2;r<t.length;r+=1)0===t[r-2][0]&&(o=t[r-2][1]).charCodeAt(o.length-1)>=55296&&o.charCodeAt(o.length-1)<=56319&&-1===t[r-1][0]&&n(t[r-1][1])&&1===t[r][0]&&n(t[r][1])&&(e=!0,t[r-1][1]=t[r-2][1].slice(-1)+t[r-1][1],t[r][1]=t[r-2][1].slice(-1)+t[r][1],t[r-2][1]=t[r-2][1].slice(0,-1));var o;if(!e)return t;var i=[];for(r=0;r<t.length;r+=1)t[r][1].length>0&&i.push(t[r]);return i}(f)}function r(t,e,r,o){var i=t.substring(0,r),a=e.substring(0,o),l=t.substring(r),s=e.substring(o),u=n(i,a),c=n(l,s);return u.concat(c)}function o(t,e){if(!t||!e||t.charAt(0)!=e.charAt(0))return 0;for(var n=0,r=Math.min(t.length,e.length),o=r,i=0;n<o;)t.substring(i,o)==e.substring(i,o)?i=n=o:r=o,o=Math.floor((r-n)/2+n);return o}function i(t,e){if(!t||!e||t.charAt(t.length-1)!=e.charAt(e.length-1))return 0;for(var n=0,r=Math.min(t.length,e.length),o=r,i=0;n<o;)t.substring(t.length-o,t.length-i)==e.substring(e.length-o,e.length-i)?i=n=o:r=o,o=Math.floor((r-n)/2+n);return o}var a=n;function l(t,e,n){for(var r=e+n-1;r>=0&&r>=e-1;r--)if(r+1<t.length){var o=t[r],i=t[r+1];o[0]===i[1]&&t.splice(r,2,[o[0],o[1]+i[1]])}return t}a.INSERT=1,a.DELETE=-1,a.EQUAL=0,t.exports=a},function(t,e){function n(t){var e=[];for(var n in t)e.push(n);return e}(t.exports="function"===typeof Object.keys?Object.keys:n).shim=n},function(t,e){var n="[object Arguments]"==function(){return Object.prototype.toString.call(arguments)}();function r(t){return"[object Arguments]"==Object.prototype.toString.call(t)}function o(t){return t&&"object"==typeof t&&"number"==typeof t.length&&Object.prototype.hasOwnProperty.call(t,"callee")&&!Object.prototype.propertyIsEnumerable.call(t,"callee")||!1}(e=t.exports=n?r:o).supported=r,e.unsupported=o},function(t,e){"use strict";var n=Object.prototype.hasOwnProperty,r="~";function o(){}function i(t,e,n){this.fn=t,this.context=e,this.once=n||!1}function a(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(r=!1)),a.prototype.eventNames=function(){var t,e,o=[];if(0===this._eventsCount)return o;for(e in t=this._events)n.call(t,e)&&o.push(r?e.slice(1):e);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},a.prototype.listeners=function(t,e){var n=r?r+t:t,o=this._events[n];if(e)return!!o;if(!o)return[];if(o.fn)return[o.fn];for(var i=0,a=o.length,l=new Array(a);i<a;i++)l[i]=o[i].fn;return l},a.prototype.emit=function(t,e,n,o,i,a){var l=r?r+t:t;if(!this._events[l])return!1;var s,u,c=this._events[l],f=arguments.length;if(c.fn){switch(c.once&&this.removeListener(t,c.fn,void 0,!0),f){case 1:return c.fn.call(c.context),!0;case 2:return c.fn.call(c.context,e),!0;case 3:return c.fn.call(c.context,e,n),!0;case 4:return c.fn.call(c.context,e,n,o),!0;case 5:return c.fn.call(c.context,e,n,o,i),!0;case 6:return c.fn.call(c.context,e,n,o,i,a),!0}for(u=1,s=new Array(f-1);u<f;u++)s[u-1]=arguments[u];c.fn.apply(c.context,s)}else{var p,h=c.length;for(u=0;u<h;u++)switch(c[u].once&&this.removeListener(t,c[u].fn,void 0,!0),f){case 1:c[u].fn.call(c[u].context);break;case 2:c[u].fn.call(c[u].context,e);break;case 3:c[u].fn.call(c[u].context,e,n);break;case 4:c[u].fn.call(c[u].context,e,n,o);break;default:if(!s)for(p=1,s=new Array(f-1);p<f;p++)s[p-1]=arguments[p];c[u].fn.apply(c[u].context,s)}}return!0},a.prototype.on=function(t,e,n){var o=new i(e,n||this),a=r?r+t:t;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],o]:this._events[a].push(o):(this._events[a]=o,this._eventsCount++),this},a.prototype.once=function(t,e,n){var o=new i(e,n||this,!0),a=r?r+t:t;return this._events[a]?this._events[a].fn?this._events[a]=[this._events[a],o]:this._events[a].push(o):(this._events[a]=o,this._eventsCount++),this},a.prototype.removeListener=function(t,e,n,i){var a=r?r+t:t;if(!this._events[a])return this;if(!e)return 0===--this._eventsCount?this._events=new o:delete this._events[a],this;var l=this._events[a];if(l.fn)l.fn!==e||i&&!l.once||n&&l.context!==n||(0===--this._eventsCount?this._events=new o:delete this._events[a]);else{for(var s=0,u=[],c=l.length;s<c;s++)(l[s].fn!==e||i&&!l[s].once||n&&l[s].context!==n)&&u.push(l[s]);u.length?this._events[a]=1===u.length?u[0]:u:0===--this._eventsCount?this._events=new o:delete this._events[a]}return this},a.prototype.removeAllListeners=function(t){var e;return t?(e=r?r+t:t,this._events[e]&&(0===--this._eventsCount?this._events=new o:delete this._events[e])):(this._events=new o,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prototype.setMaxListeners=function(){return this},a.prefixed=r,a.EventEmitter=a,"undefined"!==typeof t&&(t.exports=a)},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.matchText=e.matchSpacing=e.matchNewline=e.matchBlot=e.matchAttributor=e.default=void 0;var r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),a=b(n(3)),l=b(n(2)),s=b(n(0)),u=b(n(5)),c=b(n(10)),f=b(n(9)),p=n(36),h=n(37),d=b(n(13)),A=n(26),y=n(38),g=n(39),v=n(40);function b(t){return t&&t.__esModule?t:{default:t}}function m(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var E=(0,c.default)("quill:clipboard"),O="__ql-matcher",w=[[Node.TEXT_NODE,q],[Node.TEXT_NODE,P],["br",function(t,e){return j(e,"\n")||e.insert("\n"),e}],[Node.ELEMENT_NODE,P],[Node.ELEMENT_NODE,T],[Node.ELEMENT_NODE,Q],[Node.ELEMENT_NODE,S],[Node.ELEMENT_NODE,function(t,e){var n={},r=t.style||{};return r.fontStyle&&"italic"===N(t).fontStyle&&(n.italic=!0),r.fontWeight&&(N(t).fontWeight.startsWith("bold")||parseInt(N(t).fontWeight)>=700)&&(n.bold=!0),Object.keys(n).length>0&&(e=_(e,n)),parseFloat(r.textIndent||0)>0&&(e=(new l.default).insert("\t").concat(e)),e}],["li",function(t,e){var n=s.default.query(t);if(null==n||"list-item"!==n.blotName||!j(e,"\n"))return e;for(var r=-1,o=t.parentNode;!o.classList.contains("ql-clipboard");)"list"===(s.default.query(o)||{}).blotName&&(r+=1),o=o.parentNode;return r<=0?e:e.compose((new l.default).retain(e.length()-1).retain(1,{indent:r}))}],["b",I.bind(I,"bold")],["i",I.bind(I,"italic")],["style",function(){return new l.default}]],C=[p.AlignAttribute,y.DirectionAttribute].reduce((function(t,e){return t[e.keyName]=e,t}),{}),k=[p.AlignStyle,h.BackgroundStyle,A.ColorStyle,y.DirectionStyle,g.FontStyle,v.SizeStyle].reduce((function(t,e){return t[e.keyName]=e,t}),{}),x=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.quill.root.addEventListener("paste",r.onPaste.bind(r)),r.container=r.quill.addContainer("ql-clipboard"),r.container.setAttribute("contenteditable",!0),r.container.setAttribute("tabindex",-1),r.matchers=[],w.concat(r.options.matchers).forEach((function(t){var e=o(t,2),i=e[0],a=e[1];(n.matchVisual||a!==Q)&&r.addMatcher(i,a)})),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),i(e,[{key:"addMatcher",value:function(t,e){this.matchers.push([t,e])}},{key:"convert",value:function(t){if("string"===typeof t)return this.container.innerHTML=t.replace(/\>\r?\n +\</g,"><"),this.convert();var e=this.quill.getFormat(this.quill.selection.savedRange.index);if(e[d.default.blotName]){var n=this.container.innerText;return this.container.innerHTML="",(new l.default).insert(n,m({},d.default.blotName,e[d.default.blotName]))}var r=this.prepareMatching(),i=o(r,2),a=i[0],s=i[1],u=function t(e,n,r){return e.nodeType===e.TEXT_NODE?r.reduce((function(t,n){return n(e,t)}),new l.default):e.nodeType===e.ELEMENT_NODE?[].reduce.call(e.childNodes||[],(function(o,i){var a=t(i,n,r);return i.nodeType===e.ELEMENT_NODE&&(a=n.reduce((function(t,e){return e(i,t)}),a),a=(i[O]||[]).reduce((function(t,e){return e(i,t)}),a)),o.concat(a)}),new l.default):new l.default}(this.container,a,s);return j(u,"\n")&&null==u.ops[u.ops.length-1].attributes&&(u=u.compose((new l.default).retain(u.length()-1).delete(1))),E.log("convert",this.container.innerHTML,u),this.container.innerHTML="",u}},{key:"dangerouslyPasteHTML",value:function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u.default.sources.API;if("string"===typeof t)this.quill.setContents(this.convert(t),e),this.quill.setSelection(0,u.default.sources.SILENT);else{var r=this.convert(e);this.quill.updateContents((new l.default).retain(t).concat(r),n),this.quill.setSelection(t+r.length(),u.default.sources.SILENT)}}},{key:"onPaste",value:function(t){var e=this;if(!t.defaultPrevented&&this.quill.isEnabled()){var n=this.quill.getSelection(),r=(new l.default).retain(n.index),o=this.quill.scrollingContainer.scrollTop;this.container.focus(),this.quill.selection.update(u.default.sources.SILENT),setTimeout((function(){r=r.concat(e.convert()).delete(n.length),e.quill.updateContents(r,u.default.sources.USER),e.quill.setSelection(r.length()-n.length,u.default.sources.SILENT),e.quill.scrollingContainer.scrollTop=o,e.quill.focus()}),1)}}},{key:"prepareMatching",value:function(){var t=this,e=[],n=[];return this.matchers.forEach((function(r){var i=o(r,2),a=i[0],l=i[1];switch(a){case Node.TEXT_NODE:n.push(l);break;case Node.ELEMENT_NODE:e.push(l);break;default:[].forEach.call(t.container.querySelectorAll(a),(function(t){t[O]=t[O]||[],t[O].push(l)}))}})),[e,n]}}]),e}(f.default);function _(t,e,n){return"object"===("undefined"===typeof e?"undefined":r(e))?Object.keys(e).reduce((function(t,n){return _(t,n,e[n])}),t):t.reduce((function(t,r){return r.attributes&&r.attributes[e]?t.push(r):t.insert(r.insert,(0,a.default)({},m({},e,n),r.attributes))}),new l.default)}function N(t){return t.nodeType!==Node.ELEMENT_NODE?{}:t["__ql-computed-style"]||(t["__ql-computed-style"]=window.getComputedStyle(t))}function j(t,e){for(var n="",r=t.ops.length-1;r>=0&&n.length<e.length;--r){var o=t.ops[r];if("string"!==typeof o.insert)break;n=o.insert+n}return n.slice(-1*e.length)===e}function B(t){if(0===t.childNodes.length)return!1;var e=N(t);return["block","list-item"].indexOf(e.display)>-1}function I(t,e,n){return _(n,t,!0)}function S(t,e){var n=s.default.Attributor.Attribute.keys(t),r=s.default.Attributor.Class.keys(t),o=s.default.Attributor.Style.keys(t),i={};return n.concat(r).concat(o).forEach((function(e){var n=s.default.query(e,s.default.Scope.ATTRIBUTE);null!=n&&(i[n.attrName]=n.value(t),i[n.attrName])||(null==(n=C[e])||n.attrName!==e&&n.keyName!==e||(i[n.attrName]=n.value(t)||void 0),null==(n=k[e])||n.attrName!==e&&n.keyName!==e||(n=k[e],i[n.attrName]=n.value(t)||void 0))})),Object.keys(i).length>0&&(e=_(e,i)),e}function T(t,e){var n=s.default.query(t);if(null==n)return e;if(n.prototype instanceof s.default.Embed){var r={},o=n.value(t);null!=o&&(r[n.blotName]=o,e=(new l.default).insert(r,n.formats(t)))}else"function"===typeof n.formats&&(e=_(e,n.blotName,n.formats(t)));return e}function P(t,e){return j(e,"\n")||(B(t)||e.length()>0&&t.nextSibling&&B(t.nextSibling))&&e.insert("\n"),e}function Q(t,e){if(B(t)&&null!=t.nextElementSibling&&!j(e,"\n\n")){var n=t.offsetHeight+parseFloat(N(t).marginTop)+parseFloat(N(t).marginBottom);t.nextElementSibling.offsetTop>t.offsetTop*****n&&e.insert("\n")}return e}function q(t,e){var n=t.data;if("O:P"===t.parentNode.tagName)return e.insert(n.trim());if(0===n.trim().length&&t.parentNode.classList.contains("ql-clipboard"))return e;if(!N(t.parentNode).whiteSpace.startsWith("pre")){var r=function(t,e){return(e=e.replace(/[^\u00a0]/g,"")).length<1&&t?" ":e};n=(n=n.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,r.bind(r,!0)),(null==t.previousSibling&&B(t.parentNode)||null!=t.previousSibling&&B(t.previousSibling))&&(n=n.replace(/^\s+/,r.bind(r,!1))),(null==t.nextSibling&&B(t.parentNode)||null!=t.nextSibling&&B(t.nextSibling))&&(n=n.replace(/\s+$/,r.bind(r,!1)))}return e.insert(n)}x.DEFAULTS={matchers:[],matchVisual:!0},e.default=x,e.matchAttributor=S,e.matchBlot=T,e.matchNewline=P,e.matchSpacing=Q,e.matchText=q},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(6);function l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function s(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var u=function(t){function e(){return l(this,e),s(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"optimize",value:function(t){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}],[{key:"create",value:function(){return i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this)}},{key:"formats",value:function(){return!0}}]),e}(((r=a)&&r.__esModule?r:{default:r}).default);u.blotName="bold",u.tagName=["STRONG","B"],e.default=u},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.addControls=e.default=void 0;var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=c(n(2)),a=c(n(0)),l=c(n(5)),s=c(n(10)),u=c(n(9));function c(t){return t&&t.__esModule?t:{default:t}}function f(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var p=(0,s.default)("quill:toolbar"),h=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var o,i=f(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));if(Array.isArray(i.options.container)){var a=document.createElement("div");A(a,i.options.container),t.container.parentNode.insertBefore(a,t.container),i.container=a}else"string"===typeof i.options.container?i.container=document.querySelector(i.options.container):i.container=i.options.container;return i.container instanceof HTMLElement?(i.container.classList.add("ql-toolbar"),i.controls=[],i.handlers={},Object.keys(i.options.handlers).forEach((function(t){i.addHandler(t,i.options.handlers[t])})),[].forEach.call(i.container.querySelectorAll("button, select"),(function(t){i.attach(t)})),i.quill.on(l.default.events.EDITOR_CHANGE,(function(t,e){t===l.default.events.SELECTION_CHANGE&&i.update(e)})),i.quill.on(l.default.events.SCROLL_OPTIMIZE,(function(){var t=i.quill.selection.getRange(),e=r(t,1)[0];i.update(e)})),i):(o=p.error("Container required for toolbar",i.options),f(i,o))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"addHandler",value:function(t,e){this.handlers[t]=e}},{key:"attach",value:function(t){var e=this,n=[].find.call(t.classList,(function(t){return 0===t.indexOf("ql-")}));if(n){if(n=n.slice("ql-".length),"BUTTON"===t.tagName&&t.setAttribute("type","button"),null==this.handlers[n]){if(null!=this.quill.scroll.whitelist&&null==this.quill.scroll.whitelist[n])return void p.warn("ignoring attaching to disabled format",n,t);if(null==a.default.query(n))return void p.warn("ignoring attaching to nonexistent format",n,t)}var o="SELECT"===t.tagName?"change":"click";t.addEventListener(o,(function(o){var s=void 0;if("SELECT"===t.tagName){if(t.selectedIndex<0)return;var u=t.options[t.selectedIndex];s=!u.hasAttribute("selected")&&(u.value||!1)}else s=!t.classList.contains("ql-active")&&(t.value||!t.hasAttribute("value")),o.preventDefault();e.quill.focus();var c=e.quill.selection.getRange(),f=r(c,1)[0];if(null!=e.handlers[n])e.handlers[n].call(e,s);else if(a.default.query(n).prototype instanceof a.default.Embed){if(!(s=prompt("Enter "+n)))return;e.quill.updateContents((new i.default).retain(f.index).delete(f.length).insert(function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}({},n,s)),l.default.sources.USER)}else e.quill.format(n,s,l.default.sources.USER);e.update(f)})),this.controls.push([n,t])}}},{key:"update",value:function(t){var e=null==t?{}:this.quill.getFormat(t);this.controls.forEach((function(n){var o=r(n,2),i=o[0],a=o[1];if("SELECT"===a.tagName){var l=void 0;if(null==t)l=null;else if(null==e[i])l=a.querySelector("option[selected]");else if(!Array.isArray(e[i])){var s=e[i];"string"===typeof s&&(s=s.replace(/\"/g,'\\"')),l=a.querySelector('option[value="'+s+'"]')}null==l?(a.value="",a.selectedIndex=-1):l.selected=!0}else if(null==t)a.classList.remove("ql-active");else if(a.hasAttribute("value")){var u=e[i]===a.getAttribute("value")||null!=e[i]&&e[i].toString()===a.getAttribute("value")||null==e[i]&&!a.getAttribute("value");a.classList.toggle("ql-active",u)}else a.classList.toggle("ql-active",null!=e[i])}))}}]),e}(u.default);function d(t,e,n){var r=document.createElement("button");r.setAttribute("type","button"),r.classList.add("ql-"+e),null!=n&&(r.value=n),t.appendChild(r)}function A(t,e){Array.isArray(e[0])||(e=[e]),e.forEach((function(e){var n=document.createElement("span");n.classList.add("ql-formats"),e.forEach((function(t){if("string"===typeof t)d(n,t);else{var e=Object.keys(t)[0],r=t[e];Array.isArray(r)?function(t,e,n){var r=document.createElement("select");r.classList.add("ql-"+e),n.forEach((function(t){var e=document.createElement("option");!1!==t?e.setAttribute("value",t):e.setAttribute("selected","selected"),r.appendChild(e)})),t.appendChild(r)}(n,e,r):d(n,e,r)}})),t.appendChild(n)}))}h.DEFAULTS={},h.DEFAULTS={container:null,handlers:{clean:function(){var t=this,e=this.quill.getSelection();if(null!=e)if(0==e.length){var n=this.quill.getFormat();Object.keys(n).forEach((function(e){null!=a.default.query(e,a.default.Scope.INLINE)&&t.quill.format(e,!1)}))}else this.quill.removeFormat(e,l.default.sources.USER)},direction:function(t){var e=this.quill.getFormat().align;"rtl"===t&&null==e?this.quill.format("align","right",l.default.sources.USER):t||"right"!==e||this.quill.format("align",!1,l.default.sources.USER),this.quill.format("direction",t,l.default.sources.USER)},indent:function(t){var e=this.quill.getSelection(),n=this.quill.getFormat(e),r=parseInt(n.indent||0);if("+1"===t||"-1"===t){var o="+1"===t?1:-1;"rtl"===n.direction&&(o*=-1),this.quill.format("indent",r+o,l.default.sources.USER)}},link:function(t){!0===t&&(t=prompt("Enter link URL:")),this.quill.format("link",t,l.default.sources.USER)},list:function(t){var e=this.quill.getSelection(),n=this.quill.getFormat(e);"check"===t?"checked"===n.list||"unchecked"===n.list?this.quill.format("list",!1,l.default.sources.USER):this.quill.format("list","unchecked",l.default.sources.USER):this.quill.format("list",t,l.default.sources.USER)}}},e.default=h,e.addControls=A},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polyline class="ql-even ql-stroke" points="5 7 3 9 5 11"></polyline> <polyline class="ql-even ql-stroke" points="13 7 15 9 13 11"></polyline> <line class=ql-stroke x1=10 x2=8 y1=5 y2=13></line> </svg>'},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(28),l=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return r.label.innerHTML=n,r.container.classList.add("ql-color-picker"),[].slice.call(r.container.querySelectorAll(".ql-picker-item"),0,7).forEach((function(t){t.classList.add("ql-primary")})),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"buildItem",value:function(t){var n=i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"buildItem",this).call(this,t);return n.style.backgroundColor=t.getAttribute("value")||"",n}},{key:"selectItem",value:function(t,n){i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"selectItem",this).call(this,t,n);var r=this.label.querySelector(".ql-color-label"),o=t&&t.getAttribute("data-value")||"";r&&("line"===r.tagName?r.style.stroke=o:r.style.fill=o)}}]),e}(((r=a)&&r.__esModule?r:{default:r}).default);e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(28),a=function(t){function e(t,n){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,e);var r=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return r.container.classList.add("ql-icon-picker"),[].forEach.call(r.container.querySelectorAll(".ql-picker-item"),(function(t){t.innerHTML=n[t.getAttribute("data-value")||""]})),r.defaultItem=r.container.querySelector(".ql-selected"),r.selectItem(r.defaultItem),r}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"selectItem",value:function(t,n){(function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0})(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"selectItem",this).call(this,t,n),t=t||this.defaultItem,this.label.innerHTML=t.innerHTML}}]),e}(((r=i)&&r.__esModule?r:{default:r}).default);e.default=a},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function(){function t(e,n){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.boundsContainer=n||document.body,this.root=e.addContainer("ql-tooltip"),this.root.innerHTML=this.constructor.TEMPLATE,this.quill.root===this.quill.scrollingContainer&&this.quill.root.addEventListener("scroll",(function(){r.root.style.marginTop=-1*r.quill.root.scrollTop+"px"})),this.hide()}return r(t,[{key:"hide",value:function(){this.root.classList.add("ql-hidden")}},{key:"position",value:function(t){var e=t.left+t.width/2-this.root.offsetWidth/2,n=t.bottom+this.quill.root.scrollTop;this.root.style.left=e+"px",this.root.style.top=n+"px",this.root.classList.remove("ql-flip");var r=this.boundsContainer.getBoundingClientRect(),o=this.root.getBoundingClientRect(),i=0;if(o.right>r.right&&(i=r.right-o.right,this.root.style.left=e+i+"px"),o.left<r.left&&(i=r.left-o.left,this.root.style.left=e+i+"px"),o.bottom>r.bottom){var a=o.bottom-o.top,l=t.bottom-t.top+a;this.root.style.top=n-l+"px",this.root.classList.add("ql-flip")}return i}},{key:"show",value:function(){this.root.classList.remove("ql-editing"),this.root.classList.remove("ql-hidden")}}]),t}();e.default=o},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=function(t,e){if(Array.isArray(t))return t;if(Symbol.iterator in Object(t))return function(t,e){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=t[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!e||n.length!==e);r=!0);}catch(s){o=!0,i=s}finally{try{!r&&l.return&&l.return()}finally{if(o)throw i}}return n}(t,e);throw new TypeError("Invalid attempt to destructure non-iterable instance")},o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),a=h(n(3)),l=h(n(8)),s=n(43),u=h(s),c=h(n(27)),f=n(15),p=h(n(41));function h(t){return t&&t.__esModule?t:{default:t}}function d(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function A(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function y(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var g=[[{header:["1","2","3",!1]}],["bold","italic","underline","link"],[{list:"ordered"},{list:"bullet"}],["clean"]],v=function(t){function e(t,n){d(this,e),null!=n.modules.toolbar&&null==n.modules.toolbar.container&&(n.modules.toolbar.container=g);var r=A(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.quill.container.classList.add("ql-snow"),r}return y(e,t),i(e,[{key:"extendToolbar",value:function(t){t.container.classList.add("ql-snow"),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),p.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),p.default),this.tooltip=new b(this.quill,this.options.bounds),t.container.querySelector(".ql-link")&&this.quill.keyboard.addBinding({key:"K",shortKey:!0},(function(e,n){t.handlers.link.call(t,!n.format.link)}))}}]),e}(u.default);v.DEFAULTS=(0,a.default)(!0,{},u.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){if(t){var e=this.quill.getSelection();if(null==e||0==e.length)return;var n=this.quill.getText(e);/^\S+@\S+\.\S+$/.test(n)&&0!==n.indexOf("mailto:")&&(n="mailto:"+n),this.quill.theme.tooltip.edit("link",n)}else this.quill.format("link",!1)}}}}});var b=function(t){function e(t,n){d(this,e);var r=A(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.preview=r.root.querySelector("a.ql-preview"),r}return y(e,t),i(e,[{key:"listen",value:function(){var t=this;o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"listen",this).call(this),this.root.querySelector("a.ql-action").addEventListener("click",(function(e){t.root.classList.contains("ql-editing")?t.save():t.edit("link",t.preview.textContent),e.preventDefault()})),this.root.querySelector("a.ql-remove").addEventListener("click",(function(e){if(null!=t.linkRange){var n=t.linkRange;t.restoreFocus(),t.quill.formatText(n,"link",!1,l.default.sources.USER),delete t.linkRange}e.preventDefault(),t.hide()})),this.quill.on(l.default.events.SELECTION_CHANGE,(function(e,n,o){if(null!=e){if(0===e.length&&o===l.default.sources.USER){var i=t.quill.scroll.descendant(c.default,e.index),a=r(i,2),s=a[0],u=a[1];if(null!=s){t.linkRange=new f.Range(e.index-u,s.length());var p=c.default.formats(s.domNode);return t.preview.textContent=p,t.preview.setAttribute("href",p),t.show(),void t.position(t.quill.getBounds(t.linkRange))}}else delete t.linkRange;t.hide()}}))}},{key:"show",value:function(){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"show",this).call(this),this.root.removeAttribute("data-mode")}}]),e}(s.BaseTooltip);b.TEMPLATE=['<a class="ql-preview" rel="noopener noreferrer" target="_blank" href="about:blank"></a>','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-action"></a>','<a class="ql-remove"></a>'].join(""),e.default=v},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=Q(n(29)),o=n(36),i=n(38),a=n(64),l=Q(n(65)),s=Q(n(66)),u=n(67),c=Q(u),f=n(37),p=n(26),h=n(39),d=n(40),A=Q(n(56)),y=Q(n(68)),g=Q(n(27)),v=Q(n(69)),b=Q(n(70)),m=Q(n(71)),E=Q(n(72)),O=Q(n(73)),w=n(13),C=Q(w),k=Q(n(74)),x=Q(n(75)),_=Q(n(57)),N=Q(n(41)),j=Q(n(28)),B=Q(n(59)),I=Q(n(60)),S=Q(n(61)),T=Q(n(108)),P=Q(n(62));function Q(t){return t&&t.__esModule?t:{default:t}}r.default.register({"attributors/attribute/direction":i.DirectionAttribute,"attributors/class/align":o.AlignClass,"attributors/class/background":f.BackgroundClass,"attributors/class/color":p.ColorClass,"attributors/class/direction":i.DirectionClass,"attributors/class/font":h.FontClass,"attributors/class/size":d.SizeClass,"attributors/style/align":o.AlignStyle,"attributors/style/background":f.BackgroundStyle,"attributors/style/color":p.ColorStyle,"attributors/style/direction":i.DirectionStyle,"attributors/style/font":h.FontStyle,"attributors/style/size":d.SizeStyle},!0),r.default.register({"formats/align":o.AlignClass,"formats/direction":i.DirectionClass,"formats/indent":a.IndentClass,"formats/background":f.BackgroundStyle,"formats/color":p.ColorStyle,"formats/font":h.FontClass,"formats/size":d.SizeClass,"formats/blockquote":l.default,"formats/code-block":C.default,"formats/header":s.default,"formats/list":c.default,"formats/bold":A.default,"formats/code":w.Code,"formats/italic":y.default,"formats/link":g.default,"formats/script":v.default,"formats/strike":b.default,"formats/underline":m.default,"formats/image":E.default,"formats/video":O.default,"formats/list/item":u.ListItem,"modules/formula":k.default,"modules/syntax":x.default,"modules/toolbar":_.default,"themes/bubble":T.default,"themes/snow":P.default,"ui/icons":N.default,"ui/picker":j.default,"ui/icon-picker":I.default,"ui/color-picker":B.default,"ui/tooltip":S.default},!0),e.default=r.default},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IndentClass=void 0;var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(0),l=(r=a)&&r.__esModule?r:{default:r};function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var c=new(function(t){function e(){return s(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"add",value:function(t,n){if("+1"===n||"-1"===n){var r=this.value(t)||0;n="+1"===n?r+1:r-1}return 0===n?(this.remove(t),!0):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"add",this).call(this,t,n)}},{key:"canAdd",value:function(t,n){return i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"canAdd",this).call(this,t,n)||i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"canAdd",this).call(this,t,parseInt(n))}},{key:"value",value:function(t){return parseInt(i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"value",this).call(this,t))||void 0}}]),e}(l.default.Attributor.Class))("indent","ql-indent",{scope:l.default.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});e.IndentClass=c},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=n(4);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var l=function(t){function e(){return i(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((r=o)&&r.__esModule?r:{default:r}).default);l.blotName="blockquote",l.tagName="blockquote",e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(4);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var s=function(t){function e(){return a(this,e),l(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,null,[{key:"formats",value:function(t){return this.tagName.indexOf(t.tagName)+1}}]),e}(((r=i)&&r.__esModule?r:{default:r}).default);s.blotName="header",s.tagName=["H1","H2","H3","H4","H5","H6"],e.default=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.ListItem=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},i=s(n(0)),a=s(n(4)),l=s(n(25));function s(t){return t&&t.__esModule?t:{default:t}}function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function f(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var p=function(t){function e(){return u(this,e),c(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return f(e,t),r(e,[{key:"format",value:function(t,n){t!==h.blotName||n?o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n):this.replaceWith(i.default.create(this.statics.scope))}},{key:"remove",value:function(){null==this.prev&&null==this.next?this.parent.remove():o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"remove",this).call(this)}},{key:"replaceWith",value:function(t,n){return this.parent.isolate(this.offset(this.parent),this.length()),t===this.parent.statics.blotName?(this.parent.replaceWith(t,n),this):(this.parent.unwrap(),o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replaceWith",this).call(this,t,n))}}],[{key:"formats",value:function(t){return t.tagName===this.tagName?void 0:o(e.__proto__||Object.getPrototypeOf(e),"formats",this).call(this,t)}}]),e}(a.default);p.blotName="list-item",p.tagName="LI";var h=function(t){function e(t){u(this,e);var n=c(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t)),r=function(e){if(e.target.parentNode===t){var r=n.statics.formats(t),o=i.default.find(e.target);"checked"===r?o.format("list","unchecked"):"unchecked"===r&&o.format("list","checked")}};return t.addEventListener("touchstart",r),t.addEventListener("mousedown",r),n}return f(e,t),r(e,null,[{key:"create",value:function(t){var n="ordered"===t?"OL":"UL",r=o(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,n);return"checked"!==t&&"unchecked"!==t||r.setAttribute("data-checked","checked"===t),r}},{key:"formats",value:function(t){return"OL"===t.tagName?"ordered":"UL"===t.tagName?t.hasAttribute("data-checked")?"true"===t.getAttribute("data-checked")?"checked":"unchecked":"bullet":void 0}}]),r(e,[{key:"format",value:function(t,e){this.children.length>0&&this.children.tail.format(t,e)}},{key:"formats",value:function(){return t={},e=this.statics.blotName,n=this.statics.formats(this.domNode),e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t;var t,e,n}},{key:"insertBefore",value:function(t,n){if(t instanceof p)o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"insertBefore",this).call(this,t,n);else{var r=null==n?this.length():n.offset(this),i=this.split(r);i.parent.insertBefore(t,i)}}},{key:"optimize",value:function(t){o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"optimize",this).call(this,t);var n=this.next;null!=n&&n.prev===this&&n.statics.blotName===this.statics.blotName&&n.domNode.tagName===this.domNode.tagName&&n.domNode.getAttribute("data-checked")===this.domNode.getAttribute("data-checked")&&(n.moveChildren(this),n.remove())}},{key:"replace",value:function(t){if(t.statics.blotName!==this.statics.blotName){var n=i.default.create(this.statics.defaultChild);t.moveChildren(n),this.appendChild(n)}o(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replace",this).call(this,t)}}]),e}(l.default);h.blotName="list",h.scope=i.default.Scope.BLOCK_BLOT,h.tagName=["OL","UL"],h.defaultChild="list-item",h.allowedChildren=[p],e.ListItem=p,e.default=h},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=n(56);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var l=function(t){function e(){return i(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((r=o)&&r.__esModule?r:{default:r}).default);l.blotName="italic",l.tagName=["EM","I"],e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=n(6);function a(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function l(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var s=function(t){function e(){return a(this,e),l(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,null,[{key:"create",value:function(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t)}},{key:"formats",value:function(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}]),e}(((r=i)&&r.__esModule?r:{default:r}).default);s.blotName="script",s.tagName=["SUB","SUP"],e.default=s},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=n(6);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var l=function(t){function e(){return i(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((r=o)&&r.__esModule?r:{default:r}).default);l.blotName="strike",l.tagName="S",e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=n(6);function i(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var l=function(t){function e(){return i(this,e),a(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),e}(((r=o)&&r.__esModule?r:{default:r}).default);l.blotName="underline",l.tagName="U",e.default=l},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(0),l=(r=a)&&r.__esModule?r:{default:r},s=n(27);function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var f=["alt","height","width"],p=function(t){function e(){return u(this,e),c(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"format",value:function(t,n){f.indexOf(t)>-1?n?this.domNode.setAttribute(t,n):this.domNode.removeAttribute(t):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n)}}],[{key:"create",value:function(t){var n=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return"string"===typeof t&&n.setAttribute("src",this.sanitize(t)),n}},{key:"formats",value:function(t){return f.reduce((function(e,n){return t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e}),{})}},{key:"match",value:function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}},{key:"sanitize",value:function(t){return(0,s.sanitize)(t,["http","https","data"])?t:"//:0"}},{key:"value",value:function(t){return t.getAttribute("src")}}]),e}(l.default.Embed);p.blotName="image",p.tagName="IMG",e.default=p},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r,o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},a=n(4),l=n(27),s=(r=l)&&r.__esModule?r:{default:r};function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function c(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}var f=["height","width"],p=function(t){function e(){return u(this,e),c(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return function(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}(e,t),o(e,[{key:"format",value:function(t,n){f.indexOf(t)>-1?n?this.domNode.setAttribute(t,n):this.domNode.removeAttribute(t):i(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"format",this).call(this,t,n)}}],[{key:"create",value:function(t){var n=i(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return n.setAttribute("frameborder","0"),n.setAttribute("allowfullscreen",!0),n.setAttribute("src",this.sanitize(t)),n}},{key:"formats",value:function(t){return f.reduce((function(e,n){return t.hasAttribute(n)&&(e[n]=t.getAttribute(n)),e}),{})}},{key:"sanitize",value:function(t){return s.default.sanitize(t)}},{key:"value",value:function(t){return t.getAttribute("src")}}]),e}(a.BlockEmbed);p.blotName="video",p.className="ql-video",p.tagName="IFRAME",e.default=p},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.FormulaBlot=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=l(n(35)),i=l(n(5)),a=l(n(9));function l(t){return t&&t.__esModule?t:{default:t}}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function c(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function e(){return s(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return c(e,t),r(e,null,[{key:"create",value:function(t){var n=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.__proto__||Object.getPrototypeOf(e),"create",this).call(this,t);return"string"===typeof t&&(window.katex.render(t,n,{throwOnError:!1,errorColor:"#f00"}),n.setAttribute("data-value",t)),n}},{key:"value",value:function(t){return t.getAttribute("data-value")}}]),e}(o.default);f.blotName="formula",f.className="ql-formula",f.tagName="SPAN";var p=function(t){function e(){s(this,e);var t=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));if(null==window.katex)throw new Error("Formula module requires KaTeX.");return t}return c(e,t),r(e,null,[{key:"register",value:function(){i.default.register(f,!0)}}]),e}(a.default);e.FormulaBlot=f,e.default=p},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.CodeToken=e.CodeBlock=void 0;var r=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),o=l(n(0)),i=l(n(5)),a=l(n(9));function l(t){return t&&t.__esModule?t:{default:t}}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function u(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function c(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var f=function(t){function e(){return s(this,e),u(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return c(e,t),r(e,[{key:"replaceWith",value:function(t){this.domNode.textContent=this.domNode.textContent,this.attach(),function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0}(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"replaceWith",this).call(this,t)}},{key:"highlight",value:function(t){var e=this.domNode.textContent;this.cachedText!==e&&((e.trim().length>0||null==this.cachedText)&&(this.domNode.innerHTML=t(e),this.domNode.normalize(),this.attach()),this.cachedText=e)}}]),e}(l(n(13)).default);f.className="ql-syntax";var p=new o.default.Attributor.Class("token","hljs",{scope:o.default.Scope.INLINE}),h=function(t){function e(t,n){s(this,e);var r=u(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));if("function"!==typeof r.options.highlight)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");var o=null;return r.quill.on(i.default.events.SCROLL_OPTIMIZE,(function(){clearTimeout(o),o=setTimeout((function(){r.highlight(),o=null}),r.options.interval)})),r.highlight(),r}return c(e,t),r(e,null,[{key:"register",value:function(){i.default.register(p,!0),i.default.register(f,!0)}}]),r(e,[{key:"highlight",value:function(){var t=this;if(!this.quill.selection.composing){this.quill.update(i.default.sources.USER);var e=this.quill.getSelection();this.quill.scroll.descendants(f).forEach((function(e){e.highlight(t.options.highlight)})),this.quill.update(i.default.sources.SILENT),null!=e&&this.quill.setSelection(e,i.default.sources.SILENT)}}}]),e}(a.default);h.DEFAULTS={highlight:null==window.hljs?null:function(t){return window.hljs.highlightAuto(t).value},interval:1e3},e.CodeBlock=f,e.CodeToken=p,e.default=h},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=13 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=14 x2=4 y1=14 y2=14></line> <line class=ql-stroke x1=12 x2=6 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=5 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=9 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=15 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=15 x2=3 y1=14 y2=14></line> <line class=ql-stroke x1=15 x2=3 y1=4 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <g class="ql-fill ql-color-label"> <polygon points="6 6.868 6 6 5 6 5 7 5.942 7 6 6.868"></polygon> <rect height=1 width=1 x=4 y=4></rect> <polygon points="6.817 5 6 5 6 6 6.38 6 6.817 5"></polygon> <rect height=1 width=1 x=2 y=6></rect> <rect height=1 width=1 x=3 y=5></rect> <rect height=1 width=1 x=4 y=7></rect> <polygon points="4 11.439 4 11 3 11 3 12 3.755 12 4 11.439"></polygon> <rect height=1 width=1 x=2 y=12></rect> <rect height=1 width=1 x=2 y=9></rect> <rect height=1 width=1 x=2 y=15></rect> <polygon points="4.63 10 4 10 4 11 4.192 11 4.63 10"></polygon> <rect height=1 width=1 x=3 y=8></rect> <path d=M10.832,4.2L11,4.582V4H10.708A1.948,1.948,0,0,1,10.832,4.2Z></path> <path d=M7,4.582L7.168,4.2A1.929,1.929,0,0,1,7.292,4H7V4.582Z></path> <path d=M8,13H7.683l-0.351.8a1.933,1.933,0,0,1-.124.2H8V13Z></path> <rect height=1 width=1 x=12 y=2></rect> <rect height=1 width=1 x=11 y=3></rect> <path d=M9,3H8V3.282A1.985,1.985,0,0,1,9,3Z></path> <rect height=1 width=1 x=2 y=3></rect> <rect height=1 width=1 x=6 y=2></rect> <rect height=1 width=1 x=3 y=2></rect> <rect height=1 width=1 x=5 y=3></rect> <rect height=1 width=1 x=9 y=2></rect> <rect height=1 width=1 x=15 y=14></rect> <polygon points="13.447 10.174 13.469 10.225 13.472 10.232 13.808 11 14 11 14 10 13.37 10 13.447 10.174"></polygon> <rect height=1 width=1 x=13 y=7></rect> <rect height=1 width=1 x=15 y=5></rect> <rect height=1 width=1 x=14 y=6></rect> <rect height=1 width=1 x=15 y=8></rect> <rect height=1 width=1 x=14 y=9></rect> <path d=M3.775,14H3v1H4V14.314A1.97,1.97,0,0,1,3.775,14Z></path> <rect height=1 width=1 x=14 y=3></rect> <polygon points="12 6.868 12 6 11.62 6 12 6.868"></polygon> <rect height=1 width=1 x=15 y=2></rect> <rect height=1 width=1 x=12 y=5></rect> <rect height=1 width=1 x=13 y=4></rect> <polygon points="12.933 9 13 9 13 8 12.495 8 12.933 9"></polygon> <rect height=1 width=1 x=9 y=14></rect> <rect height=1 width=1 x=8 y=15></rect> <path d=M6,14.926V15H7V14.316A1.993,1.993,0,0,1,6,14.926Z></path> <rect height=1 width=1 x=5 y=15></rect> <path d=M10.668,13.8L10.317,13H10v1h0.792A1.947,1.947,0,0,1,10.668,13.8Z></path> <rect height=1 width=1 x=11 y=15></rect> <path d=M14.332,12.2a1.99,1.99,0,0,1,.166.8H15V12H14.245Z></path> <rect height=1 width=1 x=14 y=15></rect> <rect height=1 width=1 x=15 y=11></rect> </g> <polyline class=ql-stroke points="5.5 13 9 5 12.5 13"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=11 y2=11></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class="ql-fill ql-stroke" height=3 width=3 x=4 y=5></rect> <rect class="ql-fill ql-stroke" height=3 width=3 x=11 y=5></rect> <path class="ql-even ql-fill ql-stroke" d=M7,8c0,4.031-3,5-3,5></path> <path class="ql-even ql-fill ql-stroke" d=M14,8c0,4.031-3,5-3,5></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,4H9.5A2.5,2.5,0,0,1,12,6.5v0A2.5,2.5,0,0,1,9.5,9H5A0,0,0,0,1,5,9V4A0,0,0,0,1,5,4Z></path> <path class=ql-stroke d=M5,9h5.5A2.5,2.5,0,0,1,13,11.5v0A2.5,2.5,0,0,1,10.5,14H5a0,0,0,0,1,0,0V9A0,0,0,0,1,5,9Z></path> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=5 x2=13 y1=3 y2=3></line> <line class=ql-stroke x1=6 x2=9.35 y1=12 y2=3></line> <line class=ql-stroke x1=11 x2=15 y1=11 y2=15></line> <line class=ql-stroke x1=15 x2=11 y1=11 y2=15></line> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=7 x=2 y=14></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-color-label ql-stroke ql-transparent" x1=3 x2=15 y1=15 y2=15></line> <polyline class=ql-stroke points="5.5 11 9 3 12.5 11"></polyline> <line class=ql-stroke x1=11.63 x2=6.38 y1=9 y2=9></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="3 11 5 9 3 7 3 11"></polygon> <line class="ql-stroke ql-fill" x1=15 x2=11 y1=4 y2=4></line> <path class=ql-fill d=M11,3a3,3,0,0,0,0,6h1V3H11Z></path> <rect class=ql-fill height=11 width=1 x=11 y=4></rect> <rect class=ql-fill height=11 width=1 x=13 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class="ql-stroke ql-fill" points="15 12 13 10 15 8 15 12"></polygon> <line class="ql-stroke ql-fill" x1=9 x2=5 y1=4 y2=4></line> <path class=ql-fill d=M5,3A3,3,0,0,0,5,9H6V3H5Z></path> <rect class=ql-fill height=11 width=1 x=5 y=4></rect> <rect class=ql-fill height=11 width=1 x=7 y=4></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M14,16H4a1,1,0,0,1,0-2H14A1,1,0,0,1,14,16Z /> <path class=ql-fill d=M14,4H4A1,1,0,0,1,4,2H14A1,1,0,0,1,14,4Z /> <rect class=ql-fill x=3 y=6 width=12 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M13,16H5a1,1,0,0,1,0-2h8A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H5A1,1,0,0,1,5,2h8A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=2 y=6 width=14 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15,8H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,8Z /> <path class=ql-fill d=M15,12H13a1,1,0,0,1,0-2h2A1,1,0,0,1,15,12Z /> <path class=ql-fill d=M15,16H5a1,1,0,0,1,0-2H15A1,1,0,0,1,15,16Z /> <path class=ql-fill d=M15,4H5A1,1,0,0,1,5,2H15A1,1,0,0,1,15,4Z /> <rect class=ql-fill x=2 y=6 width=8 height=6 rx=1 ry=1 /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M5,8H3A1,1,0,0,1,3,6H5A1,1,0,0,1,5,8Z /> <path class=ql-fill d=M5,12H3a1,1,0,0,1,0-2H5A1,1,0,0,1,5,12Z /> <path class=ql-fill d=M13,16H3a1,1,0,0,1,0-2H13A1,1,0,0,1,13,16Z /> <path class=ql-fill d=M13,4H3A1,1,0,0,1,3,2H13A1,1,0,0,1,13,4Z /> <rect class=ql-fill x=8 y=6 width=8 height=6 rx=1 ry=1 transform="translate(24 18) rotate(-180)"/> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M11.759,2.482a2.561,2.561,0,0,0-3.53.607A7.656,7.656,0,0,0,6.8,6.2C6.109,9.188,5.275,14.677,4.15,14.927a1.545,1.545,0,0,0-1.3-.933A0.922,0.922,0,0,0,2,15.036S1.954,16,4.119,16s3.091-2.691,3.7-5.553c0.177-.826.36-1.726,0.554-2.6L8.775,6.2c0.381-1.421.807-2.521,1.306-2.676a1.014,1.014,0,0,0,1.02.56A0.966,0.966,0,0,0,11.759,2.482Z></path> <rect class=ql-fill height=1.6 rx=0.8 ry=0.8 width=5 x=5.15 y=6.2></rect> <path class=ql-fill d=M13.663,12.027a1.662,1.662,0,0,1,.266-0.276q0.193,0.069.456,0.138a2.1,2.1,0,0,0,.535.069,1.075,1.075,0,0,0,.767-0.3,1.044,1.044,0,0,0,.314-0.8,0.84,0.84,0,0,0-.238-0.619,0.8,0.8,0,0,0-.594-0.239,1.154,1.154,0,0,0-.781.3,4.607,4.607,0,0,0-.781,1q-0.091.15-.218,0.346l-0.246.38c-0.068-.288-0.137-0.582-0.212-0.885-0.459-1.847-2.494-.984-2.941-0.8-0.482.2-.353,0.647-0.094,0.529a0.869,0.869,0,0,1,1.281.585c0.217,0.751.377,1.436,0.527,2.038a5.688,5.688,0,0,1-.362.467,2.69,2.69,0,0,1-.264.271q-0.221-.08-0.471-0.147a2.029,2.029,0,0,0-.522-0.066,1.079,1.079,0,0,0-.768.3A1.058,1.058,0,0,0,9,15.131a0.82,0.82,0,0,0,.832.852,1.134,1.134,0,0,0,.787-0.3,5.11,5.11,0,0,0,.776-0.993q0.141-.219.215-0.34c0.046-.076.122-0.194,0.223-0.346a2.786,2.786,0,0,0,.918,1.726,2.582,2.582,0,0,0,2.376-.185c0.317-.181.212-0.565,0-0.494A0.807,0.807,0,0,1,14.176,15a5.159,5.159,0,0,1-.913-2.446l0,0Q13.487,12.24,13.663,12.027Z></path> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M10,4V14a1,1,0,0,1-2,0V10H3v4a1,1,0,0,1-2,0V4A1,1,0,0,1,3,4V8H8V4a1,1,0,0,1,2,0Zm6.06787,9.209H14.98975V7.59863a.54085.54085,0,0,0-.605-.60547h-.62744a1.01119,1.01119,0,0,0-.748.29688L11.645,8.56641a.5435.5435,0,0,0-.022.8584l.28613.30762a.53861.53861,0,0,0,.84717.0332l.09912-.08789a1.2137,1.2137,0,0,0,.2417-.35254h.02246s-.01123.30859-.01123.60547V13.209H12.041a.54085.54085,0,0,0-.605.60547v.43945a.54085.54085,0,0,0,.605.60547h4.02686a.54085.54085,0,0,0,.605-.60547v-.43945A.54085.54085,0,0,0,16.06787,13.209Z /> </svg>'},function(t,e){t.exports='<svg viewBox="0 0 18 18"> <path class=ql-fill d=M16.73975,13.81445v.43945a.54085.54085,0,0,1-.605.60547H11.855a.58392.58392,0,0,1-.64893-.60547V14.0127c0-2.90527,3.39941-3.42187,3.39941-4.55469a.77675.77675,0,0,0-.84717-.78125,1.17684,1.17684,0,0,0-.83594.38477c-.2749.26367-.561.374-.85791.13184l-.4292-.34082c-.30811-.24219-.38525-.51758-.1543-.81445a2.97155,2.97155,0,0,1,2.45361-1.17676,2.45393,2.45393,0,0,1,2.68408,2.40918c0,2.45312-3.1792,2.92676-3.27832,3.93848h2.79443A.54085.54085,0,0,1,16.73975,13.81445ZM9,3A.99974.99974,0,0,0,8,4V8H3V4A1,1,0,0,0,1,4V14a1,1,0,0,0,2,0V10H8v4a1,1,0,0,0,2,0V4A.99974.99974,0,0,0,9,3Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=13 y1=4 y2=4></line> <line class=ql-stroke x1=5 x2=11 y1=14 y2=14></line> <line class=ql-stroke x1=8 x2=10 y1=14 y2=4></line> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=10 width=12 x=3 y=4></rect> <circle class=ql-fill cx=6 cy=7 r=1></circle> <polyline class="ql-even ql-fill" points="5 12 5 11 7 9 8 10 11 7 13 9 13 12 5 12"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class="ql-fill ql-stroke" points="3 7 3 11 5 9 3 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=3 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="5 7 5 11 3 9 5 7"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=11 y1=7 y2=11></line> <path class="ql-even ql-stroke" d=M8.9,4.577a3.476,3.476,0,0,1,.36,4.679A3.476,3.476,0,0,1,4.577,8.9C3.185,7.5,2.035,6.4,4.217,4.217S7.5,3.185,8.9,4.577Z></path> <path class="ql-even ql-stroke" d=M13.423,9.1a3.476,3.476,0,0,0-4.679-.36,3.476,3.476,0,0,0,.36,4.679c1.392,1.392,2.5,2.542,4.679.36S14.815,10.5,13.423,9.1Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=7 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=7 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=7 x2=15 y1=14 y2=14></line> <line class="ql-stroke ql-thin" x1=2.5 x2=4.5 y1=5.5 y2=5.5></line> <path class=ql-fill d=M3.5,6A0.5,0.5,0,0,1,3,5.5V3.085l-0.276.138A0.5,0.5,0,0,1,2.053,3c-0.124-.247-0.023-0.324.224-0.447l1-.5A0.5,0.5,0,0,1,4,2.5v3A0.5,0.5,0,0,1,3.5,6Z></path> <path class="ql-stroke ql-thin" d=M4.5,10.5h-2c0-.234,1.85-1.076,1.85-2.234A0.959,0.959,0,0,0,2.5,8.156></path> <path class="ql-stroke ql-thin" d=M2.5,14.846a0.959,0.959,0,0,0,1.85-.109A0.7,0.7,0,0,0,3.75,14a0.688,0.688,0,0,0,.6-0.736,0.959,0.959,0,0,0-1.85-.109></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class=ql-stroke x1=6 x2=15 y1=4 y2=4></line> <line class=ql-stroke x1=6 x2=15 y1=9 y2=9></line> <line class=ql-stroke x1=6 x2=15 y1=14 y2=14></line> <line class=ql-stroke x1=3 x2=3 y1=4 y2=4></line> <line class=ql-stroke x1=3 x2=3 y1=9 y2=9></line> <line class=ql-stroke x1=3 x2=3 y1=14 y2=14></line> </svg>'},function(t,e){t.exports='<svg class="" viewbox="0 0 18 18"> <line class=ql-stroke x1=9 x2=15 y1=4 y2=4></line> <polyline class=ql-stroke points="3 4 4 5 6 3"></polyline> <line class=ql-stroke x1=9 x2=15 y1=14 y2=14></line> <polyline class=ql-stroke points="3 14 4 15 6 13"></polyline> <line class=ql-stroke x1=9 x2=15 y1=9 y2=9></line> <polyline class=ql-stroke points="3 9 4 10 6 8"></polyline> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,15H13.861a3.858,3.858,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.921,1.921,0,0,0,12.021,11.7a0.50013,0.50013,0,1,0,.957.291h0a0.914,0.914,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.076-1.16971,1.86982-1.93971,2.43082A1.45639,1.45639,0,0,0,12,15.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,15Z /> <path class=ql-fill d=M9.65,5.241a1,1,0,0,0-1.409.108L6,7.964,3.759,5.349A1,1,0,0,0,2.192,6.59178Q2.21541,6.6213,2.241,6.649L4.684,9.5,2.241,12.35A1,1,0,0,0,3.71,13.70722q0.02557-.02768.049-0.05722L6,11.036,8.241,13.65a1,1,0,1,0,1.567-1.24277Q9.78459,12.3777,9.759,12.35L7.316,9.5,9.759,6.651A1,1,0,0,0,9.65,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-fill d=M15.5,7H13.861a4.015,4.015,0,0,0,1.914-2.975,1.8,1.8,0,0,0-1.6-1.751A1.922,1.922,0,0,0,12.021,3.7a0.5,0.5,0,1,0,.957.291,0.917,0.917,0,0,1,1.053-.725,0.81,0.81,0,0,1,.744.762c0,1.077-1.164,1.925-1.934,2.486A1.423,1.423,0,0,0,12,7.5a0.5,0.5,0,0,0,.5.5h3A0.5,0.5,0,0,0,15.5,7Z /> <path class=ql-fill d=M9.651,5.241a1,1,0,0,0-1.41.108L6,7.964,3.759,5.349a1,1,0,1,0-1.519,1.3L4.683,9.5,2.241,12.35a1,1,0,1,0,1.519,1.3L6,11.036,8.241,13.65a1,1,0,0,0,1.519-1.3L7.317,9.5,9.759,6.651A1,1,0,0,0,9.651,5.241Z /> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <line class="ql-stroke ql-thin" x1=15.5 x2=2.5 y1=8.5 y2=9.5></line> <path class=ql-fill d=M9.007,8C6.542,7.791,6,7.519,6,6.5,6,5.792,7.283,5,9,5c1.571,0,2.765.679,2.969,1.309a1,1,0,0,0,1.9-.617C13.356,4.106,11.354,3,9,3,6.2,3,4,4.538,4,6.5a3.2,3.2,0,0,0,.5,1.843Z></path> <path class=ql-fill d=M8.984,10C11.457,10.208,12,10.479,12,11.5c0,0.708-1.283,1.5-3,1.5-1.571,0-2.765-.679-2.969-1.309a1,1,0,1,0-1.9.617C4.644,13.894,6.646,15,9,15c2.8,0,5-1.538,5-3.5a3.2,3.2,0,0,0-.5-1.843Z></path> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <path class=ql-stroke d=M5,3V9a4.012,4.012,0,0,0,4,4H9a4.012,4.012,0,0,0,4-4V3></path> <rect class=ql-fill height=1 rx=0.5 ry=0.5 width=12 x=3 y=15></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <rect class=ql-stroke height=12 width=12 x=3 y=3></rect> <rect class=ql-fill height=12 width=1 x=5 y=3></rect> <rect class=ql-fill height=12 width=1 x=12 y=3></rect> <rect class=ql-fill height=2 width=8 x=5 y=8></rect> <rect class=ql-fill height=1 width=3 x=3 y=5></rect> <rect class=ql-fill height=1 width=3 x=3 y=7></rect> <rect class=ql-fill height=1 width=3 x=3 y=10></rect> <rect class=ql-fill height=1 width=3 x=3 y=12></rect> <rect class=ql-fill height=1 width=3 x=12 y=5></rect> <rect class=ql-fill height=1 width=3 x=12 y=7></rect> <rect class=ql-fill height=1 width=3 x=12 y=10></rect> <rect class=ql-fill height=1 width=3 x=12 y=12></rect> </svg>'},function(t,e){t.exports='<svg viewbox="0 0 18 18"> <polygon class=ql-stroke points="7 11 9 13 11 11 7 11"></polygon> <polygon class=ql-stroke points="7 7 9 5 11 7 7 7"></polygon> </svg>'},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=e.BubbleTooltip=void 0;var r=function t(e,n,r){null===e&&(e=Function.prototype);var o=Object.getOwnPropertyDescriptor(e,n);if(void 0===o){var i=Object.getPrototypeOf(e);return null===i?void 0:t(i,n,r)}if("value"in o)return o.value;var a=o.get;return void 0!==a?a.call(r):void 0},o=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),i=f(n(3)),a=f(n(8)),l=n(43),s=f(l),u=n(15),c=f(n(41));function f(t){return t&&t.__esModule?t:{default:t}}function p(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function h(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==typeof e&&"function"!==typeof e?t:e}function d(t,e){if("function"!==typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var A=[["bold","italic","link"],[{header:1},{header:2},"blockquote"]],y=function(t){function e(t,n){p(this,e),null!=n.modules.toolbar&&null==n.modules.toolbar.container&&(n.modules.toolbar.container=A);var r=h(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.quill.container.classList.add("ql-bubble"),r}return d(e,t),o(e,[{key:"extendToolbar",value:function(t){this.tooltip=new g(this.quill,this.options.bounds),this.tooltip.root.appendChild(t.container),this.buildButtons([].slice.call(t.container.querySelectorAll("button")),c.default),this.buildPickers([].slice.call(t.container.querySelectorAll("select")),c.default)}}]),e}(s.default);y.DEFAULTS=(0,i.default)(!0,{},s.default.DEFAULTS,{modules:{toolbar:{handlers:{link:function(t){t?this.quill.theme.tooltip.edit():this.quill.format("link",!1)}}}}});var g=function(t){function e(t,n){p(this,e);var r=h(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t,n));return r.quill.on(a.default.events.EDITOR_CHANGE,(function(t,e,n,o){if(t===a.default.events.SELECTION_CHANGE)if(null!=e&&e.length>0&&o===a.default.sources.USER){r.show(),r.root.style.left="0px",r.root.style.width="",r.root.style.width=r.root.offsetWidth+"px";var i=r.quill.getLines(e.index,e.length);if(1===i.length)r.position(r.quill.getBounds(e));else{var l=i[i.length-1],s=r.quill.getIndex(l),c=Math.min(l.length()-1,e.index+e.length-s),f=r.quill.getBounds(new u.Range(s,c));r.position(f)}}else document.activeElement!==r.textbox&&r.quill.hasFocus()&&r.hide()})),r}return d(e,t),o(e,[{key:"listen",value:function(){var t=this;r(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"listen",this).call(this),this.root.querySelector(".ql-close").addEventListener("click",(function(){t.root.classList.remove("ql-editing")})),this.quill.on(a.default.events.SCROLL_OPTIMIZE,(function(){setTimeout((function(){if(!t.root.classList.contains("ql-hidden")){var e=t.quill.getSelection();null!=e&&t.position(t.quill.getBounds(e))}}),1)}))}},{key:"cancel",value:function(){this.show()}},{key:"position",value:function(t){var n=r(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"position",this).call(this,t),o=this.root.querySelector(".ql-tooltip-arrow");if(o.style.marginLeft="",0===n)return n;o.style.marginLeft=-1*n-o.offsetWidth/2+"px"}}]),e}(l.BaseTooltip);g.TEMPLATE=['<span class="ql-tooltip-arrow"></span>','<div class="ql-tooltip-editor">','<input type="text" data-formula="e=mc^2" data-link="https://quilljs.com" data-video="Embed URL">','<a class="ql-close"></a>',"</div>"].join(""),e.BubbleTooltip=g,e.default=y},function(t,e,n){t.exports=n(63)}]).default},t.exports=n()}).call(this,n(962).Buffer)},962:function(t,e,n){"use strict";(function(t){var r=n(963),o=n(964),i=n(965);function a(){return s.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function l(t,e){if(a()<e)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e)).__proto__=s.prototype:(null===t&&(t=new s(e)),t.length=e),t}function s(t,e,n){if(!s.TYPED_ARRAY_SUPPORT&&!(this instanceof s))return new s(t,e,n);if("number"===typeof t){if("string"===typeof e)throw new Error("If encoding is specified then the first argument must be a string");return f(this,t)}return u(this,t,e,n)}function u(t,e,n,r){if("number"===typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!==typeof ArrayBuffer&&e instanceof ArrayBuffer?function(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r);s.TYPED_ARRAY_SUPPORT?(t=e).__proto__=s.prototype:t=p(t,e);return t}(t,e,n,r):"string"===typeof e?function(t,e,n){"string"===typeof n&&""!==n||(n="utf8");if(!s.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|d(e,n),o=(t=l(t,r)).write(e,n);o!==r&&(t=t.slice(0,o));return t}(t,e,n):function(t,e){if(s.isBuffer(e)){var n=0|h(e.length);return 0===(t=l(t,n)).length||e.copy(t,0,0,n),t}if(e){if("undefined"!==typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!==typeof e.length||(r=e.length)!==r?l(t,0):p(t,e);if("Buffer"===e.type&&i(e.data))return p(t,e.data)}var r;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(t,e)}function c(t){if("number"!==typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function f(t,e){if(c(e),t=l(t,e<0?0:0|h(e)),!s.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function p(t,e){var n=e.length<0?0:0|h(e.length);t=l(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function h(t){if(t>=a())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+a().toString(16)+" bytes");return 0|t}function d(t,e){if(s.isBuffer(t))return t.length;if("undefined"!==typeof ArrayBuffer&&"function"===typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!==typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return U(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return D(t).length;default:if(r)return U(t).length;e=(""+e).toLowerCase(),r=!0}}function A(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(e>>>=0))return"";for(t||(t="utf8");;)switch(t){case"hex":return j(this,e,n);case"utf8":case"utf-8":return x(this,e,n);case"ascii":return _(this,e,n);case"latin1":case"binary":return N(this,e,n);case"base64":return k(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return B(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function y(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function g(t,e,n,r,o){if(0===t.length)return-1;if("string"===typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=o?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(o)return-1;n=t.length-1}else if(n<0){if(!o)return-1;n=0}if("string"===typeof e&&(e=s.from(e,r)),s.isBuffer(e))return 0===e.length?-1:v(t,e,n,r,o);if("number"===typeof e)return e&=255,s.TYPED_ARRAY_SUPPORT&&"function"===typeof Uint8Array.prototype.indexOf?o?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):v(t,[e],n,r,o);throw new TypeError("val must be string, number or Buffer")}function v(t,e,n,r,o){var i,a=1,l=t.length,s=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;a=2,l/=2,s/=2,n/=2}function u(t,e){return 1===a?t[e]:t.readUInt16BE(e*a)}if(o){var c=-1;for(i=n;i<l;i++)if(u(t,i)===u(e,-1===c?0:i-c)){if(-1===c&&(c=i),i-c+1===s)return c*a}else-1!==c&&(i-=i-c),c=-1}else for(n+s>l&&(n=l-s),i=n;i>=0;i--){for(var f=!0,p=0;p<s;p++)if(u(t,i+p)!==u(e,p)){f=!1;break}if(f)return i}return-1}function b(t,e,n,r){n=Number(n)||0;var o=t.length-n;r?(r=Number(r))>o&&(r=o):r=o;var i=e.length;if(i%2!==0)throw new TypeError("Invalid hex string");r>i/2&&(r=i/2);for(var a=0;a<r;++a){var l=parseInt(e.substr(2*a,2),16);if(isNaN(l))return a;t[n+a]=l}return a}function m(t,e,n,r){return F(U(e,t.length-n),t,n,r)}function E(t,e,n,r){return F(function(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}(e),t,n,r)}function O(t,e,n,r){return E(t,e,n,r)}function w(t,e,n,r){return F(D(e),t,n,r)}function C(t,e,n,r){return F(function(t,e){for(var n,r,o,i=[],a=0;a<t.length&&!((e-=2)<0);++a)n=t.charCodeAt(a),r=n>>8,o=n%256,i.push(o),i.push(r);return i}(e,t.length-n),t,n,r)}function k(t,e,n){return 0===e&&n===t.length?r.fromByteArray(t):r.fromByteArray(t.slice(e,n))}function x(t,e,n){n=Math.min(t.length,n);for(var r=[],o=e;o<n;){var i,a,l,s,u=t[o],c=null,f=u>239?4:u>223?3:u>191?2:1;if(o+f<=n)switch(f){case 1:u<128&&(c=u);break;case 2:128===(192&(i=t[o+1]))&&(s=(31&u)<<6|63&i)>127&&(c=s);break;case 3:i=t[o+1],a=t[o+2],128===(192&i)&&128===(192&a)&&(s=(15&u)<<12|(63&i)<<6|63&a)>2047&&(s<55296||s>57343)&&(c=s);break;case 4:i=t[o+1],a=t[o+2],l=t[o+3],128===(192&i)&&128===(192&a)&&128===(192&l)&&(s=(15&u)<<18|(63&i)<<12|(63&a)<<6|63&l)>65535&&s<1114112&&(c=s)}null===c?(c=65533,f=1):c>65535&&(c-=65536,r.push(c>>>10&1023|55296),c=56320|1023&c),r.push(c),o+=f}return function(t){var e=t.length;if(e<=4096)return String.fromCharCode.apply(String,t);var n="",r=0;for(;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=4096));return n}(r)}e.Buffer=s,e.SlowBuffer=function(t){+t!=t&&(t=0);return s.alloc(+t)},e.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"===typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(e){return!1}}(),e.kMaxLength=a(),s.poolSize=8192,s._augment=function(t){return t.__proto__=s.prototype,t},s.from=function(t,e,n){return u(null,t,e,n)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!==typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(t,e,n){return function(t,e,n,r){return c(e),e<=0?l(t,e):void 0!==n?"string"===typeof r?l(t,e).fill(n,r):l(t,e).fill(n):l(t,e)}(null,t,e,n)},s.allocUnsafe=function(t){return f(null,t)},s.allocUnsafeSlow=function(t){return f(null,t)},s.isBuffer=function(t){return!(null==t||!t._isBuffer)},s.compare=function(t,e){if(!s.isBuffer(t)||!s.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,o=0,i=Math.min(n,r);o<i;++o)if(t[o]!==e[o]){n=t[o],r=e[o];break}return n<r?-1:r<n?1:0},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!i(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=s.allocUnsafe(e),o=0;for(n=0;n<t.length;++n){var a=t[n];if(!s.isBuffer(a))throw new TypeError('"list" argument must be an Array of Buffers');a.copy(r,o),o+=a.length}return r},s.byteLength=d,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)y(this,e,e+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)y(this,e,e+3),y(this,e+1,e+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)y(this,e,e+7),y(this,e+1,e+6),y(this,e+2,e+5),y(this,e+3,e+4);return this},s.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?x(this,0,t):A.apply(this,arguments)},s.prototype.equals=function(t){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},s.prototype.compare=function(t,e,n,r,o){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===o&&(o=this.length),e<0||n>t.length||r<0||o>this.length)throw new RangeError("out of range index");if(r>=o&&e>=n)return 0;if(r>=o)return-1;if(e>=n)return 1;if(this===t)return 0;for(var i=(o>>>=0)-(r>>>=0),a=(n>>>=0)-(e>>>=0),l=Math.min(i,a),u=this.slice(r,o),c=t.slice(e,n),f=0;f<l;++f)if(u[f]!==c[f]){i=u[f],a=c[f];break}return i<a?-1:a<i?1:0},s.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},s.prototype.indexOf=function(t,e,n){return g(this,t,e,n,!0)},s.prototype.lastIndexOf=function(t,e,n){return g(this,t,e,n,!1)},s.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"===typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var o=this.length-e;if((void 0===n||n>o)&&(n=o),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var i=!1;;)switch(r){case"hex":return b(this,t,e,n);case"utf8":case"utf-8":return m(this,t,e,n);case"ascii":return E(this,t,e,n);case"latin1":case"binary":return O(this,t,e,n);case"base64":return w(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return C(this,t,e,n);default:if(i)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),i=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function _(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(127&t[o]);return r}function N(t,e,n){var r="";n=Math.min(t.length,n);for(var o=e;o<n;++o)r+=String.fromCharCode(t[o]);return r}function j(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var o="",i=e;i<n;++i)o+=M(t[i]);return o}function B(t,e,n){for(var r=t.slice(e,n),o="",i=0;i<r.length;i+=2)o+=String.fromCharCode(r[i]+256*r[i+1]);return o}function I(t,e,n){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function S(t,e,n,r,o,i){if(!s.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<i)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function T(t,e,n,r){e<0&&(e=65535+e+1);for(var o=0,i=Math.min(t.length-n,2);o<i;++o)t[n+o]=(e&255<<8*(r?o:1-o))>>>8*(r?o:1-o)}function P(t,e,n,r){e<0&&(e=4294967295+e+1);for(var o=0,i=Math.min(t.length-n,4);o<i;++o)t[n+o]=e>>>8*(r?o:3-o)&255}function Q(t,e,n,r,o,i){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function q(t,e,n,r,i){return i||Q(t,0,n,4),o.write(t,e,n,r,23,4),n+4}function R(t,e,n,r,i){return i||Q(t,0,n,8),o.write(t,e,n,r,52,8),n+8}s.prototype.slice=function(t,e){var n,r=this.length;if((t=~~t)<0?(t+=r)<0&&(t=0):t>r&&(t=r),(e=void 0===e?r:~~e)<0?(e+=r)<0&&(e=0):e>r&&(e=r),e<t&&(e=t),s.TYPED_ARRAY_SUPPORT)(n=this.subarray(t,e)).__proto__=s.prototype;else{var o=e-t;n=new s(o,void 0);for(var i=0;i<o;++i)n[i]=this[i+t]}return n},s.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||I(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r},s.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||I(t,e,this.length);for(var r=this[t+--e],o=1;e>0&&(o*=256);)r+=this[t+--e]*o;return r},s.prototype.readUInt8=function(t,e){return e||I(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,e){return e||I(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,e){return e||I(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,e){return e||I(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},s.prototype.readUInt32BE=function(t,e){return e||I(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||I(t,e,this.length);for(var r=this[t],o=1,i=0;++i<e&&(o*=256);)r+=this[t+i]*o;return r>=(o*=128)&&(r-=Math.pow(2,8*e)),r},s.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||I(t,e,this.length);for(var r=e,o=1,i=this[t+--r];r>0&&(o*=256);)i+=this[t+--r]*o;return i>=(o*=128)&&(i-=Math.pow(2,8*e)),i},s.prototype.readInt8=function(t,e){return e||I(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},s.prototype.readInt16LE=function(t,e){e||I(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(t,e){e||I(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(t,e){return e||I(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return e||I(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,e){return e||I(t,4,this.length),o.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return e||I(t,4,this.length),o.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return e||I(t,8,this.length),o.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return e||I(t,8,this.length),o.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,e,n,r){(t=+t,e|=0,n|=0,r)||S(this,t,e,n,Math.pow(2,8*n)-1,0);var o=1,i=0;for(this[e]=255&t;++i<n&&(o*=256);)this[e+i]=t/o&255;return e+n},s.prototype.writeUIntBE=function(t,e,n,r){(t=+t,e|=0,n|=0,r)||S(this,t,e,n,Math.pow(2,8*n)-1,0);var o=n-1,i=1;for(this[e+o]=255&t;--o>=0&&(i*=256);)this[e+o]=t/i&255;return e+n},s.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,1,255,0),s.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},s.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):T(this,t,e,!0),e+2},s.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):T(this,t,e,!1),e+2},s.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):P(this,t,e,!0),e+4},s.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):P(this,t,e,!1),e+4},s.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);S(this,t,e,n,o-1,-o)}var i=0,a=1,l=0;for(this[e]=255&t;++i<n&&(a*=256);)t<0&&0===l&&0!==this[e+i-1]&&(l=1),this[e+i]=(t/a>>0)-l&255;return e+n},s.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var o=Math.pow(2,8*n-1);S(this,t,e,n,o-1,-o)}var i=n-1,a=1,l=0;for(this[e+i]=255&t;--i>=0&&(a*=256);)t<0&&0===l&&0!==this[e+i+1]&&(l=1),this[e+i]=(t/a>>0)-l&255;return e+n},s.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,1,127,-128),s.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):T(this,t,e,!0),e+2},s.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):T(this,t,e,!1),e+2},s.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,4,2147483647,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):P(this,t,e,!0),e+4},s.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||S(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):P(this,t,e,!1),e+4},s.prototype.writeFloatLE=function(t,e,n){return q(this,t,e,!0,n)},s.prototype.writeFloatBE=function(t,e,n){return q(this,t,e,!1,n)},s.prototype.writeDoubleLE=function(t,e,n){return R(this,t,e,!0,n)},s.prototype.writeDoubleBE=function(t,e,n){return R(this,t,e,!1,n)},s.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var o,i=r-n;if(this===t&&n<e&&e<r)for(o=i-1;o>=0;--o)t[o+e]=this[o+n];else if(i<1e3||!s.TYPED_ARRAY_SUPPORT)for(o=0;o<i;++o)t[o+e]=this[o+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+i),e);return i},s.prototype.fill=function(t,e,n,r){if("string"===typeof t){if("string"===typeof e?(r=e,e=0,n=this.length):"string"===typeof n&&(r=n,n=this.length),1===t.length){var o=t.charCodeAt(0);o<256&&(t=o)}if(void 0!==r&&"string"!==typeof r)throw new TypeError("encoding must be a string");if("string"===typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"===typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;var i;if(e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0),"number"===typeof t)for(i=e;i<n;++i)this[i]=t;else{var a=s.isBuffer(t)?t:U(new s(t,r).toString()),l=a.length;for(i=0;i<n-e;++i)this[i+e]=a[i%l]}return this};var L=/[^+\/0-9A-Za-z-_]/g;function M(t){return t<16?"0"+t.toString(16):t.toString(16)}function U(t,e){var n;e=e||1/0;for(var r=t.length,o=null,i=[],a=0;a<r;++a){if((n=t.charCodeAt(a))>55295&&n<57344){if(!o){if(n>56319){(e-=3)>-1&&i.push(239,191,189);continue}if(a+1===r){(e-=3)>-1&&i.push(239,191,189);continue}o=n;continue}if(n<56320){(e-=3)>-1&&i.push(239,191,189),o=n;continue}n=65536+(o-55296<<10|n-56320)}else o&&(e-=3)>-1&&i.push(239,191,189);if(o=null,n<128){if((e-=1)<0)break;i.push(n)}else if(n<2048){if((e-=2)<0)break;i.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;i.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;i.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return i}function D(t){return r.toByteArray(function(t){if((t=function(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}(t).replace(L,"")).length<2)return"";for(;t.length%4!==0;)t+="=";return t}(t))}function F(t,e,n,r){for(var o=0;o<r&&!(o+n>=e.length||o>=t.length);++o)e[o+n]=t[o];return o}}).call(this,n(164))},963:function(t,e,n){"use strict";e.byteLength=function(t){var e=u(t),n=e[0],r=e[1];return 3*(n+r)/4-r},e.toByteArray=function(t){var e,n,r=u(t),a=r[0],l=r[1],s=new i(function(t,e,n){return 3*(e+n)/4-n}(0,a,l)),c=0,f=l>0?a-4:a;for(n=0;n<f;n+=4)e=o[t.charCodeAt(n)]<<18|o[t.charCodeAt(n+1)]<<12|o[t.charCodeAt(n+2)]<<6|o[t.charCodeAt(n+3)],s[c++]=e>>16&255,s[c++]=e>>8&255,s[c++]=255&e;2===l&&(e=o[t.charCodeAt(n)]<<2|o[t.charCodeAt(n+1)]>>4,s[c++]=255&e);1===l&&(e=o[t.charCodeAt(n)]<<10|o[t.charCodeAt(n+1)]<<4|o[t.charCodeAt(n+2)]>>2,s[c++]=e>>8&255,s[c++]=255&e);return s},e.fromByteArray=function(t){for(var e,n=t.length,o=n%3,i=[],a=0,l=n-o;a<l;a+=16383)i.push(c(t,a,a+16383>l?l:a+16383));1===o?(e=t[n-1],i.push(r[e>>2]+r[e<<4&63]+"==")):2===o&&(e=(t[n-2]<<8)+t[n-1],i.push(r[e>>10]+r[e>>4&63]+r[e<<2&63]+"="));return i.join("")};for(var r=[],o=[],i="undefined"!==typeof Uint8Array?Uint8Array:Array,a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=0,s=a.length;l<s;++l)r[l]=a[l],o[a.charCodeAt(l)]=l;function u(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var n=t.indexOf("=");return-1===n&&(n=e),[n,n===e?0:4-n%4]}function c(t,e,n){for(var o,i,a=[],l=e;l<n;l+=3)o=(t[l]<<16&16711680)+(t[l+1]<<8&65280)+(255&t[l+2]),a.push(r[(i=o)>>18&63]+r[i>>12&63]+r[i>>6&63]+r[63&i]);return a.join("")}o["-".charCodeAt(0)]=62,o["_".charCodeAt(0)]=63},964:function(t,e){e.read=function(t,e,n,r,o){var i,a,l=8*o-r-1,s=(1<<l)-1,u=s>>1,c=-7,f=n?o-1:0,p=n?-1:1,h=t[e+f];for(f+=p,i=h&(1<<-c)-1,h>>=-c,c+=l;c>0;i=256*i+t[e+f],f+=p,c-=8);for(a=i&(1<<-c)-1,i>>=-c,c+=r;c>0;a=256*a+t[e+f],f+=p,c-=8);if(0===i)i=1-u;else{if(i===s)return a?NaN:1/0*(h?-1:1);a+=Math.pow(2,r),i-=u}return(h?-1:1)*a*Math.pow(2,i-r)},e.write=function(t,e,n,r,o,i){var a,l,s,u=8*i-o-1,c=(1<<u)-1,f=c>>1,p=23===o?Math.pow(2,-24)-Math.pow(2,-77):0,h=r?0:i-1,d=r?1:-1,A=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(l=isNaN(e)?1:0,a=c):(a=Math.floor(Math.log(e)/Math.LN2),e*(s=Math.pow(2,-a))<1&&(a--,s*=2),(e+=a+f>=1?p/s:p*Math.pow(2,1-f))*s>=2&&(a++,s/=2),a+f>=c?(l=0,a=c):a+f>=1?(l=(e*s-1)*Math.pow(2,o),a+=f):(l=e*Math.pow(2,f-1)*Math.pow(2,o),a=0));o>=8;t[n+h]=255&l,h+=d,l/=256,o-=8);for(a=a<<o|l,u+=o;u>0;t[n+h]=255&a,h+=d,a/=256,u-=8);t[n+h-d]|=128*A}},965:function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},966:function(t,e,n){},967:function(t,e){t.exports="data:image/png;base64,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"}}]);
(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[29],{1410:function(t,e,n){t.exports={container:"attentions_container__2-RmY",empty:"attentions_empty__tfoXB",noData:"attentions_noData__Y8pty",text:"attentions_text__1rR6g"}},1451:function(t,e,n){"use strict";n.r(e);var a=n(6),r=n.n(a),o=n(536),c=n(9),s=n(163),i=n(5),u=n.n(i),l=n(914),f=n(530),m=n(1410),p=n.n(m),d=n(508);e.default=function(t){var e=t.history,a=Object(i.useState)([]),m=Object(s.a)(a,2),b=m[0],y=m[1],h=Object(i.useState)(!1),E=Object(s.a)(h,2),w=E[0],v=E[1],j=function(){var t=Object(c.a)(r.a.mark((function t(){var e,n,a,c;return r.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(d.j)();case 3:if(e=t.sent,window.console.log("getFollowersList--sucess--",e),!(e&&"00000"===e.retCode&&e.data&&e.data.followers)){t.next=13;break}return(a=(n=[]).concat.apply(n,Object(o.a)(b))).length||(a=(c=[]).concat.apply(c,Object(o.a)(e.data.followers))),window.console.log("attentionArr--sucess--",a),a.forEach((function(t){e.data.followers.forEach((function(e){t.userId===e.userId&&(t.icon=e.icon,t.nickname=e.nickname,t.contentCount=e.contentCount,t.fansCount=e.fansCount,t.status=e.status)}))})),y(a),v(!0),t.abrupt("return");case 13:y([]),v(!0),t.next=22;break;case 17:t.prev=17,t.t0=t.catch(0),window.console.log("getFollowersList--err--",t.t0),y([]),v(!0);case 22:case"end":return t.stop()}}),t,null,[[0,17]])})));return function(){return t.apply(this,arguments)}}(),g=function(t){var e,n=(e=[]).concat.apply(e,Object(o.a)(b));n.forEach((function(e){e.userId===t&&(e.fansCount-=1,e.status=2)})),y(n)};return Object(i.useEffect)((function(){j()}),[]),u.a.createElement(u.a.Fragment,null,u.a.createElement(f.a,{title:"\u5173\u6ce8"}),!1===w?u.a.createElement("div",{className:p.a.container}):u.a.createElement("div",{className:p.a.container},b.length>0?b.map((function(t,n){return u.a.createElement("div",{style:{background:"#FFFFFF"},key:n},u.a.createElement(l.a,Object.assign({key:n},t,{history:e,showType:0,update:j,change:g})),n!==b.length-1?u.a.createElement("div",{style:{borderBottom:"0.5px solid #EEEEEE",width:"100%",marginLeft:"0.24rem"}}):"")})):u.a.createElement("div",{className:p.a.empty},u.a.createElement("img",{className:p.a.noData,src:n(915),alt:""}),u.a.createElement("div",{className:p.a.text},"\u60a8\u8fd8\u6ca1\u6709\u5173\u6ce8\u8fc7\u522b\u4eba"))))}},536:function(t,e,n){"use strict";var a=n(116);var r=n(165);function o(t){return function(t){if(Array.isArray(t))return Object(a.a)(t)}(t)||function(t){if("undefined"!==typeof Symbol&&Symbol.iterator in Object(t))return Array.from(t)}(t)||Object(r.a)(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}n.d(e,"a",(function(){return o}))}}]);
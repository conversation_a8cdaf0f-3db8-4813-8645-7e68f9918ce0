(this["webpackJsonpapp-home"]=this["webpackJsonpapp-home"]||[]).push([[4],{583:function(t,e,i){"use strict";i(514),i(708)},584:function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=p(i(512)),o=p(i(46)),s=p(i(0)),r=p(i(1)),a=p(i(2)),l=p(i(3)),u=p(i(504)),c=function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e.default=t,e}(i(5)),d=p(i(786));function p(t){return t&&t.__esModule?t:{default:t}}var h=function(t,e){var i={};for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e.indexOf(n)<0&&(i[n]=t[n]);if(null!=t&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(n=Object.getOwnPropertySymbols(t);o<n.length;o++)e.indexOf(n[o])<0&&(i[n[o]]=t[n[o]])}return i},f=function(t){function e(t){(0,s.default)(this,e);var i=(0,a.default)(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return i.onChange=function(t){i.setState({selectedIndex:t},(function(){i.props.afterChange&&i.props.afterChange(t)}))},i.state={selectedIndex:i.props.selectedIndex},i}return(0,l.default)(e,t),(0,r.default)(e,[{key:"render",value:function(){var t=this.props,e=t.infinite,i=t.selectedIndex,s=t.beforeChange,r=(t.afterChange,t.dots),a=h(t,["infinite","selectedIndex","beforeChange","afterChange","dots"]),l=a.prefixCls,p=a.dotActiveStyle,f=a.dotStyle,g=a.className,v=a.vertical,S=(0,o.default)({},a,{wrapAround:e,slideIndex:i,beforeSlide:s}),y=[];r&&(y=[{component:function(t){for(var e=t.slideCount,i=t.slidesToScroll,o=t.currentSlide,s=[],r=0;r<e;r+=i)s.push(r);var a=s.map((function(t){var e=(0,u.default)(l+"-wrap-dot",(0,n.default)({},l+"-wrap-dot-active",t===o)),i=t===o?p:f;return c.createElement("div",{className:e,key:t},c.createElement("span",{style:i}))}));return c.createElement("div",{className:l+"-wrap"},a)},position:"BottomCenter"}]);var m=(0,u.default)(l,g,(0,n.default)({},l+"-vertical",v));return c.createElement(d.default,(0,o.default)({},S,{className:m,decorators:y,afterSlide:this.onChange}))}}]),e}(c.Component);e.default=f,f.defaultProps={prefixCls:"am-carousel",dots:!0,arrows:!1,autoplay:!1,infinite:!1,cellAlign:"center",selectedIndex:0,dotStyle:{},dotActiveStyle:{}},t.exports=e.default},676:function(t,e,i){(function(e){for(var n=i(710),o="undefined"===typeof window?e:window,s=["moz","webkit"],r="AnimationFrame",a=o["request"+r],l=o["cancel"+r]||o["cancelRequest"+r],u=0;!a&&u<s.length;u++)a=o[s[u]+"Request"+r],l=o[s[u]+"Cancel"+r]||o[s[u]+"CancelRequest"+r];if(!a||!l){var c=0,d=0,p=[];a=function(t){if(0===p.length){var e=n(),i=Math.max(0,1e3/60-(e-c));c=i+e,setTimeout((function(){var t=p.slice(0);p.length=0;for(var e=0;e<t.length;e++)if(!t[e].cancelled)try{t[e].callback(c)}catch(i){setTimeout((function(){throw i}),0)}}),Math.round(i))}return p.push({handle:++d,callback:t,cancelled:!1}),d},l=function(t){for(var e=0;e<p.length;e++)p[e].handle===t&&(p[e].cancelled=!0)}}t.exports=function(t){return a.call(o,t)},t.exports.cancel=function(){l.apply(o,arguments)},t.exports.polyfill=function(t){t||(t=o),t.requestAnimationFrame=a,t.cancelAnimationFrame=l}}).call(this,i(164))},708:function(t,e,i){},709:function(t,e,i){var n;!function(){"use strict";var o=!("undefined"===typeof window||!window.document||!window.document.createElement),s={canUseDOM:o,canUseWorkers:"undefined"!==typeof Worker,canUseEventListeners:o&&!(!window.addEventListener&&!window.attachEvent),canUseViewport:o&&!!window.screen};void 0===(n=function(){return s}.call(e,i,e,t))||(t.exports=n)}()},710:function(t,e,i){(function(e){(function(){var i,n,o,s,r,a;"undefined"!==typeof performance&&null!==performance&&performance.now?t.exports=function(){return performance.now()}:"undefined"!==typeof e&&null!==e&&e.hrtime?(t.exports=function(){return(i()-r)/1e6},n=e.hrtime,s=(i=function(){var t;return 1e9*(t=n())[0]+t[1]})(),a=1e9*e.uptime(),r=s-a):Date.now?(t.exports=function(){return Date.now()-o},o=Date.now()):(t.exports=function(){return(new Date).getTime()-o},o=(new Date).getTime())}).call(this)}).call(this,i(706))},786:function(t,e,i){"use strict";i.r(e);var n=i(46),o=i.n(n),s=i(0),r=i.n(s),a=i(1),l=i.n(a),u=i(2),c=i.n(u),d=i(3),p=i.n(d),h=i(5),f=i.n(h),g=[{component:function(t){function e(){r()(this,e);var t=c()(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments));return t.handleClick=function(e){e.preventDefault(),t.props.previousSlide()},t}return p()(e,t),l()(e,[{key:"render",value:function(){return f.a.createElement("button",{style:this.getButtonStyles(0===this.props.currentSlide&&!this.props.wrapAround),onClick:this.handleClick},"PREV")}},{key:"getButtonStyles",value:function(t){return{border:0,background:"rgba(0,0,0,0.4)",color:"white",padding:10,outline:0,opacity:t?.3:1,cursor:"pointer"}}}]),e}(f.a.Component),position:"CenterLeft"},{component:function(t){function e(){r()(this,e);var t=c()(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments));return t.handleClick=function(e){e.preventDefault(),t.props.nextSlide&&t.props.nextSlide()},t}return p()(e,t),l()(e,[{key:"render",value:function(){return f.a.createElement("button",{style:this.getButtonStyles(this.props.currentSlide+this.props.slidesToScroll>=this.props.slideCount&&!this.props.wrapAround),onClick:this.handleClick},"NEXT")}},{key:"getButtonStyles",value:function(t){return{border:0,background:"rgba(0,0,0,0.4)",color:"white",padding:10,outline:0,opacity:t?.3:1,cursor:"pointer"}}}]),e}(f.a.Component),position:"CenterRight"},{component:function(t){function e(){return r()(this,e),c()(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return p()(e,t),l()(e,[{key:"render",value:function(){var t=this,e=this.getIndexes(this.props.slideCount,this.props.slidesToScroll);return f.a.createElement("ul",{style:this.getListStyles()},e.map((function(e){return f.a.createElement("li",{style:t.getListItemStyles(),key:e},f.a.createElement("button",{style:t.getButtonStyles(t.props.currentSlide===e),onClick:t.props.goToSlide&&t.props.goToSlide.bind(null,e)},"\u2022"))})))}},{key:"getIndexes",value:function(t,e){for(var i=[],n=0;n<t;n+=e)i.push(n);return i}},{key:"getListStyles",value:function(){return{position:"relative",margin:0,top:-10,padding:0}}},{key:"getListItemStyles",value:function(){return{listStyleType:"none",display:"inline-block"}}},{key:"getButtonStyles",value:function(t){return{border:0,background:"transparent",color:"black",cursor:"pointer",padding:10,outline:0,fontSize:24,opacity:t?1:.5}}}]),e}(f.a.Component),position:"BottomCenter"}],v=i(709),S=i.n(v),y=i(676),m=i.n(y);var b={ADDITIVE:"ADDITIVE",DESTRUCTIVE:"DESTRUCTIVE"},w=function(t,e,i){null!==t&&"undefined"!==typeof t&&(t.addEventListener?t.addEventListener(e,i,!1):t.attachEvent?t.attachEvent("on"+e,i):t["on"+e]=i)},T=function(t,e,i){null!==t&&"undefined"!==typeof t&&(t.removeEventListener?t.removeEventListener(e,i,!1):t.detachEvent?t.detachEvent("on"+e,i):t["on"+e]=null)},k=function(t){function e(t){r()(this,e);var i=c()(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,t));return i._rafCb=function(){var t=i.state;if(0!==t.tweenQueue.length){for(var e=Date.now(),n=[],o=0;o<t.tweenQueue.length;o++){var s=t.tweenQueue[o],r=s.initTime,a=s.config;e-r<a.duration?n.push(s):a.onEnd&&a.onEnd()}-1!==i._rafID&&(i.setState({tweenQueue:n}),i._rafID=m()(i._rafCb))}},i.handleClick=function(t){!0===i.clickSafe&&(t.preventDefault(),t.stopPropagation(),t.nativeEvent&&t.nativeEvent.stopPropagation())},i.autoplayIterator=function(){if(i.props.wrapAround)return i.nextSlide();i.state.currentSlide!==i.state.slideCount-i.state.slidesToShow?i.nextSlide():i.stopAutoplay()},i.goToSlide=function(t){var e=i.props,n=e.beforeSlide,o=e.afterSlide;if(t>=f.a.Children.count(i.props.children)||t<0){if(!i.props.wrapAround)return;if(t>=f.a.Children.count(i.props.children))return n(i.state.currentSlide,0),i.setState({currentSlide:0},(function(){i.animateSlide(null,null,i.getTargetLeft(null,t),(function(){i.animateSlide(null,.01),o(0),i.resetAutoplay(),i.setExternalData()}))}));var s=f.a.Children.count(i.props.children)-i.state.slidesToScroll;return n(i.state.currentSlide,s),i.setState({currentSlide:s},(function(){i.animateSlide(null,null,i.getTargetLeft(null,t),(function(){i.animateSlide(null,.01),o(s),i.resetAutoplay(),i.setExternalData()}))}))}n(i.state.currentSlide,t),i.setState({currentSlide:t},(function(){i.animateSlide(),i.props.afterSlide(t),i.resetAutoplay(),i.setExternalData()}))},i.nextSlide=function(){var t=f.a.Children.count(i.props.children),e=i.props.slidesToShow;if("auto"===i.props.slidesToScroll&&(e=i.state.slidesToScroll),!(i.state.currentSlide>=t-e)||i.props.wrapAround)if(i.props.wrapAround)i.goToSlide(i.state.currentSlide+i.state.slidesToScroll);else{if(1!==i.props.slideWidth)return i.goToSlide(i.state.currentSlide+i.state.slidesToScroll);i.goToSlide(Math.min(i.state.currentSlide+i.state.slidesToScroll,t-e))}},i.previousSlide=function(){i.state.currentSlide<=0&&!i.props.wrapAround||(i.props.wrapAround?i.goToSlide(i.state.currentSlide-i.state.slidesToScroll):i.goToSlide(Math.max(0,i.state.currentSlide-i.state.slidesToScroll)))},i.onResize=function(){i.setDimensions()},i.onReadyStateChange=function(){i.setDimensions()},i.state={currentSlide:i.props.slideIndex,dragging:!1,frameWidth:0,left:0,slideCount:0,slidesToScroll:i.props.slidesToScroll,slideWidth:0,top:0,tweenQueue:[]},i.touchObject={},i.clickSafe=!0,i}return p()(e,t),l()(e,[{key:"componentWillMount",value:function(){this.setInitialDimensions()}},{key:"componentDidMount",value:function(){this.setDimensions(),this.bindEvents(),this.setExternalData(),this.props.autoplay&&this.startAutoplay()}},{key:"componentWillReceiveProps",value:function(t){this.setState({slideCount:t.children.length}),this.setDimensions(t),this.props.slideIndex!==t.slideIndex&&t.slideIndex!==this.state.currentSlide&&this.goToSlide(t.slideIndex),this.props.autoplay!==t.autoplay&&(t.autoplay?this.startAutoplay():this.stopAutoplay())}},{key:"componentWillUnmount",value:function(){this.unbindEvents(),this.stopAutoplay(),m.a.cancel(this._rafID),this._rafID=-1}},{key:"tweenState",value:function(t,e){var i=this,n=e.easing,o=e.duration,s=e.delay,r=e.beginValue,a=e.endValue,l=e.onEnd,u=e.stackBehavior;this.setState((function(e){var c=e,d=void 0,p=void 0;if("string"===typeof t)d=t,p=t;else{for(var h=0;h<t.length-1;h++)c=c[t[h]];d=t[t.length-1],p=t.join("|")}var f={easing:n,duration:null==o?300:o,delay:null==s?0:s,beginValue:null==r?c[d]:r,endValue:a,onEnd:l,stackBehavior:u||"ADDITIVE"},g=e.tweenQueue;return f.stackBehavior===b.DESTRUCTIVE&&(g=e.tweenQueue.filter((function(t){return t.pathHash!==p}))),g.push({pathHash:p,config:f,initTime:Date.now()+f.delay}),c[d]=f.endValue,1===g.length&&(i._rafID=m()(i._rafCb)),{tweenQueue:g}}))}},{key:"getTweeningValue",value:function(t){var e=this.state,i=void 0,n=void 0;if("string"===typeof t)i=e[t],n=t;else{i=e;for(var o=0;o<t.length;o++)i=i[t[o]];n=t.join("|")}for(var s=Date.now(),r=0;r<e.tweenQueue.length;r++){var a=e.tweenQueue[r],l=a.pathHash,u=a.initTime,c=a.config;if(l===n){var d=s-u>c.duration?c.duration:Math.max(0,s-u);i+=(0===c.duration?c.endValue:c.easing(d,c.beginValue,c.endValue,c.duration))-c.endValue}}return i}},{key:"render",value:function(){var t=this,e=f.a.Children.count(this.props.children)>1?this.formatChildren(this.props.children):this.props.children;return f.a.createElement("div",{className:["slider",this.props.className||""].join(" "),ref:"slider",style:o()({},this.getSliderStyles(),this.props.style)},f.a.createElement("div",o()({className:"slider-frame",ref:"frame",style:this.getFrameStyles()},this.getTouchEvents(),this.getMouseEvents(),{onClick:this.handleClick}),f.a.createElement("ul",{className:"slider-list",ref:"list",style:this.getListStyles()},e)),this.props.decorators?this.props.decorators.map((function(e,i){return f.a.createElement("div",{style:o()({},t.getDecoratorStyles(e.position),e.style||{}),className:"slider-decorator-"+i,key:i},f.a.createElement(e.component,{currentSlide:t.state.currentSlide,slideCount:t.state.slideCount,frameWidth:t.state.frameWidth,slideWidth:t.state.slideWidth,slidesToScroll:t.state.slidesToScroll,cellSpacing:t.props.cellSpacing,slidesToShow:t.props.slidesToShow,wrapAround:t.props.wrapAround,nextSlide:t.nextSlide,previousSlide:t.previousSlide,goToSlide:t.goToSlide}))})):null,f.a.createElement("style",{type:"text/css",dangerouslySetInnerHTML:{__html:this.getStyleTagStyles()}}))}},{key:"getTouchEvents",value:function(){var t=this;return!1===this.props.swiping?null:{onTouchStart:function(e){t.touchObject={startX:e.touches[0].pageX,startY:e.touches[0].pageY},t.handleMouseOver()},onTouchMove:function(e){var i=t.swipeDirection(t.touchObject.startX,e.touches[0].pageX,t.touchObject.startY,e.touches[0].pageY);0!==i&&e.preventDefault();var n=t.props.vertical?Math.round(Math.sqrt(Math.pow(e.touches[0].pageY-t.touchObject.startY,2))):Math.round(Math.sqrt(Math.pow(e.touches[0].pageX-t.touchObject.startX,2)));t.touchObject={startX:t.touchObject.startX,startY:t.touchObject.startY,endX:e.touches[0].pageX,endY:e.touches[0].pageY,length:n,direction:i},t.setState({left:t.props.vertical?0:t.getTargetLeft(t.touchObject.length*t.touchObject.direction),top:t.props.vertical?t.getTargetLeft(t.touchObject.length*t.touchObject.direction):0})},onTouchEnd:function(e){t.handleSwipe(e),t.handleMouseOut()},onTouchCancel:function(e){t.handleSwipe(e)}}}},{key:"getMouseEvents",value:function(){var t=this;return!1===this.props.dragging?null:{onMouseOver:function(){t.handleMouseOver()},onMouseOut:function(){t.handleMouseOut()},onMouseDown:function(e){t.touchObject={startX:e.clientX,startY:e.clientY},t.setState({dragging:!0})},onMouseMove:function(e){if(t.state.dragging){var i=t.swipeDirection(t.touchObject.startX,e.clientX,t.touchObject.startY,e.clientY);0!==i&&e.preventDefault();var n=t.props.vertical?Math.round(Math.sqrt(Math.pow(e.clientY-t.touchObject.startY,2))):Math.round(Math.sqrt(Math.pow(e.clientX-t.touchObject.startX,2)));t.touchObject={startX:t.touchObject.startX,startY:t.touchObject.startY,endX:e.clientX,endY:e.clientY,length:n,direction:i},t.setState({left:t.props.vertical?0:t.getTargetLeft(t.touchObject.length*t.touchObject.direction),top:t.props.vertical?t.getTargetLeft(t.touchObject.length*t.touchObject.direction):0})}},onMouseUp:function(e){t.state.dragging&&t.handleSwipe(e)},onMouseLeave:function(e){t.state.dragging&&t.handleSwipe(e)}}}},{key:"handleMouseOver",value:function(){this.props.autoplay&&(this.autoplayPaused=!0,this.stopAutoplay())}},{key:"handleMouseOut",value:function(){this.props.autoplay&&this.autoplayPaused&&(this.startAutoplay(),this.autoplayPaused=null)}},{key:"handleSwipe",value:function(t){"undefined"!==typeof this.touchObject.length&&this.touchObject.length>44?this.clickSafe=!0:this.clickSafe=!1;var e=this.props,i=e.slidesToShow,n=e.slidesToScroll,o=e.swipeSpeed;"auto"===n&&(i=this.state.slidesToScroll),f.a.Children.count(this.props.children)>1&&this.touchObject.length>this.state.slideWidth/i/o?1===this.touchObject.direction?this.state.currentSlide>=f.a.Children.count(this.props.children)-i&&!this.props.wrapAround?this.animateSlide(this.props.edgeEasing):this.nextSlide():-1===this.touchObject.direction&&(this.state.currentSlide<=0&&!this.props.wrapAround?this.animateSlide(this.props.edgeEasing):this.previousSlide()):this.goToSlide(this.state.currentSlide),this.touchObject={},this.setState({dragging:!1})}},{key:"swipeDirection",value:function(t,e,i,n){var o=t-e,s=i-n,r=Math.atan2(s,o),a=Math.round(180*r/Math.PI);return a<0&&(a=360-Math.abs(a)),a<=45&&a>=0||a<=360&&a>=315?1:a>=135&&a<=225?-1:!0===this.props.vertical?a>=35&&a<=135?1:-1:0}},{key:"startAutoplay",value:function(){f.a.Children.count(this.props.children)<=1||(this.autoplayID=setInterval(this.autoplayIterator,this.props.autoplayInterval))}},{key:"resetAutoplay",value:function(){this.props.resetAutoplay&&this.props.autoplay&&!this.autoplayPaused&&(this.stopAutoplay(),this.startAutoplay())}},{key:"stopAutoplay",value:function(){this.autoplayID&&clearInterval(this.autoplayID)}},{key:"animateSlide",value:function(t,e,i,n){this.tweenState(this.props.vertical?"top":"left",{easing:t||this.props.easing,duration:e||this.props.speed,endValue:i||this.getTargetLeft(),delay:null,beginValue:null,onEnd:n||null,stackBehavior:b})}},{key:"getTargetLeft",value:function(t,e){var i=void 0,n=e||this.state.currentSlide,o=this.props.cellSpacing;switch(this.props.cellAlign){case"left":i=0,i-=o*n;break;case"center":i=(this.state.frameWidth-this.state.slideWidth)/2,i-=o*n;break;case"right":i=this.state.frameWidth-this.state.slideWidth,i-=o*n}var s=this.state.slideWidth*n;return this.state.currentSlide>0&&n+this.state.slidesToScroll>=this.state.slideCount&&1!==this.props.slideWidth&&!this.props.wrapAround&&"auto"===this.props.slidesToScroll&&(s=this.state.slideWidth*this.state.slideCount-this.state.frameWidth,i=0,i-=o*(this.state.slideCount-1)),-1*(s-(i-=t||0))}},{key:"bindEvents",value:function(){S.a.canUseDOM&&(w(window,"resize",this.onResize),w(document,"readystatechange",this.onReadyStateChange))}},{key:"unbindEvents",value:function(){S.a.canUseDOM&&(T(window,"resize",this.onResize),T(document,"readystatechange",this.onReadyStateChange))}},{key:"formatChildren",value:function(t){var e=this,i=this.props.vertical?this.getTweeningValue("top"):this.getTweeningValue("left");return f.a.Children.map(t,(function(t,n){return f.a.createElement("li",{className:"slider-slide",style:e.getSlideStyles(n,i),key:n},t)}))}},{key:"setInitialDimensions",value:function(){var t=this,e=this.props,i=e.vertical,n=e.initialSlideHeight,o=e.initialSlideWidth,s=e.slidesToShow,r=e.cellSpacing,a=e.children,l=i?n||0:o||0,u=n?n*s:0,c=u+r*(s-1);this.setState({slideHeight:u,frameWidth:i?c:"100%",slideCount:f.a.Children.count(a),slideWidth:l},(function(){t.setLeft(),t.setExternalData()}))}},{key:"setDimensions",value:function(t){var e,i,n=this,o=void 0,s=void 0,r=(t=t||this.props).slidesToScroll,a=this.refs.frame,l=a.childNodes[0].childNodes[0];l?(l.style.height="auto",o=this.props.vertical?l.offsetHeight*t.slidesToShow:l.offsetHeight):o=100,s="number"!==typeof t.slideWidth?parseInt(t.slideWidth,10):t.vertical?o/t.slidesToShow*t.slideWidth:a.offsetWidth/t.slidesToShow*t.slideWidth,t.vertical||(s-=t.cellSpacing*((100-100/t.slidesToShow)/100)),i=o+t.cellSpacing*(t.slidesToShow-1),e=t.vertical?i:a.offsetWidth,"auto"===t.slidesToScroll&&(r=Math.floor(e/(s+t.cellSpacing))),this.setState({slideHeight:o,frameWidth:e,slideWidth:s,slidesToScroll:r,left:t.vertical?0:this.getTargetLeft(),top:t.vertical?this.getTargetLeft():0},(function(){n.setLeft()}))}},{key:"setLeft",value:function(){this.setState({left:this.props.vertical?0:this.getTargetLeft(),top:this.props.vertical?this.getTargetLeft():0})}},{key:"setExternalData",value:function(){this.props.data&&this.props.data()}},{key:"getListStyles",value:function(){var t=this.state.slideWidth*f.a.Children.count(this.props.children),e=this.props.cellSpacing,i=e*f.a.Children.count(this.props.children),n="translate3d("+this.getTweeningValue("left")+"px, "+this.getTweeningValue("top")+"px, 0)";return{transform:n,WebkitTransform:n,msTransform:"translate("+this.getTweeningValue("left")+"px, "+this.getTweeningValue("top")+"px)",position:"relative",display:"block",margin:this.props.vertical?e/2*-1+"px 0px":"0px "+e/2*-1+"px",padding:0,height:this.props.vertical?t+i:this.state.slideHeight,width:this.props.vertical?"auto":t+i,cursor:!0===this.state.dragging?"pointer":"inherit",boxSizing:"border-box",MozBoxSizing:"border-box"}}},{key:"getFrameStyles",value:function(){return{position:"relative",display:"block",overflow:this.props.frameOverflow,height:this.props.vertical?this.state.frameWidth||"initial":"auto",margin:this.props.framePadding,padding:0,transform:"translate3d(0, 0, 0)",WebkitTransform:"translate3d(0, 0, 0)",msTransform:"translate(0, 0)",boxSizing:"border-box",MozBoxSizing:"border-box"}}},{key:"getSlideStyles",value:function(t,e){var i=this.getSlideTargetPosition(t,e),n=this.props.cellSpacing;return{position:"absolute",left:this.props.vertical?0:i,top:this.props.vertical?i:0,display:this.props.vertical?"block":"inline-block",listStyleType:"none",verticalAlign:"top",width:this.props.vertical?"100%":this.state.slideWidth,height:"auto",boxSizing:"border-box",MozBoxSizing:"border-box",marginLeft:this.props.vertical?"auto":n/2,marginRight:this.props.vertical?"auto":n/2,marginTop:this.props.vertical?n/2:"auto",marginBottom:this.props.vertical?n/2:"auto"}}},{key:"getSlideTargetPosition",value:function(t,e){var i=this.state.frameWidth/this.state.slideWidth,n=(this.state.slideWidth+this.props.cellSpacing)*t,o=(this.state.slideWidth+this.props.cellSpacing)*i*-1;if(this.props.wrapAround){var s=Math.ceil(e/this.state.slideWidth);if(this.state.slideCount-s<=t)return(this.state.slideWidth+this.props.cellSpacing)*(this.state.slideCount-t)*-1;var r=Math.ceil((Math.abs(e)-Math.abs(o))/this.state.slideWidth);if(1!==this.state.slideWidth&&(r=Math.ceil((Math.abs(e)-this.state.slideWidth)/this.state.slideWidth)),t<=r-1)return(this.state.slideWidth+this.props.cellSpacing)*(this.state.slideCount+t)}return n}},{key:"getSliderStyles",value:function(){return{position:"relative",display:"block",width:this.props.width,height:"auto",boxSizing:"border-box",MozBoxSizing:"border-box",visibility:this.state.slideWidth?"visible":"hidden"}}},{key:"getStyleTagStyles",value:function(){return".slider-slide > img {width: 100%; display: block;}"}},{key:"getDecoratorStyles",value:function(t){switch(t){case"TopLeft":return{position:"absolute",top:0,left:0};case"TopCenter":return{position:"absolute",top:0,left:"50%",transform:"translateX(-50%)",WebkitTransform:"translateX(-50%)",msTransform:"translateX(-50%)"};case"TopRight":return{position:"absolute",top:0,right:0};case"CenterLeft":return{position:"absolute",top:"50%",left:0,transform:"translateY(-50%)",WebkitTransform:"translateY(-50%)",msTransform:"translateY(-50%)"};case"CenterCenter":return{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%,-50%)",WebkitTransform:"translate(-50%, -50%)",msTransform:"translate(-50%, -50%)"};case"CenterRight":return{position:"absolute",top:"50%",right:0,transform:"translateY(-50%)",WebkitTransform:"translateY(-50%)",msTransform:"translateY(-50%)"};case"BottomLeft":return{position:"absolute",bottom:0,left:0};case"BottomCenter":return{position:"absolute",bottom:0,width:"100%",textAlign:"center"};case"BottomRight":return{position:"absolute",bottom:0,right:0};default:return{position:"absolute",top:0,left:0}}}}]),e}(f.a.Component);k.defaultProps={afterSlide:function(){},autoplay:!1,resetAutoplay:!0,swipeSpeed:12,autoplayInterval:3e3,beforeSlide:function(){},cellAlign:"left",cellSpacing:0,data:function(){},decorators:g,dragging:!0,easing:function(t,e,i,n){return(i-e)*Math.sqrt(1-(t=t/n-1)*t)+e},edgeEasing:function(t,e,i,n){return(i-e)*t/n+e},framePadding:"0px",frameOverflow:"hidden",slideIndex:0,slidesToScroll:1,slidesToShow:1,slideWidth:1,speed:500,swiping:!0,vertical:!1,width:"100%",wrapAround:!1,style:{}};var C=k;i.d(e,"default",(function(){return C}))}}]);
self.__precacheManifest = (self.__precacheManifest || []).concat([
  {
    "revision": "7b5eddbb0bf20119a11b1cc0e47a2efd",
    "url": "./index.html"
  },
  {
    "revision": "b1444448327dd5e64326",
    "url": "./static/css/2.7622cd2f.chunk.css"
  },
  {
    "revision": "dbe0d25b5afb2f9d8407",
    "url": "./static/css/3.76e71233.chunk.css"
  },
  {
    "revision": "7f39c757d8b3a1d6e6b3",
    "url": "./static/css/4.74b44675.chunk.css"
  },
  {
    "revision": "e298864552990503e809",
    "url": "./static/css/main.2bae9e50.chunk.css"
  },
  {
    "revision": "b1444448327dd5e64326",
    "url": "./static/js/2.aa4ce7b5.chunk.js"
  },
  {
    "revision": "dbe0d25b5afb2f9d8407",
    "url": "./static/js/3.a40ea46e.chunk.js"
  },
  {
    "revision": "7f39c757d8b3a1d6e6b3",
    "url": "./static/js/4.5fa28972.chunk.js"
  },
  {
    "revision": "e298864552990503e809",
    "url": "./static/js/main.ac50c945.chunk.js"
  },
  {
    "revision": "dcd09ba503e79f60bd23",
    "url": "./static/js/runtime-main.95f99a86.js"
  },
  {
    "revision": "4d630b8abe461da490f61dfec579e58b",
    "url": "./static/media/bg.4d630b8a.png"
  },
  {
    "revision": "68719f530337f135d7048ede65a15835",
    "url": "./static/media/bg_x.68719f53.png"
  }
]);
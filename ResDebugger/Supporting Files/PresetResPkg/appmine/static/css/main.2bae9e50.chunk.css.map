{"version": 3, "sources": ["index.css"], "names": [], "mappings": "AAAA,KACE,QAAS,CACT,mIAEY,CACZ,kCAAmC,CACnC,iCACF,CAEA,WACE,iBAAkB,CAClB,KAAM,CACN,UAEF,CAEA,6CAHE,WAKF,CAEA,uCACE,aAAc,CACd,aAAc,CACd,cACF,CAEA,SACE,sBAAyB,CACzB,WAAY,CACZ,4BAA8B,CAC9B,mBACF,CACA,sBACE,WACF,CACA,2BACE,qBACF,CACA,6BACE,SACF,CACA,sBACE,gBACF,CACA,6DACE,qBACF,CACA,8CACE,gBAAkB,CAClB,eAAiB,CACjB,UAAW,CACX,kBAAoB,CACpB,aACF,CACA,4CACE,kBAAoB,CACpB,aAAe,CACf,UAAW,CACX,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,gCAAyB,CAAzB,iBAAyB,CAAzB,iBAAyB,CAAzB,wBAAyB,CACzB,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBACF,CACA,oBACE,cACF,CACA,qCACE,6BACF,CACA,kCACE,oBACF,CACA,iDACE,YAAc,CACd,YAAc,CACd,4TACF", "file": "main.2bae9e50.chunk.css", "sourcesContent": ["body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", \"Roboto\", \"Oxygen\",\n    \"Ubuntu\", \"Cantarell\", \"Fira Sans\", \"Droid Sans\", \"Helvetica Neue\",\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n\n.myloading {\n  position: absolute;\n  top: 0;\n  width: 100%;\n  height: 100%;\n}\n\n.myloading .am-activity-indicator {\n  height: 100%;\n}\n\n.myloading .am-activity-indicator span {\n  margin: 0 auto;\n  width: 1.02rem;\n  height: 1.02rem;\n}\n\n.am-card {\n  margin: 0.24rem 0.24rem 0;\n  border: none;\n  border-radius: 10px !important;\n  padding: 0 !important;\n}\n#root .am-card::before {\n  border: none;\n}\n#root .am-card-body::before {\n  background-color: #fff;\n}\n#root .am-card .am-card-body {\n  padding: 0;\n}\n#root .am-card-header {\n  padding: 0 0.24rem;\n}\n#root .am-list-body div:not(:last-child) .am-list-line::after {\n  background-color: #eee;\n}\n#root .am-card-header .am-card-header-content {\n  font-size: 0.34rem;\n  font-weight: bold;\n  color: #333;\n  line-height: 0.88rem;\n  height: 0.88rem;\n}\n#root .am-card-header .am-card-header-extra {\n  line-height: 0.88rem;\n  height: 0.88rem;\n  color: #999;\n  display: flex;\n  justify-content: flex-end;\n  align-items: center;\n}\n#root .am-list-item {\n  padding-left: 0;\n}\n#root .am-list-item:not(:last-child) {\n  border-bottom: 0.5px solid #EEEEEE ;\n}\n#root .am-list-item .am-list-line {\n  padding-right: 0.24rem;\n}\n#root .am-list-item .am-list-line .am-list-arrow {\n  width: 0.12rem;\n  height: 0.2rem;\n  background-image: url(../src/assets/images/arrow.png);\n}\n"]}
{"version": 3, "sources": ["index.module.css"], "names": [], "mappings": "AAAA,uBACE,iBAAkB,CAClB,YAAa,CAEb,iBAAkB,CAClB,gBAAkB,CAClB,UAAc,CACd,oBAEF,CACA,qBACE,iBAAkB,CAClB,oBAAsB,CACtB,YAAc,CACd,aAAe,CACf,iBAAkB,CAClB,sBACF,CACA,yBACE,iBAAkB,CAClB,OAAO,CACP,QAAQ,CACR,sCAA+B,CAA/B,kCAA+B,CAA/B,8BAA+B,CAC/B,kBAAoB,CACpB,UAAW,CACX,qBACF,CACA,yBACE,iBAAkB,CAClB,SAAU,CACV,UAAW,CACX,WAAa,CACb,WAAa,CACb,iBAAkB,CAClB,cACF,CACA,sBACE,iBAAkB,CAClB,UAAY,CACZ,WACF,CACA,0BACE,wBAAyB,CACzB,iBAAkB,CAClB,WAAY,CACZ,UAAW,CACX,gBAAiB,CACjB,cAAe,CACf,WACF,CACA,kDACE,UAAW,CACX,mBACF,CArDA,iBACE,sBAAyB,CACzB,kBAAmB,CACnB,eAAgB,CAChB,yCACF,CALA,2BAEC,KAAM,CACN,MAAO,CACP,OAAQ,CACR,eAAgB,CAChB,WAGD,CACA,uBACE,sBAAyB,CACzB,yBAA2B,CAC3B,eACF,CACA,2BACE,SAAqB,CACrB,eAAgB,CAChB,yBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,aAAc,CACd,UACF,CACA,gCACE,iBAAkB,CAClB,eAAgB,CAChB,eAAiB,CACjB,UAAW,CACX,aAAc,CACd,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,UAAW,CACX,kBACF,CACA,2CACE,gBAAkB,CAClB,iBACF,CACA,+BACE,eAAiB,CACjB,gBAAkB,CAClB,qBAAsB,CACtB,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,kBAAmB,CACnB,oCAA6B,CAA7B,wBAA6B,CAA7B,4BAA6B,CAC7B,eACF,CACA,mCACE,oBAAqB,CACrB,iBAAkB,CAClB,gBAAkB,CAClB,UACF,CACA,uCACE,aAAc,CACd,oBAAsB,CACtB,WAAa,CACb,YACF,CACA,8BACE,eAAiB,CACjB,eAAgB,CAIhB,yBAA2B,CAC3B,kBAAmB,CACnB,eACF,CACA,kCAIE,6BAAiC,CACjC,mBAAqB,CACrB,oCAAuC,CAEvC,eAAgB,CAChB,yBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,aAAc,CACd,UACF,CACA,sDACE,0BACF,CACA,uCACE,eAAgB,CAChB,yBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,eAAiB,CACjB,UAAW,CACX,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,UAAW,CACX,kBACF,CACA,kDACE,gBAAkB,CAClB,UAAW,CACX,cAAe,CACf,eAAgB,CAChB,yBAAuB,CAAvB,sBAAuB,CACvB,kBAAmB,CACnB,aAAc,CACd,UACF,CACA,4BACE,YAAc,CACd,aAAe,CACf,qBAAc,CAAd,mBAAc,CAAd,aAAc,CACd,kBACF,CACA,gCACE,UAAW,CACX,WAEF,CACA,wBAEE,aAAc,CACd,aAAc,CAMd,WAEF,CACA,4BACE,WAAY,CACZ,kBAAoB,CACpB,oBAAqB,CACrB,qBACF,CAKA,gCACE,sBAAyB,CACzB,UAAW,CACX,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,oCAA6B,CAA7B,wBAA6B,CAA7B,4BACF,CACA,8BACE,cAAc,CACd,qBAAsB,CACtB,oBACF,CACA,oDACE,eACF,CACA,+CACE,kBACF,CACA,8CACE,kBACF,CACA,mFACE,uBAAgB,CAAhB,4BAAgB,CAAhB,eACF,CACA,gEACE,kBACF,CACA,0BACE,aAAe,CACf,kBAAmB,CACnB,eACF,CACA,wCACE,kBACF,CACA,4CACE,YAAc,CACd,aAAe,CACf,qBACF,CACA,mEACE,mBACF,CACA,uEACE,eAAiB,CACjB,UACF,CACA,4BACE,SAAU,CACV,UAAW,CACX,KAAM,CACN,OAAQ,CACR,iBAAkB,CAClB,cACF,CAhMA,qBACE,wDAMF,CAEA,2CAPE,2BAA4B,CAC5B,oBAAqB,CACrB,cAAe,CACf,UAAW,CACX,UAUF,CAPA,sBACE,0DAMF,CAEA,wBACE,wBAAuB,CACvB,cAAe,CACf,UAAW,CACX,UACF,CAEA,sBAEE,MAAO,CACP,OAAQ,CACR,KAAM,CACN,QAAS,CACT,kBAAoB,CACpB,aAAe,CACf,mBAAqB,CAGrB,qCAA8B,CAA9B,qBAA8B,CAA9B,qBAA8B,CAA9B,6BAA8B,CAE9B,eAAgB,CAChB,yBAA2B,CAC3B,UAAW,CACX,oBAAqB,CACrB,uBAAwB,CACxB,+BACF,CAEA,kDApBE,cAAe,CAQf,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CAEnB,YAuBF,CAdA,4BAEE,SAAU,CACV,UAAY,CACZ,aAAe,CAEf,UAAW,CACX,eAAiB,CACjB,0BAA+B,CAC/B,UAAW,CACX,mBAAoB,CACpB,gBAGF,CAEA,0BACE,aAAc,CACd,kBAAoB,CACpB,YAAc,CACd,aAAe,CACf,sTAAqD,CACrD,yBACF,CAEA,wBACE,cAAe,CACf,MAAO,CACP,OAAQ,CACR,QAAS,CACT,kBAAoB,CACpB,aAAe,CACf,YAAa,CACb,UAAW,CACX,KACF,CAEA,4CACE,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,eAAgB,CAChB,mBAAqB,CACrB,aACF,CAEA,gDACE,0BAAmB,CAAnB,qBAAmB,CAAnB,qBAAmB,CAAnB,kBAAmB,CACnB,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CACb,eACF,CAEA,4CACE,iBAAkB,CAClB,qBAAc,CAAd,mBAAc,CAAd,aACF,CAEA,4CAEE,YAAc,CACd,cAAgB,CAChB,oBAAqB,CACrB,qBAAc,CAAd,mBAAc,CAAd,aACF,CAEA,kDACE,6BAAoC,CACpC,8BAAiC,CACjC,6BAAgC,CAChC,aAAe,CACf,kBAAoB,CACpB,6BAA8B,CAC9B,qBAAc,CAAd,mBAAc,CAAd,aAAc,CACd,gBAAkB,CAClB,iBAAkB,CAClB,eACF,CAEA,sEACE,eAAiB,CACjB,kBAAmB,CACnB,eAAgB,CAChB,yBAAuB,CAAvB,sBAAuB,CACvB,gBAAiB,CACjB,kBACF,CAmCA,qBACE,oBAAa,CAAb,gBAAa,CAAb,mBAAa,CAAb,YAAa,CAEb,kBAAoB,CACpB,kBACF,CAEA,mBACE,YACF,CAEA,uBACE,YAAc,CACd,mBAAqB,CACrB,qBACF,CAQA,uBACE,kBAAmB,CACnB,eAAgB,CAChB,iBAAkB,CAClB,UAAW,CACX,WAAY,CACZ,gBAAiB,CACjB,SAAU,CACV,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,WAAY,CACZ,QACF", "file": "3.76e71233.chunk.css", "sourcesContent": [".topBg {\n  background-image: url(\"../../assets/images/bg.png\");\n  background-repeat: no-repeat;\n  background-size: 100%;\n  position: fixed;\n  z-Index: -1; \n  width: 100%\n}\n\n.topBgX {\n  background-image: url(\"../../assets/images/bg_x.png\");\n  background-repeat: no-repeat;\n  background-size: 100%;\n  position: fixed;\n  z-Index: -1; \n  width: 100%\n}\n\n.topWhite {\n  background: transparent;\n  position: fixed;\n  z-Index: -1; \n  width: 100%\n}\n\n.navbar {\n  position: fixed;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  line-height: 0.96rem;\n  height: 0.96rem;\n  padding-left: 0.24rem;\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  z-index: 9999;\n  font-weight: 600;\n  width: calc(100% - 0.24rem);\n  color: #fff;\n  border-image-width: 0;\n  transform: translateZ(0);\n  -webkit-transform: translateZ(0);\n}\n\n.personCenter {\n  position: fixed;\n  left: 65px;\n  top: 0.78rem;\n  height: 0.36rem;\n  z-index: 9999;\n  color: #333;\n  font-size: 0.2rem;\n  background: rgba(0, 0, 0, 0.25);\n  color: #fff;\n  border-radius: 9.5px;\n  padding: 0 0.16rem;\n  display: flex;\n  align-items: center;\n}\n\n.arrowRight {\n  display: block;\n  margin-left: 0.14rem;\n  width: 0.13rem;\n  height: 0.18rem;\n  background: url('../../assets/images/arrowRight.png');\n  background-size: 100% 100%;\n}\n\n.navbarbg {\n  position: fixed;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  line-height: 0.96rem;\n  height: 0.96rem;\n  z-index: 9998;\n  width: 100%;\n  top: 0;\n}\n\n.navbar .person {\n  display: flex;\n  align-items: center;\n  overflow: hidden;\n  margin-right: 0.42rem;\n  min-width: 60%;\n}\n\n.navbar .person>div {\n  align-items: center;\n  display: flex;\n  overflow: hidden;\n}\n\n.navbar .avatar {\n  border-radius: 50%;\n  flex-shrink: 0;\n}\n\n.navbar .member {\n  /* width: 0.5rem; */\n  height: 0.5rem;\n  padding: 0.12rem;\n  display: inline-block;\n  flex-shrink: 0;\n}\n\n.navbar .membercenter {\n  background: rgba(255, 255, 255, 0.3);\n  padding: 2px 0.16rem 2px 0.227rem;\n  border-radius: 0.3rem 0 0 0.3rem;\n  height: 0.39rem;\n  line-height: 0.39rem;\n  font-family: PingFangSC-Medium;\n  flex-shrink: 0;\n  font-size: 0.25rem;\n  text-align: center;\n  font-weight: 300;\n}\n\n.navbar .person .personname {\n  font-size: 0.4rem;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  margin-left: 10px;\n  line-height: normal;\n}\n\n/* .assets {\n  padding: 0 0.2rem 0.1rem;\n  display: flex;\n  justify-content: space-around;\n  text-align: center;\n  color: #fff;\n}\n.assetsData p:first-child {\n  font-family: PingFangSC-Medium;\n  font-size: 0.36rem;\n  margin-bottom: 0.2rem;\n}\n.assetsData p:last-child {\n  font-family: PingFangSC-Light;\n  font-size: 0.24rem;\n  margin-top: 0;\n} */\n/* .info {\n  margin: 0 0.24rem;\n  border-radius: 10px;\n  overflow: hidden;\n  padding-top: 0.1rem;\n  padding-bottom: 0.1rem;\n}\n.info :global(.am-list-line .am-list-extra) {\n  display: flex;\n  justify-content: flex-end;\n} */\n\n/* .border {\n  border-right: 1px solid #eee;\n  margin: 0 0.1rem;\n} */\n.right {\n  display: flex;\n  /* margin-top: 5px; */\n  margin-left: 0.36rem;\n  margin-right: 3.5px;\n}\n\n.msg {\n  left: -0.24rem;\n}\n\n.msg img {\n  width: 0.48rem;\n  padding-left: 0.48rem;\n  vertical-align: middle;\n}\n\n/* .msg :global(.am-badge-dot) {\n  top: 2px;\n  right: -5px;\n  width: 6px;\n  height: 6px;\n} */\n.msg sup {\n  background: #ed2856;\n  font-weight: 400;\n  border-radius: 50%;\n  width: 13px;\n  height: 13px;\n  line-height: 13px;\n  padding: 0;\n  font-size: 8px;\n  display: block;\n  text-align: center;\n  right: -10px;\n  top: -6px;\n}"]}
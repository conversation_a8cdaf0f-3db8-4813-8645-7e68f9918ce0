{"version": 3, "sources": ["utils/uplus-api.ts", "utils/http-agent.tsx", "utils/url.ts", "utils/funs.tsx", "App.tsx", "index.tsx"], "names": ["uplusApi", "UplusApi", "lastClickTime", "vdnGotoPage", "url", "a", "noNetworkTip", "console", "log", "curClickTime", "Date", "getTime", "toString", "Number", "online", "isOnline", "ret", "retData", "initDeviceReady", "upVdnModule", "goToPage", "then", "res", "err", "toastInfo", "getUserInfo", "Promise", "resolve", "reject", "userData", "upAppinfoModule", "getAppInfo", "retCode", "appId", "appKey", "appVersion", "clientId", "upUserModule", "userId", "offUserId", "phoneNumber", "avatar", "sdToken", "user_center_access_token", "accessToken", "uhome_access_token", "nickname", "email", "retInfo", "window", "JSON", "stringify", "addResumeEventListener", "cb", "name", "error", "addPauseEventListener", "removeOnlineEventListener", "addOnlineEventListener", "upNetworkModule", "getStatusBarHeight", "devicePixelRatio", "devPixelRatio", "height", "test", "navigator", "userAgent", "resumeEvent", "userInfoChangeListener", "document", "addEventListener", "reportPageClickFn", "actionCode", "extentInfo", "upTraceModule", "reportPageClickEvent", "getIntegerData", "storageName", "orderedType", "upStorageModule", "getIntegerValue", "hasToastInstance", "toastInstance", "http", "headers", "data", "method", "isToast", "signType", "tempData", "tempHeaders", "formatHeader", "openToast", "upRequestModule", "signRequest", "toUpperCase", "result", "closeToast", "parse", "errorCode", "msg", "errorFn", "message", "clearTimeout", "loading", "className", "setTimeout", "hide", "header", "timestamp", "formatFn", "appInfo", "accountToken", "sequenceId", "timezone", "indexOf", "Object", "keys", "length", "jump_url_sc", "jump_url_ys", "content", "info", "getAds", "adLocation", "retObj", "getMsgNum", "getAppType", "haierUserCenterUrl", "getFamilyList", "getUserFeedBack", "getAllUnReadApi", "getUserDetial", "throttle", "fn", "wait", "callback", "timerId", "firstInvoke", "args", "apply", "this", "addUrlParam", "param", "value", "urlTemp", "replace", "parseUrl", "indexEnd", "lastIndexOf", "indexAPICloud", "indexApiCloud", "indexH5ResPkg", "indexmPaaS", "tempUrl", "substring", "location", "hash", "Home", "lazy", "Loading", "WrappedComponent", "with<PERSON><PERSON><PERSON>", "useState", "userInfo", "jumpUrl", "statusBarHeight", "commonInfo", "setCommonInfo", "useEffect", "getCurUserInfo", "preState", "logined", "getCurBarHeight", "getCurAppInfo", "infoObj", "obj", "access_token", "uhome_user_id", "fallback", "path", "render", "props", "FastClick", "attach", "body", "prototype", "focus", "ele", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "require", "href", "e", "__bl", "singleton", "pid", "appType", "imgUrl", "page", "autoSendPv", "behavior", "enableSPA", "disable<PERSON><PERSON>", "environment", "grayMode", "release", "uid", "ignore", "ignoreUrls", "str", "parseHash", "pipe", "setConfig", "ReactDOM", "getElementById"], "mappings": "klBAGaA,EAAW,IAAIC,IAQxBC,EAAwB,GACrB,SAAeC,EAAtB,kC,4CAAO,WAA2BC,GAA3B,iCAAAC,EAAA,yDAAwCC,IAAxC,iCACLC,QAAQC,IAAI,yBAA0BN,GAClCO,GAAe,IAAIC,MAAOC,UAAUC,WACxCL,QAAQC,IAAI,wBAAyBC,KACjCI,OAAOJ,GAAgBI,OAAOX,GAAiB,KAJ9C,oBAKCY,GAAS,EACe,mBAAjBR,IAA8BA,EANtC,2CASmBS,IATnB,QASOC,EATP,OAUCT,QAAQC,IAAI,4CAAeQ,GAC3BF,EAASE,EAAIC,QAAQF,SAXtB,kDAaCR,QAAQC,IAAI,4CAAZ,MACAM,GAAS,EAdV,YAiBCA,EAjBD,kCAkBKd,EAASkB,kBAlBd,QAmBDlB,EAASmB,YAAYC,SAAS,CAC5B,IAAOhB,IACNiB,MAAK,SAACC,GACPf,QAAQC,IAAI,uBAAwBc,MACnC,SAACC,GACFhB,QAAQC,IAAI,sBAAuBe,MAxBpC,wBA2BDC,YAAU,kCA3BT,QA6BHtB,EAAgBO,EA7Bb,2D,sBAoCA,SAAegB,IAAtB,+B,4CAAO,sBAAApB,EAAA,+EAGE,IAAIqB,QAAJ,uCAAY,WAAOC,EAASC,GAAhB,iBAAAvB,EAAA,6DACXwB,EAA+D,CACnE,QAAW,GACX,QAAW,GACX,QAAW,CACT,MAAS,GACT,OAAU,GACV,WAAc,GACd,SAAY,GACZ,OAAU,GACV,UAAa,GACb,YAAe,GACf,QAAW,GACX,OAAU,GACV,SAAY,GACZ,YAAe,GACf,MAAS,KAhBI,kBAoBT7B,EAASkB,kBApBA,cAqBXlB,EAAS8B,iBACX9B,EAAS8B,gBAAgBC,aAAaV,MAAK,SAACL,GACtCA,GAAOA,EAAIC,SAA2B,WAAhBD,EAAIgB,UAC5BH,EAASZ,QAAQgB,MAAQjB,EAAIC,QAAQgB,OAAS,GAC9CJ,EAASZ,QAAQiB,OAASlB,EAAIC,QAAQiB,QAAU,GAChDL,EAASZ,QAAQkB,WAAanB,EAAIC,QAAQkB,YAAc,GACxDN,EAASZ,QAAQmB,SAAWpB,EAAIC,QAAQmB,UAAY,OA3B3C,SAgCGpC,EAASqC,aAAaZ,cAAtB,OAA0C,SAACF,GAAD,OAAcA,KAhC3D,QAgCTP,EAhCS,SAiCJA,EAAIC,SAA2B,WAAhBD,EAAIgB,SAAwBL,GACpDE,EAASZ,QAAQqB,OAAStB,EAAIC,QAAQqB,QAAU,GAChDT,EAASZ,QAAQsB,UAAYvB,EAAIC,QAAQsB,WAAa,GACtDV,EAASZ,QAAQuB,YAAcxB,EAAIC,QAAQuB,aAAe,GAC1DX,EAASZ,QAAQwB,OAASzB,EAAIC,QAAQwB,QAAU,GAChDZ,EAASZ,QAAQyB,QAAU1B,EAAIC,QAAQ0B,0BAA4B,GACnEd,EAASZ,QAAQ2B,YAAc5B,EAAIC,QAAQ4B,oBAAsB,GACjEhB,EAASZ,QAAQ6B,SAAW9B,EAAIC,QAAQ6B,UAAY,GACpDjB,EAASZ,QAAQ8B,MAAQ/B,EAAIC,QAAQ8B,OAAS,GAC9ClB,EAASG,QAAU,SACnBH,EAASmB,QAAU,mDACnBrB,EAAQE,KAERA,EAASG,QAAUhB,EAAIgB,SAAW,GAClCH,EAASmB,QAAUhC,EAAIgC,SAAW,GAClCnB,EAASZ,QAAUD,EAAIC,SAAW,GAClCW,EAAOC,IAjDM,kDAqDfoB,OAAO1C,QAAQC,IAAI,0BAA2B0C,KAAKC,UAAL,OArD/B,0DAAZ,0DAHF,4C,sBAiEA,SAAeC,EAAtB,oC,4CAAO,WAAsCC,EAAcC,GAApD,SAAAjD,EAAA,sDACL,IACEL,EAASoD,uBAAuBC,EAAIC,GACpC,MAAOC,GACPN,OAAO1C,QAAQC,IAAI,qCAAsC0C,KAAKC,UAAUI,IAJrE,4C,sBAYA,SAAeC,EAAtB,oC,4CAAO,WAAqCH,EAAcC,GAAnD,SAAAjD,EAAA,sDACL,IACEL,EAASwD,sBAAsBH,EAAIC,GACnC,MAAOC,GACPN,OAAO1C,QAAQC,IAAI,oCAAqC0C,KAAKC,UAAUI,IAJpE,4C,sBAYA,SAASE,EAA0BJ,IAYnC,SAASK,EAAuBL,IAuBhC,SAAetC,IAAtB,+B,4CAAO,sBAAAV,EAAA,sEACCL,EAASkB,kBADV,gCAEElB,EAAS2D,gBAAgB5C,YAF3B,4C,sBAQA,SAAegB,IAAtB,+B,4CAAO,sBAAA1B,EAAA,sEACCL,EAASkB,kBADV,gCAEElB,EAAS8B,gBAAgBC,cAF3B,4C,sBAkDA,SAAe6B,IAAtB,+B,4CAAO,sBAAAvD,EAAA,sEAICL,EAASkB,kBAJV,gCAKE,IAAIQ,QAAJ,uCAAY,WAAOC,EAASC,GAAhB,mBAAAvB,EAAA,sEACCL,EAAS4D,qBADV,QACX5C,EADW,SAEU,WAAhBA,EAAIgB,SAAwBhB,EAAIC,SAAWU,GAEpDpB,QAAQC,IAAI,iGAAuBQ,EAAK,4BAA6BiC,OAAOY,kBACxEC,EAAgBb,OAAOY,iBACvBE,EAAS/C,EAAIC,QAAQ8C,OAAS,EAC5B,wBAAwBC,KAAKf,OAAOgB,UAAUC,aAClDH,GAAkBD,GAEpBnC,EAAQoC,IAERnC,EAAOZ,GAZQ,2CAAZ,0DALF,4C,sBA0BA,SAAemD,EAAtB,oC,4CAAO,WAA2Bd,EAAcC,GAAzC,SAAAjD,EAAA,sDACL,IACEL,EAASoD,uBAAuBC,EAAIC,GACpC,MAAOC,GACPN,OAAO1C,QAAQC,IAAI,qCAAsC0C,KAAKC,UAAUI,IAJrE,4C,sBAoDA,SAAea,EAAtB,kC,4CAAO,WAAsCf,GAAtC,SAAAhD,EAAA,sDACDgD,GAAoB,oBAAPA,IACfgB,SAASC,iBAAiB,yBAA0BjB,GAAI,GACxDgB,SAASC,iBAAiB,qBAAsBjB,GAAI,GACpDgB,SAASC,iBAAiB,6BAA8BjB,GAAI,IAJzD,4C,sBAeA,SAASkB,EAAT,GAAkE,IAArCnE,EAAoC,EAApCA,IAAKoE,EAA+B,EAA/BA,WAAYC,EAAmB,EAAnBA,WAEnD,OADAlE,QAAQC,IAAR,iCAAsC0C,KAAKC,UAAU,CAAE/C,MAAKoE,aAAYC,iBACjE,IAAI/C,SAAQ,SAACC,EAASC,GAC3B5B,EAASkB,kBAAkBG,MAAK,WAC9BrB,EAAS0E,cAAcC,qBAAqB,CAAEvE,MAAKoE,aAAYC,eAC5DpD,MAAK,SAACC,GACLf,QAAQC,IAAR,wCAA6C0C,KAAKC,UAAU7B,KAC5DK,EAAQL,MAHZ,OAKS,SAACiC,GACNhD,QAAQC,IAAR,qCAA0C0C,KAAKC,UAAUI,KACzD3B,EAAO2B,YAWV,SAAeqB,EAAtB,kC,4CAAO,WAA8BC,GAA9B,2BAAAxE,EAAA,6DAAmDyE,EAAnD,+BAAyE,EAAzE,SACC9E,EAASkB,kBADV,gCAEElB,EAAS+E,gBAAgBC,gBAAgB,CAC9CH,cACAC,iBAJG,4C,kMCrVHG,GAA4B,EAC5BC,GAAqB,EAKlB,SAAeC,EAAtB,kC,4CAAO,+DAAA9E,EAAA,6DACLD,WADK,MACC,GADD,MAELgF,eAFK,MAEK,GAFL,MAGLC,YAHK,MAGE,GAHF,MAILC,cAJK,MAII,GAJJ,MAMLC,eANK,aAOLC,gBAPK,MAOM,SAPN,WAUCC,EAA2B,kBAATJ,EAAoBnC,KAAKC,UAAUkC,GAAQA,EAC7DK,EAAcC,EAAaP,EAAShF,GAXrC,wDAeHG,QAAQC,IAAI,cAAekF,GAGvBH,GACFK,IAnBC,UAuBG5F,IAASkB,kBAvBZ,yBAwBgBlB,IAAS6F,gBAAgBC,YAAY,CACtD,IAAO1F,EACP,QAAWsF,EACX,KAAQD,EACR,OAAUH,EAAOS,cACjB,SAAYP,EACZ,WAAa,IA9BZ,QAwBCQ,EAxBD,OAgCHzF,QAAQC,IAAI,uBAAwBJ,EAAK4F,GAEzCC,IACIhF,EAAe,GACnB,IACEA,EAAUiC,KAAKgD,MAAMF,EAAO/E,SAC5B,MAAOsC,GACPtC,EAAU+E,EAAO/E,QAvChB,IA0CCA,GAA+B,UAApBA,EAAQe,QA1CpB,0CA6CMN,QAAQC,QAAQV,IA7CtB,eAiDGkF,EAAYlF,EAAQe,QAjDvB,kBAkDIN,QAAQE,OAAO,CACpB,UAAaX,EAAQ+B,QACrB,UAAamD,EACb,MAASH,KArDR,kCAyDHC,IACA1F,QAAQC,IAAI,QAAZ,MACI4F,EAAM,GACV,IACEA,EAAMlD,KAAKgD,MAAM,KAAMjF,SACvB,MAAOM,GACP6E,EAAM,KAAMnF,QA/DX,yBAiEIoF,EAAQD,EAAKb,IAjEjB,2D,+BAqEQc,E,gFAAf,WAAuBC,EAAcf,GAArC,SAAAlF,EAAA,sDACEE,QAAQC,IAAI,2BAAQ8F,EAASf,GAD/B,4C,sBAgBO,SAASK,IACVV,GACFjC,OAAOsD,aAAarB,GAGjBD,IACH,IAAMuB,QAAQ,yBAAKC,UAAU,wBAA8B,IAC3DxB,GAAmB,GAIhB,SAASgB,IACVf,GACFjC,OAAOsD,aAAarB,GAEtBA,EAAgBjC,OAAOyD,YAAW,WAC5BzB,IACF,IAAM0B,OACN1B,GAAmB,KAEpB,IAML,SAASU,IAAgD,IAAnCiB,EAAkC,uDAAjB,GAAIxG,EAAa,uCACtD,IACE,IAAMyG,GAAY,IAAInG,MAAOC,UAAUC,WACjCkG,EAAW,SAACzB,EAAWjF,GAAiB,IAAD,EASvCiF,EAPFpD,aAFyC,MAEjC,GAFiC,IASvCoD,EANFnD,cAHyC,MAGhC,GAHgC,IASvCmD,EALFlD,kBAJyC,MAI5B,GAJ4B,IASvCkD,EAJFjD,gBALyC,MAK9B,GAL8B,IASvCiD,EAHFzC,mBANyC,MAM3B,GAN2B,IASvCyC,EAFF3C,eAPyC,MAO/B,GAP+B,IASvC2C,EADF/C,OAEIyE,EAAe,CACnB,eAAgB,iCAChB9E,QACAG,WACAE,YAdyC,MAQhC,GARgC,EAezCM,cACAoE,aAActE,EACdmE,YACA1E,aACA8E,WAAYJ,EAEZK,SAAU,iBAKZ,OAH4B,IAAxB9G,EAAI+G,QAAQ,SACdJ,EAAQ7E,OAASA,GAEZ6E,GAGT,OAAIH,GAAUQ,OAAOC,KAAKT,GAAQU,OAAS,EAClCR,EAASF,EAAQxG,GAEnB,GAQP,MAAOmD,GACPhD,QAAQC,IAAI,sBAAuB+C,I,YClKjCgE,EAAc,CAChB,SAAY,wBAEZ,YAAe,0GACf,YAAe,+HACf,eAAkB,wCAClB,cAAiB,gEACjB,iBAAoB,8DAEpB,WAAc,uCACd,iBAAoB,oIACpB,YAAe,oIAEf,eAAkB,2CAClB,UAAa,oIAGb,aAAgB,qGAChB,WAAc,iGACd,eAAkB,yGAClB,aAAgB,kGAChB,eAAiB,uHAGjB,iBAAoB,sGACpB,gBAAmB,0CACnB,aAAgB,kDAChB,gBAAmB,wGACnB,gBAAmB,0CACnB,kBAAoB,GAEpB,YAAe,gGACf,cAAiB,wGACjB,YAAe,wGACf,aAAgB,+FAChB,iBAAoB,mGAEpB,gBAAmB,uCAEnB,gBAAmB,6HAEnB,YAAe,sCACf,eAAkB,6GAClB,WAAc,mDACd,WAAc,kBACd,aAAgB,+GAEhB,YAAe,uEACf,aAAgB,oHAChB,gBAAmB,2CAEnB,aAAgB,gGAEhB,gBAAmB,iFACnB,WAAc,2EACd,cAAiB,qCACjB,WAAc,mGAgBd,aAAgB,0DAIdC,EAAW,eACVD,EADU,CAEb,YAAe,mIAEf,aAAgB,0H,sXCzCb,SAAS/F,EAAUiG,GACpBA,GACFxE,OAAOyD,YAAW,kBAAM,IAAMgB,KAAKD,KAAU,IAO1C,SAAeE,EAAtB,kC,4CAAO,+BAAAtH,EAAA,6DAAwBuG,EAAxB,EAAwBA,OAAxB,kBAEuBzB,EAAK,CAC7B,IAAO,6CACP,QAAW,CAAEyB,UAEb,KAAQ,CAAEgB,WAAY,QACtB,OAAU,OACV,SAAW,IARV,UAEGC,EAFH,OAUHtH,QAAQC,IAAI,sBAAuBqH,IAC/BA,GAA6B,UAAnBA,EAAO7F,UAAuB6F,EAAOxC,KAXhD,yCAYM3D,QAAQC,QAAQkG,EAAOxC,OAZ7B,gCAcI3D,QAAQE,OAAOiG,EAAO7F,UAd1B,yCAiBHzB,QAAQC,IAAI,oBAAZ,MAjBG,kBAkBIkB,QAAQE,OAAR,OAlBJ,2D,sBA6EA,SAAekG,EAAtB,kC,4CAAO,+BAAAzH,EAAA,6DAA2BuG,EAA3B,EAA2BA,OAA3B,kBAEuBzB,EAAK,CAC7B,IAAO,gDACP,OAAU,OACV,QAAWyB,EACX,SAAW,IANV,UAEGiB,EAFH,OAQHtH,QAAQC,IAAI,uBAAwBqH,IAChCA,GAA6B,UAAnBA,EAAO7F,UAAuB6F,EAAO5G,QAThD,yCAUMS,QAAQC,QAAQkG,EAAO5G,UAV7B,gCAYIS,QAAQE,OAAOiG,EAAO7F,UAZ1B,yCAcHzB,QAAQC,IAAI,qBAAZ,MAdG,kBAeIkB,QAAQE,OAAR,OAfJ,2D,sBAyCA,SAASmG,IAA6C,IAAlCC,EAAiC,uDAAJ,GAEtD,OADAzH,QAAQC,IAAI,qBAAsBwH,GACP,mCAAvBA,GAAkF,KAAvBA,GAC7DzH,QAAQC,IAAI,4BACL+G,IAEPhH,QAAQC,IAAI,kCACLgH,GAOJ,SAAeS,EAAtB,kC,4CAAO,+BAAA5H,EAAA,6DAA+BuG,EAA/B,EAA+BA,OAA/B,kBAEuBzB,EAAK,CAC7B,IAAO,wDACP,OAAU,OACV,SAAW,EACX,QAAWyB,EACX,SAAY,QAPX,YAEGiB,EAFH,SAS8B,UAAnBA,EAAO7F,UAAuB6F,EAAOxC,KAThD,yCAUM3D,QAAQC,QAAQkG,EAAOxC,OAV7B,gCAYI3D,QAAQE,OAAOiG,EAAO7F,UAZ1B,2DAeIN,QAAQE,OAAR,OAfJ,2D,sBAoBA,SAAesG,EAAtB,kC,4CAAO,+BAAA7H,EAAA,6DAAiCuG,EAAjC,EAAiCA,OAAjC,kBAEuBzB,EAAK,CAC7B,IAAO,+DACP,OAAU,MACV,QAAWyB,EACX,SAAW,IANV,UAEGiB,EAFH,OAQHtH,QAAQC,IAAI,6BAA8BqH,IACtCA,GAA6B,UAAnBA,EAAO7F,UAAuB6F,EAAOxC,KAThD,yCAUM3D,QAAQC,QAAQkG,EAAOxC,OAV7B,gCAYI3D,QAAQE,OAAOiG,EAAO7F,UAZ1B,yCAcHzB,QAAQC,IAAI,2BAAZ,MAdG,kBAeIkB,QAAQE,OAAR,OAfJ,2D,sBAoBA,IAAMuG,EAAkB,SAAC,GAAqB,IAAnBvB,EAAkB,EAAlBA,OAChC,OAAOzB,EAAK,CACV,IAAO,0EACP,OAAU,OACV,QAAWyB,EACX,KAAQ,CACN,SAAYA,EAAOxE,SACnB,OAAUwE,EAAOtE,WAMhB,SAAe8F,EAAtB,kC,4CAAO,+BAAA/H,EAAA,yDAA+BuG,EAA/B,EAA+BA,OAA/B,SAEEA,EAAOxE,SAFT,iEAGuB+C,EAAK,CAC7B,IAAO,2CACP,OAAU,MACV,QAAWyB,EACX,SAAW,IAPV,UAGGiB,EAHH,OASHtH,QAAQC,IAAI,2BAA4BqH,IACpCA,GAA6B,UAAnBA,EAAO7F,UAAuB6F,EAAOxC,KAVhD,0CAWM3D,QAAQC,QAAQkG,EAAOxC,OAX7B,iCAaI3D,QAAQE,OAAOiG,EAAO7F,UAb1B,yCAeHzB,QAAQC,IAAI,yBAAZ,MAfG,kBAgBIkB,QAAQE,OAAR,OAhBJ,2D,sBAsBA,SAASyG,EAASC,EAAcC,GACrC,IAAMC,EAAWF,EACbG,EAAe,KAGfC,GAAc,EAElB,OAAO,WAA8C,IAAC,IAAD,0BAAbC,EAAa,yBAAbA,EAAa,gBAEnD,GAAID,EAGF,OAFAF,EAASI,MAAMC,KAAMF,QACrBD,GAAc,GAKZD,IAIJA,EAAU/B,YAAW,WACnBH,aAAakC,GACbA,EAAU,KACVD,EAASI,MAAM,EAAMD,KACpBJ,KAKA,SAASO,EAAY1I,EAAa2I,EAAeC,GACtD,IACE,GAAI5I,EAAI+G,QAAQ,kBAAoB,EAClC,OAAO/G,EAET,IAAI6I,EAAU,GACR5D,EAAO0D,EAAQ,IAAMC,EAa3B,OAVEC,EADE,UAAUjF,KAAK5D,GACPA,EAAI8I,QAAQ,cAAe,IAAM7D,EAAO,KAG9C,UAAUrB,KAAK5D,GACPA,EAAI8I,QAAQ,gBAAiB,MAAQ7D,GAErCjF,EAAM,IAAMiF,EAG1B9E,QAAQC,IAAIyI,GACLA,EACP,MAAO1F,GAEP,OADAhD,QAAQC,IAAI,uBAAwB+C,GAC7BnD,GAKJ,IAAM+I,EAAW,SAAC/I,GAEvB,GAAIA,EAAI+G,QAAQ,SAAW,EAAG,CAE5B,IAAIiC,EAAWhJ,EAAIiJ,YAAY,KAC/BD,GAAyB,IAAdA,EAAkBhJ,EAAIkH,OAAS8B,EAC1C,IAAME,EAAgBlJ,EAAI+G,QAAQ,YAC5BoC,EAAgBnJ,EAAI+G,QAAQ,YAC5BqC,EAAgBpJ,EAAI+G,QAAQ,YAC5BsC,EAAarJ,EAAI+G,QAAQ,SAC3BuC,EAAU,GAed,OAdID,GAAc,IAChBC,EAAUtJ,EAAIuJ,UAAUF,EAAa,EAAGL,IAEtCE,GAAiB,IACnBI,EAAUtJ,EAAIuJ,UAAUL,EAAgB,EAAGF,IAEzCG,GAAiB,IACnBG,EAAUtJ,EAAIuJ,UAAUJ,EAAgB,EAAGH,IAEzCI,GAAiB,IACnBE,EAAUtJ,EAAIuJ,UAAUH,EAAgB,EAAGJ,IAG7CM,GADAA,EAAUA,EAAQC,UAAUD,EAAQvC,QAAQ,KAAO,IAC/BlE,OAAO2G,SAASC,MAAQ,GAG9C,OAAOzJ,I,olDC/UH0J,EAAOC,gBAAK,kBAAM,uDAoJxB,SAASC,IACP,OACE,yBAAKvD,UAAU,aACb,6BAKN,IAEewD,EAFeC,aA1J9B,WAAgB,IAAD,EACuBC,mBAAS,CAC3CpD,QAAS,GACTqD,SAAU,GACVC,QAAStC,cACTuC,gBAAiB,KALN,mBACNC,EADM,KACMC,EADN,KAuIb,OA/HAC,qBAAU,WAAO,SACAC,IADD,2EACd,kCAAArK,EAAA,+EAGqBoB,cAHrB,OAGI2I,EAHJ,OAII7J,QAAQC,IAAI,2BAA4B4J,GACxCI,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEEP,SAAS,eAAMA,EAASnJ,cARhC,gDAYIV,QAAQC,IAAI,wBAAZ,MACI,MAA2B,WAAlB,KAAMwB,QACjBwI,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEEP,SAAU,CAAEQ,SAAS,QAIzBlE,YAAW,WACTgE,MACC,KAvBT,QA0BE,IACQzJ,EAAUmJ,GAAYA,EAASnJ,QAAUmJ,EAASnJ,QAAU,IAC5D+G,EAAqB/G,GAAWA,EAAQ+G,mBAAqB/G,EAAQ+G,mBAAqB,MAExFqC,EAAUtC,YAAWC,GAC3BwC,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEEN,gBAIN,MAAO9G,GACPhD,QAAQC,IAAI+C,GAvChB,0DADc,kEA2Cd,4BAAAlD,EAAA,+EAE+B0B,cAF/B,OAEUgF,EAFV,OAGIxG,QAAQC,IAAI,wBAAyBuG,GACrCyD,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEE5D,QAASA,EAAQ9F,aAPzB,gDAWIuJ,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEE5D,QAAS,QAGbxG,QAAQC,IAAI,uBAAZ,MAjBJ,0DA3Cc,kEA+Dd,4BAAAH,EAAA,+EAE8BuD,cAF9B,OAEUG,EAFV,OAGIxD,QAAQC,IAAI,0BAA2BuD,GACvCyG,GAAc,SAACG,GACb,OAAO,eACFA,EADL,CAEEL,gBAAiBvG,OAPzB,gDAWIxD,QAAQC,IAAI,yBAAZ,MAXJ,0DA/Dc,sBAyHd,OAzHc,mCA6EdqK,GACAH,IA9Ec,mCA+EdI,GACA1G,aAAuB,SAACsD,GACtBnH,QAAQC,IAAI,4BAA6B0C,KAAKC,UAAUuE,IACxD,IAAIqD,EAAe,GACnB,IACEA,EAAU7H,KAAKgD,MAAMhD,KAAKC,UAAUuE,IACpC,MAAOnE,GACPhD,QAAQC,IAAI+C,GAEdhD,QAAQC,IAAI,4BAA6BuK,GACrCA,GAAWA,EAAQ1F,MAAQ0F,EAAQ1F,KAAKpE,QAC1CuJ,GAAc,SAACG,GACb,IAAMK,EAAQ,eAAQL,GAYtB,OAXII,EAAQ1F,KAAKpE,QAAQqB,eAChByI,EAAQ1F,KAAKpE,QAAQqB,OAE9B0I,EAAIZ,SAAJ,eAAoBY,EAAIZ,SAAxB,GAAqCW,EAAQ1F,KAAKpE,SAC9C8J,EAAQ1F,KAAKpE,QAAQgK,eACvBD,EAAIZ,SAASxH,YAAcmI,EAAQ1F,KAAKpE,QAAQ4B,mBAChDmI,EAAIZ,SAAS1H,QAAUqI,EAAQ1F,KAAKpE,QAAQgK,aAC5CD,EAAIZ,SAAS9H,OAASyI,EAAQ1F,KAAKpE,QAAQiK,cAC3CF,EAAIZ,SAASQ,SAAU,GAEzBrK,QAAQC,IAAI,qBAAsBwK,GAC3BA,KAGTN,OAGJvG,YAAYuG,EAAgB,kBAC5BlH,aAAsB,WACpBgH,GAAc,SAACG,GACb,IAAII,EAAe,GACnB,IACEA,EAAU7H,KAAKgD,MAAMhD,KAAKC,UAAUwH,IACpC,MAAOpH,GACPhD,QAAQC,IAAI+C,GAEd,OAAOwH,OAER,qBACI,WACL3G,aAAuB,kBAAM,WAG9B,IAGD,kBAAC,IAAD,KACE,kBAAC,WAAD,CAAU+G,SAAU,kBAACnB,EAAD,OAClB,kBAAC,IAAD,KACE,kBAAC,IAAD,CAAOoB,KAAK,IAAIC,OAAQ,SAACC,GAAD,OAAW,kBAACxB,EAAD,iBAAUwB,EAAWf,aAiBlE,iB,kBCzJAgB,IAAUC,OAAOnH,SAASoH,MAE1BF,IAAUG,UAAUC,MAAQ,SAACC,GAAD,OAAcA,EAAID,SAE9C,IAAME,EAAgBC,EAAQ,KACxB1L,EAAM6C,OAAO2G,SAASmC,KAAKnL,WAEjCqC,OAAOqB,iBAAiB,OAAxB,uCAAgC,WAAgB0H,GAAhB,mBAAA3L,EAAA,+EAGtBL,IAASkB,kBAHa,uBAIVlB,IAAS8B,gBAAgBC,aAJf,OAItBT,EAJsB,OAKtByF,EAAUzF,EAAIL,SACdgL,EAAOJ,EAAcK,UAAU,CACnCC,IAAI,6BACJC,QAAQ,MACRC,OAAO,2CACPC,KAAMnD,YAAS/I,GACfmM,YAAY,EACZC,UAAS,EACTC,WAAU,EACVC,aAAY,EACZC,YAAa5F,EAAQ6F,SAAW,OAAO,OACvCC,QAAS9F,EAAQ5E,WACjB2K,IAAK/F,EAAQ3E,SACb2K,OAAQ,CACNC,WAAY,CACV,SAAUC,GACR,OAAIA,EAAI9F,QAAQ,YAAc,GAAK8F,EAAI9F,QAAQ,cAAgB,GAAK8F,EAAI9F,QAAQ,cAAgB,KAMtG+F,UAAW,SAAUrD,GACnB,OAAOV,YAAS/I,OAGf+M,KAAO,CAAC,UAAWhE,YAAS/I,IACjC6L,EAAKmB,UAAU,CACbF,UAAW,SAAUrD,GACnB,OAAOV,YAAS/I,MAlCQ,kDAsC5BG,QAAQC,IAAI,gCAAZ,MAtC4B,0DAAhC,uDA0CA6M,IAAShC,OAAO,kBAAC,EAAD,MAAShH,SAASiJ,eAAe,W", "file": "static/js/main.ac50c945.chunk.js", "sourcesContent": ["import UplusApi from '@uplus/uplus-api';\nimport { toastInfo } from '../utils/funs';\n\nexport const uplusApi = new UplusApi();\n\n/**\n * 通过vdn跳转\n * @param {string} url 跳转地址\n * @param {boolean} noNetworkTip 断网状态是否提示“网络不可用”,不传默认提示\n */\n// 防止双击\nvar lastClickTime: string = '';\nexport async function vdnGotoPage(url: string, noNetworkTip: boolean = true): Promise<void> {\n  console.log('=========lastclickTime', lastClickTime);\n  let curClickTime = new Date().getTime().toString();\n  console.log('=========curClickTime', curClickTime);\n  if (Number(curClickTime) - Number(lastClickTime) > 200) {\n    let online = true;\n    if (typeof noNetworkTip === 'boolean' && noNetworkTip) {\n      try {\n        // 判断是否断网\n        const ret = await isOnline();\n        console.log('断网状态==成功===', ret);\n        online = ret.retData.isOnline;\n      } catch (e) {\n        console.log('断网状态==失败===', e);\n        online = false;\n      }\n    }\n    if (online) {\n      await uplusApi.initDeviceReady()\n      uplusApi.upVdnModule.goToPage({\n        'url': url\n      }).then((res: any) => {\n        console.log('cz--goToPage--sucess', res);\n      }, (err: any) => {\n        console.log('cz--goToPage--error', err);\n      });\n    } else {\n      toastInfo('网络不可用');\n    }\n    lastClickTime = curClickTime;\n  }\n}\n\n/**\n * 获取用户信息，容器的接口\n */\nexport async function getUserInfo() {\n  // await uplusApi.initDeviceReady();\n  // return uplusApi.userModule.getUserInfo();\n  return new Promise(async (resolve, reject) => {\n    const userData: { retCode: string, retInfo: string, retData: any } = {\n      'retCode': '',\n      'retInfo': '',\n      'retData': {\n        'appId': '',\n        'appKey': '',\n        'appVersion': '',\n        'clientId': '',\n        'userId': '',\n        'offUserId': '',\n        'accessToken': '',\n        'sdToken': '',\n        'avatar': '',\n        'nickname': '',\n        'phoneNumber': '',\n        'email': ''\n      }\n    }\n    try {\n      await uplusApi.initDeviceReady()\n      if (uplusApi.upAppinfoModule) {\n        uplusApi.upAppinfoModule.getAppInfo().then((ret: any) => {\n          if (ret && ret.retData && ret.retCode === '000000') {\n            userData.retData.appId = ret.retData.appId || ''\n            userData.retData.appKey = ret.retData.appKey || ''\n            userData.retData.appVersion = ret.retData.appVersion || ''\n            userData.retData.clientId = ret.retData.clientId || ''\n          }\n        })\n      }\n\n      const ret = await uplusApi.upUserModule.getUserInfo().catch((err: any) => err);\n      if (ret && ret.retData && ret.retCode === '000000' && resolve) {\n        userData.retData.userId = ret.retData.userId || ''\n        userData.retData.offUserId = ret.retData.offUserId || ''\n        userData.retData.phoneNumber = ret.retData.phoneNumber || ''\n        userData.retData.avatar = ret.retData.avatar || ''\n        userData.retData.sdToken = ret.retData.user_center_access_token || ''\n        userData.retData.accessToken = ret.retData.uhome_access_token || ''\n        userData.retData.nickname = ret.retData.nickname || ''\n        userData.retData.email = ret.retData.email || ''\n        userData.retCode = '000000'\n        userData.retInfo = '获取用户信息成功'\n        resolve(userData);\n      } else {\n        userData.retCode = ret.retCode || ''\n        userData.retInfo = ret.retInfo || ''\n        userData.retData = ret.retData || {}\n        reject(userData);\n      }\n\n    } catch (error) {\n      window.console.log('wyf--getUserInfo--error', JSON.stringify(error))\n    }\n  })\n}\n\n/**\n * 添加容器进入前台事件监听器\n * @param cb \n */\nexport async function addResumeEventListener(cb: Function, name: string) {\n  try {\n    uplusApi.addResumeEventListener(cb, name);\n  } catch (error) {\n    window.console.log('wyf--addResumeEventListener--error', JSON.stringify(error));\n  }\n}\n\n/**\n * 添加容器进入后台事件监听器\n * @param cb \n */\nexport async function addPauseEventListener(cb: Function, name: string) {\n  try {\n    uplusApi.addPauseEventListener(cb, name);\n  } catch (error) {\n    window.console.log('wyf--addPauseEventListener--error', JSON.stringify(error));\n  }\n}\n\n/**\n * 移除容器监听到网络连接时\n * @param cb \n */\nexport function removeOnlineEventListener(cb: Function) {\n  // uplusApi.initDeviceReady().then(() => {\n  //   window.api.removeEventListener({\n  //     name: 'online'\n  //   }, cb);\n  // });\n}\n\n/**\n * 添加容器监听到网络连接时\n * @param cb \n */\nexport function addOnlineEventListener(cb: Function) {\n  // uplusApi.initDeviceReady().then(() => {\n  //   window.api.addEventListener({\n  //     name: 'online'\n  //   }, cb);\n  // });\n}\n\n/**\n * 移除容器进入前台事件监听器\n * @param cb \n */\nexport async function removeResumeEventListener(name: string) {\n  try {\n    uplusApi.removeResumeEventListener(name);\n  } catch (error) {\n    window.console.log('wyf--removeResumeEventListener--error', JSON.stringify(error));\n  }\n}\n\n/**\n * 网络状态\n */\nexport async function isOnline() {\n  await uplusApi.initDeviceReady();\n  return uplusApi.upNetworkModule.isOnline();\n}\n\n/***\n * 获取app信息\n */\nexport async function getAppInfo() {\n  await uplusApi.initDeviceReady();\n  return uplusApi.upAppinfoModule.getAppInfo()\n}\n\n/**\n * 获取登录状态 1.登陆中 2.已登录 3.未登录；如果用户当前处于‘正在登录中’，循环5次查询用户当前状态\n */\nexport async function getLoginStatus() {\n  let times = 0;\n  return new Promise((resolve, reject) => {\n    const loginStatusFn = setInterval(async () => {\n      times++;\n      if (times - 0 >= 5) {\n        clearInterval(loginStatusFn);\n        resolve(false);\n      }\n      try {\n        await uplusApi.initDeviceReady();\n        const res = await uplusApi.upUserModule.getLoginStatus();\n        if (res.retCode && res.retCode === '000000' && res.retData &&\n          typeof res.retData.isLogin === 'boolean' && typeof res.retData.isLogining === 'boolean') {\n          let isLogin = res.retData.isLogin;\n          let isLogining = res.retData.isLogining;\n          if (isLogin && !isLogining) {\n            // 已登录\n            clearInterval(loginStatusFn);\n            console.log('当前登录状态======已登录');\n            resolve(true);\n          } else if (!isLogin && !isLogining) {\n            // 未登录\n            clearInterval(loginStatusFn);\n            console.log('当前登录状态======未登录');\n            resolve(false);\n          } else {\n            // 登录中\n            console.log('当前登录状态======登录中');\n          }\n        }\n        reject(res);\n      } catch (error) {\n        window.console.log('wyf--getLoginStatus--error', JSON.stringify(error))\n      }\n    }, 200);\n  })\n}\n\n/**\n * 获取手机顶部状态栏高度\n */\nexport async function getStatusBarHeight() {\n  // if (/iPhone|mac|iPod|iPad/i.test(window.navigator.userAgent)) {\n  //   return 0;\n  // };\n  await uplusApi.initDeviceReady();\n  return new Promise(async (resolve, reject) => {\n    const ret = await uplusApi.getStatusBarHeight()\n    if (ret && ret.retCode === \"000000\" && ret.retData && resolve) {\n      // Window 属性 devicePixelRatio 能够返回当前显示设备的物理像素分辨率与 CSS 像素分辨率的比率\n      console.log('获取手机顶部状态栏高度--容器返回--', ret, 'window.devicePixelRatio--', window.devicePixelRatio);\n      let devPixelRatio = window.devicePixelRatio;\n      let height = ret.retData.height - 0;\n      if (!(/iPhone|mac|iPod|iPad/i.test(window.navigator.userAgent))) {\n        height = height / devPixelRatio;\n      };\n      resolve(height)\n    } else {\n      reject(ret)\n    }\n  })\n}\n\n/**\n * 容器进入前台事件监听器\n */\n// let resumeIosEventStack: Array<any> = [];\nexport async function resumeEvent(cb: Function, name: string) {\n  try {\n    uplusApi.addResumeEventListener(cb, name);\n  } catch (error) {\n    window.console.log('wyf--addResumeEventListener--error', JSON.stringify(error))\n  }\n  // if (/iPhone|mac|iPod|iPad/i.test(window.navigator.userAgent)) {\n  //   console.log(cb);\n  //   addIosResumeEventListener(cb);\n  //   window.api.addEventListener({\n  //     name: 'resume',\n  //   }, () => resumeIosEventStack.map((eventHandle: any) => {\n  //     try {\n  //       eventHandle();\n  //     } catch (err) {\n  //       console.log('resumeIosEventStack--err--', err);\n  //     }\n  //   }));\n  // }\n}\n\n// function addIosResumeEventListener(eventListener: Function): void {\n//   if (typeof eventListener === 'function') {\n//     let flag = false;\n//     for (let i = 0; i < resumeIosEventStack.length; i++) {\n//       if (resumeIosEventStack[i] === eventListener) {\n//         flag = true;\n//         break;\n//       }\n//     }\n//     if (!flag) {\n//       resumeIosEventStack.push(eventListener);\n//     }\n//   }\n// }\n\n\n/**\n * 移除容器进入前台事件监听器\n * @param cb \n */\nexport async function removeResumeEvent(name: string) {\n  try {\n    uplusApi.removeResumeEventListener(name);\n  } catch (error) {\n    window.console.log('wyf--removeResumeEventListener--error', JSON.stringify(error))\n  }\n}\n\n/**\n * 监听token的变化\n */\nexport async function userInfoChangeListener(cb: any) {\n  if (cb && typeof cb === 'function') {\n    document.addEventListener('publishUserTokenChange', cb, false);\n    document.addEventListener('publishAfterLogOut', cb, false);\n    document.addEventListener('publishUserExtraInfoChange', cb, false);\n  }\n}\n\n/**\n * @method reportPageClickFn 交互事件打点处理\n * @param {object}\n *      url（string,当前页面url）\n *      actionCode（string,交互点标识）\n *      extentInfo（object,额外信息）\n */\nexport function reportPageClickFn({ url, actionCode, extentInfo }: any) {\n  console.log(`reportPageClickEvent:  ${JSON.stringify({ url, actionCode, extentInfo })}`);\n  return new Promise((resolve, reject) => {\n    uplusApi.initDeviceReady().then(() => {\n      uplusApi.upTraceModule.reportPageClickEvent({ url, actionCode, extentInfo })\n        .then((res: any) => {\n          console.log(`reportPageClickEvent success: ${JSON.stringify(res)}`);\n          resolve(res);\n        })\n        .catch((error: any) => {\n          console.log(`reportPageClickEvent fail: ${JSON.stringify(error)}`);\n          reject(error);\n        });\n    });\n  });\n}\n\n/**\n * @method getIntegerData 获取原生整型存储数据\n * @param {string} storageName 存储变量名\n * @param {number} orderedType 排序方式,取值范围0/1（0：ASC；1：DESC）\n */\nexport async function getIntegerData(storageName: string, orderedType: number = 0) {\n  await uplusApi.initDeviceReady();\n  return uplusApi.upStorageModule.getIntegerValue({\n    storageName,\n    orderedType\n  });\n}\n", "import React from 'react';\nimport { Toast } from 'antd-mobile';\nimport { uplusApi } from './uplus-api';\n\nlet hasToastInstance: boolean = false;\nlet toastInstance: any = false;\n\n/**\n * @method 发送http请求\n */\nexport async function http({\n  url = '',\n  headers = {},\n  data = '',\n  method = '',\n  // status = 'loggedin',\n  isToast = false,\n  signType = 'SHA256'\n}: any) {\n  try {\n    let tempData = typeof data === 'object' ? JSON.stringify(data) : data;\n    let tempHeaders = formatHeader(headers, url);\n    if (!tempHeaders) {\n      return;\n    }\n    console.log('tempHeaders', tempHeaders);\n\n    // 只开一个toast\n    if (isToast) {\n      openToast();\n    }\n    // 发送请求\n    // let begin = Date.now();\n    await uplusApi.initDeviceReady()\n    let result = await uplusApi.upRequestModule.signRequest({\n      'url': url,\n      'headers': tempHeaders,\n      'body': tempData,\n      'method': method.toUpperCase(), //GET、POST、PUT、DELETE\n      'signType': signType, //SHA256、MD5\n      'transform': true\n    });\n    console.log('====================', url, result);\n    // 关闭toast\n    closeToast();\n    let retData: any = '';\n    try {\n      retData = JSON.parse(result.retData);\n    } catch (error) {\n      retData = result.retData;\n    }\n    // 服务正常通信\n    if (retData && retData.retCode === '00000') {\n      // let time = Date.now() - begin;\n      // window.__bl && window.__bl.api(url, true, time, retData.retCode, retData.retInfo);\n      return Promise.resolve(retData);\n    }\n    // let time = Date.now() - begin;\n    // window.__bl && window.__bl.api(url, false, time, retData.retCode, retData.retInfo);\n    const errorCode = retData.retCode;\n    return Promise.reject({\n      'errorInfo': retData.retInfo,\n      'errorCode': errorCode,\n      'error': result\n    });\n  } catch (error) {\n    // 关闭toast\n    closeToast();\n    console.log('catch', error);\n    let msg = '';\n    try {\n      msg = JSON.parse(error.retData);\n    } catch (err) {\n      msg = error.retData;\n    }\n    return errorFn(msg, isToast);\n  }\n}\n\nasync function errorFn(message: any, isToast: any) {\n  console.log('异常信息', message, isToast)\n  // let online = true;\n  // try {\n  //   // 判断是否断网\n  //   const ret = await isOnline();\n  //   online = ret && ret.retData;\n  // } catch (e) {\n  //   online = false;\n  // }\n  // if (!online && isToast) {\n  //   toastInfo('网络异常');\n  // }\n  // return;\n}\n\nexport function openToast() {\n  if (toastInstance) {\n    window.clearTimeout(toastInstance);\n  }\n  // 只开一个toast\n  if (!hasToastInstance) {\n    Toast.loading(<div className=\"user-center-loading\"></div>, 30);\n    hasToastInstance = true;\n  }\n}\n\nexport function closeToast() {\n  if (toastInstance) {\n    window.clearTimeout(toastInstance);\n  }\n  toastInstance = window.setTimeout(() => {\n    if (hasToastInstance) {\n      Toast.hide();\n      hasToastInstance = false;\n    }\n  }, 10);\n}\n\n/**\n * @method 格式化header\n */\nfunction formatHeader(header: object = {}, url: string) {\n  try {\n    const timestamp = new Date().getTime().toString();\n    const formatFn = (data: any, url: string) => {\n      const {\n        appId = '',\n        appKey = '',\n        appVersion = '',\n        clientId = '',\n        accessToken = '',\n        sdToken = '',\n        userId = ''\n      } = data;\n      const appInfo: any = {\n        'Content-Type': 'application/json;charset=UTF-8',\n        appId,\n        clientId,\n        userId,\n        accessToken,\n        accountToken: sdToken,\n        timestamp,\n        appVersion,\n        sequenceId: timestamp,\n        // timezone 是获取未读消息数必须传的参数，否则报错\n        timezone: 'Asia/Shanghai'\n      };\n      if (url.indexOf('scs') === -1) {\n        appInfo.appKey = appKey\n      }\n      return appInfo;\n    };\n\n    if (header && Object.keys(header).length > 0) {\n      return formatFn(header, url);\n    }\n    return '';\n    //  else {\n    //   console.log('走了重新获取uerInfo')\n    //   const ret = await getUserInfo();\n    //   if (ret && ret.retData) {\n    //     return formatFn(ret.retData);\n    //   }\n    // }\n  } catch (error) {\n    console.log('formatHeader--error', error);\n  }\n}\n\n", "// container_type： 1-cordova  2--apicloud\n\n// 生产\nconst jump_url_sc = {\n    'loginURL': 'apicloud://usercenter',\n\n    'userinfoURL': 'http://uplus.haier.com/uplusapp/personalCenter/userinfo.html?hidesBottomBarWhenPushed=1&needAuthLogin=1',//个人中心\n    'haiervipURL': 'https://hzy.haier.com/cn/vipcode/myVip?type=haier&resource=uhome&container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//海尔会员、会员中心\n    'messageinfoURL': 'mpaas://messageCenter?needAuthLogin=1',//消息中心\n    'haiershellURL': 'apicloud://memberPoints?uChannelCode=3004-109&needAuthLogin=1',//领海贝\n    'hotactivitiesURL': 'mpaas://popularEvents?uChannelCode=3004-109&needAuthLogin=0',//热门活动 免登\n\n    'serviceURL': 'apicloud://wzService?needAuthLogin=1',// 我的服务\n    'progressQueryURL': 'https://haierservice.haier.net/entranceapi/userCenter/gateWay/getWay1?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',// 进度查询\n    'elecQuipURL': 'https://haierservice.haier.net/entranceapi/userCenter/gateWay/getWay4?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//我的家电\n    // 'serviceMineURL': 'http://uplus.haier.com/uplusapp/xn/xnmain.html?hidesBottomBarWhenPushed=1&VdnPageFlag=1&needAuthLogin=1',//在线客服\n    'serviceMineURL': 'apicloud://onlineService?needAuthLogin=1',//在线客服\n    'RepairURL': 'https://haierservice.haier.net/entranceapi/userCenter/gateWay/getWay3?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',// 报装报修\n\n    // 'orderMineURL': 'https://m.ehaier.com/www/oauthLogin.html?redirect_url=https%3A%2F%2Fm.ehaier.com%2Fwww%2F%23%2ForderManage%2F0%2F%2F%25E5%2585%25A8%25E9%2583%25A8%25E8%25AE%25A2%25E5%258D%2595%2F0',//我的订单\n    'orderMineURL': 'https://m.ehaier.com/sgmobile/orderList?container_type=2&hidesBottomBarWhenPushe=1&needAuthLogin=1',//我的订单\n    'carMineURL': 'https://m.ehaier.com/sgmobile/cart?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//购物车\n    'collectMineURL': 'https://m.ehaier.com/sgmobile/myCollection?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//收藏\n    'afterSaleURL': 'https://m.ehaier.com/sgmobile/repair?container_type=2&hidesBottomBarWhenPushe=1&needAuthLogin=1',//退款/售后\n    'whiteStripeURL':'https://www.haiercash.com:15006/sgbt/jumpPage.html?channelNo=J4&container_type=3&show_title_bar=true&needAuthLogin=1',//智家白条\n    // 'couponCenterURL': 'https://m.ehaier.com/sgmobile/voucher?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//领券中心\n\n    'scanJonFamilyURL': 'http://uplus.haier.com/uplusapp/main/scanjoinfamily.html?hidesBottomBarWhenPushed=1&needAuthLogin=1',// 加入家庭\n    'familyManageURL': 'apicloud://familymanage?needAuthLogin=1',// 家庭管理\n    'addMemberURL': 'apicloud://familymanage?needAuthLogin=1#members',// 我的家人\n    'deviceManageURL': 'http://uplus.haier.com/uplusapp/DeviceList/deviceList.html?hidesBottomBarWhenPushed=1&needAuthLogin=1',// 设备管理\n    'messageBoardURL': 'apicloud://messageBoard?needAuthLogin=1',//留言板\n    'warrantyManageURL':'',// 质保期管理\n\n    'inviteqrURL': 'http://uplus.haier.com/uplusapp/im/myInviteQt.html?needAuthLogin=1&hidesBottomBarWhenPushed=1',//邀请二维码\n    'goodfriendURL': 'http://uplus.haier.com/uplusapp/im/main.html?needAuthLogin=1&selectIndex=1&hidesBottomBarWhenPushed=1',//我的好友\n    'mydeviceURL': 'http://uplus.haier.com/uplusapp/im/main.html?needAuthLogin=1&selectIndex=2&hidesBottomBarWhenPushed=1',//我的设备\n    'newfriendURL': 'http://uplus.haier.com/uplusapp/im/newFriend.html?needAuthLogin=1&hidesBottomBarWhenPushed=1',//新的朋友\n    'scanaddfriendURL': 'http://uplus.haier.com/uplusapp/im/scanAddFriend.html?needAuthLogin=1&hidesBottomBarWhenPushed=1',//添加好友\n\n    'healthRecordURL': 'mpaas://healthRecord?needAuthLogin=1',//家人健康\n\n    'otherDevicesURL': 'https://www.haigeek.com/mobile-page/thirdParty/thirdIndex.html?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//其他平台授权\n\n    'familyalbum': 'mpaas://familyalbum?needAuthLogin=1',\n    'virtualListURL': 'http://uplus.haier.com/uplusapp/personalCenter/virtualList.html?hidesBottomBarWhenPushed=1&needAuthLogin=0',// 虚拟体验\n    'myActivity': 'apicloud://enjoyFoodMineActivity?needAuthLogin=1',// 我的活动\n    'apphomeURL': 'mpaas://apphome',//app主页\n    'communityURL': 'http://bbs.haier.com/app/village/myvillage.shtml?container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//我的小区\n    // 'awardMineURL': 'https://hzy.haier.com/cn/vipcode/lottery?resource=uhome&container_type=2&hidesBottomBarWhenPushed=1',//我的奖品\n    'myRedBagURL': 'https://wallet.haier.net/userwallet?needAuthLogin=1&container_type=3',//我的红包\n    'awardMineURL': 'https://hzy.haier.com/cn/vipcode/prize?resource=uhome&container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//我的奖品\n    'inviteFriendURL': 'apicloud://invitefriends?needAuthLogin=1',//邀请好友\n\n    'systemSetURL': 'http://uplus.haier.com/uplusapp/setting/index.html?hidesBottomBarWhenPushed=1&needAuthLogin=1',//设置\n    // 'userFeedBackURL': 'mpaas://feedback?needAuthLogin=1',//问题反馈（改名为：我要反馈）\n    'userFeedBackURL': 'https://uplus.haier.com/uplusapp/problemFeedBack/feedback.html?needAuthLogin=1',//我要反馈（改名为：我要吐槽）\n    'toScoreURL': 'http://uplus.haier.com/uplusapp/evaluation.html?animated=0&VdnPageFlag=1',//去评分 免登\n    'helpCenterURL': 'mpaas://helpCenter?needAuthLogin=0',//帮助中心 免登\n    'aboutUsURL': 'http://uplus.haier.com/uplusapp/settings/aboutus.html?hidesBottomBarWhenPushed=1&needAuthLogin=0',//关于我们 免登\n\n    // 'inviteCodeURL': 'https://resource.haier.net/download/mall/invitecode/index.html?container_type=2&hidesBottomBarWhenPushed=1',//填写邀请码\n    // 'editPhoneURL':'apicloud://phoneNumberChange',//修改手机号\n    // 'casartevipURL': 'https://hzy.haier.com/cn/vipcode/myVip?type=casarte&resource=uhome&container_type=1',//卡萨帝会员\n    // 'haiershellURL': 'https://hzy.haier.com/cn/vipcode/integral?resource=uhome&container_type=1',//海贝详情页\n    // 'uhomeshellURL': 'apicloud://memberPoints#records',//u币详情页\n    // 'honorURL': 'apicloud://memberShip',//勋章详情页\n    // 'deviceClean': 'https://haierservice.haier.net/entranceapi/userCenter/gateWay/getWay2?container_type=2&hidesBottomBarWhenPushed=1',// 家电清洗\n    // 'Charges': 'http://hrservice.haier.net/haier/beta/homepage/go_home_page/policy?tab=cost&out_side=1&container_type=2&hidesBottomBarWhenPushed=1',// 收费标准\n    // 'community': 'https://m.haier.com/cn/bbs/shequn/?container_type=2&hidesBottomBarWhenPushed=1',// 我的社群\n    // 'post': 'https://m.haier.com/bbs/home/<USER>',// 帖子\n    // 'question': 'https://m.haier.com/bbs/home/<USER>',// 问答\n    // 'activity': 'https://m.haier.com/bbs/home/<USER>',// 活动\n    // 'medal': 'https://m.haier.com/bbs/home/<USER>',// 荣誉\n    // 'familyHealth': 'apicloud://healthRecord',//家人健康\n    'userHomePage': 'https://bbs.haier.com/app/center/home?container_type=2' //个人主页\n};\n\n// 验收\nconst jump_url_ys = {\n    ...jump_url_sc,\n    'haiervipURL': 'https://hzytest.haier.com/cn/vipcode/myVip?type=haier&resource=uhome&container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//海尔会员、会员中心\n    // 'awardMineURL': 'https://hzytest.haier.com/cn/vipcode/lottery?resource=uhome&container_type=1&hidesBottomBarWhenPushed=1',//我的奖品\n    'awardMineURL': 'https://hzytest.haier.com/cn/vipcode/prize?resource=uhome&container_type=2&hidesBottomBarWhenPushed=1&needAuthLogin=1',//我的奖品\n    // 'haiershellURL': 'https://hzytest.haier.com/cn/vipcode/integral?resource=uhome&container_type=1',//海贝详情页\n    // 'casartevipURL': 'https://hzytest.haier.com/cn/vipcode/myVip?type=casarte&resource=uhome&container_type=1',//卡萨帝会员\n};\n\nexport {\n    jump_url_sc,\n    jump_url_ys\n};", "import React from 'react';\nimport { Toast } from 'antd-mobile';\nimport { http } from './http-agent';\nimport { jump_url_sc, jump_url_ys } from '../utils/url';\nimport { uplusApi } from './uplus-api';\n\nlet toastInstance: any;\nlet hasToastInstance: boolean = false;\n\n/**\n * 打开loading\n */\nexport function openLoading() {\n  if (toastInstance) {\n    window.clearTimeout(toastInstance);\n  }\n  // 只开一个toast\n  if (!hasToastInstance) {\n    Toast.loading((<div className=\"my-loading\" />), 30);\n    hasToastInstance = true;\n  }\n}\n\n/**\n * 关闭loading\n */\nexport function closeToast() {\n  if (toastInstance) {\n    window.clearTimeout(toastInstance);\n  }\n  toastInstance = window.setTimeout(() => {\n    if (hasToastInstance) {\n      Toast.hide();\n      hasToastInstance = false;\n    }\n  }, 10);\n}\n\n/**\n * 弹框提示\n * @param content 弹框提示内容\n */\nexport function toastInfo(content: string) {\n  if (content) {\n    window.setTimeout(() => Toast.info(content), 10);\n  }\n}\n\n/**\n * 获取广告轮播图\n */\nexport async function getAds({ header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uhome.haier.net/omsappapi/ad/popup',\n      'headers': { header },\n      //暂时不确定广告内容  1006\n      'data': { adLocation: '1024' },\n      'method': 'POST',\n      'isToast': false\n    });\n    console.log('getAds---success---', retObj)\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n\n  } catch (error) {\n    console.log('getAds---error---', error)\n    return Promise.reject(error);\n  }\n}\n\n// 获取用户会员等级（海尔会员、卡萨帝会员）（暂时未使用）\nexport async function getRankInfoByUser({ userId, brandName, header }: any) {\n  return http({\n    'url': 'https://uhome.haier.net/emuplus/member/grade/getRankInfoByUser',\n    'data': { userId, brandName, source: 'hb' },\n    'method': 'POST',\n    'headers': header,\n    'isToast': false\n  }).catch((e) => console.log('getRankInfoByUser--' + brandName + '--error--', JSON.stringify(e)));\n}\n\n// 获取海贝总数（暂时未使用）\nexport async function getHaierShell({ userId, header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uhome.haier.net/omsappapi/haiBei/v1/getHaiBeiTotal',\n      'data': { userId },\n      'method': 'POST',\n      'headers': header,\n      'isToast': false\n    });\n    console.log('getHaierShell--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n\n  } catch (error) {\n    console.log('getHaierShel--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n// 获取U币、勋章数量（暂时未使用）\nexport async function getAssets({ header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uhome.haier.net/omsappapi/user/membershipinfo',\n      'method': 'POST',\n      'headers': header,\n      'isToast': false\n    });\n    console.log('getAssets--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n\n  } catch (error) {\n    console.log('getAssets--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n// 获取消息中心未读数\nexport async function getMsgNum({ header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uws.haier.net/ums/v3/msg/getUnreadNum',\n      'method': 'POST',\n      'headers': header,\n      'isToast': false\n    });\n    console.log('getMsgNum--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.retData) {\n      return Promise.resolve(retObj.retData);\n    }\n    return Promise.reject(retObj.retCode);\n  } catch (error) {\n    console.log('getMsgNum--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n// 获取热门推荐数据（暂时未使用）\nexport async function getRecommends() {\n  try {\n    await uplusApi.initDeviceReady();\n    const retObj: any = await uplusApi.httpModule.post({\n      'url': 'https://uhome.haier.net:443/omsappapi/quick/entry',\n      'data': { local: '3004' },\n      'headers': {}\n    });\n    console.log('getRecommends--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n\n  } catch (error) {\n    console.log('getRecommends--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n// 判断app是生产还是验收app\nexport function getAppType(haierUserCenterUrl: string = '') {\n  console.log('haierUserCenterUrl', haierUserCenterUrl);\n  if (haierUserCenterUrl === 'https://account-api.haier.net/' || haierUserCenterUrl === '') {\n    console.log('生产环境');\n    return jump_url_sc;\n  } else {\n    console.log('非生产环境');\n    return jump_url_ys;\n  }\n}\n\n/**\n * 获取家庭列表\n */\nexport async function getFamilyList({ header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uhome.haier.net/emuplus/family/v1/family/list',\n      'method': 'POST',\n      'isToast': false,\n      'headers': header,\n      'signType': 'MD5'\n    });\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n\n  } catch (error) {\n    return Promise.reject(error);\n  }\n}\n\n// 判断是否有新的用户反馈\nexport async function getUserFeedBack({ header }: any) {\n  try {\n    const retObj: any = await http({\n      'url': 'https://uhome.haier.net/omsappapi/feedback/v1/problem/status',\n      'method': 'GET',\n      'headers': header,\n      'isToast': false,\n    });\n    console.log('getUserFeedBack--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n  } catch (error) {\n    console.log('getUserFeedBack--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n// 获得所有家庭留言状态--未读数\nexport const getAllUnReadApi = ({ header }: any) => {\n  return http({\n    'url': 'https://uos.haier.net/board/api/v1/message/board/messageNum/user/unread',\n    'method': 'POST',\n    'headers': header,\n    'data': {\n      'deviceId': header.clientId,\n      'userId': header.userId\n    }\n  })\n}\n\n// 获取用户基本信息--作品数、粉丝数、收藏数、关注数\nexport async function getUserDetial({ header }: any) {\n  try {\n    if (!header.clientId) return;\n    const retObj: any = await http({\n      'url': 'https://zj.haier.net/scs/users/v1/detail',\n      'method': 'GET',\n      'headers': header,\n      'isToast': false,\n    });\n    console.log('getUserDetial--success--', retObj);\n    if (retObj && retObj.retCode === '00000' && retObj.data) {\n      return Promise.resolve(retObj.data);\n    }\n    return Promise.reject(retObj.retCode);\n  } catch (error) {\n    console.log('getUserDetial--error--', error);\n    return Promise.reject(error);\n  }\n}\n\n\n/* 节流 */\nexport function throttle(fn: Function, wait: number) {\n  const callback = fn\n  let timerId: any = null\n\n  // 是否是第一次执行\n  let firstInvoke = true\n\n  return function throttled(this: any, ...args: any[]) {\n    // 如果是第一次触发，直接执行\n    if (firstInvoke) {\n      callback.apply(this, args)\n      firstInvoke = false\n      return\n    }\n\n    // 如果定时器已存在，直接返回。\n    if (timerId) {\n      return\n    }\n\n    timerId = setTimeout(() => {\n      clearTimeout(timerId)\n      timerId = null\n      callback.apply(this, args)\n    }, wait)\n  }\n}\n\n// url 添加指定参数\nexport function addUrlParam(url: string, param: string, value: string) {\n  try {\n    if (url.indexOf('container_type') > 0) {\n      return url;\n    }\n    let urlTemp = '';\n    const data = param + '=' + value;\n    // 判断是否已携带部分参数\n    if (/.*\\?.*=/.test(url)) {\n      urlTemp = url.replace(/\\?(?=.*=.*)/, '?' + data + '&');\n    } else {\n      // 判断是否包含#路由\n      if (/.*#\\/.*/.test(url)) {\n        urlTemp = url.replace(/(.*)(?=#\\/.*)/, '$1?' + data);\n      } else {\n        urlTemp = url + '?' + data\n      }\n    }\n    console.log(urlTemp);\n    return urlTemp;\n  } catch (error) {\n    console.log('addUrlParam--error--', error);\n    return url;\n  }\n}\n\n// arms 解析url\nexport const parseUrl = (url: string) => {\n  // 针对资源包和外部链接\n  if (url.indexOf('file') > -1) {\n    // 处理资源包 \n    let indexEnd = url.lastIndexOf('?');\n    indexEnd = indexEnd === -1 ? url.length : indexEnd;\n    const indexAPICloud = url.indexOf('APICloud');\n    const indexApiCloud = url.indexOf('ApiCloud');\n    const indexH5ResPkg = url.indexOf('H5ResPkg');\n    const indexmPaaS = url.indexOf('mPaaS');\n    let tempUrl = '';\n    if (indexmPaaS >= 0) {\n      tempUrl = url.substring(indexmPaaS + 6, indexEnd);\n    }\n    if (indexAPICloud >= 0) {\n      tempUrl = url.substring(indexAPICloud + 9, indexEnd);\n    }\n    if (indexApiCloud >= 0) {\n      tempUrl = url.substring(indexApiCloud + 9, indexEnd);\n    }\n    if (indexH5ResPkg >= 0) {\n      tempUrl = url.substring(indexH5ResPkg + 9, indexEnd);\n    }\n    tempUrl = tempUrl.substring(tempUrl.indexOf('/') + 1);\n    tempUrl = tempUrl + window.location.hash || '';\n    return tempUrl;\n  }\n  return url;\n};\n", "import React, { lazy, Suspense, useEffect, useState } from 'react';\nimport { Switch, HashRouter, Route, withRouter } from 'react-router-dom';\nimport { ActivityIndicator } from 'antd-mobile';\nimport { getUserInfo, getAppInfo, getStatusBarHeight, userInfoChangeListener, resumeEvent, addPauseEventListener } from '../src/utils/uplus-api';\nimport { getAppType } from '../src/utils/funs';\n\nconst Home = lazy(() => import('./pages/home/<USER>'));\n\nfunction App() {\n  const [commonInfo, setCommonInfo] = useState({\n    appInfo: {},\n    userInfo: {},\n    jumpUrl: getAppType(),\n    statusBarHeight: 20\n  });\n\n  useEffect(() => {\n    async function getCurUserInfo() {\n      let userInfo: any;\n      try {\n        userInfo = await getUserInfo();\n        console.log('getCurUserInfo--userinfo', userInfo);\n        setCommonInfo((preState: any) => {\n          return {\n            ...preState,\n            userInfo: { ...userInfo.retData },\n          }\n        })\n      } catch (error) {\n        console.log('getCurUserInfo--error', error)\n        if (error && error.retCode !== '110002') {\n          setCommonInfo((preState: any) => {\n            return {\n              ...preState,\n              userInfo: { logined: false },\n            }\n          });\n        } else {\n          setTimeout(() => {\n            getCurUserInfo();\n          }, 1000);\n        }\n      }\n      try {\n        const retData = userInfo && userInfo.retData ? userInfo.retData : {};\n        const haierUserCenterUrl = retData && retData.haierUserCenterUrl ? retData.haierUserCenterUrl : '';\n        if (haierUserCenterUrl) {\n          const jumpUrl = getAppType(haierUserCenterUrl);\n          setCommonInfo((preState: any) => {\n            return {\n              ...preState,\n              jumpUrl\n            }\n          })\n        }\n      } catch (error) {\n        console.log(error);\n      }\n    }\n    async function getCurAppInfo() {\n      try {\n        const appInfo: any = await getAppInfo();\n        console.log('getCurAppInfo-appinfo', appInfo);\n        setCommonInfo((preState: any) => {\n          return {\n            ...preState,\n            appInfo: appInfo.retData\n          }\n        })\n      } catch (error) {\n        setCommonInfo((preState: any) => {\n          return {\n            ...preState,\n            appInfo: {}\n          }\n        })\n        console.log('getCurAppInfo--error', error)\n      }\n    }\n    async function getCurBarHeight() {\n      try {\n        const height: any = await getStatusBarHeight();\n        console.log('getCurBarHeight-appinfo', height);\n        setCommonInfo((preState: any) => {\n          return {\n            ...preState,\n            statusBarHeight: height\n          }\n        })\n      } catch (error) {\n        console.log('getCurBarHeight--error', error)\n      }\n    }\n    getCurBarHeight();\n    getCurUserInfo();\n    getCurAppInfo();\n    userInfoChangeListener((info: any) => {\n      console.log('userInfoChangeListener --', JSON.stringify(info))\n      let infoObj: any = {};\n      try {\n        infoObj = JSON.parse(JSON.stringify(info));\n      } catch (error) {\n        console.log(error);\n      }\n      console.log('userInfoChangeListener ==', infoObj)\n      if (infoObj && infoObj.data && infoObj.data.retData) {\n        setCommonInfo((preState: any) => {\n          const obj: any = { ...preState };\n          if (infoObj.data.retData.userId) {\n            delete infoObj.data.retData.userId;\n          }\n          obj.userInfo = { ...obj.userInfo, ...infoObj.data.retData };\n          if (infoObj.data.retData.access_token) {\n            obj.userInfo.accessToken = infoObj.data.retData.uhome_access_token;\n            obj.userInfo.sdToken = infoObj.data.retData.access_token;\n            obj.userInfo.userId = infoObj.data.retData.uhome_user_id;\n            obj.userInfo.logined = true;\n          }\n          console.log('******************', obj);\n          return obj;\n        })\n      } else {\n        getCurUserInfo();\n      }\n    });\n    resumeEvent(getCurUserInfo, 'getCurUserInfo');\n    addPauseEventListener(() => {\n      setCommonInfo((preState: any) => {\n        let infoObj: any = {};\n        try {\n          infoObj = JSON.parse(JSON.stringify(preState));\n        } catch (error) {\n          console.log(error);\n        }\n        return infoObj;\n      });\n    }, 'updateLoginStatus');\n    return () => {\n      userInfoChangeListener(() => null);\n      // removeResumeEvent('getCurUserInfo');\n    }\n  }, []);\n\n  return (\n    <HashRouter>\n      <Suspense fallback={<Loading />}>\n        <Switch>\n          <Route path=\"/\" render={(props) => <Home {...props} {...commonInfo} />} />\n        </Switch>\n      </Suspense>\n    </HashRouter>\n  );\n}\n\nfunction Loading() {\n  return (\n    <div className=\"myloading\">\n      <ActivityIndicator />\n    </div>\n  );\n}\n\nconst WrappedComponent: any = withRouter((App));\n\nexport default WrappedComponent.WrappedComponent;\n", "// import 'react-app-polyfill/stable';\nimport 'core-js/stable';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport './index.css';\nimport App from './App';\n// i18n\nimport FastClick from 'fastclick';\nimport { uplusApi } from './utils/uplus-api';\nimport { parseUrl } from './utils/funs';\n// https://github.com/ant-design/ant-design-mobile/issues/576\nFastClick.attach(document.body);\n// https://github.com/ftlabs/fastclick/issues/583\nFastClick.prototype.focus = (ele: any) => ele.focus(); // 修改focus()方法\n\nconst BrowserLogger = require('alife-logger');\nconst url = window.location.href.toString();\n\nwindow.addEventListener('load', async function (e) {\n  // 调用 setConfig 方法修改 SDK 配置项\n  try {\n    await uplusApi.initDeviceReady();\n    const res = await uplusApi.upAppinfoModule.getAppInfo();\n    const appInfo = res.retData;\n    const __bl = BrowserLogger.singleton({\n      pid:'gmew8onwl7@54db5daaccf7c99',\n      appType:'web',\n      imgUrl:'https://arms-retcode.aliyuncs.com/r.png?',\n      page: parseUrl(url),\n      autoSendPv: false,\n      behavior:false,\n      enableSPA:true,\n      disableHook:true,\n      environment: appInfo.grayMode ? 'gray':'prod',\n      release: appInfo.appVersion,\n      uid: appInfo.clientId,\n      ignore: {\n        ignoreUrls: [\n          function (str: string) {\n            if (str.indexOf('192.168') >= 0 || str.indexOf('localhost') >= 0 || str.indexOf('127.0.0.1') >= 0) {\n              return true;\n            }\n            return false;\n          }]\n      },\n      parseHash: function (hash: any) {\n        return parseUrl(url);\n      }\n    });\n    __bl.pipe = ['setPage', parseUrl(url)];  \n    __bl.setConfig({\n      parseHash: function (hash: any) {\n        return parseUrl(url);\n      }\n    });\n  } catch (err) {\n    console.log('arms went wrong..............', err);\n  }\n});\n\nReactDOM.render(<App />, document.getElementById('root'));\n"], "sourceRoot": ""}
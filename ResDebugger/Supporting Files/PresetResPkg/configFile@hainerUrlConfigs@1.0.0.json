{"mpaas": ["privacyRights", "account-delete", "changeThemeSkin", "memberPoints", "ugc", "invitefriends"], "http": ["zjrs.haier.net/zjapp/ugczj/index.html"], "https": ["zjrs.haier.net/haierActivitys/aggregatePage/index.html", "zjrs.haier.net/haierActivitys/intelligentHouse/index.html", "zjrs.haier.net/haierActivitys/visualizedActivity/index.html", "zjrs.haier.net/release/haierActivitys/HexagonEliminate/index.html", "zjrs.haier.net/zjapp/scan/scanStatic.html", "zjrs.haier.net/zjapp/ugczj/index.html", "m.ehaier.com/sgmobile/myCollection", "m.ehaier.com/sgmobile/myReserve"], "file": [], "ExceptionToggle": {"Open": ["LoadingFailure", "LoadingTimeout", "BlankScreen", "MessagingException", "NativeException", "WKProcessTerminate"]}}
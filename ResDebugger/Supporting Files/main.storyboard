<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="18122" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" colorMatched="YES" initialViewController="Qhj-1W-dHZ">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="18093"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--Root View Controller-->
        <scene sceneID="0vU-DV-Yid">
            <objects>
                <viewController id="SAV-z5-lrO" customClass="RootViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="kdv-It-FhT"/>
                        <viewControllerLayoutGuide type="bottom" id="Sqe-kt-GnH"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="vFv-j5-nmA">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="启用测试数据" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="k5D-by-3U3">
                                <rect key="frame" x="16" y="62" width="104" height="21"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="21" id="jil-YY-fyy"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="Kyt-cY-7ey">
                                <rect key="frame" x="310" y="57" width="51" height="31"/>
                                <connections>
                                    <action selector="onTestSwitchValueChnaged:" destination="SAV-z5-lrO" eventType="valueChanged" id="KQn-zA-Pls"/>
                                </connections>
                            </switch>
                            <segmentedControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="top" segmentControlStyle="plain" selectedSegmentIndex="0" translatesAutoresizingMaskIntoConstraints="NO" id="Vuc-EU-pFy">
                                <rect key="frame" x="25" y="96" width="325" height="29"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="28" id="6Nb-wJ-18p"/>
                                </constraints>
                                <segments>
                                    <segment title="更新"/>
                                    <segment title="查询"/>
                                </segments>
                                <connections>
                                    <action selector="segementedControlDidChanged:" destination="SAV-z5-lrO" eventType="valueChanged" id="1Jb-VJ-Sch"/>
                                </connections>
                            </segmentedControl>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="aRj-f2-CWI" userLabel="QueryView">
                                <rect key="frame" x="0.0" y="132" width="375" height="535"/>
                                <subviews>
                                    <segmentedControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="top" segmentControlStyle="plain" selectedSegmentIndex="0" translatesAutoresizingMaskIntoConstraints="NO" id="NZG-hF-LMn">
                                        <rect key="frame" x="8" y="4" width="359" height="29"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="28" id="GcB-QM-Oik"/>
                                        </constraints>
                                        <segments>
                                            <segment title="普通资源"/>
                                            <segment title="配置文件"/>
                                            <segment title="设备资源"/>
                                            <segment title="全部资源"/>
                                        </segments>
                                        <connections>
                                            <action selector="resourceTypeChanged:" destination="SAV-z5-lrO" eventType="valueChanged" id="1bI-ne-TvT"/>
                                        </connections>
                                    </segmentedControl>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="120" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="8CQ-vd-vw6">
                                        <rect key="frame" x="0.0" y="76" width="375" height="459"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="UPResTableViewCell" rowHeight="120" id="HOv-DE-9dY" customClass="UPResTableViewCell">
                                                <rect key="frame" x="0.0" y="24.5" width="375" height="120"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="HOv-DE-9dY" id="euj-xj-iFD">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="120"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Title" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2l5-y1-9Lh">
                                                            <rect key="frame" x="24" y="11" width="287" height="21"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="21" id="MDb-Vq-c3U"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="16"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1.0.0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="A2k-zQ-0Xh">
                                                            <rect key="frame" x="24" y="33" width="145" height="21"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="145" id="8Jb-6h-O3c"/>
                                                                <constraint firstAttribute="height" constant="21" id="tRB-Gx-pU7"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="12"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="BiH-Ce-M63">
                                                            <rect key="frame" x="16" y="62" width="80" height="30"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="30" id="PKc-x5-v6H"/>
                                                                <constraint firstAttribute="width" constant="80" id="Wa1-qh-cqE"/>
                                                            </constraints>
                                                            <state key="normal" title="安装"/>
                                                            <connections>
                                                                <action selector="onActionButtons:" destination="HOv-DE-9dY" eventType="touchUpInside" id="gJT-du-Mbh"/>
                                                            </connections>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IKX-jX-wa7">
                                                            <rect key="frame" x="106" y="62" width="79.5" height="30"/>
                                                            <state key="normal" title="更新"/>
                                                            <connections>
                                                                <action selector="onActionButtons:" destination="HOv-DE-9dY" eventType="touchUpInside" id="ZJn-fv-Ntl"/>
                                                            </connections>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="xLi-FU-mj4">
                                                            <rect key="frame" x="195.5" y="62" width="80" height="30"/>
                                                            <state key="normal" title="取消"/>
                                                            <connections>
                                                                <action selector="onActionButtons:" destination="HOv-DE-9dY" eventType="touchUpInside" id="G5g-Uh-J70"/>
                                                            </connections>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="giS-4J-QTS">
                                                            <rect key="frame" x="285.5" y="62" width="79.5" height="30"/>
                                                            <state key="normal" title="删除"/>
                                                            <connections>
                                                                <action selector="onActionButtons:" destination="HOv-DE-9dY" eventType="touchUpInside" id="Vc3-Cx-CTh"/>
                                                            </connections>
                                                        </button>
                                                        <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="azI-pr-dmz">
                                                            <rect key="frame" x="16" y="100" width="343" height="2"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="2" id="A1s-zI-xrK"/>
                                                            </constraints>
                                                        </progressView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Config" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dw8-Th-oHv">
                                                            <rect key="frame" x="319" y="13" width="40" height="16"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="16" id="HrP-CF-Ie4"/>
                                                                <constraint firstAttribute="width" constant="40" id="sBQ-B4-Ryn"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" type="system" pointSize="13"/>
                                                            <nil key="textColor"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <constraints>
                                                        <constraint firstItem="IKX-jX-wa7" firstAttribute="height" secondItem="BiH-Ce-M63" secondAttribute="height" id="2Ce-Tc-Clt"/>
                                                        <constraint firstItem="xLi-FU-mj4" firstAttribute="height" secondItem="BiH-Ce-M63" secondAttribute="height" id="BBu-xG-0xs"/>
                                                        <constraint firstItem="IKX-jX-wa7" firstAttribute="width" secondItem="BiH-Ce-M63" secondAttribute="width" id="CDP-FB-hAP"/>
                                                        <constraint firstItem="Dw8-Th-oHv" firstAttribute="leading" secondItem="2l5-y1-9Lh" secondAttribute="trailing" constant="8" id="Gn4-1r-g6L"/>
                                                        <constraint firstItem="BiH-Ce-M63" firstAttribute="top" secondItem="A2k-zQ-0Xh" secondAttribute="bottom" constant="8" id="Hwc-uF-n4u"/>
                                                        <constraint firstItem="giS-4J-QTS" firstAttribute="width" secondItem="BiH-Ce-M63" secondAttribute="width" id="Oo0-dX-ADd"/>
                                                        <constraint firstItem="A2k-zQ-0Xh" firstAttribute="top" secondItem="2l5-y1-9Lh" secondAttribute="bottom" constant="1" id="Pst-gC-73S"/>
                                                        <constraint firstItem="xLi-FU-mj4" firstAttribute="width" secondItem="BiH-Ce-M63" secondAttribute="width" id="TAD-nG-MPt"/>
                                                        <constraint firstItem="Dw8-Th-oHv" firstAttribute="leading" secondItem="2l5-y1-9Lh" secondAttribute="trailing" constant="8" id="UTF-ve-8TO"/>
                                                        <constraint firstItem="azI-pr-dmz" firstAttribute="top" secondItem="BiH-Ce-M63" secondAttribute="bottom" constant="8" id="VeI-hj-jQN"/>
                                                        <constraint firstItem="azI-pr-dmz" firstAttribute="leading" secondItem="euj-xj-iFD" secondAttribute="leadingMargin" id="YWn-3P-AXv"/>
                                                        <constraint firstItem="xLi-FU-mj4" firstAttribute="centerY" secondItem="BiH-Ce-M63" secondAttribute="centerY" id="h0f-3F-H2C"/>
                                                        <constraint firstItem="IKX-jX-wa7" firstAttribute="centerY" secondItem="BiH-Ce-M63" secondAttribute="centerY" id="hEb-71-jii"/>
                                                        <constraint firstItem="Dw8-Th-oHv" firstAttribute="top" secondItem="euj-xj-iFD" secondAttribute="topMargin" constant="2" id="iUf-DB-Veh"/>
                                                        <constraint firstAttribute="trailing" secondItem="giS-4J-QTS" secondAttribute="trailing" constant="10" id="j49-dS-LLW"/>
                                                        <constraint firstItem="giS-4J-QTS" firstAttribute="height" secondItem="BiH-Ce-M63" secondAttribute="height" id="k9Y-Iy-rt3"/>
                                                        <constraint firstItem="giS-4J-QTS" firstAttribute="centerY" secondItem="BiH-Ce-M63" secondAttribute="centerY" id="qzG-2T-BhQ"/>
                                                        <constraint firstItem="IKX-jX-wa7" firstAttribute="leading" secondItem="BiH-Ce-M63" secondAttribute="trailing" constant="10" id="r0A-rD-kL5"/>
                                                        <constraint firstItem="Dw8-Th-oHv" firstAttribute="trailing" secondItem="euj-xj-iFD" secondAttribute="trailingMargin" id="r9L-P8-TlY"/>
                                                        <constraint firstItem="2l5-y1-9Lh" firstAttribute="leading" secondItem="euj-xj-iFD" secondAttribute="leadingMargin" constant="8" id="sAr-1T-uck"/>
                                                        <constraint firstItem="BiH-Ce-M63" firstAttribute="leading" secondItem="euj-xj-iFD" secondAttribute="leadingMargin" id="swx-qy-TbK"/>
                                                        <constraint firstItem="giS-4J-QTS" firstAttribute="leading" secondItem="xLi-FU-mj4" secondAttribute="trailing" constant="10" id="uOi-NP-Alo"/>
                                                        <constraint firstItem="2l5-y1-9Lh" firstAttribute="top" secondItem="euj-xj-iFD" secondAttribute="topMargin" id="wJm-WD-NEk"/>
                                                        <constraint firstItem="A2k-zQ-0Xh" firstAttribute="leading" secondItem="euj-xj-iFD" secondAttribute="leadingMargin" constant="8" id="yFj-D3-DeD"/>
                                                        <constraint firstItem="xLi-FU-mj4" firstAttribute="leading" secondItem="IKX-jX-wa7" secondAttribute="trailing" constant="10" id="yKJ-cd-xbQ"/>
                                                        <constraint firstItem="azI-pr-dmz" firstAttribute="trailing" secondItem="euj-xj-iFD" secondAttribute="trailingMargin" id="zee-mB-b8u"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <connections>
                                                    <outlet property="cancelButton" destination="xLi-FU-mj4" id="cJy-bG-det"/>
                                                    <outlet property="deleteButton" destination="giS-4J-QTS" id="o7J-GE-V3n"/>
                                                    <outlet property="installButton" destination="BiH-Ce-M63" id="opV-4T-7We"/>
                                                    <outlet property="progressView" destination="azI-pr-dmz" id="HfY-mh-yhm"/>
                                                    <outlet property="resTitle" destination="2l5-y1-9Lh" id="loP-mZ-W0a"/>
                                                    <outlet property="resType" destination="Dw8-Th-oHv" id="Uct-3f-6Kr"/>
                                                    <outlet property="resVersion" destination="A2k-zQ-0Xh" id="Hab-ax-WBZ"/>
                                                    <outlet property="updateButton" destination="IKX-jX-wa7" id="ZhU-SO-MRn"/>
                                                </connections>
                                            </tableViewCell>
                                        </prototypes>
                                        <connections>
                                            <outlet property="dataSource" destination="SAV-z5-lrO" id="Ldd-52-DPX"/>
                                            <outlet property="delegate" destination="SAV-z5-lrO" id="auL-Hl-K9s"/>
                                        </connections>
                                    </tableView>
                                    <segmentedControl opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="top" segmentControlStyle="plain" selectedSegmentIndex="0" translatesAutoresizingMaskIntoConstraints="NO" id="VMM-dP-ZNP">
                                        <rect key="frame" x="8" y="40" width="359" height="29"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="28" id="vr4-z0-Jex"/>
                                        </constraints>
                                        <segments>
                                            <segment title="全部列表"/>
                                            <segment title="已安装"/>
                                        </segments>
                                        <connections>
                                            <action selector="resourceStateChanged:" destination="SAV-z5-lrO" eventType="valueChanged" id="SIV-Rg-dqY"/>
                                        </connections>
                                    </segmentedControl>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="VMM-dP-ZNP" secondAttribute="trailing" constant="8" id="9HM-xc-O2W"/>
                                    <constraint firstItem="NZG-hF-LMn" firstAttribute="top" secondItem="aRj-f2-CWI" secondAttribute="top" constant="4" id="Gfs-ar-ghM"/>
                                    <constraint firstItem="VMM-dP-ZNP" firstAttribute="leading" secondItem="aRj-f2-CWI" secondAttribute="leading" constant="8" id="Hbc-pL-e7d"/>
                                    <constraint firstItem="8CQ-vd-vw6" firstAttribute="leading" secondItem="aRj-f2-CWI" secondAttribute="leading" id="L22-WF-7RQ"/>
                                    <constraint firstItem="8CQ-vd-vw6" firstAttribute="top" secondItem="VMM-dP-ZNP" secondAttribute="bottom" constant="8" id="RFd-8c-2a3"/>
                                    <constraint firstAttribute="trailing" secondItem="8CQ-vd-vw6" secondAttribute="trailing" id="VjM-f6-xVm"/>
                                    <constraint firstAttribute="bottom" secondItem="8CQ-vd-vw6" secondAttribute="bottom" id="ZPO-eU-Ap0"/>
                                    <constraint firstItem="VMM-dP-ZNP" firstAttribute="top" secondItem="NZG-hF-LMn" secondAttribute="bottom" constant="8" id="ane-Ye-t6U"/>
                                    <constraint firstItem="NZG-hF-LMn" firstAttribute="leading" secondItem="aRj-f2-CWI" secondAttribute="leading" constant="8" id="d53-Ua-wvF"/>
                                    <constraint firstAttribute="trailing" secondItem="NZG-hF-LMn" secondAttribute="trailing" constant="8" id="uXk-o4-B92"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="pxf-xW-V6U" userLabel="UpdateView">
                                <rect key="frame" x="0.0" y="132" width="375" height="535"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="KYP-9q-Yma">
                                        <rect key="frame" x="68.5" y="50" width="238" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="5EK-ke-RmH"/>
                                            <constraint firstAttribute="width" constant="238" id="LPx-Lw-CL4"/>
                                        </constraints>
                                        <state key="normal" title="更新H5资源包列表"/>
                                        <connections>
                                            <action selector="onUpdateH5PackageList:" destination="SAV-z5-lrO" eventType="touchUpInside" id="de9-wv-6vJ"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="设备型号:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="joo-dO-M1u">
                                        <rect key="frame" x="4" y="90" width="90" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="90" id="Q9V-YA-XED"/>
                                            <constraint firstAttribute="height" constant="21" id="uwA-NY-158"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" systemColor="scrollViewTexturedBackgroundColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="设备标识:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="bfO-G8-jyA">
                                        <rect key="frame" x="4" y="128" width="90" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="21" id="5hE-eS-jmy"/>
                                            <constraint firstAttribute="width" constant="90" id="Q5Q-gY-56F"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" systemColor="scrollViewTexturedBackgroundColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="产品编码:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UUU-1v-6JP">
                                        <rect key="frame" x="4" y="166" width="90" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="90" id="E43-Yn-gB9"/>
                                            <constraint firstAttribute="height" constant="21" id="ZXk-Se-SJZ"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" systemColor="scrollViewTexturedBackgroundColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="KFR-72L/ABA81AU1室内机总成" borderStyle="roundedRect" placeholder="请输入设备型号" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="jHT-8T-1yU">
                                        <rect key="frame" x="108" y="90" width="237" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="LT9-Li-I06"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" text="201c120024000810031200000000000000000000020000000000000000000040" borderStyle="roundedRect" placeholder="请输入TypeId" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Uvk-5C-Ope">
                                        <rect key="frame" x="108" y="129" width="237" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="7CG-sV-qlP"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="请输入8位产品编码" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="GZr-SY-2VT">
                                        <rect key="frame" x="108" y="167" width="237" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="MHN-ra-cVm"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Tit-ib-YLZ">
                                        <rect key="frame" x="38" y="251" width="299" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="ZgH-dp-2DV"/>
                                        </constraints>
                                        <state key="normal" title="更新设备配置文件"/>
                                        <connections>
                                            <action selector="onUpdateDeviceConfig:" destination="SAV-z5-lrO" eventType="touchUpInside" id="nqc-j6-g1d"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ZlN-j3-Uej">
                                        <rect key="frame" x="38" y="291" width="299" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="Nx1-yk-XbG"/>
                                        </constraints>
                                        <state key="normal" title="清除缓存"/>
                                        <connections>
                                            <action selector="deleteAction:" destination="SAV-z5-lrO" eventType="touchUpInside" id="bqM-rQ-wST"/>
                                        </connections>
                                    </button>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="设备类型:" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="OMK-jg-1if">
                                        <rect key="frame" x="4" y="204" width="90" height="21"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="21" id="063-hd-4bs"/>
                                            <constraint firstAttribute="width" constant="90" id="h7K-qC-of8"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <color key="textColor" systemColor="scrollViewTexturedBackgroundColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" placeholder="请输入设备类型" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="X6v-IT-rk0">
                                        <rect key="frame" x="108" y="206" width="237" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="SNY-fc-9jX"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aY1-LU-xIk">
                                        <rect key="frame" x="8" y="331" width="359" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="7Bp-Yj-S0q"/>
                                        </constraints>
                                        <state key="normal" title="自动升级本地资源"/>
                                        <connections>
                                            <action selector="batchUninstall:" destination="SAV-z5-lrO" eventType="touchUpInside" id="AVq-M9-3DL"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="IKl-SR-laE">
                                        <rect key="frame" x="0.0" y="361" width="375" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="oCk-tj-yE8"/>
                                        </constraints>
                                        <state key="normal" title="打开下载资源框"/>
                                        <connections>
                                            <action selector="openDownloaderControllerAction:" destination="SAV-z5-lrO" eventType="touchUpInside" id="Heg-XW-OXh"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qSW-k4-fan">
                                        <rect key="frame" x="0.0" y="391" width="375" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="SFZ-eM-jSl"/>
                                        </constraints>
                                        <state key="normal" title="预加载"/>
                                        <connections>
                                            <action selector="preLoadAction:" destination="SAV-z5-lrO" eventType="touchUpInside" id="ZmE-cE-PoZ"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ny7-UE-kqF">
                                        <rect key="frame" x="10" y="10" width="355" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="Z4d-3e-yo7"/>
                                        </constraints>
                                        <state key="normal" title="更新MPAAS资源包"/>
                                        <connections>
                                            <action selector="UPDateMpaasRes:" destination="SAV-z5-lrO" eventType="touchUpInside" id="GL9-JF-Gvd"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Nvr-Rq-5FS">
                                        <rect key="frame" x="0.0" y="421" width="375" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="Sxr-El-tDN"/>
                                        </constraints>
                                        <state key="normal" title="异步插入资源信息"/>
                                        <connections>
                                            <action selector="insertRes:" destination="SAV-z5-lrO" eventType="touchUpInside" id="1Fd-Na-SFU"/>
                                        </connections>
                                    </button>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="UMv-D2-iP3">
                                        <rect key="frame" x="0.0" y="451" width="375" height="30"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="30" id="nj3-FE-HcK"/>
                                        </constraints>
                                        <state key="normal" title="查询资源列表（resList）"/>
                                        <connections>
                                            <action selector="insertRes:" destination="SAV-z5-lrO" eventType="touchUpInside" id="cON-mB-Ul4"/>
                                            <segue destination="pK4-8q-Axm" kind="show" id="YnF-Qv-mYG"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="ZlN-j3-Uej" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="38" id="15f-se-5gO"/>
                                    <constraint firstItem="Tit-ib-YLZ" firstAttribute="top" secondItem="X6v-IT-rk0" secondAttribute="bottom" constant="15" id="1GD-6d-9mf"/>
                                    <constraint firstItem="bfO-G8-jyA" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="4" id="2hi-KT-xAE"/>
                                    <constraint firstItem="qSW-k4-fan" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" id="2jV-DD-OAc"/>
                                    <constraint firstItem="OMK-jg-1if" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="4" id="3TR-bG-DeQ"/>
                                    <constraint firstItem="KYP-9q-Yma" firstAttribute="top" secondItem="ny7-UE-kqF" secondAttribute="bottom" constant="10" id="3cY-Hc-mUo"/>
                                    <constraint firstItem="Uvk-5C-Ope" firstAttribute="top" secondItem="jHT-8T-1yU" secondAttribute="bottom" constant="9" id="733-7c-d1g"/>
                                    <constraint firstItem="KYP-9q-Yma" firstAttribute="centerX" secondItem="pxf-xW-V6U" secondAttribute="centerX" id="8wj-Ih-Mur"/>
                                    <constraint firstItem="X6v-IT-rk0" firstAttribute="leading" secondItem="OMK-jg-1if" secondAttribute="trailing" constant="14" id="9i0-Ps-ub1"/>
                                    <constraint firstItem="jHT-8T-1yU" firstAttribute="leading" secondItem="joo-dO-M1u" secondAttribute="trailing" constant="14" id="Ad5-iR-2yj"/>
                                    <constraint firstItem="joo-dO-M1u" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="4" id="BsG-ba-eZy"/>
                                    <constraint firstItem="ny7-UE-kqF" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="10" id="C85-jp-row"/>
                                    <constraint firstAttribute="trailing" secondItem="aY1-LU-xIk" secondAttribute="trailing" constant="8" id="DiR-Iv-WOP"/>
                                    <constraint firstItem="UMv-D2-iP3" firstAttribute="top" secondItem="Nvr-Rq-5FS" secondAttribute="bottom" id="Dqy-aM-MsL"/>
                                    <constraint firstItem="OMK-jg-1if" firstAttribute="top" secondItem="UUU-1v-6JP" secondAttribute="bottom" constant="17" id="Fjh-cA-GXe"/>
                                    <constraint firstItem="aY1-LU-xIk" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="8" id="GAn-U8-3k5"/>
                                    <constraint firstAttribute="trailing" secondItem="Uvk-5C-Ope" secondAttribute="trailing" constant="30" id="Gpg-HK-mVa"/>
                                    <constraint firstItem="Uvk-5C-Ope" firstAttribute="leading" secondItem="bfO-G8-jyA" secondAttribute="trailing" constant="14" id="HMy-50-sxR"/>
                                    <constraint firstItem="UUU-1v-6JP" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" constant="4" id="I59-W9-SrE"/>
                                    <constraint firstItem="GZr-SY-2VT" firstAttribute="leading" secondItem="UUU-1v-6JP" secondAttribute="trailing" constant="14" id="IvR-Hb-DiX"/>
                                    <constraint firstAttribute="trailing" secondItem="qSW-k4-fan" secondAttribute="trailing" id="P8v-2c-FEK"/>
                                    <constraint firstAttribute="trailing" secondItem="ZlN-j3-Uej" secondAttribute="trailing" constant="38" id="PHy-TM-laB"/>
                                    <constraint firstAttribute="trailing" secondItem="ny7-UE-kqF" secondAttribute="trailing" constant="10" id="QE7-IY-51B"/>
                                    <constraint firstItem="bfO-G8-jyA" firstAttribute="top" secondItem="joo-dO-M1u" secondAttribute="bottom" constant="17" id="R2z-hf-8Xt"/>
                                    <constraint firstItem="jHT-8T-1yU" firstAttribute="top" secondItem="KYP-9q-Yma" secondAttribute="bottom" constant="10" id="TTU-9V-klD"/>
                                    <constraint firstItem="Nvr-Rq-5FS" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" id="UH1-zx-SMe"/>
                                    <constraint firstItem="IKl-SR-laE" firstAttribute="top" secondItem="aY1-LU-xIk" secondAttribute="bottom" id="ait-NC-Alh"/>
                                    <constraint firstItem="UMv-D2-iP3" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" id="d99-b7-gLX"/>
                                    <constraint firstItem="IKl-SR-laE" firstAttribute="leading" secondItem="pxf-xW-V6U" secondAttribute="leading" id="eFp-rN-Ngg"/>
                                    <constraint firstItem="Nvr-Rq-5FS" firstAttribute="top" secondItem="qSW-k4-fan" secondAttribute="bottom" id="etM-d0-wPQ"/>
                                    <constraint firstItem="Tit-ib-YLZ" firstAttribute="trailing" secondItem="ZlN-j3-Uej" secondAttribute="trailing" id="fH2-NE-LM4"/>
                                    <constraint firstItem="qSW-k4-fan" firstAttribute="top" secondItem="IKl-SR-laE" secondAttribute="bottom" id="i7J-3b-CoN"/>
                                    <constraint firstItem="UUU-1v-6JP" firstAttribute="top" secondItem="bfO-G8-jyA" secondAttribute="bottom" constant="17" id="j8V-fN-fwO"/>
                                    <constraint firstItem="ZlN-j3-Uej" firstAttribute="top" secondItem="Tit-ib-YLZ" secondAttribute="bottom" constant="10" id="jHS-zR-6sB"/>
                                    <constraint firstItem="X6v-IT-rk0" firstAttribute="top" secondItem="GZr-SY-2VT" secondAttribute="bottom" constant="9" id="l0A-Nw-5TN"/>
                                    <constraint firstAttribute="trailing" secondItem="IKl-SR-laE" secondAttribute="trailing" id="l2r-qX-AlS"/>
                                    <constraint firstAttribute="trailing" secondItem="UMv-D2-iP3" secondAttribute="trailing" id="lf8-nw-VaJ"/>
                                    <constraint firstItem="ny7-UE-kqF" firstAttribute="top" secondItem="pxf-xW-V6U" secondAttribute="top" constant="10" id="lux-b9-Of4"/>
                                    <constraint firstAttribute="trailing" secondItem="jHT-8T-1yU" secondAttribute="trailing" constant="30" id="m6O-Yz-jwG"/>
                                    <constraint firstAttribute="trailing" secondItem="X6v-IT-rk0" secondAttribute="trailing" constant="30" id="mw8-A6-frb"/>
                                    <constraint firstItem="aY1-LU-xIk" firstAttribute="top" secondItem="ZlN-j3-Uej" secondAttribute="bottom" constant="10" id="nD6-tw-kVi"/>
                                    <constraint firstAttribute="trailing" secondItem="Nvr-Rq-5FS" secondAttribute="trailing" id="otF-K0-F18"/>
                                    <constraint firstItem="GZr-SY-2VT" firstAttribute="top" secondItem="Uvk-5C-Ope" secondAttribute="bottom" constant="8" id="pAA-Wp-u6r"/>
                                    <constraint firstItem="Tit-ib-YLZ" firstAttribute="centerX" secondItem="ZlN-j3-Uej" secondAttribute="centerX" id="tBn-IW-XKk"/>
                                    <constraint firstAttribute="trailing" secondItem="GZr-SY-2VT" secondAttribute="trailing" constant="30" id="u8F-rq-KcD"/>
                                    <constraint firstItem="joo-dO-M1u" firstAttribute="top" secondItem="KYP-9q-Yma" secondAttribute="bottom" constant="10" id="uwn-jD-f18"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Vuc-EU-pFy" firstAttribute="leading" secondItem="vFv-j5-nmA" secondAttribute="leading" constant="25" id="1lA-cr-aOc"/>
                            <constraint firstAttribute="trailing" secondItem="aRj-f2-CWI" secondAttribute="trailing" id="2MO-nI-zzS"/>
                            <constraint firstItem="pxf-xW-V6U" firstAttribute="trailing" secondItem="vFv-j5-nmA" secondAttribute="trailing" id="2bl-Bp-o8r"/>
                            <constraint firstAttribute="trailing" secondItem="Vuc-EU-pFy" secondAttribute="trailing" constant="25" id="Co9-mc-Dl4"/>
                            <constraint firstItem="pxf-xW-V6U" firstAttribute="leading" secondItem="vFv-j5-nmA" secondAttribute="leading" id="FuZ-Nh-B0U"/>
                            <constraint firstItem="pxf-xW-V6U" firstAttribute="bottom" secondItem="Sqe-kt-GnH" secondAttribute="top" id="Nda-92-ncf"/>
                            <constraint firstItem="Vuc-EU-pFy" firstAttribute="top" secondItem="Kyt-cY-7ey" secondAttribute="bottom" constant="8" id="QqP-N5-rob"/>
                            <constraint firstItem="aRj-f2-CWI" firstAttribute="leading" secondItem="vFv-j5-nmA" secondAttribute="leading" id="R3Q-34-dy9"/>
                            <constraint firstAttribute="trailing" secondItem="Kyt-cY-7ey" secondAttribute="trailing" constant="16" id="T5K-t6-kVs"/>
                            <constraint firstItem="k5D-by-3U3" firstAttribute="leading" secondItem="vFv-j5-nmA" secondAttribute="leading" constant="16" id="d7x-wu-7YL"/>
                            <constraint firstItem="k5D-by-3U3" firstAttribute="top" secondItem="kdv-It-FhT" secondAttribute="bottom" constant="18" id="dSB-8W-WyF"/>
                            <constraint firstItem="Kyt-cY-7ey" firstAttribute="top" secondItem="kdv-It-FhT" secondAttribute="bottom" constant="13" id="oWF-OR-Jiu"/>
                            <constraint firstItem="pxf-xW-V6U" firstAttribute="top" secondItem="Vuc-EU-pFy" secondAttribute="bottom" constant="8" id="pw4-aj-1Qq"/>
                            <constraint firstItem="Sqe-kt-GnH" firstAttribute="top" secondItem="aRj-f2-CWI" secondAttribute="bottom" id="vqK-kf-Thn"/>
                            <constraint firstItem="aRj-f2-CWI" firstAttribute="top" secondItem="Vuc-EU-pFy" secondAttribute="bottom" constant="8" id="wpZ-T9-E4q"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="YM7-Qq-epU"/>
                    <connections>
                        <outlet property="buttonUpdateDeviceConfig" destination="Tit-ib-YLZ" id="vdf-ha-kDl"/>
                        <outlet property="buttonUpdateH5Package" destination="KYP-9q-Yma" id="jAo-uQ-nfX"/>
                        <outlet property="deviceType" destination="X6v-IT-rk0" id="2cW-Bq-p7w"/>
                        <outlet property="queryView" destination="aRj-f2-CWI" id="f6s-vU-0YJ"/>
                        <outlet property="tableView" destination="8CQ-vd-vw6" id="P4E-pE-heU"/>
                        <outlet property="testSwitch" destination="Kyt-cY-7ey" id="LIA-zY-tbF"/>
                        <outlet property="textFieldProNo" destination="GZr-SY-2VT" id="PoN-EH-maN"/>
                        <outlet property="textFieldTypeID" destination="Uvk-5C-Ope" id="FS0-hX-qne"/>
                        <outlet property="textFiledModel" destination="jHT-8T-1yU" id="un4-7B-09k"/>
                        <outlet property="updateView" destination="pxf-xW-V6U" id="Nek-La-GF4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="MF4-TU-uUi" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-127.2" y="135.38230884557723"/>
        </scene>
        <!--Request Normal View Controller-->
        <scene sceneID="v9N-Hs-pQq">
            <objects>
                <viewController id="pK4-8q-Axm" customClass="UPRequestNormalViewController" sceneMemberID="viewController">
                    <layoutGuides>
                        <viewControllerLayoutGuide type="top" id="bpQ-3p-f2q"/>
                        <viewControllerLayoutGuide type="bottom" id="job-86-BTW"/>
                    </layoutGuides>
                    <view key="view" contentMode="scaleToFill" id="o8D-rr-y73">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FUT-AH-1iA">
                                <rect key="frame" x="16" y="84" width="343" height="34"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="资源名称" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="CiV-6W-h8r">
                                        <rect key="frame" x="10" y="0.0" width="70" height="34"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="QJo-D9-CTj">
                                        <rect key="frame" x="90" y="0.0" width="253" height="34"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="QJo-D9-CTj" firstAttribute="leading" secondItem="CiV-6W-h8r" secondAttribute="trailing" constant="10" id="Ah6-GT-m5l"/>
                                    <constraint firstAttribute="bottom" secondItem="QJo-D9-CTj" secondAttribute="bottom" id="CRl-U8-ajm"/>
                                    <constraint firstAttribute="trailing" secondItem="QJo-D9-CTj" secondAttribute="trailing" id="Jib-Es-uaB"/>
                                    <constraint firstAttribute="bottom" secondItem="CiV-6W-h8r" secondAttribute="bottom" id="aP7-g1-usI"/>
                                    <constraint firstItem="CiV-6W-h8r" firstAttribute="top" secondItem="FUT-AH-1iA" secondAttribute="top" id="iZ4-fy-mZq"/>
                                    <constraint firstItem="QJo-D9-CTj" firstAttribute="top" secondItem="FUT-AH-1iA" secondAttribute="top" id="jOf-Vp-Xgq"/>
                                    <constraint firstItem="CiV-6W-h8r" firstAttribute="leading" secondItem="FUT-AH-1iA" secondAttribute="leading" constant="10" id="y0y-cK-aTA"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="CDo-U6-C5c">
                                <rect key="frame" x="16" y="128" width="343" height="34"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="资源类型" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ed9-K0-4Kw">
                                        <rect key="frame" x="10" y="0.0" width="70" height="34"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                        <nil key="textColor"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="m9X-fu-cnv">
                                        <rect key="frame" x="90" y="0.0" width="253" height="34"/>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits"/>
                                    </textField>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="Ed9-K0-4Kw" firstAttribute="leading" secondItem="CDo-U6-C5c" secondAttribute="leading" constant="10" id="5Ye-Xy-5sS"/>
                                    <constraint firstAttribute="bottom" secondItem="Ed9-K0-4Kw" secondAttribute="bottom" id="8gk-99-gKx"/>
                                    <constraint firstAttribute="trailing" secondItem="m9X-fu-cnv" secondAttribute="trailing" id="JAu-Pj-tff"/>
                                    <constraint firstItem="Ed9-K0-4Kw" firstAttribute="top" secondItem="CDo-U6-C5c" secondAttribute="top" id="pfc-XG-u6Z"/>
                                    <constraint firstItem="m9X-fu-cnv" firstAttribute="leading" secondItem="Ed9-K0-4Kw" secondAttribute="trailing" constant="10" id="pxj-9C-lFw"/>
                                    <constraint firstAttribute="bottom" secondItem="m9X-fu-cnv" secondAttribute="bottom" id="r9L-Df-DRN"/>
                                    <constraint firstItem="m9X-fu-cnv" firstAttribute="top" secondItem="CDo-U6-C5c" secondAttribute="top" id="sk7-Mf-kIm"/>
                                </constraints>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Bw4-L0-uRe">
                                <rect key="frame" x="26" y="262" width="323" height="30"/>
                                <state key="normal" title="查询服务器资源"/>
                                <connections>
                                    <action selector="searchAction:" destination="pK4-8q-Axm" eventType="touchUpInside" id="lPj-BX-ql3"/>
                                </connections>
                            </button>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" showsVerticalScrollIndicator="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="gKT-78-Fdv">
                                <rect key="frame" x="26" y="397" width="323" height="260"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <color key="textColor" systemColor="labelColor"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                            </textView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eFp-ru-tEm">
                                <rect key="frame" x="26" y="302" width="323" height="30"/>
                                <state key="normal" title="安装"/>
                                <connections>
                                    <action selector="installAction:" destination="pK4-8q-Axm" eventType="touchUpInside" id="Liw-xr-Uki"/>
                                </connections>
                            </button>
                            <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" translatesAutoresizingMaskIntoConstraints="NO" id="GIT-jG-Mmm">
                                <rect key="frame" x="26" y="352" width="323" height="4"/>
                            </progressView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0%" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rVS-HX-h1S">
                                <rect key="frame" x="174.5" y="366" width="26" height="21"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="CU0-Xb-Yss">
                                <rect key="frame" x="26" y="182" width="323" height="30"/>
                                <state key="normal" title="查询本地已安装最新资源"/>
                                <connections>
                                    <action selector="getLatestInstalledAction:" destination="pK4-8q-Axm" eventType="touchUpInside" id="C9V-Zn-Qtx"/>
                                </connections>
                            </button>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="cgt-RD-7sm">
                                <rect key="frame" x="26" y="222" width="323" height="30"/>
                                <state key="normal" title="查询本地资源"/>
                                <connections>
                                    <action selector="getLocalResource:" destination="pK4-8q-Axm" eventType="touchUpInside" id="7ZI-gZ-6dT"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="gKT-78-Fdv" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="2Ms-GB-KeU"/>
                            <constraint firstItem="gKT-78-Fdv" firstAttribute="top" secondItem="rVS-HX-h1S" secondAttribute="bottom" constant="10" id="9zr-OO-UAd"/>
                            <constraint firstAttribute="trailingMargin" secondItem="Bw4-L0-uRe" secondAttribute="trailing" constant="10" id="BDH-V0-3QR"/>
                            <constraint firstItem="cgt-RD-7sm" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="CvR-df-yX4"/>
                            <constraint firstItem="rVS-HX-h1S" firstAttribute="top" secondItem="GIT-jG-Mmm" secondAttribute="bottom" constant="10" id="EnV-WQ-SOc"/>
                            <constraint firstItem="GIT-jG-Mmm" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="Gct-EK-2os"/>
                            <constraint firstAttribute="trailingMargin" secondItem="GIT-jG-Mmm" secondAttribute="trailing" constant="10" id="Isu-R3-rvb"/>
                            <constraint firstAttribute="trailingMargin" secondItem="FUT-AH-1iA" secondAttribute="trailing" id="L1W-b9-q0b"/>
                            <constraint firstItem="eFp-ru-tEm" firstAttribute="top" secondItem="Bw4-L0-uRe" secondAttribute="bottom" constant="10" id="MAa-Db-cIT"/>
                            <constraint firstItem="CU0-Xb-Yss" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="MPT-Lc-bjD"/>
                            <constraint firstItem="Bw4-L0-uRe" firstAttribute="top" secondItem="cgt-RD-7sm" secondAttribute="bottom" constant="10" id="OLv-yy-JA9"/>
                            <constraint firstItem="CU0-Xb-Yss" firstAttribute="top" secondItem="CDo-U6-C5c" secondAttribute="bottom" constant="20" id="PKi-YI-8vn"/>
                            <constraint firstAttribute="trailingMargin" secondItem="CU0-Xb-Yss" secondAttribute="trailing" constant="10" id="VVU-F5-Fhv"/>
                            <constraint firstItem="CDo-U6-C5c" firstAttribute="top" secondItem="FUT-AH-1iA" secondAttribute="bottom" constant="10" id="WCE-La-1Yb"/>
                            <constraint firstItem="eFp-ru-tEm" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="YME-T0-pcW"/>
                            <constraint firstItem="CDo-U6-C5c" firstAttribute="trailing" secondItem="o8D-rr-y73" secondAttribute="trailingMargin" id="Yjd-fn-oUW"/>
                            <constraint firstItem="GIT-jG-Mmm" firstAttribute="top" secondItem="eFp-ru-tEm" secondAttribute="bottom" constant="20" id="Zmu-uv-eUG"/>
                            <constraint firstItem="cgt-RD-7sm" firstAttribute="top" secondItem="CU0-Xb-Yss" secondAttribute="bottom" constant="10" id="dg7-5V-jcE"/>
                            <constraint firstAttribute="trailingMargin" secondItem="eFp-ru-tEm" secondAttribute="trailing" constant="10" id="fEB-yr-0bU"/>
                            <constraint firstAttribute="trailingMargin" secondItem="gKT-78-Fdv" secondAttribute="trailing" constant="10" id="hSi-b5-uFr"/>
                            <constraint firstItem="job-86-BTW" firstAttribute="top" secondItem="gKT-78-Fdv" secondAttribute="bottom" constant="10" id="nDM-9F-SAR"/>
                            <constraint firstItem="Bw4-L0-uRe" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" constant="10" id="oip-Qy-LaI"/>
                            <constraint firstAttribute="trailingMargin" secondItem="cgt-RD-7sm" secondAttribute="trailing" constant="10" id="qNz-aj-5cP"/>
                            <constraint firstItem="FUT-AH-1iA" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" id="tB9-2I-iJA"/>
                            <constraint firstItem="CDo-U6-C5c" firstAttribute="leading" secondItem="o8D-rr-y73" secondAttribute="leadingMargin" id="tfo-IS-Oh6"/>
                            <constraint firstItem="rVS-HX-h1S" firstAttribute="centerX" secondItem="o8D-rr-y73" secondAttribute="centerX" id="uO5-sE-NlC"/>
                            <constraint firstItem="FUT-AH-1iA" firstAttribute="top" secondItem="bpQ-3p-f2q" secondAttribute="bottom" constant="40" id="znN-Ag-9ME"/>
                        </constraints>
                    </view>
                    <navigationItem key="navigationItem" id="wvA-j0-4Fx"/>
                    <connections>
                        <outlet property="progressLabel" destination="rVS-HX-h1S" id="aFy-CV-cnY"/>
                        <outlet property="progressView" destination="GIT-jG-Mmm" id="d0g-Ef-20s"/>
                        <outlet property="resNameTextField" destination="QJo-D9-CTj" id="JUt-br-NBC"/>
                        <outlet property="resTypeTextField" destination="m9X-fu-cnv" id="uCV-wM-wjL"/>
                        <outlet property="resultTextView" destination="gKT-78-Fdv" id="7Ii-sm-g0W"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="oxQ-Xe-hIQ" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="644" y="134.48275862068968"/>
        </scene>
        <!--Navigation Controller-->
        <scene sceneID="C1i-gy-dQr">
            <objects>
                <navigationController id="Qhj-1W-dHZ" sceneMemberID="viewController">
                    <navigationBar key="navigationBar" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="GfC-9F-9q5">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                    </navigationBar>
                    <connections>
                        <segue destination="SAV-z5-lrO" kind="relationship" relationship="rootViewController" id="MIV-vX-BQe"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hTZ-v9-GFJ" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-897" y="137"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="scrollViewTexturedBackgroundColor">
            <color red="0.43529411764705878" green="0.44313725490196082" blue="0.47450980392156861" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

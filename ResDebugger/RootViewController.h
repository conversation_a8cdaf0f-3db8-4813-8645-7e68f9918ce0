//
//  RootViewController.h
//  ResDebugger
//
//  Created by <PERSON> on 2018/9/12.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <UIKit/UIKit.h>
#import "UPTableViewController.h"
#import "UPResourceInfo.h"

@interface RootViewController : UIViewController
@property (nonatomic, weak) IBOutlet UIView *updateView;
@property (weak, nonatomic) IBOutlet UITextField *textFiledModel;
@property (weak, nonatomic) IBOutlet UITextField *textFieldTypeID;
@property (weak, nonatomic) IBOutlet UITextField *textFieldProNo;
@property (weak, nonatomic) IBOutlet UIView *queryView;
@property (weak, nonatomic) IBOutlet UITableView *tableView;
@property (weak, nonatomic) IBOutlet UISwitch *testSwitch;
@property (weak, nonatomic) IBOutlet UIButton *buttonUpdateH5Package;
@property (weak, nonatomic) IBOutlet UIButton *buttonUpdateDeviceConfig;
@property (weak, nonatomic) IBOutlet UITextField *deviceType;


@property (nonatomic, strong) NSArray<UPResourceInfo *> *resInfoList;

- (IBAction)onTestSwitchValueChnaged:(UISwitch *)sender;
- (IBAction)segementedControlDidChanged:(UISegmentedControl *)sender;
- (IBAction)onUpdateH5PackageList:(UIButton *)sender;
- (IBAction)onUpdateDeviceConfig:(UIButton *)sender;
- (IBAction)resourceTypeChanged:(UISegmentedControl *)sender;
- (IBAction)resourceStateChanged:(UISegmentedControl *)sender;
@end

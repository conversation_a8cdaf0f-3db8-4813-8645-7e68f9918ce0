//
//  UPTableViewController.m
//  ResDebugger
//
//  Created by <PERSON> on 2018/9/12.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPTableViewController.h"

@interface UPTableViewController ()
- (void)initializeBackBarItem;
@end

@implementation UPTableViewController
#pragma mark - Non-Public Methods
- (void)initializeBackBarItem
{
    UIBarButtonItem *item = [[UIBarButtonItem alloc] initWithTitle:@"返回" style:UIBarButtonItemStylePlain target:self action:@selector(onBackBarButtonItem:)];
    self.navigationItem.backBarButtonItem = item;
}

#pragma mark - Public Methods
- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view.
    [self initializeBackBarItem];
    [self initializeTableView];
}

- (void)initializeTableView
{
    [self.view setBackgroundColor:[UIColor whiteColor]];
    UITableView *tableView = [[UITableView alloc] initWithFrame:self.view.frame style:UITableViewStyleGrouped];
    tableView.delegate = self;
    tableView.dataSource = self;
    [self.view addSubview:tableView];
    self.tableView = tableView;
}

- (void)onBackBarButtonItem:(UIBarButtonItem *)sender
{
    [self.navigationController popViewControllerAnimated:YES];
}

#pragma mark - UITableViewDelegate
- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath
{
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Warc-performSelector-leaks"
    [self performSelector:NSSelectorFromString(self.tableViewItems[indexPath.row * 3 + 2])
               withObject:nil];
#pragma clang diagnostic pop

    [tableView deselectRowAtIndexPath:indexPath
                             animated:YES];
}

#pragma mark - UITableViewDataSource
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.tableViewItems.count / 3;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath
{
    static NSString *cellIdentifier = @"cellIdentifier";
    UITableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:cellIdentifier];
    if (cell == nil) {
        cell = [[UITableViewCell alloc] initWithStyle:UITableViewCellStyleSubtitle reuseIdentifier:cellIdentifier];
    }
    cell.textLabel.text = self.tableViewItems[indexPath.row * 3];
    cell.detailTextLabel.text = self.tableViewItems[indexPath.row * 3 + 1];
    return cell;
}

@end

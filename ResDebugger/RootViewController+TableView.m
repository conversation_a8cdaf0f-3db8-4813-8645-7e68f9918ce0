//
//  RootViewController+TableView.m
//  ResDebugger
//
//  Created by <PERSON> on 2018/9/25.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RootViewController+TableView.h"
#import "UPResTableViewCell.h"

@implementation RootViewController (TableView)
- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section
{
    return self.resInfoList.count;
}

- (nonnull UITableViewCell *)tableView:(nonnull UITableView *)tableView cellForRowAtIndexPath:(nonnull NSIndexPath *)indexPath
{
    UPResTableViewCell *cell = [tableView dequeueReusableCellWithIdentifier:@"UPResTableViewCell"];
    [cell dispose];
    [cell updateInfoWithResourceInfo:self.resInfoList[indexPath.row]];
    return cell;
}

@end

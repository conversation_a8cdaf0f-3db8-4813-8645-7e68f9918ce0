//
//  UPRequestNormalViewController.m
//  ResDebugger
//
//  Created by 景彦铭 on 2022/9/28.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPRequestNormalViewController.h"
#import "UPResourceInjection.h"
#import <MJExtension/MJExtension.h>
@interface UPRequestNormalViewController () <UITextViewDelegate, UPResourceCallback, UPResourceListener, UpResourceSelector>

/// 资源名称
@property (nonatomic, strong) IBOutlet UITextField *resNameTextField;

/// 资源类型
@property (nonatomic, strong) IBOutlet UITextField *resTypeTextField;

/// 结果
@property (nonatomic, strong) IBOutlet UITextView *resultTextView;

/// 进度
@property (nonatomic, strong) IBOutlet UIProgressView *progressView;

/// 进度label
@property (nonatomic, strong) IBOutlet UILabel *progressLabel;

/// 资源列表
@property (nonatomic, strong) NSArray<UPResourceInfo *> *resInfos;

@end

@implementation UPRequestNormalViewController

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.resultTextView.delegate = self;
    self.resNameTextField.text = @"voice_assistant";
    self.resTypeTextField.text = @"flutter";
    //        self.resNameTextField.text = @"testVideoDown";
    //        self.resTypeTextField.text = @"video";
}

- (BOOL)textViewShouldBeginEditing:(UITextView *)textView
{
    return NO;
}

- (UPResourceInfo *)selectFrom:(NSArray<UPResourceInfo *> *)infoList
{
    return infoList.firstObject;
}

- (IBAction)searchAction:(id)sender
{
    if (self.resNameTextField.text.length == 0 || self.resTypeTextField.text.length == 0) {
        return;
    }
    UPResourceInfo *info = [[UPResourceInjection getInstance].resourceManager getCommonResource:_resNameTextField.text type:[UPResourceInfo typeValueOfString:_resTypeTextField.text] selector:self callback:self listener:nil];
    self.resultTextView.text = [NSString stringWithFormat:@"%@", info.mj_keyValues];
    //    [[UPResourceInjection getInstance]
    //            .resourceManager requestNormalResList:_resNameTextField.text
    //                                             type:[UPResourceInfo typeValueOfString:_resTypeTextField.text]
    //                                        immediate:YES
    //                                       completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
    //                                         dispatch_async(dispatch_get_main_queue(), ^{
    //                                           if (error) {
    //                                               self.resultTextView.text = error.localizedDescription;
    //                                               return;
    //                                           }
    //                                           NSMutableString *stringM = [[NSMutableString alloc] init];
    //                                           [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
    //                                             [stringM appendFormat:@"%@\n", obj.description];
    //                                           }];
    //                                           self.resultTextView.text = stringM.copy;
    //                                           self.resInfos = infoList;
    //                                         });
    //                                       }];
}

- (IBAction)installAction:(id)sender
{
    if (self.resInfos.count == 0) {
        return;
    }
    [[UPResourceInjection getInstance].resourceManager install:self.resInfos.firstObject callback:self listener:self];
}

- (IBAction)getLatestInstalledAction:(id)sender
{
    if (self.resNameTextField.text.length == 0 || self.resTypeTextField.text.length == 0) {
        return;
    }
    UPResourceInfo *info = [[UPResourceInjection getInstance].resourceManager getLatestInstalledInfo:self.resNameTextField.text type:[UPResourceInfo typeValueOfString:_resTypeTextField.text]];
    self.resultTextView.text = [NSString stringWithFormat:@"%@", info.mj_keyValues];
}

- (IBAction)getLocalResource:(id)sender
{
    if (self.resNameTextField.text.length == 0 || self.resTypeTextField.text.length == 0) {
        return;
    }
    UPResourceInfo *info = [[UPResourceInjection getInstance].resourceManager getLatestInfoByName:self.resNameTextField.text type:[UPResourceInfo typeValueOfString:_resTypeTextField.text]];
    self.resultTextView.text = [NSString stringWithFormat:@"%@", info.mj_keyValues];
}

- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
    self.progressLabel.text = [NSString stringWithFormat:@"%zd%%", progress];
    self.progressView.progress = progress / 100.0;
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    dispatch_async(dispatch_get_main_queue(), ^{
      self.resultTextView.text = [self.resultTextView.text stringByAppendingFormat:@"资源：%@@%@ 安装%@, 文件目录：%@", info.name, info.version, success ? @"成功" : @"失败", info.path];
    });
}


@end

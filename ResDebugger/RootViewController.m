//
//  RootViewController.m
//  ResDebugger
//
//  Created by <PERSON> on 2018/9/12.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RootViewController.h"
#import <UPLog/UPLog.h>
#import "UPResourceManager.h"
#import <SVProgressHUD/SVProgressHUD.h>
#import "UPResourceFilter.h"
#import "UPResourceInjection.h"
#import "UPResourceDownloaderHelper.h"
#import "UPResourceConfig.h"
#import "UpPreloadResourceInfo.h"
#import <uplog/UPLogger.h>
#import <uplog/UPLogUpload.h>
#import <upnetwork/UPNetwork.h>
#import "UPResourceTrackerIMP.h"
#import <uplog/UPLogInjection.h>
@interface RootViewController () <UPResourceFilter, UPResourceListCallback, UPResourceCallback, UPResCacheCleanCallback>
@property (nonatomic, assign) NSUInteger resType;
@property (nonatomic, assign) NSUInteger resState;
/**
 <#xxx#>
 **/
@property (nonatomic, strong) UPResourceManager *manager;
- (void)initializeAppInfo;
- (void)updateCacheResourcesListInfo;
- (void)updateDeviceConfigFileInfo;
- (void)reloadTableView;
- (void)resignAllTextFields;
- (void)optimizedButtonStyle;
@end

@implementation RootViewController
#pragma mark - UPResCacheCleanCallback
- (void)didFinishCacheCleaningWithOptions:(NSDictionary *)options
{
    [SVProgressHUD showSuccessWithStatus:@"删除成功!"];
}

- (void)resourceCacheDidFailCleanWithError:(NSError *)error
{
    [SVProgressHUD showErrorWithStatus:@"删除失败"];
}

- (BOOL)accept:(UPResourceInfo *)info
{

    if (self.resType == 1) {

        if (info.type == UPResourceTypeDeviceConfig || info.type == UPResourceTypeConfigApp) {

            if (self.resState == 0) {

                return YES;
            }
            else {
                return info.active;
            }
        }
    }
    else if (self.resType == 0) {

        if (info.type != UPResourceTypeDeviceConfig && info.type == UPResourceTypeConfigApp) {

            if (self.resState == 0 && [info.name isEqualToString:@"DWCW4B171U1"]) {

                return YES;
            }
            else {
                return info.active;
            }
        }
    }
    else if (self.resType == 3) {

        if (self.resState == 0) {

            return YES;
        }
        else {
            return info.active;
        }
    }
    return NO;
}
#pragma mark - Non-Public Methods
- (void)initializeAppInfo
{
    NSMutableDictionary *modulesMap = @{
        @"UPLogDefaultLogger" : @"未在白名单内的库使用的默认输出器",
        @"UPResource" : @"资源管理库",
        @"UPUserDomain" : @"用户库",
        @"UPUserdomain" : @"用户库",
        @"UPLog" : @"日志库",
        @"UPNetwork" : @"网络库",
        @"UPPageTrace" : @"打点库",
        @"UplusKit" : @"初始化库",
        @"UPStorage" : @"通用存储库",
        @"UPPush" : @"推送库",
        @"UpTrace" : @"时长埋点",
        @"UpPlugins" : @"插件库",
        @"UpResourcePlugin" : @"资源插件库",
        @"UpFamilyPlugin" : @"家庭插件库",
        @"UPHttpPlugin" : @"网络插件库",
        @"UpUserPlugin" : @"用户插件库",
        @"UpPluginNetworkImp" : @"网络插件库",
        @"NebulaConfigure" : @"Nebula库",
        @"Bluetooth" : @"Nebula库",
        @"UPNebula" : @"Nebula库",
        @"UpNebula" : @"Nebula库",
        @"MPAAS_JSAPI_DEVICE_LogPrefix" : @"Nebula库",
        @"AnalyticsModule" : @"主线库",
        @"iRateModule" : @"主线库",
        @"Sign" : @"主线库",
        @"FakeSplashScreen-------" : @"主线库/UplusBase库",
        @"SingleAccessService" : @"主线库",
        @"MainBox" : @"主线库",
        @"mainbox" : @"主线库",
        @"UpScan" : @"扫一扫",
        @"UpSharePlugin" : @"分享插件",
        @"UpUmengPlugin" : @"友盟插件",
        @"FlutterPluginLog" : @"Flutter日志",
        @"flutter-runtime" : @"Flutter 动态化",
        @"KmmStorage" : @"KmmStorage日志"
    }.mutableCopy;
    UPLogEnvironment environment = UPLogEnvironmentAcceptance;
    UPLogUpload *kLogUpload = [[UPLogUpload alloc] init];
    kLogUpload.appID = @"MB-UZHSH-0001";
    kLogUpload.clientID = [UPNetworkSettings sharedSettings].clientID;
    kLogUpload.upmAppID = @"smarthome";
    UPLogInjection *kUPLogInjection =
        [UPLogInjection initInjection:kLogUpload
                          moduleNames:modulesMap];
    [UPLogConfig sharedInstance].mEnvirenment = environment;
    [UPLogConfig sharedInstance].mPlatform = UPLogPlatformChina;
    [[UPLogConfig sharedInstance] enablePrivacyAgreement:YES];
    [kUPLogInjection.upLog setMaxLogsPerSecond:2400];
    [kUPLogInjection.upLog setLoglevel:UPLogLevelDebug];
    [UPNetworkSettings sharedSettings].appVersion = [[[NSBundle mainBundle] infoDictionary] objectForKey:@"CFBundleShortVersionString"];

    //    [UPResourceConfig shareInstance].appPlatform = UPResourceAppPlatformSoutheasAsia;
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        [UPNetworkSettings sharedSettings].appID = @"MB-SHEYJDNYB-0000";
        [UPNetworkSettings sharedSettings].appKey = @"5959a2a4679f7990ba3cc557daa53986";
        [UPNetworkSettings sharedSettings].appVersion = @"2.0.0";
    }
    else {
        [UPNetworkSettings sharedSettings].appID = @"MB-UZHSH-0001";
        [UPNetworkSettings sharedSettings].appKey = @"5dfca8714eb26e3a776e58a8273c8752";
    }

    [UPResourceConfig shareInstance].requestEnv = UPRequestEnvTest;
    [UPResourceConfig shareInstance].appID = [UPNetworkSettings sharedSettings].appID;
    [UPResourceConfig shareInstance].appKey = [UPNetworkSettings sharedSettings].appKey;
    [UPResourceConfig shareInstance].appVersion = [UPNetworkSettings sharedSettings].appVersion;
    [UPResourceConfig shareInstance].clientID = [UPNetworkSettings sharedSettings].clientID;
    [SVProgressHUD setDefaultStyle:SVProgressHUDStyleDark];
    [SVProgressHUD setMinimumDismissTimeInterval:1.0];
    [UPResourceInjection initializeWithTestModeOn:YES];
    self.manager = [UPResourceInjection getInstance].resourceManager;
    //    self.manager.tracker = [[UPResourceTrackerIMP alloc] init];
    self.manager.downloadPolicy = TYPE_NONE;
    NSLog(@"----开始同步预置");
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"apphome" type:UPResourceTypeMPAAS];
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"appmine" type:UPResourceTypeMPAAS];
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"EShop" type:UPResourceTypeMPAAS];
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"usercenter" type:UPResourceTypeAPICloud];
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"wzService" type:UPResourceTypeAPICloud];
    //    [[UPResourceInjection getInstance] syncPresetResPkg:@"configPoints" type:UPResourceTypeConfigApp];

    [[UPResourceInjection getInstance].resourceManager setGifTypeCallback:self.resourceDownloadGif];
    NSLog(@"----结束同步预置");
    [self.manager cleanUselessResource:1];
    unsigned long long size = [self.manager getLocalDataSize];
    NSLog(@"同步获取资源包大小%lld", size);
    [self.manager getLocalDataSizeAsync:^(unsigned long long size, NSError *error) {
      NSLog(@"异步获取资源大小%lld", size);
    }];
    NSLog(@"----开始异步预置");
    [[UPResourceInjection getInstance] presetResPkg:self];
    NSLog(@"----结束异步预置");
}
- (UPResourceDownloadGifType (^)(void))resourceDownloadGif
{
    return ^UPResourceDownloadGifType {

      return UPRXiaoYouGif;
    };
}
#pragma mark - FunctionsF
- (void)updateCacheResourcesListInfo
{
    [SVProgressHUD showInfoWithStatus:@"开始更新..."];
    [self.manager updateNormalResList:@"EShop1"
                                 type:UPResourceTypeMPAAS
                           completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {

                             if (error) {
                                 NSString *errMsg = [NSString stringWithFormat:@"资源包列表更新失败！error:%@", error];
                                 UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
                                 [SVProgressHUD showErrorWithStatus:errMsg];
                                 return;
                             }
                             [SVProgressHUD showSuccessWithStatus:@"更新成功!"];
                             UPLogDebug(@"UPResource", @"%s[%d]资源包列表更新成功！", __PRETTY_FUNCTION__, __LINE__);

                           }];
}

- (void)updateDeviceConfigFileInfo
{
    [SVProgressHUD showInfoWithStatus:@"开始更新..."];

    UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:UPResourceTypeLua];
    condition.typeId = @"201c80c70c50031c1201c040c2427100000005c20f206d21185e1676a9b52740";
    condition.model = @"智能摄像头HCC-18B30-U1";
    condition.prodNo = @"NC01N2M00";
    condition.appVersion = [UPResourceConfig shareInstance].appVersion;
    condition.deviceType = @"1200100a";

    [self.manager updateDeviceResList:condition
                           completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
                             if (error) {
                                 NSString *errMsg = [NSString stringWithFormat:@"配置文件更新失败！error:%@", error];
                                 UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
                                 [SVProgressHUD showErrorWithStatus:errMsg];
                                 return;
                             }
        [self.manager install:infoList.firstObject callback:self listener:nil];
                             [SVProgressHUD showSuccessWithStatus:@"更新成功!"];
                             UPLogDebug(@"UPResource", @"%s[%d]设备配置文件更新成功！", __PRETTY_FUNCTION__, __LINE__);
                           }];
}

- (void)reloadTableView
{
    self.resInfoList = [self.manager getEntireList:self];
    [self.tableView reloadData];
}

- (void)resignAllTextFields
{
    [self.textFieldProNo resignFirstResponder];
    [self.textFiledModel resignFirstResponder];
    [self.textFieldTypeID resignFirstResponder];
}

- (void)optimizedButtonStyle
{
    CGFloat radius = 5.0;
    CGColorRef borderColor = self.buttonUpdateH5Package.tintColor.CGColor;
    CGFloat borderWidth = 1.0;
    self.buttonUpdateH5Package.layer.cornerRadius = radius;
    self.buttonUpdateH5Package.layer.borderColor = borderColor;
    self.buttonUpdateH5Package.layer.borderWidth = borderWidth;

    self.buttonUpdateDeviceConfig.layer.cornerRadius = radius;
    self.buttonUpdateDeviceConfig.layer.borderColor = borderColor;
    self.buttonUpdateDeviceConfig.layer.borderWidth = borderWidth;
}

#pragma mark - Public Methods
- (void)viewDidLoad
{
    [super viewDidLoad];
    // Do any additional setup after loading the view
    [self initializeAppInfo];

    self.queryView.hidden = YES;
    [self optimizedButtonStyle];
}

- (IBAction)deleteAction:(UIButton *)sender
{
    [self.manager cleanLocalResourceCache:self];
}


- (void)touchesBegan:(NSSet<UITouch *> *)touches withEvent:(UIEvent *)event
{
    [self.manager autoUpgradeLocalResouces];
    [self.view endEditing:YES];
}

- (IBAction)onTestSwitchValueChnaged:(UISwitch *)sender
{
    self.manager.environment = sender.on ? UPResEnvironmentTest : UPResEnvironmentProduction;
    [self resignAllTextFields];
}

- (IBAction)segementedControlDidChanged:(UISegmentedControl *)sender
{
    self.updateView.hidden = sender.selectedSegmentIndex == 1;
    self.queryView.hidden = sender.selectedSegmentIndex == 0;
    [self resignAllTextFields];
    if (!self.queryView.hidden) {
        [self reloadTableView];
    }
}

- (IBAction)onUpdateH5PackageList:(UIButton *)sender
{
    [self resignAllTextFields];
    [self updateCacheResourcesListInfo];
}

- (IBAction)onUpdateDeviceConfig:(UIButton *)sender
{
    [self updateDeviceConfigFileInfo];
}

- (IBAction)resourceTypeChanged:(UISegmentedControl *)sender
{
    self.resType = sender.selectedSegmentIndex;
    [self reloadTableView];
}

- (IBAction)resourceStateChanged:(UISegmentedControl *)sender
{
    self.resState = sender.selectedSegmentIndex;
    [self reloadTableView];
}

- (IBAction)preLoadAction:(id)sender
{

    NSArray *array = @[ @"mpaastest", @"memberPoints", @"123", @"333", @"customized", @"sceneStore", @"messageCenter", @"wisdomHomeStrategy", @"familymanage", @"apphome", @"usercenter", @"appmine", @"xiaoYVoice", @"444", @"EShop", @"wzService" ];
    NSMutableArray *list = [NSMutableArray array];
    for (NSString *name in array) {
        UpPreloadResourceInfo *info = [[UpPreloadResourceInfo alloc] init];
        info.name = name;
        if ([name isEqualToString:@"mpaastest"]) {
            info.type = UPResourceTypeMPAAS;
        }
        else {
            info.type = UPResourceTypeAPICloud;
        }
        [list addObject:info];
    }

    [self.manager preloadNormalResList:list callback:self];
}

- (IBAction)batchUninstall:(id)sender
{
    NSArray *array = @[ @"mpaastest", @"memberPoints", @"444", @"customized" ];
    NSMutableArray *list = [NSMutableArray array];
    for (NSString *name in array) {
        UpPreloadResourceInfo *info = [[UpPreloadResourceInfo alloc] init];
        info.name = name;
        if ([name isEqualToString:@"mpaastest"]) {
            info.type = UPResourceTypeMPAAS;
        }
        else {
            info.type = UPResourceTypeAPICloud;
        }
        [list addObject:info];
    }
    [self.manager autoUpgradeCurrentResources:list callback:self];
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    /**
           baseDir =  /var/mobile/Containers/Data/Application/C26667C1-B9E9-4AFA-BE9A-B0E216E79031/Documents/uplus-resource-test/Lua
         path = /var/mobile/Containers/Data/Application/C26667C1-B9E9-4AFA-BE9A-B0E216E79031/Documents/uplus-resource-test/Lua/<EMAIL>
            name = camera@1.0.0
           name = camera@1.0.1     */
    NSString *str= [self.manager getPathByType:UPResourceTypeLua];
    NSLog(@"单资源安装---%@---info%@", message, info);
}
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
}
- (void)onResult:(BOOL)success message:(NSString *)message infoList:(NSArray<UPResourceInfo *> *)infoList
{
    NSLog(@"---%@---info%@", message, infoList);
}
- (IBAction)openDownloaderControllerAction:(id)sender
{
    [[NSUserDefaults standardUserDefaults] setObject:@"tm" forKey:@"app_current_lan_key"];
    [UPResourceConfig shareInstance].appPlatform = UPResourceAppPlatformSoutheasAsia;
    UPResourceDownloaderHelper *helper = [[UPResourceDownloaderHelper alloc] initResourceDownloaderHelper:@"EShop" type:UPResourceTypeMPAAS];
    [helper startDownloader:^(NSDictionary *_Nonnull data){

    }];
}
- (IBAction)UPDateMpaasRes:(UIButton *)sender
{

    [SVProgressHUD showInfoWithStatus:@"开始更新..."];
    [self.manager updateNormalResList:@"smarthome"
                                 type:UPResourceTypeMPAAS
                           completion:^(NSArray<UPResourceInfo *> *infoList, NSError *error) {

                             if (error) {
                                 NSString *errMsg = [NSString stringWithFormat:@"资源包列表更新失败！error:%@", error];
                                 UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
                                 [SVProgressHUD showErrorWithStatus:errMsg];
                                 return;
                             }
                             [SVProgressHUD showSuccessWithStatus:@"更新成功!"];
                             UPLogDebug(@"UPResource", @"%s[%d]资源包列表更新成功！", __PRETTY_FUNCTION__, __LINE__);

                           }];
}

- (IBAction)insertRes:(UIButton *)sender
{
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.name = @"usercenter";
    info.type = UPResourceTypeAPICloud;
    info.hashStr = @"25234262fe06da35ea8ff24ed5a17d71";
    info.version = @"1.6.28";
    info.link = @"https://oss-zjrs.haier.net/resource/confFile/2020060517013206216543.zip";
    [self.manager asyncInsertAndInstallResInfo:info isDevRes:NO deviceNetType:@"netdevice" callback:self];
    /*
     deviceTypeIndex = "";
                    id = 1511;
                    md5 = 25234262fe06da35ea8ff24ed5a17d71;
                    model = "";
                    name = usercenter;
                    prodNo = "";
                    resType = apicloud;
                    resUrl = "https://oss-zjrs.haier.net/resource/confFile/2020060517013206216543.zip";
                    resVersion = "1.6.28";
                    typeId = "";
     **/
}
@end

//
//  UPResourceTracker.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/8.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceTrackerIMP.h"
#import <UPLog/UPLog.h>
static NSString *const kUplusResourceInstalled = @"uplusResourceInstalled";
static NSString *const kUplusResourceABTestDownload = @"uplusResourceABTestDownload";
@implementation UPResourceTrackerIMP

- (void)reportInstallResult:(UPResourceReportInfo *)reportInfo
{
    NSMutableDictionary *par = [self getCustomVariableWith:reportInfo];
    UPLogInfo(@"UPResource", @"%s%d上报到达率参数为%@", __func__, __LINE__, par);
}

- (void)reportABTestTrack:(nonnull UPResourceReportInfo *)reportInfo
{
    NSMutableDictionary *par = [self getCustomVariableWith:reportInfo];
    UPLogInfo(@"UPResource", @"%s%d上报到达率参数为%@", __func__, __LINE__, par);
}

- (NSMutableDictionary *)getCustomVariableWith:(UPResourceReportInfo *)reportInfo
{
    NSMutableDictionary *par = [NSMutableDictionary dictionary];
    par[@"res_name"] = reportInfo.res_name;
    par[@"res_type"] = reportInfo.res_type;
    par[@"res_version"] = reportInfo.res_version;
    par[@"is_preset"] = reportInfo.is_preset;
    par[@"user_id"] = reportInfo.user_id;
    par[@"client_id"] = reportInfo.client_id;
    par[@"app_version"] = reportInfo.app_version;
    par[@"installed_time"] = reportInfo.installed_time;
    par[@"is_gray"] = reportInfo.is_gray;
    return par;
}

@end

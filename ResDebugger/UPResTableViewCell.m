//
//  UPResTableViewCell.m
//  ResDebugger
//
//  Created by <PERSON> on 2018/9/25.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResTableViewCell.h"
#import "UPResourceManager.h"
#import "UPResourceCallback.h"
#import "UPResourceListener.h"
#import "UPResCommonFunctions.h"
#import "SVProgressHUD.h"
#import "UPResourceTask.h"
#import <UPLog/UPLog.h>
#import "UPResourceDownloaderController.h"
#import "AppDelegate.h"
#import "UPResourceInjection.h"
@interface UPResTableViewCell () <UPResourceCallback, UPResourceListener>
@property (nonatomic, strong) UPResourceInfo *info;
@property (nonatomic, strong) NSString *taskID;
- (void)optimizedButtons;
- (void)updateStateOfButtonsByInfo:(UPResourceInfo *)info;
@end

@implementation UPResTableViewCell
#pragma mark - UPResourceCallback
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
    [UPResourceInjection initializeWithTestModeOn:NO];
    UPResourceManager *manager = [UPResourceInjection getInstance].resourceManager;
    [manager cancel:task.taskId];
    UPLogDebug(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, action);
}

- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{

    if (success) {
        [SVProgressHUD showSuccessWithStatus:message];
    }
    else {
        [SVProgressHUD showErrorWithStatus:message];
    }
    self.progressView.hidden = YES;
    self.progressView.progress = 0.0;
    [self updateInfoWithResourceInfo:info];
}

#pragma mark - UPResourceListener
- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
    self.progressView.progress = progress / 100.0;
    self.installButton.enabled = NO;
    self.updateButton.enabled = NO;
    self.deleteButton.enabled = NO;
    self.cancelButton.enabled = YES;
}

#pragma mark - Non-Public Methods
- (void)updateStateOfButtonsByInfo:(UPResourceInfo *)info
{
    if (UPRes_isEmptyString(info.path)) {
        self.installButton.enabled = YES;
        self.updateButton.enabled = NO;
        self.cancelButton.enabled = NO;
        self.deleteButton.enabled = NO;
    }
    else {
        self.installButton.enabled = NO;
        self.updateButton.enabled = YES;
        self.cancelButton.enabled = NO;
        self.deleteButton.enabled = YES;
    }
}

#pragma mark - Public Methods
- (void)awakeFromNib
{
    [super awakeFromNib];
    // Initialization code
}

- (void)setSelected:(BOOL)selected animated:(BOOL)animated
{
    [super setSelected:selected animated:animated];

    // Configure the view for the selected state
}

- (IBAction)onActionButtons:(UIButton *)sender
{
    [UPResourceInjection initializeWithTestModeOn:NO];
    UPResourceManager *manager = [UPResourceInjection getInstance].resourceManager;
    if (sender == self.installButton) {

        self.taskID = [manager install:self.info callback:self listener:self];
        self.progressView.hidden = NO;
    }
    else if (sender == self.updateButton) {

        self.progressView.hidden = NO;
    }
    else if (sender == self.cancelButton) {
        [manager cancel:self.taskID];
    }
    else if (sender == self.deleteButton) {
        self.taskID = [manager uninstall:self.info callback:self];
    }
}

- (void)optimizedButtons
{
    CGFloat radius = 5.0;
    CGColorRef borderColor = self.installButton.tintColor.CGColor;
    CGFloat borderWidth = 1.0;
    self.installButton.layer.cornerRadius = radius;
    self.installButton.layer.borderColor = borderColor;
    self.installButton.layer.borderWidth = borderWidth;

    self.updateButton.layer.cornerRadius = radius;
    self.updateButton.layer.borderColor = borderColor;
    self.updateButton.layer.borderWidth = borderWidth;

    self.cancelButton.layer.cornerRadius = radius;
    self.cancelButton.layer.borderColor = borderColor;
    self.cancelButton.layer.borderWidth = borderWidth;

    self.deleteButton.layer.cornerRadius = radius;
    self.deleteButton.layer.borderColor = borderColor;
    self.deleteButton.layer.borderWidth = borderWidth;
}

- (void)updateInfoWithResourceInfo:(UPResourceInfo *)info
{
    self.resTitle.text = info.name;
    self.resVersion.text = info.version;
    self.resType.text = [UPResourceInfo stringValueOfResourceType:info.type];
    self.progressView.hidden = YES;
    self.info = info;
    [self optimizedButtons];
    [self updateStateOfButtonsByInfo:info];
}

- (void)dispose
{
    self.info = nil;
    self.taskID = nil;
}

@end

//
//  FakeCacheCleanCallback.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2020/3/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CacheCleanCallback.h"

@implementation CacheCleanCallback
#pragma mark - UPResCacheCleanCallback
- (void)didFinishCacheCleaningWithOptions:(NSDictionary *)options
{
    self.result = TestCallbackResultSuccessful;
}

- (void)resourceCacheDidFailCleanWithError:(NSError *)error
{
    self.result = TestCallbackResultFailed;
}

@end

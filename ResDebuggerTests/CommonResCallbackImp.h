//
//  CommonResCallbackImp.h
//  ResDebuggerTests
//
//  Created by luxu on 2022/11/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XCTest/XCTestExpectation.h>
#import "UPResourceInfo.h"
#import "UPResourceCallback.h"
#import "UPResourceListCallback.h"
NS_ASSUME_NONNULL_BEGIN

@interface UPResourceInfo (UnitTestPreloadResult)
@property (nonatomic, assign) BOOL success;
@end

@interface CommonResCallbackImp : NSObject <UPResourceCallback, UPResourceListCallback>
@property (nonatomic, strong, readonly) NSString *identifier;
@property (nonatomic, strong, readonly) XCTestExpectation *expectation;
@property (nonatomic, assign, readonly) BOOL result;
@property (nonatomic, strong, readonly) NSString *message;
/**
 安装结束后返回的资源列表
 **/
@property (nonatomic, strong, readonly) NSArray<UPResourceInfo *> *infoList;
/**
 安装结束后返回的资源
 **/
@property (nonatomic, strong, readonly) UPResourceInfo *info;
/**
 * @brief 批量预加载资源的回调结果。
 */
@property (nonatomic, strong, readonly) NSMutableArray<UPResourceInfo *> *preloadResInfoList;

/**
 * identifier callback的唯一性标识
 */
- (instancetype)initWithIdentifier:(NSString *)identifier;

@end

NS_ASSUME_NONNULL_END

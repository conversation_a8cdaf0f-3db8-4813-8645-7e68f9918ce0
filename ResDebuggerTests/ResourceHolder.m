//
//  ResourceHolder.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/15.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ResourceHolder.h"
#import "UPResourceManager.h"
#import "StepUtils.h"
#import "UPResourceConfig.h"
#import <OCMock/OCMock.h>
#import "UPResourceDatabaseImpl.h"
#import "UPResourceFileSystem.h"
#import "UPResourceTimeSystem.h"
#import "UPConnectionMonitor.h"
#import "UPDownloaderDelegate.h"
#import "UPOmsResDataSource.h"

@interface ResourceHolder ()


@end

@implementation ResourceHolder
+ (ResourceHolder *)sharedInstance
{
    static ResourceHolder *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
    });
    return instance;
}

- (void)initResourceManagerWithTableDataListParamters:(NSArray<NSString *> *)params
{
    if ([params count] < 9) {
        return;
    }
    NSString *appVersion = nil;
    if (![params[0] isEqualToString:@"是"]) {
        appVersion = @"5.5.0";
    }
    NSString *resourceRootPath = nil;
    if (![params[1] isEqualToString:@"是"]) {
        NSString *path = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        resourceRootPath = path;
    }
    _resourceDataSource = nil;
    if (![params[2] isEqualToString:@"是"]) {
        _resourceDataSource = OCMClassMock([UPOmsResDataSource class]);
    }
    _databaseDelegate = nil;
    if (![params[3] isEqualToString:@"是"]) {
        _databaseDelegate = OCMClassMock([UPResourceDatabaseImpl class]);
    }
    _fileDelegate = nil;
    if (![params[4] isEqualToString:@"是"]) {
        _fileDelegate = OCMClassMock([UPResourceFileSystem class]);
    }
    _timeDelegate = nil;
    if (![params[5] isEqualToString:@"是"]) {
        _timeDelegate = OCMClassMock([UPResourceTimeSystem class]);
    }
    _connectionDelegate = nil;
    if (![params[6] isEqualToString:@"是"]) {
        _connectionDelegate = OCMClassMock([UPConnectionMonitor class]);
    }
    _downloadDelegate = nil;
    if (![params[7] isEqualToString:@"是"]) {
        _downloadDelegate = OCMClassMock([UPDownloaderDelegate class]);
    }
    _resourceCleaner = nil;
    if (![params[8] isEqualToString:@"是"]) {
        _resourceCleaner = OCMProtocolMock(@protocol(UpResourceCleaner));
    }
    [UPResourceConfig shareInstance].appVersion = appVersion;
    _resourceManager = [[UPResourceManager alloc] initResourceManagerWithAppVersion:appVersion resRootPath:resourceRootPath dataSource:_resourceDataSource databaseDelegate:_databaseDelegate fileDelegate:_fileDelegate timeDelegate:_timeDelegate connectionDelegate:_connectionDelegate downloadDelegate:_downloadDelegate cleaner:_resourceCleaner];
}

- (void)initResourceManagerWithAppVersion:(NSString *)appVersion resRootPath:(NSString *)resRootPath simulatedCleaner:(BOOL)simulated;
{
    if (appVersion == nil || resRootPath == nil) {
        return;
    }
    [UPResourceConfig shareInstance].appVersion = appVersion;
    _rootPath = resRootPath;
    _databaseDelegate = OCMClassMock([UPResourceDatabaseImpl class]);
    _fileDelegate = OCMClassMock([UPResourceFileSystem class]);
    _timeDelegate = OCMClassMock([UPResourceTimeSystem class]);
    _connectionDelegate = OCMClassMock([UPConnectionMonitor class]);
    UPDownloaderDelegate *download = [[UPDownloaderDelegate alloc] init];
    _downloadDelegate = OCMPartialMock(download);
    _resourceDataSource = OCMClassMock([UPOmsResDataSource class]);
    _presetFileLoader = OCMProtocolMock(@protocol(UPPresetFileLoader));
    _resourceCleaner = nil;
    if (simulated) {
        _resourceCleaner = OCMProtocolMock(@protocol(UpResourceCleaner));
    }
    _resourceManager = [[UPResourceManager alloc] initResourceManagerWithAppVersion:appVersion resRootPath:resRootPath dataSource:_resourceDataSource databaseDelegate:_databaseDelegate fileDelegate:_fileDelegate timeDelegate:_timeDelegate connectionDelegate:_connectionDelegate downloadDelegate:_downloadDelegate cleaner:_resourceCleaner];
    _resourceManager.scanner = _presetFileLoader;
}

- (void)disposeResourceManager
{
    _resourceManager = nil;
}

- (NSArray<UPResourceInfo *> *)convertResourceInfoList:(NSArray *)array
{
    NSMutableArray *infoList = [NSMutableArray array];
    for (NSDictionary *data in array) {
        UPResourceInfo *info = [self convertResourceInfo:data];
        [infoList addObject:info];
    }
    return infoList;
}

- (UPResourceInfo *)convertResourceInfo:(NSDictionary *)data
{
    if (data == nil || ![data isKindOfClass:[NSDictionary class]]) {
        return nil;
    }
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.fileDelegate = _fileDelegate;
    info.timeDelegate = _timeDelegate;
    info.reporterDelegate = _resourceManager.reporterDelegate;
    info.itemId = ((NSString *)[data objectForKey:@"Id"]).integerValue;
    info.type = [UPResourceInfo typeValueOfString:[data objectForKey:@"Type"]];
    info.name = [data objectForKey:@"Name"];
    info.version = [data objectForKey:@"Version"];
    info.link = [data objectForKey:@"Link"];
    info.hashStr = [data objectForKey:@"MD5"];
    info.model = [data objectForKey:@"Model"];
    info.typeId = [data objectForKey:@"TypeId"];
    info.prodNo = [data objectForKey:@"ProdNo"];
    NSString *path = [data objectForKey:@"Path"];
    info.path = [path isEqualToString:@"null"] ? @"" : path;
    info.deviceTypeIndex = [data objectForKey:@"TypeCode"];
    info.isServerLatest = [[data objectForKey:@"isServerLatest"] isEqualToString:@"true"];
    info.createTime = [[data objectForKey:@"CreateTime"] integerValue] / 1000;
    info.updateTime = [[data objectForKey:@"UpdateTime"] integerValue] / 1000;
    info.preset = [[data objectForKey:@"isPreset"] isEqualToString:@"是"];
    info.resourceType = @"2";
    NSString *priority = [data objectForKey:@"Priority"];
    info.priority = UPResourceDownLoadPriorityLow;
    if ([priority isEqualToString:@"height"]) {
        info.priority = UPResourceDownLoadPriorityHeight;
    }
    else if ([priority isEqualToString:@"middle"]) {
        info.priority = UPResourceDownLoadPriorityMid;
    }
    info.resStatus = [[data objectForKey:@"ResStatus"] integerValue];
    return info;
}

- (void)initFilter:(NSString *)name type:(NSString *)type
{
    if (name == nil || type == nil) {
        return;
    }
    _filterHelper = [UPResourceHelper createNameAndTypeFilterWithName:name type:type];
}

- (NSArray<UPResourceQuery *> *)convertQueryList:(NSArray *)array
{
    NSMutableArray *queryList = [NSMutableArray array];
    for (NSArray *data in array) {
        NSString *queryStr = data[0];
        UPResourceQuery *query = combineQuery(queryStr);
        if (query) {
            [queryList addObject:query];
        }
    }
    return queryList;
}

@end

//
//  FakeCacheCleanCallback.h
//  ResDebuggerTests
//
//  Created by <PERSON> on 2020/3/4.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResCacheCleanCallback.h"

typedef NS_ENUM(NSUInteger, TestCallbackResult) {
    TestCallbackResultUnknown,
    TestCallbackResultSuccessful,
    TestCallbackResultFailed
};

NS_ASSUME_NONNULL_BEGIN

@interface CacheCleanCallback : NSObject <UPResCacheCleanCallback>
@property (nonatomic, copy) NSString *identifier;
@property (nonatomic, assign) TestCallbackResult result;

@end

NS_ASSUME_NONNULL_END

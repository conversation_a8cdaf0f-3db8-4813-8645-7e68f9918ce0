//
//  ResourceHolder.h
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/15.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceManager.h"
#import "UPResourceHelper.h"
#import "UPPresetFileLoader.h"

NS_ASSUME_NONNULL_BEGIN

@interface ResourceHolder : NSObject
@property (nonatomic, readonly, strong) UPResourceManager *resourceManager;
@property (nonatomic, strong) id<UPPresetFileLoader> presetFileLoader;
@property (nonatomic, strong) id<UPResourceFilter> filterHelper;
@property (nonatomic, copy, readonly) NSString *rootPath;

@property (nonatomic, strong) id<UPResourceDataSource> resourceDataSource;
@property (nonatomic, strong) id<UPDatabaseDelegate> databaseDelegate;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, strong) id<UPConnectionDelegate> connectionDelegate;
@property (nonatomic, strong) id<UPDownloadDelegate> downloadDelegate;
@property (nonatomic, strong) id<UpResourceCleaner> resourceCleaner;

+ (ResourceHolder *)sharedInstance;
- (void)initResourceManagerWithTableDataListParamters:(NSArray<NSString *> *)params;
- (void)initResourceManagerWithAppVersion:(NSString *)appVersion resRootPath:(NSString *)resRootPath simulatedCleaner:(BOOL)simulated;
- (void)disposeResourceManager;
- (NSArray<UPResourceInfo *> *)convertResourceInfoList:(NSArray *)array;
- (void)initFilter:(NSString *)name type:(NSString *)type;
- (NSArray<UPResourceQuery *> *)convertQueryList:(NSArray *)array;
@end

NS_ASSUME_NONNULL_END

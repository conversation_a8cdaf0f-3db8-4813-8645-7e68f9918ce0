//
//  StepUtils.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepUtils.h"
#import "UPResourceInfo.h"
#import "UPResourceQuery.h"
#import "UPResourceCondition.h"
#import "UPRLoadedReportInfo.h"

#pragma mark - Public Methods
BOOL isValidString(NSString *str)
{
    return [str isKindOfClass:[NSString class]] && str.length > 0;
}

BOOL isValidStringEqual(NSString *str, NSString *str2)
{
    return isValidString(str) && isValidString(str2) && [str isEqualToString:str2];
}

BOOL isEqualQuery(UPResourceQuery *query, UPResourceQuery *query2)
{
    if ([query2.conditionStr containsString:@"/"]) {
        query2.conditionStr = [query2.conditionStr stringByReplacingOccurrencesOfString:@"/" withString:@","];
    }
    if ([query.conditionStr containsString:@"/"]) {
        query.conditionStr = [query.conditionStr stringByReplacingOccurrencesOfString:@"/" withString:@","];
    }
    return isValidStringEqual(query.fromFunc, query2.fromFunc) && isValidStringEqual(query.appVersion, query2.appVersion) && isValidStringEqual(query.conditionStr, query2.conditionStr);
}
BOOL isEqualCondition(UPResourceCondition *condition, UPResourceCondition *condition2)
{
    return isValidStringEqual(condition.fromFunc, condition2.fromFunc) && isValidStringEqual(condition.appVersion, condition2.appVersion) && isValidStringEqual(condition.combine, condition2.combine);
}

BOOL isValidArray(NSArray *array)
{
    return [array isKindOfClass:[NSArray class]] && array.count > 0;
}

BOOL isStringArrayEqual(NSArray *array, NSArray *anotherArray)
{
    if (![array isKindOfClass:[NSArray class]] || ![anotherArray isKindOfClass:[NSArray class]] || array.count != anotherArray.count) {
        return NO;
    }
    BOOL bResult = YES;
    for (NSInteger index = 0; index < array.count; index++) {
        NSString *str = array[index];
        NSString *anotherStr = anotherArray[index];
        bResult = isValidStringEqual(str, anotherStr);
        if (!bResult) {
            break;
        }
    }
    return bResult;
}

BOOL isValidArrayOfInstances(NSArray *array, Class instanceClass)
{
    if (![array isKindOfClass:[NSArray class]]) {
        return NO;
    }
    BOOL bFoundNonClassInstance = NO;
    for (NSObject *object in array) {
        if (![object isKindOfClass:instanceClass]) {
            bFoundNonClassInstance = YES;
            break;
        }
    }
    return !bFoundNonClassInstance;
};

NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo)
{
    NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
    NSMutableArray *result = expect.mutableCopy;
    [result removeObjectAtIndex:0];
    return result;
}

NSArray<NSDictionary *> *jsonObjectsFromDataTable(NSDictionary *userInfo)
{
    if (!userInfo) {
        return nil;
    }
    NSArray<NSArray<NSString *> *> *arr = userInfo[@"DataTable"];
    if (arr.count < 2) {
        return nil;
    }

    NSMutableArray *arrM = [NSMutableArray array];
    NSArray *keys = arr[0];
    for (int i = 1; i < arr.count; i++) {
        NSArray *data = arr[i];
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        [data enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          dict[keys[idx]] = obj;
        }];
        [arrM addObject:dict];
    }
    return arrM.copy;
}

BOOL isEqualResource(UPResourceInfo *resource, UPResourceInfo *anotherResource)
{
    if (![resource isKindOfClass:[UPResourceInfo class]] || ![anotherResource isKindOfClass:[UPResourceInfo class]]) {
        return NO;
    }
    return isValidStringEqual(identifierOfResource(resource), identifierOfResource(anotherResource));
}


BOOL isDeviceResourceInfoEqual(UPResourceInfo *resource, UPResourceInfo *anotherResource)
{
    if (![resource isKindOfClass:[UPResourceInfo class]] || ![anotherResource isKindOfClass:[UPResourceInfo class]]) {
        return NO;
    }
    return isValidStringEqual(wholeIdentifierOfResource(resource), wholeIdentifierOfResource(anotherResource));
}

BOOL isResourcesContains(NSArray<UPResourceInfo *> *resources, UPResourceInfo *resource)
{
    if (!isValidArrayOfInstances(resources, [UPResourceInfo class])) {
        return NO;
    }
    BOOL bFound = NO;
    for (UPResourceInfo *res in resources) {
        if (isEqualResource(resource, res)) {
            bFound = YES;
            break;
        }
    }
    return bFound;
}

BOOL isResourcesConditionContains(NSArray<UPResourceCondition *> *resourceConditions, UPResourceCondition *condition)
{
    if (!isValidArrayOfInstances(resourceConditions, [UPResourceCondition class])) {
        return NO;
    }
    BOOL bFound = NO;
    for (UPResourceCondition *con in resourceConditions) {
        if (isEqualCondition(condition, con)) {
            bFound = YES;
            break;
        }
    }
    return bFound;
}

BOOL isEqualResourcesList(NSArray<UPResourceInfo *> *resources, NSArray<UPResourceInfo *> *anotherResources)
{
    if (resources == nil && anotherResources == nil) {
        return YES;
    }
    if (!isValidArrayOfInstances(resources, [UPResourceInfo class]) ||
        !isValidArrayOfInstances(anotherResources, [UPResourceInfo class]) ||
        resources.count != anotherResources.count) {
        return NO;
    }
    BOOL isEqual = YES;
    for (UPResourceInfo *resource in resources) {
        if (!isResourcesContains(anotherResources, resource)) {
            isEqual = NO;
            break;
        }
    }
    return isEqual;
}

BOOL isEqualConditionList(NSArray<UPResourceCondition *> *resourceCondition, NSArray<UPResourceCondition *> *anotherResourceCondition)
{
    if (resourceCondition == nil && anotherResourceCondition == nil) {
        return YES;
    }
    if (!isValidArrayOfInstances(resourceCondition, [UPResourceCondition class]) ||
        !isValidArrayOfInstances(anotherResourceCondition, [UPResourceCondition class]) ||
        resourceCondition.count != anotherResourceCondition.count) {
        return NO;
    }
    BOOL isEqual = YES;
    for (UPResourceCondition *condition in resourceCondition) {
        if (!isResourcesConditionContains(anotherResourceCondition, condition)) {
            isEqual = NO;
            break;
        }
    }
    return isEqual;
}

NSString *combineStrings(NSArray *strings)
{
    return combineStringsWithSeparator(strings, @"@");
}

NSString *combineStringsWithSeparator(NSArray *strings, NSString *separator)
{
    if (!isValidArrayOfInstances(strings, [NSString class])) {
        return @"";
    }
    NSMutableArray *mutableStringsArray = [strings mutableCopy];
    NSMutableString *combinedStr = [NSMutableString stringWithFormat:@"%@", mutableStringsArray.firstObject];
    [mutableStringsArray removeObject:mutableStringsArray.firstObject];
    for (NSString *str in mutableStringsArray) {
        [combinedStr appendFormat:@"%@%@", separator, str];
    }
    return combinedStr;
}

NSString *identifierOfResource(UPResourceInfo *info)
{
    NSString *type = [UPResourceInfo stringValueOfResourceType:info.type];
    NSString *name = isValidString(info.name) ? info.name : @"";
    NSString *version = isValidString(info.version) ? info.version : @"";
    return combineStrings(@[ type, name, version ]);
}

NSString *wholeIdentifierOfResource(UPResourceInfo *info)
{
    NSString *type = [UPResourceInfo stringValueOfResourceType:info.type];
    NSString *name = isValidString(info.name) ? info.name : @"";
    NSString *version = isValidString(info.version) ? info.version : @"";
    NSString *model = isValidString(info.model) ? info.model : @"";
    NSString *typeId = isValidString(info.typeId) ? info.typeId : @"";
    NSString *prodNo = isValidString(info.prodNo) ? info.prodNo : @"";
    NSString *typeCode = isValidString(info.deviceTypeIndex) ? info.deviceTypeIndex : @"";
    return combineStrings(@[ type, name, version, model, typeId, prodNo, typeCode ]);
}

UPResourceInfo *createResourceInfoFromPropertiesInfoArray(NSArray<NSString *> *propertiesInfoArray)
{
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.name = propertiesInfoArray[2];
    info.type = [UPResourceInfo typeValueOfString:propertiesInfoArray[1]];
    info.version = propertiesInfoArray[3];
    info.preset = YES;
    return info;
}

UPResourceQuery *combineQuery(NSString *queryStr)
{
    if ([queryStr isEqualToString:@"空对象"]) {
        return nil;
    }
    NSArray<NSString *> *list = [queryStr componentsSeparatedByString:@","];
    __block NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [list enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSArray<NSString *> *subList = [obj componentsSeparatedByString:@"="];
      NSString *key = subList.firstObject;
      NSString *value = subList.lastObject;
      dict[key] = value;
    }];
    UPResourceQuery *query = [[UPResourceQuery alloc] init];
    query.fromFunc = dict[@"fromFun"];
    query.appVersion = dict[@"appversion"];
    query.correlationId = dict[@"conditionId"];
    if (dict.allKeys.count == 5 || dict.allKeys.count == 4) {
        query.conditionStr = [NSString stringWithFormat:@"rt=%@|name=%@", dict[@"rt"], dict[@"name"]];
        return query;
    }
    query.conditionStr = [NSString stringWithFormat:@"rt=%@|name=%@|md=%@|ti=%@|pn=%@|tc=%@|dnt=%@", dict[@"rt"], dict[@"name"], dict[@"md"], dict[@"ti"], dict[@"pn"], dict[@"tc"], dict[@"dnt"]];
    return query;
}

NSArray<UPResourceQuery *> *getCombineQueryList(NSArray *queryArray)
{
    NSMutableArray *allQuerys = [NSMutableArray array];
    [queryArray enumerateObjectsUsingBlock:^(NSArray *_Nonnull array, NSUInteger idx, BOOL *_Nonnull stop) {
      UPResourceQuery *query = [[UPResourceQuery alloc] init];
      query.correlationId = array[1];
      query.appVersion = array[2];
      query.fromFunc = array[3];
      if (array.count > 6 && isValidString(array[6])) {
          query.conditionStr = [NSString stringWithFormat:@"rt=%@|name=%@|md=%@|ti=%@|pn=%@|tc=%@|dnt=%@", array[4], array[5], array[6], array[7], array[8], array[9], array[10]];
      }
      else {
          query.conditionStr = [NSString stringWithFormat:@"rt=%@|name=%@", array[4], array[5]];
      }
      [allQuerys addObject:query];
    }];
    return allQuerys;
}
UPResourceCondition *combineNormalCondition(NSString *conditionStr)
{
    if ([conditionStr isEqualToString:@"空对象"]) {
        return nil;
    }
    NSArray<NSString *> *list = [conditionStr componentsSeparatedByString:@","];
    __block NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [list enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSArray<NSString *> *subList = [obj componentsSeparatedByString:@"="];
      NSString *key = subList.firstObject;
      NSString *value = subList.lastObject;
      dict[key] = value;
    }];
    if (dict.allKeys.count == 2) {
        UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:dict[@"rt"]]];
        NSString *name = dict[@"name"];
        if ([name containsString:@"/"]) {
            name = [name stringByReplacingOccurrencesOfString:@"/" withString:@","];
        }
        condition.resName = name;
        return condition;
    }
    UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:dict[@"rt"]]];
    condition.appVersion = dict[@"appversion"];
    condition.fromFunc = dict[@"fromFun"];
    condition.model = dict[@"md"];
    condition.typeId = dict[@"ti"];
    condition.prodNo = dict[@"pn"];
    condition.deviceType = dict[@"tc"];
    condition.deviceNetType = dict[@"dnt"];
    return condition;
}

UPResourceCondition *combineCondition(NSString *conditionStr)
{
    if ([conditionStr isEqualToString:@"空对象"]) {
        return nil;
    }
    NSArray<NSString *> *list = [conditionStr componentsSeparatedByString:@","];
    __block NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [list enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSArray<NSString *> *subList = [obj componentsSeparatedByString:@"="];
      NSString *key = subList.firstObject;
      NSString *value = subList.lastObject;
      dict[key] = value;
    }];
    if (dict.allKeys.count == 4) {
        UPResourceCondition *condition = [[UPResourceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:dict[@"rt"]]];
        NSString *name = dict[@"name"];
        if ([name containsString:@"/"]) {
            name = [name stringByReplacingOccurrencesOfString:@"/" withString:@","];
        }
        condition.resName = name;
        condition.appVersion = dict[@"appversion"];
        condition.fromFunc = dict[@"fromFun"];
        return condition;
    }
    UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:dict[@"rt"]]];
    condition.appVersion = dict[@"appversion"];
    condition.fromFunc = dict[@"fromFun"];
    condition.model = dict[@"md"];
    condition.typeId = dict[@"ti"];
    condition.prodNo = dict[@"pn"];
    condition.deviceType = dict[@"tc"];
    condition.deviceNetType = dict[@"dnt"];
    return condition;
}

NSString *conditionStringOfCondition(UPResourceCondition *condition)
{
    NSString *combineStr = [condition.combine stringByReplacingOccurrencesOfString:@"|" withString:@","];
    NSString *conditionStr = [NSString stringWithFormat:@"appversion=%@,fromFun=%@,%@", condition.appVersion, condition.fromFunc, combineStr];
    return conditionStr;
}

NSString *validString(NSString *str)
{
    return isValidString(str) ? str : @"";
}

BOOL isEqualQueryList(NSArray<UPResourceQuery *> *querys, NSArray<UPResourceQuery *> *anotherQuerys)
{
    if (!isValidArrayOfInstances(querys, [UPResourceQuery class]) ||
        !isValidArrayOfInstances(anotherQuerys, [UPResourceQuery class]) ||
        querys.count != anotherQuerys.count) {
        return NO;
    }
    BOOL isEqual = YES;
    for (UPResourceQuery *resource in querys) {
        if (!isQuerysContains(anotherQuerys, resource)) {
            isEqual = NO;
            break;
        }
    }
    return isEqual;
}

BOOL isQuerysContains(NSArray<UPResourceQuery *> *querys, UPResourceQuery *query)
{
    if (!isValidArrayOfInstances(querys, [UPResourceQuery class])) {
        return NO;
    }
    BOOL bFound = NO;
    for (UPResourceQuery *compareQuery in querys) {
        if (isEqualQuery(compareQuery, query)) {
            bFound = YES;
            break;
        }
    }
    return bFound;
}

NSArray<UPResourceInfo *> *filterExpectInfos(NSDictionary *userInfo, NSInteger expectedInvocationTimes)
{
    NSArray *list = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *expectInfos = [NSMutableArray array];
    for (int i = 0; i < list.count; i++) {
        NSString *countStr = list[i][0];
        if (expectedInvocationTimes == countStr.integerValue) {
            NSArray *item = list[i];
            UPResourceInfo *info = [[UPResourceInfo alloc] init];
            info.type = [UPResourceInfo typeValueOfString:item[2]];
            info.name = item[3];
            info.version = item[4];
            [expectInfos addObject:info];
        }
    }
    return [expectInfos copy];
}

NSArray<UPRLoadedReportInfo *> *transformReportInfoFromJson(NSArray *array)
{
    if (!array) {
        return nil;
    }
    NSMutableArray<UPRLoadedReportInfo *> *reportInfoList = [NSMutableArray array];
    [array enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [reportInfoList addObject:[[UPRLoadedReportInfo alloc] initWithName:obj[@"Name"] type:obj[@"Type"] version:obj[@"Version"] timestamp:obj[@"Timestamp"]]];
    }];

    return reportInfoList.copy;
}

BOOL compareReportInfo(NSArray<UPRLoadedReportInfo *> *list1, NSArray<UPRLoadedReportInfo *> *list2)
{
    if (!list1 && !list2) {
        return YES;
    }

    if (!list1 || !list2) {
        return NO;
    }

    if (list1.count != list2.count) {
        return NO;
    }

    __block BOOL result = YES;
    [list1 enumerateObjectsUsingBlock:^(UPRLoadedReportInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      UPRLoadedReportInfo *compareInfo = list2[idx];
      if (![obj.resName isEqualToString:compareInfo.resName] || ![obj.resType isEqualToString:compareInfo.resType] || ![obj.resVersion isEqualToString:compareInfo.resVersion] || ![obj.timestamp isEqualToString:compareInfo.timestamp]) {
          result = NO;
          *stop = YES;
      }
    }];

    return result;
}

//
//  CucumberRunner.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <XCTest/XCTest.h>
#import <Cucumberish/Cucumberish.h>
#import "NetworkSteps.h"
#import "DataSourceSteps.h"
#import "SearchResourceSteps.h"
#import "FileSystemSteps.h"
#import "DataBaseSteps.h"
#import "TaskResourceSteps.h"
#import "InitializationSteps.h"
#import "PresetSteps.h"
#import "LocalCacheSteps.h"
#import "UpdateSteps.h"
#import "TimeSystemSteps.h"
#import "DownloadSteps.h"
#import "RequestSteps.h"
#import "RelationSteps.h"
#import "CombineResInfoSteps.h"
#import "AutoUpdateLocalResSteps.h"
#import "ReportResLoadedSteps.h"

@interface CucumberRunner : NSObject

@end

@implementation CucumberRunner
__attribute__((constructor)) void CucumberishInit()
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    //Define your step implememntations (the example project contains set of basic implementations using KIF)
    NetworkSteps *networkSteps = [[NetworkSteps alloc] init];
    [networkSteps defineStepsAndHocks];

    InitializationSteps *initializationSteps = [[InitializationSteps alloc] init];
    [initializationSteps defineStepsAndHocks];

    PresetSteps *presetSteps = [[PresetSteps alloc] init];
    [presetSteps defineStepsAndHocks];

    LocalCacheSteps *localCacheSteps = [[LocalCacheSteps alloc] init];
    [localCacheSteps defineStepsAndHocks];

    [[[UpdateSteps alloc] init] defineStepsAndHocks];

    DataSourceSteps *dataSourceSteps = [[DataSourceSteps alloc] init];
    [dataSourceSteps defineStepsAndHocks];

    SearchResourceSteps *searchResourceSteps = [[SearchResourceSteps alloc] init];
    [searchResourceSteps defineStepsAndHocks];

    FileSystemSteps *fileSteps = [[FileSystemSteps alloc] init];
    [fileSteps defineStepsAndHocks];

    DataBaseSteps *databaseSteps = [[DataBaseSteps alloc] init];

    [databaseSteps defineStepsAndHocks];

    TaskResourceSteps *taskResourceSteps = [[TaskResourceSteps alloc] init];
    [taskResourceSteps defineStepsAndHocks];

    [[[TimeSystemSteps alloc] init] defineStepsAndHocks];

    DownloadSteps *downloadSteps = [[DownloadSteps alloc] init];
    [downloadSteps defineStepsAndHocks];

    RequestSteps *requestSteps = [[RequestSteps alloc] init];
    [requestSteps defineStepsAndHocks];

    RelationSteps *relationSteps = [[RelationSteps alloc] init];
    [relationSteps defineStepsAndHocks];

    CombineResInfoSteps *combineSteps = [[CombineResInfoSteps alloc] init];
    [combineSteps defineStepsAndHocks];

    AutoUpdateLocalResSteps *autoUpdateLocalRes = [[AutoUpdateLocalResSteps alloc] init];
    [autoUpdateLocalRes defineStepsAndHocks];

    ReportResLoadedSteps *reportResLoadedSteps = [[ReportResLoadedSteps alloc] init];
    [reportResLoadedSteps defineStepsAndHocks];
    //Optional step, see the comment on this property for more information
    [Cucumberish instance].fixMissingLastScenario = YES;
    //Tell Cucumberish the name of your features folder, and which bundle contains this directory. And Cucumberish will handle the rest...
    //The ClassThatLocatedInTheRootTestTargetFolder could be any class that exist side by side with your Features folder.
    //So if ClassThatLocatedInTheRootTestTargetFolder exist in the directory YourProject/YourTestTarget
    //Then in our example your .feature files are expected to be in the directory YourProject/YourTestTarget/Features
    NSBundle *bundle = [NSBundle bundleForClass:[CucumberRunner class]];

    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:nil excludeTags:@[ @"ios_ignore", @"ignore" ]];

    [cucumber beginExecution];
}

@end

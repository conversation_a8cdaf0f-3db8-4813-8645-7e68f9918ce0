//
//  PresetSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/7/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "PresetSteps.h"
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import "UPResourceResult.h"
#import "CommonResCallbackImp.h"

#define ResourceManager [ResourceHolder sharedInstance].resourceManager
#define PresetFileLoader [ResourceHolder sharedInstance].presetFileLoader

@interface PresetSteps ()
@property (nonatomic, strong) UPResourceResult *syncPresetResult;
@property (nonatomic, strong) NSMutableDictionary<NSString *, CommonResCallbackImp *> *presetCallbackInfo;

@end

@implementation PresetSteps
#pragma mark - Public Methods
- (instancetype)init
{
    if (self = [super init]) {
        _presetCallbackInfo = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.syncPresetResult = nil;
      [self.presetCallbackInfo removeAllObjects];
    });
    Given(@"^扫描路径下的文件列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *fileInfoList = getTableDataListFromExpectUserInfo(userInfo);
      for (NSArray *data in fileInfoList) {
          NSString *file = data.firstObject;
          OCMStub([PresetFileLoader existFile:file]).andReturn(YES);
      }
    });
    Given(@"^预置资源加载器生成文件名\"([^\"]*)\"的全路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      NSString *path = args[1];
      OCMStub([PresetFileLoader openPresetFile:name]).andReturn(path);
    });
    When(@"^调用异步预置资源列表接口,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *callbackID = args[0];
      CommonResCallbackImp *callback = [[CommonResCallbackImp alloc] initWithIdentifier:callbackID];
      [self.presetCallbackInfo setObject:callback forKey:callbackID];
      [ResourceManager extractPresetResList:PresetFileLoader callback:callback];
    });
    Then(@"^\"([^\"]*)\"收到异步预置资源列表的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *callbackID = args[0];
      CommonResCallbackImp *callback = self.presetCallbackInfo[callbackID];
      CCIAssert(callback != nil, @"回调参数\"%@\"为nil!", callbackID);
      NSString *result = args[1];
      BOOL expectedResult = isValidStringEqual(result, @"成功");
      BOOL actualResult = callback.result;
      CCIAssert(actualResult == expectedResult, @"异步预置资源列表的回调结果应该为:%@,实际为:%@", result, actualResult ? @"成功" : @"失败");
    });
    Given(@"^预置资源加载器扫描预置路径接口持续时长为\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *seconds = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [NSThread sleepForTimeInterval:seconds.floatValue];
      };
      [[[(OCMockObject *)PresetFileLoader stub] andDo:proxyBlock] scanPresetFilenameList];
    });

    Given(@"^预置资源加载器扫描的json文件的列表为空对象", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([PresetFileLoader scanPresetFilenameList]).andReturn(nil);
    });

    Given(@"^预置资源加载器扫描的json文件的列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *fileInfoList = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *infos = [NSMutableArray array];
      for (NSArray *fileInfo in fileInfoList) {
          NSMutableDictionary *dict = [NSMutableDictionary dictionary];
          dict[@"resName"] = fileInfo[0];
          dict[@"resType"] = fileInfo[1];
          dict[@"version"] = fileInfo[2];
          dict[@"filename"] = fileInfo[3];
          dict[@"hashMD5"] = fileInfo[4];
          [infos addObject:dict];
      }
      [[[(OCMockObject *)PresetFileLoader stub] andReturn:infos] scanPresetFilenameList];
    });

    Then(@"^\"([^\"]*)\"收到异步预置资源列表的结果为\"([^\"]*)\",预置的资源信息列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *callbackID = args[0];
      CommonResCallbackImp *callback = self.presetCallbackInfo[callbackID];
      CCIAssert(callback != nil, @"回调参数\"%@\"为nil!", callbackID);

      NSString *result = args[1];
      BOOL expectedResult = isValidStringEqual(result, @"成功");
      BOOL actualResult = callback.result;
      CCIAssert(actualResult == expectedResult, @"异步预置资源列表的回调结果应该为:%@,实际为:%@", result, actualResult ? @"成功" : @"失败");

      NSArray<NSArray<NSString *> *> *resInfoList = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *expectedInfoList = [NSMutableArray array];
      for (NSArray *resourceInfo in resInfoList) {
          UPResourceInfo *info = createResourceInfoFromPropertiesInfoArray(resourceInfo);
          [expectedInfoList addObject:info];
      }
      NSArray *actualInfoList = callback.infoList;
      BOOL isEqual = [self actualPresetResResultList:actualInfoList isEqualToExpectedList:expectedInfoList];
      CCIAssert(isEqual, @"异步预置资源列表接口的回调结果中，实际预置的资源信息列表与预期不一致。");
    });
    When(@"^使用者调用同步预置资源信息接口,资源名称为\"([^\"]*)\",资源类型为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      NSString *type = args[1];
      if (isValidStringEqual(name, @"空对象")) {
          name = nil;
      }
      else if (isValidStringEqual(name, @"空字符串")) {
          name = @"";
      }
      self.syncPresetResult = [ResourceManager syncExtractPresetResInfo:PresetFileLoader name:name type:[UPResourceInfo typeValueOfString:type]];
    });
    Then(@"^同步预置资源信息的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL expectedResult = isValidStringEqual(result, @"成功");
      BOOL actualResult = self.syncPresetResult.isSuccessful;
      CCIAssert(actualResult == expectedResult, @"收到同步预置资源信息的结果为:%@,实际为:%@", result, actualResult ? @"成功" : @"失败");
    });
    Then(@"^同步预置资源信息的结果为\"([^\"]*)\",预置的资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL expectedResult = isValidStringEqual(result, @"成功");
      BOOL actualResult = self.syncPresetResult.isSuccessful;
      CCIAssert(actualResult == expectedResult, @"收到同步预置资源信息的结果为:%@,实际为:%@", result, actualResult ? @"成功" : @"失败");

      NSArray<NSArray<NSString *> *> *resInfoList = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *expectedInfoList = [NSMutableArray array];
      for (NSArray *resourceInfo in resInfoList) {
          UPResourceInfo *info = createResourceInfoFromPropertiesInfoArray(resourceInfo);
          [expectedInfoList addObject:info];
      }
      NSArray *actualInfoList = @[ self.syncPresetResult.extraData ];
      BOOL isEqual = [self actualPresetResResultList:actualInfoList isEqualToExpectedList:expectedInfoList];
      CCIAssert(isEqual, @"同步预置资源信息，实际预置的资源信息列表与预期不一致。");
    });
}

#pragma mark - Non-Public Methods
- (BOOL)actualPresetResResultList:(NSArray<UPResourceInfo *> *)actualInfoList isEqualToExpectedList:(NSArray<UPResourceInfo *> *)expectedInfoList
{
    if (!isValidArray(actualInfoList) && !isValidArray(expectedInfoList)) {
        return YES;
    }
    BOOL isEqual = YES;
    for (UPResourceInfo *resource in actualInfoList) {
        if (!isResourcesContains(expectedInfoList, resource)) {
            isEqual = NO;
            break;
        }
    }
    return isEqual;
}
@end

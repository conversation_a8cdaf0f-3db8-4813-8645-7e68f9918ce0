//
//  AutoUpdateLocalResSteps.m
//  ResDebuggerTests
//
//  Created by 冉东军 on 2021/5/20.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "AutoUpdateLocalResSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import <OCMock/OCMock.h>
#import "UPRequestDelegate.h"
@interface AutoUpdateLocalResSteps ()

@end
@implementation AutoUpdateLocalResSteps
- (void)defineStepsAndHocks
{
    Given(@"^数据库批量查询资源条件列表如下:", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *allQuerys = getCombineQueryList(array);
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any]]).andReturn(allQuerys);
    });

    Given(@"^自动更新数据库批量查询资源条件列表如下:", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *allQuerys = getCombineQueryList(array);
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any]]).andReturn(allQuerys);
    });

    When(@"^用户调用自动更新本地已安装资源接口$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[ResourceHolder sharedInstance].resourceManager autoUpgradeLocalResouces];
    });
}

@end

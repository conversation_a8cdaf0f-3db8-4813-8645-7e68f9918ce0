//
//  LocalCacheSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/7/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "LocalCacheSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import "CacheCleanCallback.h"
#import <OCMock/OCMock.h>

@interface LocalCacheSteps ()
@property (nonatomic, strong) NSMutableDictionary<NSString *, CacheCleanCallback *> *callbackInfo;
@property (nonatomic, assign) BOOL isCleaningCache;
@end

@implementation LocalCacheSteps
- (instancetype)init
{
    if (self = [super init]) {
        _callbackInfo = [NSMutableDictionary dictionary];
    }
    return self;
}

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      [self.callbackInfo removeAllObjects];
      self.isCleaningCache = NO;
    });
    When(@"^清除本地缓存,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceManager *resourceManager = [ResourceHolder sharedInstance].resourceManager;
      NSString *callbackID = args[0];
      CacheCleanCallback *callback = [[CacheCleanCallback alloc] init];
      callback.identifier = callbackID;
      [self.callbackInfo setObject:callback forKey:callbackID];
      [resourceManager cleanLocalResourceCache:callback];
    });

    Then(@"^\"([^\"]*)\"收到本地缓存清除\"([^\"]*)\"的回调$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *callbackID = args[0];
      NSString *expectResult = args[1];
      CacheCleanCallback *callback = self.callbackInfo[callbackID];
      CCIAssert(callback != nil, @"单元测试代码逻辑错误！缓存清空回调参数\"%@\"的对象为nil。", callbackID);
      BOOL bActualResult = callback.result == TestCallbackResultSuccessful;
      BOOL bExpectedResult = isValidStringEqual(expectResult, @"成功");
      CCIAssert(bActualResult == bExpectedResult, @"清除本地缓存结果应该为：%@,实际为：%@", expectResult, bActualResult ? @"成功" : @"失败");
    });

    When(@"^查询是否正在清除缓存$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceManager *resourceManager = [ResourceHolder sharedInstance].resourceManager;
      self.isCleaningCache = [resourceManager isCleaning];
    });

    Then(@"^是否正在清除缓存的查询结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL expectedResult = isValidStringEqual(resultString, @"是");
      CCIAssert(self.isCleaningCache == expectedResult, @"查询是否正在清除缓存的结果应该为：%@,实际为：%@", resultString, self.isCleaningCache ? @"是" : @"否");
    });

    Given(@"^正在执行清除本地缓存操作$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      OCMStub([ResourceHolder sharedInstance].resourceCleaner.isCleaning).andReturn(YES);
    });

    When(@"^线程\"([^\"]*)\",清除本地缓存,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceManager *resourceManager = [ResourceHolder sharedInstance].resourceManager;
      NSString *callbackID = args[1];
      CacheCleanCallback *callback = [[CacheCleanCallback alloc] init];
      callback.identifier = callbackID;
      [self.callbackInfo setObject:callback forKey:callbackID];
      dispatch_async(dispatch_get_global_queue(0, 0), ^{
        [resourceManager cleanLocalResourceCache:callback];
      });
    });

    When(@"^线程\"([^\"]*)\",等待\"([^\"]*)\"秒后调用清除本地缓存,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceManager *resourceManager = [ResourceHolder sharedInstance].resourceManager;
      NSString *time = args[1];
      NSString *callbackID = args[2];
      CacheCleanCallback *callback = [[CacheCleanCallback alloc] init];
      callback.identifier = callbackID;
      [self.callbackInfo setObject:callback forKey:callbackID];
      dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(time.integerValue * NSEC_PER_SEC)), dispatch_get_global_queue(0, 0), ^{
        [resourceManager cleanLocalResourceCache:callback];
      });
    });


    When(@"调用清除本地缓存保护指定预置资源接口,回调为\"([^\"]*)\",受保护资源列表参数为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceManager *resourceManager = [ResourceHolder sharedInstance].resourceManager;
      NSString *callbackID = args[0];
      CacheCleanCallback *callback = [[CacheCleanCallback alloc] init];
      callback.identifier = callbackID;
      [self.callbackInfo setObject:callback forKey:callbackID];
      NSArray *preloadInfoArr = [self createPreloadResourceInfoFromStepTableData:userInfo];
      [resourceManager cleanLocalDataKeepPresetRes:preloadInfoArr callback:callback];
    });
}

#pragma mark - private
- (NSArray<UpPreloadResourceInfo *> *)createPreloadResourceInfoFromStepTableData:(NSDictionary *)userInfo
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<UpPreloadResourceInfo *> *preloadResInfoList = [NSMutableArray array];
    for (NSArray *info in infoList) {
        UpPreloadResourceInfo *resInfo = [[UpPreloadResourceInfo alloc] init];
        NSString *paramType = info[0];
        NSString *paramName = info[1];
        NSString *version = info.count > 2 ? info[2] : @"";
        if ([paramType isEqualToString:@"空对象"]) {
            paramType = nil;
        }
        if ([paramName isEqualToString:@"空字符串"]) {
            paramName = @"";
        }
        if ([paramName isEqualToString:@"空对象"]) {
            paramName = nil;
        }
        if ([version isEqualToString:@"空对象"]) {
            version = nil;
        }
        if ([version isEqualToString:@"空字符串"]) {
            version = @"";
        }

        resInfo.name = paramName;
        resInfo.type = [UPResourceInfo typeValueOfString:paramType];
        resInfo.version = version;
        [preloadResInfoList addObject:resInfo];
    }
    return preloadResInfoList;
}
@end

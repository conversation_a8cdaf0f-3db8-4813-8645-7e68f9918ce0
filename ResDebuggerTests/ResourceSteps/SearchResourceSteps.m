//
//  SearchResourceSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/15.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SearchResourceSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
@interface SearchResourceSteps ()
@property (nonatomic, strong) ResourceHolder *resourceHolder;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *resourceInfoList;
@property (nonatomic, strong) UPResourceInfo *syncInfo;
@property (nonatomic, copy) NSString *resourceFilePath;

@end

@implementation SearchResourceSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.syncInfo = nil;
      self.resourceInfoList = nil;
      self.resourceFilePath = nil;
      self.resourceHolder = [ResourceHolder sharedInstance];
    });

    When(@"^调用查询本地全部资源列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.resourceInfoList = [self.resourceHolder.resourceManager getEntireList:nil];
    });
    Then(@"^查询资源结果为空数组$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.resourceInfoList.count == 0, @"查询结果应为空，实际不为空");
    });
    Then(@"^查询资源结果数组如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *resultArray = [self convertResInfoListByUserInfo:userInfo];
      BOOL result = isEqualResourcesList(self.resourceInfoList, resultArray);
      CCISAssert(result, @"查询结果与预期一致,实际不一致");
    });
    When(@"^调用查询本地全部资源列表方法,过滤条件类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.resourceHolder initFilter:args[1] type:args[0]];
      self.resourceInfoList = [self.resourceHolder.resourceManager getEntireList:self.resourceHolder.filterHelper];
    });
    When(@"^调用查询本地最新资源列表方法$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.resourceInfoList = [self.resourceHolder.resourceManager getLatestList:nil];
    });
    When(@"^调用查询本地最新资源列表方法,过滤条件类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.resourceHolder initFilter:args[1] type:args[0]];
      self.resourceInfoList = [self.resourceHolder.resourceManager getLatestList:self.resourceHolder.filterHelper];
    });
    When(@"^调用查询本地最新资源方法,传入参数类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *type = args[0];
      NSString *name = args[1];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      if ([type isEqualToString:@"空对象"]) {
          type = @"";
      }
      self.syncInfo = [self.resourceHolder.resourceManager getLatestInfoByName:name type:[UPResourceInfo typeValueOfString:type]];
    });

    Then(@"^查询结果为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.syncInfo == nil, @"结果与预期一致，实际不一致");
    });

    When(@"^调用查询本地指定资源方法,传入参数类型为\"([^\"]*)\",名字为\"([^\"]*)\",版本为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *type = args[0];
      NSString *name = args[1];
      NSString *version = args[2];
      if ([type isEqualToString:@"空对象"]) {
          type = @"";
      }
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      if ([version isEqualToString:@"空对象"]) {
          version = nil;
      }
      self.syncInfo = [self.resourceHolder.resourceManager getResourceInfo:name type:[UPResourceInfo typeValueOfString:type] version:version];
    });
    Then(@"^查询资源结果如下:", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *resultArray = [self convertResInfoListByUserInfo:userInfo];
      CCIAssert(isEqualResource(self.syncInfo, resultArray.firstObject), @"查询结果与实际一致，实际不一致");
    });

    When(@"^调用查询本地已安装的最新资源方法,传入参数类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *type = args[0];
      NSString *name = args[1];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      if ([type isEqualToString:@"空对象"]) {
          type = @"";
      }
      self.syncInfo = [self.resourceHolder.resourceManager getLatestInstalledInfo:name type:[UPResourceInfo typeValueOfString:type]];
    });
    When(@"^调用查询本地已安装的最新资源列表方法,传入参数类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *type = args[0];
      NSString *name = args[1];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      if ([type isEqualToString:@"空对象"]) {
          type = @"";
      }
      NSArray *names = [name componentsSeparatedByString:@","];
      self.resourceInfoList = [self.resourceHolder.resourceManager getLatestInstalledList:^BOOL(UPResourceInfo *info) {
        BOOL accept = NO;
        if (info.type == [UPResourceInfo typeValueOfString:type]) {
            accept = [names containsObject:info.name];
        }
        return accept;
      }];
    });
    When(@"^调用获取指定类型资源的安装路径方法,传入参数类型\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *type = args[0];
      self.resourceFilePath = [self.resourceHolder.resourceManager getPathByType:[UPResourceInfo typeValueOfString:type]];
    });
    Then(@"^获取文件路径结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      if ([result isEqualToString:@"空对象"]) {
          CCIAssert(self.resourceFilePath == nil, @"获取结果应为空对象，实际不是");
      }
      else {
          CCIAssert([self.resourceFilePath isEqualToString:result], @"获取结果应为与预期不一致，实际结果为%@--预期结果为%@", self.resourceFilePath, result);
      }
    });
    When(@"^调用查询普通资源列表方法,传入参数类型为\"([^\"]*)\",名字为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *typeStr = args[0];
      NSString *name = args[1];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      self.resourceInfoList = [self.resourceHolder.resourceManager searchNormalResList:name type:[UPResourceInfo typeValueOfString:typeStr]];
    });
    Then(@"^查询普通资源结果为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.resourceInfoList == nil, @"查询结果应该为空对象，实际不为空对象");
    });
    When(@"^调用查询设备资源列表方法,传入参数条件为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *conditionStr = args[0];
      UPResourceDeviceCondition *condition = (UPResourceDeviceCondition *)combineCondition(conditionStr);
      self.resourceInfoList = [self.resourceHolder.resourceManager searchDeviceResList:condition];
    });
    Then(@"^查询设备资源结果为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.resourceInfoList == nil, @"查询结果应该为空对象，实际不为空对象");
    });

    When(@"^调用资源\"([^\"]*)\"的路径属性$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resName = args[0];
      for (UPResourceInfo *resourceInfo in self.resourceInfoList) {
          if ([resourceInfo.name isEqualToString:resName]) {
              NSString *path = resourceInfo.path;
              break;
          }
      }
    });
}

#pragma mark private Methods

- (NSArray<UPResourceInfo *> *)convertResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [[ResourceHolder sharedInstance] convertResourceInfoList:array];
}

@end

//
//  RequestSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/3/6.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RequestSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import <OCMock/OCMock.h>
#import "UPRequestDelegate.h"
#import "UPResourceRequestDelegate.h"
typedef void (^resultBlock)(NSArray<UPResourceInfo *> *infoList, NSError *error);
@interface RequestSteps ()
@property (nonatomic, strong) NSArray<UPResourceInfo *> *syncResourceInfoList;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *asyncResourceInfoList;
@property (nonatomic, strong) XCTestExpectation *expectation;
@end

@implementation RequestSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.syncResourceInfoList = nil;
      self.asyncResourceInfoList = nil;
      self.expectation = nil;
    });

    Given(@"^资源请求代理使用模拟的$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[ResourceHolder sharedInstance].resourceManager setValue:OCMProtocolMock(@protocol(UPRequestDelegate)) forKey:@"requestDelegate"];
    });

    When(@"^调用请求资源列表方法,参数请求条件为\"([^\"]*)\",立即请求参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *conditonStr = args[0];
      NSString *immediateStr = args[1];
      UPResourceCondition *condition = combineCondition(conditonStr);
      self.syncResourceInfoList = [[ResourceHolder sharedInstance]
                                       .resourceManager.requestDelegate requestResourceList:condition
                                                                                  immediate:immediateStr.boolValue
                                                                                 completion:^(NSArray<UPResourceInfo *> *_Nonnull infoList, NSError *_Nonnull error) {
                                                                                   self.asyncResourceInfoList = infoList;
                                                                                   [self.expectation fulfill];
                                                                                 }];
    });

    Then(@"^请求资源列表返回的资源列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resutl = args[0];
      if ([resutl isEqualToString:@"空对象"]) {
          CCIAssert(self.syncResourceInfoList == nil, @"请求同步结果应为空对象,实际不为空");
      }
      else if ([resutl isEqualToString:@"空列表"]) {
          CCIAssert(self.syncResourceInfoList == nil || self.syncResourceInfoList.count == 0, @"请求同步结果应为空列表,实际不为空");
      }
    });

    Then(@"^请求资源列表返回的资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      NSArray<UPResourceInfo *> *infoList = self.syncResourceInfoList;
      CCIAssert(isEqualResourcesList(infoList, resultInfoList), @"同步请求结果应该与预期一致，实际不一致");
    });

    Then(@"^请求资源列表异步操作结果为失败$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self waitForExpectations:@"请求资源列表异步回调"];
      CCIAssert(self.asyncResourceInfoList == nil, @"请求异步结果应为失败,实际为成功");
    });

    Then(@"^请求资源列表异步操作结果为成功,资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self waitForExpectations:@"请求资源列表异步回调"];
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      NSArray<UPResourceInfo *> *infoList = self.asyncResourceInfoList;
      CCIAssert(isEqualResourcesList(infoList, resultInfoList), @"异步请求结果应该与预期一致，实际不一致,实际为infoList的个数=%ld,期望的个数=%ld", infoList.count, resultInfoList.count);
    });
}
#pragma mark private Methods

- (NSArray<UPResourceInfo *> *)convertResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [[ResourceHolder sharedInstance] convertResourceInfoList:array];
}

- (void)waitForExpectations:(NSString *)desc
{
    self.expectation = [[XCTestExpectation alloc] initWithDescription:desc];
    [XCTWaiter waitForExpectations:@[ self.expectation ] timeout:0.1 enforceOrder:NO];
}

@end

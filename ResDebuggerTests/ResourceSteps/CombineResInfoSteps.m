//
//  CombineResInfoSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2020/3/17.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CombineResInfoSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceSelectorDelegate.h"
#import "ResourceHolder.h"
#import "UpPreloadResourceInfo.h"
#import <OCMock/OCMock.h>
#import "UPResourcePreLoadTask.h"
#import "UPRequestDelegate.h"
#import "UPInstallDelegate.h"
#import "CommonResCallbackImp.h"

typedef void (^resultBlock)(NSArray<UPResourceInfo *> *infoList, NSError *error);
@interface CombineResInfoSteps ()
@property (nonatomic, strong) UPResourceInfo *info;
@property (nonatomic, strong) NSArray *infoList;
@property (nonatomic, strong) NSMutableDictionary *successDict;
@property (nonatomic, strong) NSMutableDictionary *faileDict;
@property (nonatomic, strong) NSMutableArray<UPResourceCondition *> *actualResourceConditions;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *actualResourceList;
@property (nonatomic, strong) CommonResCallbackImp *callback;
@end

@implementation CombineResInfoSteps

#pragma mark - Public Methods
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.callback = [[CommonResCallbackImp alloc] initWithIdentifier:@"callBack"];
      self.info = nil;
      self.infoList = nil;
      self.successDict = [NSMutableDictionary dictionary];
      self.faileDict = [NSMutableDictionary dictionary];
      self.actualResourceConditions = [NSMutableArray array];
      self.actualResourceList = [NSMutableArray array];
    });

    Given(@"^资源安装代理使用模拟的$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [[ResourceHolder sharedInstance].resourceManager setValue:OCMProtocolMock(@protocol(UPInstallDelegate)) forKey:@"installDelegate"];
    });

    Given(@"^资源请求代理请求资源接口同步返回资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *infoList = [self convertResInfoListByUserInfo:userInfo];
      self.infoList = infoList;
    });

    When(@"^执行自动升级\"([^\"]*)\"操作,资源类型为\"([^\"]*)\",资源名称为\"([^\"]*)\",设备信息为\"([^\"]*)\",选择器信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiTypeStr = args[0];
      NSString *resTypeStr = args[1];
      NSString *resName = args[2];
      NSString *deviceInfoStr = args[3];
      NSString *selectorInfoStr = args[4];
      id<UpResourceSelector> selector = nil;
      if (!isValidStringEqual(selectorInfoStr, @"空对象")) {
          NSString *selectorType = [selectorInfoStr componentsSeparatedByString:@","].firstObject;
          NSString *selectorName = [selectorInfoStr componentsSeparatedByString:@","].lastObject;
          [[ResourceSelectorDelegate getInstance] mockResourceName:selectorName type:[UPResourceInfo typeValueOfString:selectorType]];
          selector = [ResourceSelectorDelegate getInstance];
      }
      if ([apiTypeStr isEqualToString:@"普通资源"]) {
          self.info = [[ResourceHolder sharedInstance].resourceManager getCommonResource:resName type:[UPResourceInfo typeValueOfString:resTypeStr] selector:selector callback:self.callback listener:nil];
      }
      else {
          NSArray<NSString *> *deviceInfoArray = [deviceInfoStr componentsSeparatedByString:@","];
          if (!isValidArray(deviceInfoArray)) {
              return;
          }
          NSString *model = deviceInfoArray[0];
          NSString *typeId = deviceInfoArray[1];
          NSString *typeCode = deviceInfoArray[2];
          NSString *prodNo = deviceInfoArray[3];
          if (isValidStringEqual(resTypeStr, @"DEVICE_CONFIG")) {
              resTypeStr = [UPResourceInfo stringValueOfResourceType:UPResourceTypeDeviceConfig];
          }
          UPResourceDeviceCondition *condition = [[UPResourceDeviceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:resTypeStr]];
          condition.model = model;
          condition.typeId = typeId;
          condition.deviceType = typeCode;
          condition.prodNo = prodNo;
          condition.resName = resName;
          self.info = [[ResourceHolder sharedInstance].resourceManager getDeviceResource:condition selector:selector callback:self.callback listener:nil];
      }
    });


    Then(@"^资源请求代理接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].resourceManager.requestDelegate requestResourceList:[OCMArg any] immediate:NO completion:[OCMArg any]]);
    });

    Then(@"^资源请求代理接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSArray<UPResourceCondition *> *expectResourceConditions = [self convertResourceConditionFromStepSimpleTableData:userInfo];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].resourceManager.requestDelegate requestResourceList:[OCMArg any] immediate:NO completion:[OCMArg any]]);
      CCISAssert(isEqualConditionList(expectResourceConditions, self.actualResourceConditions), @"资源请求代理接口参数与实际不一致");
    });

    Then(@"^资源安装代理接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].resourceManager.installDelegate install:[OCMArg any] callback:[OCMArg any] listener:[OCMArg any]]);
    });

    Then(@"^资源安装代理接口被调用\"([^\"]*)\"次,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *countStr = args[0];
      NSUInteger expectCount = countStr.integerValue;
      NSArray<UPResourceInfo *> *expectResInfoList = [self createResourceInfoFromStepTableData:userInfo];
      OCMVerify(times(expectCount), [[ResourceHolder sharedInstance].resourceManager.installDelegate install:[OCMArg any] callback:[OCMArg any] listener:[OCMArg any]]);
      CCIAssert(isEqualResourcesList(expectResInfoList, self.actualResourceList), @"资源安装代理接口期望为：%@,实际为：%@", expectResInfoList, self.actualResourceList);
    });

    Then(@"^自动升级\"([^\"]*)\"直接返回已安装资源为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiType = args[0];
      CCIAssert(self.info == nil, @"自动升级%@接口直接回调结果应为空对象，实际为:%@", apiType, self.info);
    });

    Then(@"^自动升级\"([^\"]*)\"直接返回已安装资源为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiType = args[0];
      NSArray<UPResourceInfo *> *expectResInfoList = [self createResourceInfoFromStepTableData:userInfo];
      BOOL bResult = NO;
      if (isValidStringEqual(@"普通资源", apiType)) {
          bResult = isEqualResource(expectResInfoList.firstObject, self.info);
      }
      else {
          bResult = isDeviceResourceInfoEqual(expectResInfoList.firstObject, self.info);
      }

      CCIAssert(bResult, @"自动升级%@接口直接返回已安装的资源期望为：%@,实际为：%@", apiType, wholeIdentifierOfResource(expectResInfoList.firstObject), wholeIdentifierOfResource(self.info));
    });

    Then(@"^自动升级\"([^\"]*)\"回调结果为\"([^\"]*)\",资源包为空对象$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiType = args[0];
      NSString *resultStr = args[1];
      BOOL expectResult = isValidStringEqual(@"成功", resultStr);
      BOOL actualResult = self.callback.result;
      CCIAssert(expectResult == actualResult, @"自动升级%@接口回调结果应为：%@，实际为：%@", apiType, resultStr, actualResult ? @"成功" : @"失败");
      UPResourceInfo *resultInfo = self.callback.info;
      CCIAssert(resultInfo == nil, @"自动升级%@接口回调的资源包应为：空对象，实际为：%@", apiType, resultInfo);
    });

    Given(@"^资源请求代理请求资源接口异步回调\"([^\"]*)\"，资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {

      NSString *resultStr = args[0];
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      NSMutableArray *nameArray = [NSMutableArray array];
      for (UPResourceInfo *info in resultInfoList) {
          [nameArray addObject:info.name];
      }
      NSString *name = [nameArray componentsJoinedByString:@","];
      if ([resultStr isEqualToString:@"成功"]) {
          self.successDict[name] = resultInfoList;
      }
      else {
          self.faileDict[name] = resultInfoList;
      }
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        id obj = self.infoList;
        [invocation setReturnValue:&obj];
        void *param;
        [invocation getArgument:&param atIndex:2];
        UPResourceCondition *condition = (__bridge UPResourceCondition *)param;
        [self.actualResourceConditions addObject:condition];
        void *blockPointer;
        [invocation getArgument:&blockPointer atIndex:4];
        resultBlock block = (__bridge resultBlock)blockPointer;
        NSArray *succList = self.successDict.allValues.firstObject;
        if ([self.successDict.allKeys.firstObject containsString:condition.resName ? condition.resName : @""]) {
            for (UPResourceInfo *info in succList) {
                if ([info.name isEqualToString:condition.resName]) {
                    block([NSArray arrayWithObject:info], nil);
                }
            }
        }
        if ([self.faileDict.allKeys.firstObject containsString:condition.resName ? condition.resName : @""] || [self.faileDict.allKeys.firstObject isEqualToString:@""]) {
            NSError *error = [NSError errorWithDomain:@"请求失败" code:100001 userInfo:nil];
            block(self.faileDict.allValues.firstObject ? self.faileDict.allValues.firstObject : nil, error);
        }
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].resourceManager.requestDelegate stub] andDo:proxyBlock] requestResourceList:[OCMArg any] immediate:NO completion:[OCMArg any]];
    });

    Then(@"^自动升级\"([^\"]*)\"回调结果为\"([^\"]*)\",资源包为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiType = args[0];
      NSString *resultStr = args[1];
      BOOL expectResult = isValidStringEqual(@"成功", resultStr);
      BOOL actualResult = self.callback.result;
      CCIAssert(expectResult == actualResult, @"自动升级%@接口回调结果应为：%@，实际为：%@", apiType, resultStr, actualResult ? @"成功" : @"失败");

      UPResourceInfo *expectResInfo = [self createResourceInfoFromStepTableData:userInfo].firstObject;
      UPResourceInfo *resultInfo = self.callback.info;
      CCIAssert(isEqualResource(expectResInfo, resultInfo), @"自动升级%@接口回调的资源包应为：%@，实际为：%@", wholeIdentifierOfResource(expectResInfo), wholeIdentifierOfResource(resultInfo));
    });

    Given(@"^资源安装代理接口回调\"([^\"]*)\"，资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *vCallback;
        [invocation getArgument:&vCallback atIndex:3];
        id<UPResourceCallback> callback = (__bridge id<UPResourceCallback>)vCallback;
        void *vResInfo;
        [invocation getArgument:&vResInfo atIndex:2];
        UPResourceInfo *info = (__bridge UPResourceInfo *)vResInfo;
        [self.actualResourceList addObject:info];
        [callback onResult:[resultString isEqualToString:@"成功"] message:@"" resourceInfo:info];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].resourceManager.installDelegate stub] andDo:proxyBlock] install:[OCMArg any] callback:[OCMArg any] listener:[OCMArg any]];
    });

    When(@"^执行批量预加载资源操作,预加载资源列表为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UpPreloadResourceInfo *> *preloadResInfoList = [self createPreloadResourceInfoFromStepTableData:userInfo];
      [[ResourceHolder sharedInstance].resourceManager preloadNormalResList:preloadResInfoList callback:self.callback];
    });

    When(@"^执行批量预加载资源操作,预加载资源列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *paramterDescription = args[0];
      NSArray<UpPreloadResourceInfo *> *preloadResInfoList = nil;
      if (isValidStringEqual(@"空对象", paramterDescription)) {
          preloadResInfoList = nil;
      }
      else if (isValidStringEqual(@"空数组", paramterDescription)) {
          preloadResInfoList = @[];
      }
      else {
          CCIAssert(NO, @"不支持的批量预加载资源操作的资源列表参数描述：%@", paramterDescription);
      }
      [[ResourceHolder sharedInstance].resourceManager preloadNormalResList:preloadResInfoList callback:self.callback];
    });

    Then(@"^批量预加载资源结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL expectResult = isValidStringEqual(@"成功", resultString);
      BOOL actualResult = self.callback.result;
      CCIAssert(expectResult == actualResult, @"批量预加载资源结果应为：%@，实际为：%@", resultString, actualResult ? @"成功" : @"失败");
    });

    Then(@"^批量预加载资源结果为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *expectInfoResultList = [self createPreloadResourceCallbackInfoListFromStepTableData:userInfo];
      NSArray<UPResourceInfo *> *actualInfoResultList = self.callback.preloadResInfoList;
      CCIAssert([self isPreloadCallbackResInfoList:actualInfoResultList equalToExpectList:expectInfoResultList], @"批量预加载资源结果应为：%@，实际为：%@", expectInfoResultList, actualInfoResultList);
    });


    When(@"^执行自动安装最新资源操作,需要优先安装的资源列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *paramterDescription = args[0];
      NSArray<UpPreloadResourceInfo *> *preloadResInfoList = nil;
      if (isValidStringEqual(@"空对象", paramterDescription)) {
          preloadResInfoList = nil;
      }
      else if (isValidStringEqual(@"空数组", paramterDescription)) {
          preloadResInfoList = @[];
      }
      else {
          CCIAssert(NO, @"不支持的批量预加载资源操作的资源列表参数描述：%@", paramterDescription);
      }
      [[ResourceHolder sharedInstance].resourceManager autoUpgradeCurrentResources:preloadResInfoList callback:self.callback];
    });

    Then(@"^自动安装最新资源结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL expectResult = isValidStringEqual(@"成功", resultString);
      BOOL actualResult = self.callback.result;
      CCIAssert(expectResult == actualResult, @"自动安装最新资源结果应为：%@，实际为：%@", resultString, actualResult ? @"成功" : @"失败");
    });

    Then(@"^自动安装最新资源结果为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *expectInfoResultList = [self createPreloadResourceCallbackInfoListFromStepTableData:userInfo];
      NSArray<UPResourceInfo *> *actualInfoResultList = self.callback.preloadResInfoList;
      CCIAssert([self isPreloadCallbackResInfoList:actualInfoResultList equalToExpectList:expectInfoResultList], @"自动安装最新资源结果应为：%@，实际为：%@", expectInfoResultList, actualInfoResultList);
    });

    When(@"^执行自动安装最新资源操作,需要优先安装的资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UpPreloadResourceInfo *> *preloadResInfoList = [self createPreloadResourceInfoFromStepTableData:userInfo];
      [[ResourceHolder sharedInstance].resourceManager autoUpgradeCurrentResources:preloadResInfoList callback:self.callback];
    });
}

- (NSArray<UPResourceInfo *> *)convertResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [[ResourceHolder sharedInstance] convertResourceInfoList:array];
}

- (NSArray<UPResourceCondition *> *)convertResourceConditionFromStepSimpleTableData:(NSDictionary *)userInfo
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<UPResourceCondition *> *conditions = [NSMutableArray array];
    for (NSArray<NSString *> *info in infoList) {
        UPResourceCondition *condition = nil;
        NSString *deviceInfoStr = info[2];
        if (!isValidString(deviceInfoStr)) {
            condition = [[UPResourceCondition alloc] init];
            condition.resourceType = [UPResourceInfo typeValueOfString:info[0]];
            condition.resName = info[1];
        }
        else {
            UPResourceDeviceCondition *deviceCond = [[UPResourceDeviceCondition alloc] init];
            deviceCond.resourceType = [UPResourceInfo typeValueOfString:info[0]];
            deviceCond.resName = info[1];
            NSArray *deviceInfoArr = [deviceInfoStr componentsSeparatedByString:@","];
            deviceCond.model = deviceInfoArr[0];
            deviceCond.typeId = deviceInfoArr[1];
            deviceCond.deviceType = deviceInfoArr[2];
            deviceCond.prodNo = deviceInfoArr[3];
            condition = deviceCond;
        }

        if (!isValidString(deviceInfoStr)) {
            condition.fromFunc = FROM_FUNC_COMMON_RESOURCE;
        }
        else {
            condition.fromFunc = [UPResourceDeviceCondition stringFromfuncOfResourceType:condition.resourceType];
        }
        if (info.count > 3 && ![info[3] isEqualToString:@""]) {
            condition.fromFunc = info[3];
        }
        condition.appVersion = @"6.9.0";
        [conditions addObject:condition];
    }
    return conditions;
}

- (NSArray<UPResourceInfo *> *)createResourceInfoFromStepTableData:(NSDictionary *)userInfo
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<UPResourceInfo *> *resources = [NSMutableArray array];
    for (NSArray<NSString *> *info in infoList) {
        UPResourceInfo *resInfo = [[UPResourceInfo alloc] init];
        resInfo.itemId = info[0].integerValue;
        resInfo.type = [UPResourceInfo typeValueOfString:info[1]];
        resInfo.name = info[2];
        resInfo.version = info[3];
        resInfo.link = info[4];
        resInfo.hashStr = info[5];
        resInfo.path = info[6];
        NSString *deviceInfoStr = info[7];
        if (isValidString(deviceInfoStr)) {
            NSArray *deviceInfoArr = [deviceInfoStr componentsSeparatedByString:@","];
            resInfo.model = deviceInfoArr[0];
            resInfo.typeId = deviceInfoArr[1];
            resInfo.deviceTypeIndex = deviceInfoArr[2];
            resInfo.prodNo = deviceInfoArr[3];
        }
        [resources addObject:resInfo];
    }
    return resources;
}

- (NSArray<UpPreloadResourceInfo *> *)createPreloadResourceInfoFromStepTableData:(NSDictionary *)userInfo
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<UpPreloadResourceInfo *> *preloadResInfoList = [NSMutableArray array];
    for (NSArray *info in infoList) {
        UpPreloadResourceInfo *resInfo = [[UpPreloadResourceInfo alloc] init];
        resInfo.name = info[1];
        resInfo.type = [UPResourceInfo typeValueOfString:info[0]];
        [preloadResInfoList addObject:resInfo];
    }
    return preloadResInfoList;
}

- (NSArray<UPResourceInfo *> *)createPreloadResourceCallbackInfoListFromStepTableData:(NSDictionary *)userInfo
{
    NSArray<NSArray<NSString *> *> *infoList = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray<UPResourceInfo *> *resList = [NSMutableArray array];
    for (NSArray<NSString *> *info in infoList) {
        UPResourceInfo *resInfo = [[UPResourceInfo alloc] init];
        resInfo.itemId = info[0].integerValue;
        resInfo.type = [UPResourceInfo typeValueOfString:info[1]];
        resInfo.name = info[2];
        resInfo.version = info[3];
        resInfo.link = info[4];
        resInfo.hashStr = info[5];
        resInfo.path = info[6];
        resInfo.success = isValidStringEqual(@"成功", info[7]);
        [resList addObject:resInfo];
    }
    return resList;
}

- (BOOL)isPreloadCallbackResInfoList:(NSArray<UPResourceInfo *> *)infoList equalToExpectList:(NSArray<UPResourceInfo *> *)expectList
{
    if (infoList.count != expectList.count) {
        return NO;
    }
    BOOL bResult = YES;
    for (UPResourceInfo *info in infoList) {
        BOOL bContain = NO;
        for (UPResourceInfo *expectInfo in expectList) {
            if ([info.name isEqualToString:expectInfo.name] && [info.version isEqualToString:expectInfo.version]) {
                bContain = YES;
                break;
            }
        }
        if (!bContain) {
            bResult = NO;
            break;
        }
    }
    return bResult;
}

- (BOOL)isConditions:(NSArray<UPResourceCondition *> *)conditions equalToConditions:(NSArray<UPResourceCondition *> *)expectConditions
{
    if (conditions.count != expectConditions.count) {
        return NO;
    }
    BOOL bResult = YES;
    for (UPResourceCondition *condition in conditions) {
        BOOL bContain = NO;
        for (UPResourceCondition *expectCondition in expectConditions) {
            if (isEqualCondition(condition, expectCondition)) {
                bContain = YES;
                break;
            }
        }
        if (!bContain) {
            bResult = NO;
            break;
        }
    }
    return bResult;
}

@end

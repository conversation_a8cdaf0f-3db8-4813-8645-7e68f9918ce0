//
//  UpdateSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/7/3.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UpdateSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"

@interface UpdateSteps ()
@property (nonatomic, strong) NSArray<UPResourceInfo *> *syncResourceInfoList;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *asyncResourceInfoList;
@property (nonatomic, strong) NSError *error;
@end


@implementation UpdateSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.syncResourceInfoList = nil;
      self.asyncResourceInfoList = nil;
      self.error = nil;
    });
    When(@"^调用更新\"([^\"]*)\"列表方法,资源类型为\"([^\"]*)\",资源名称为\"([^\"]*)\",设备信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *apiString = args[0];
      NSString *resTypeStr = args[1];
      NSString *resName = args[2];
      NSString *deviceInfo = args[3];
      void (^completion)(NSArray<UPResourceInfo *> *infoList, NSError *error) = ^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
        self.asyncResourceInfoList = infoList;
        self.error = error;
      };
      NSArray<NSString *> *deviceInfoArray = [deviceInfo componentsSeparatedByString:@","];
      if (isValidStringEqual(apiString, @"updateNormalResList")) {
          resName = isValidStringEqual(resName, @"空对象") ? nil : resName;
          [[ResourceHolder sharedInstance].resourceManager requestNormalResList:resName type:[UPResourceInfo typeValueOfString:resTypeStr] immediate:NO completion:completion];
          return;
      }
      UPResourceDeviceCondition *condition = nil;
      if (!isValidStringEqual(deviceInfo, @"空对象")) {
          NSString *model = deviceInfoArray[0];
          NSString *typeId = deviceInfoArray[1];
          NSString *typeCode = deviceInfoArray[2];
          NSString *prodNo = deviceInfoArray[3];
          NSString *deviceNetType = nil;
          if (deviceInfoArray.count >= 5)
              deviceNetType = deviceInfoArray[4];
          condition = [[UPResourceDeviceCondition alloc] initResourceType:[UPResourceInfo typeValueOfString:resTypeStr]];
          condition.resName = resName;
          condition.prodNo = prodNo;
          condition.model = model;
          condition.typeId = typeId;
          condition.deviceType = typeCode;
          condition.deviceNetType = deviceNetType;
      }
      [[ResourceHolder sharedInstance].resourceManager requestDeviceResList:condition immediate:NO completion:completion];

    });

    Then(@"^更新\"([^\"]*)\"列表异步操作结果为\"([^\"]*)\"，资源列表如下：$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[1];
      NSArray *array = jsonObjectsFromDataTable(userInfo);
      NSArray<UPResourceInfo *> *resultInfoList = [[ResourceHolder sharedInstance] convertResourceInfoList:array];
      NSArray<UPResourceInfo *> *infoList = self.asyncResourceInfoList;
      CCIAssert(isEqualResourcesList(infoList, resultInfoList), @"查询结果与预期一致");
      if (![resultStr isEqualToString:@"成功"]) {
          CCIAssert(self.error != nil, @"查询结果与预期一致");
      }
    });

    Then(@"^更新\"([^\"]*)\"列表返回的资源列表为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.syncResourceInfoList == nil, @"同步返回列表应为空对象，实际不为空");
    });

    Then(@"^更新\"([^\"]*)\"列表异步操作结果为失败$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert(self.error != nil, @"查询结果与预期一致");
    });
}
@end

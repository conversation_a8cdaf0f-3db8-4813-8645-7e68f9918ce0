//
//  InitializationSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/7/2.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "InitializationSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"


@implementation InitializationSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      [[ResourceHolder sharedInstance] disposeResourceManager];
    });
    Given(@"^初始化资源管理器,清理器为\"([^\"]*)\",调度器为\"([^\"]*)\",APP版本号为\"([^\"]*)\",数据存储的根路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *cleanerString = args[0];
      BOOL isSimulatedCleaner = [cleanerString isEqualToString:@"模拟的"];
      NSString *appVersion = args[2];
      NSString *rootPath = args[3];
      [[ResourceHolder sharedInstance] initResourceManagerWithAppVersion:appVersion resRootPath:rootPath simulatedCleaner:isSimulatedCleaner];
    });
    Given(@"^初始化资源管理器,清理器为\"([^\"]*)\",APP版本号为\"([^\"]*)\",数据存储的根路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *cleanerString = args[0];
      BOOL isSimulatedCleaner = [cleanerString isEqualToString:@"模拟的"];
      NSString *appVersion = args[1];
      NSString *rootPath = args[2];
      [[ResourceHolder sharedInstance] initResourceManagerWithAppVersion:appVersion resRootPath:rootPath simulatedCleaner:isSimulatedCleaner];
    });
    When(@"初始化资源管理器对象,各参数是否为空对象的情况如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *tableListData = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<NSString *> *params = tableListData.firstObject;
      [[ResourceHolder sharedInstance] initResourceManagerWithTableDataListParamters:params];
    });
    Then(@"^资源管理器初始化\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectResult = args[0];
      BOOL bIsInstance = [[ResourceHolder sharedInstance].resourceManager isKindOfClass:[UPResourceManager class]];
      if ([expectResult isEqualToString:@"失败"]) {
          CCIAssert(bIsInstance == NO, @"资源管理器初始化不为空！");
      }
      else {
          CCIAssert(bIsInstance, @"资源管理器初始化为空！");
      }
    });
}
@end

//
//  TaskResourceSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/19.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "TaskResourceSteps.h"
#import <XCTest/XCTest.h>
#import <OCMock/OCMock.h>
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import "UPResourceTracker.h"
#import "UPResourceConfig.h"
#import "CommonResCallbackImp.h"

@interface TaskResourceSteps ()
@property (nonatomic, strong) ResourceHolder *resourceHolder;
@property (nonatomic, copy) NSString *taskId;
@property (nonatomic, assign) BOOL cancelTaskResult;
@property (nonatomic, assign) CGFloat waitTime;
@property (nonatomic, strong) CommonResCallbackImp *callback;
@property (nonatomic, strong) UPResourceReportInfo *reportInfo;

@end

@implementation TaskResourceSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.taskId = nil;
      self.cancelTaskResult = NO;
      self.waitTime = 5.0;
      self.reportInfo = nil;
      self.resourceHolder = [ResourceHolder sharedInstance];
    });
    Given(@"^资源安装结果上报代理使用模拟的$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.resourceHolder.resourceManager.tracker = OCMProtocolMock(@protocol(UPResourceTracker));
      void (^proxyBlock)(NSInvocation *invocation) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *r;
        [invocation getArgument:&r atIndex:2];
        self.reportInfo = (__bridge UPResourceReportInfo *)r;
      };
      OCMStub([[ResourceHolder sharedInstance].resourceManager.tracker reportInstallResult:[OCMArg any]]).andDo(proxyBlock);
    });
    Given(@"^设置用户id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *userId = args[0];
      [UPResourceConfig shareInstance].user_id = userId;
    });
    Given(@"^设置客户端id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *clientId = args[0];
      [UPResourceConfig shareInstance].clientID = clientId;
    });

    When(@"^调用安装资源方法,传入资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *infoList = [self convertTaskResInfoListByUserInfo:userInfo];
      CommonResCallbackImp *callBack = nil;
      if (infoList.count == 0) {
          callBack = [[CommonResCallbackImp alloc] initWithIdentifier:[NSString stringWithFormat:@"callBack"]];
          self.taskId = [self.resourceHolder.resourceManager install:nil callback:callBack listener:nil];
      }
      else {
          for (UPResourceInfo *resInfo in infoList) {
              callBack = [[CommonResCallbackImp alloc] initWithIdentifier:[NSString stringWithFormat:@"callBack%@", resInfo.name]];
              self.taskId = [self.resourceHolder.resourceManager install:resInfo callback:callBack listener:nil];
          }
      }
      self.callback = callBack;

    });
    Then(@"^安装资源的任务id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *taskId = args[0];
      [self assertTaskId:taskId];
    });
    Then(@"^安装资源的异步回调结果为\"([^\"]*)\",资源包为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *resInfo = args[1];
      [self waitForExpectations:@"安装资源的异步回调结果"];
      [self assertInstallResult:result];
      if ([resInfo isEqualToString:@"空对象"]) {
          CCIAssert(self.callback.info == nil, @"资源包应为空，实际不为空");
      }
    });
    Then(@"^安装资源的异步回调结果为\"([^\"]*)\",资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [self waitForExpectations:@"安装资源的异步回调结果"];
      [self assertInstallResult:result];
      NSArray<UPResourceInfo *> *infoList = [self convertTaskResInfoListByUserInfo:userInfo];
      CCIAssert(isEqualResource(infoList.lastObject, self.callback.info), @"资源包应相同，实际不为相同");
    });
    When(@"^调用卸载资源方法,传入资源包为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resourceStr = args[0];
      if (isValidStringEqual(resourceStr, @"空对象")) {
          UPResourceInfo *uninstallInfo = nil;
          self.callback = [[CommonResCallbackImp alloc] initWithIdentifier:@"callBack"];
          self.taskId = [self.resourceHolder.resourceManager uninstall:uninstallInfo callback:self.callback];
      }
    });
    When(@"^调用卸载资源方法,传入资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *infoList = [self convertTaskResInfoListByUserInfo:userInfo];
      self.callback = [[CommonResCallbackImp alloc] initWithIdentifier:@"callBack"];
      self.taskId = [self.resourceHolder.resourceManager uninstall:infoList.firstObject callback:self.callback];
    });
    Then(@"^卸载资源的任务id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *taskId = args[0];
      [self assertTaskId:taskId];
    });
    Then(@"^卸载资源的异步回调结果为\"([^\"]*)\",资源包为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSString *resInfo = args[1];
      [self waitForExpectations:@"异步卸载资源列表结果回调"];
      [self assertUnintallResult:result];
      if ([resInfo isEqualToString:@"空对象"]) {
          CCIAssert(self.callback.info == nil, @"资源包应为空，实际不为空");
      }
    });
    Then(@"^卸载资源的异步回调结果为\"([^\"]*)\",资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      [self waitForExpectations:@"异步卸载资源列表结果回调"];
      [self assertUnintallResult:result];
      NSArray *infoList = [self convertTaskResInfoListByUserInfo:userInfo];
      CCIAssert(isEqualResource(self.callback.info, infoList.firstObject), @"卸载结果与预期一致,实际不一致");
    });
    When(@"^调用取消任务方法,传入参数任务id为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *taskId = args[0];
      if ([taskId isEqualToString:@"空对象"]) {
          taskId = nil;
      }
      else if ([taskId isEqualToString:@"空字符串"]) {
          taskId = @"";
      }
      self.cancelTaskResult = [self.resourceHolder.resourceManager cancel:taskId];
    });
    Then(@"^取消任务的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *cancelResult = args[0];
      BOOL expectedResult = isValidStringEqual(cancelResult, @"成功");
      BOOL actualResult = self.cancelTaskResult;
      CCIAssert(actualResult == expectedResult, @"取消任务结果与实际不一致,期望:%@,实际:%@", cancelResult, actualResult ? @"成功" : @"失败");
    });
    When(@"^重新启动，使用异步线程调度器$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //iOS忽略
         });

    When(@"^等待\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *waitTime = args[0];
      [XCTWaiter waitForExpectations:@[ [[XCTestExpectation alloc] initWithDescription:@"等待异步回调结果"] ] timeout:waitTime.floatValue enforceOrder:NO];
    });

    When(@"^调用取消任务方法,传入参数为已保存的任务id$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.cancelTaskResult = [self.resourceHolder.resourceManager cancel:self.taskId];
    });
    When(@"^调用获取正在执行的任务id方法,传入资源包为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resourceStr = args[0];
      if (isValidStringEqual(resourceStr, @"空对象")) {
          UPResourceInfo *info = nil;
          self.taskId = [self.resourceHolder.resourceManager getTaskIdByInfo:info];
      }
    });
    Then(@"^获取正在执行的任务id的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *taskId = args[0];
      [self assertTaskId:taskId];
    });
    When(@"^调用获取正在执行的任务id方法,传入资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *infoList = [self convertTaskResInfoListByUserInfo:userInfo];
      self.taskId = [self.resourceHolder.resourceManager getTaskIdByInfo:infoList.firstObject];
    });
    Then(@"^资源安装结果上报接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceManager.tracker reportInstallResult:[OCMArg any]]);
    });
    Then(@"^资源安装结果上报接口被调用\"([^\"]*)\"次,参数资源名称为\"([^\"]*)\",类型为\"([^\"]*)\",版本为\"([^\"]*)\",是否预置为\"([^\"]*)\",用户ID为\"([^\"]*)\",客户端ID为\"([^\"]*)\",app版本号为\"([^\"]*)\",安装时间为\"([^\"]*)\",灰度为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceManager.tracker reportInstallResult:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      [self assertResourceReportInfo:args];
    });

    Then(@"^资源下载结果上报接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceManager.tracker reportABTestTrack:[OCMArg any]]);
    });
}
#pragma mark private Methods
- (void)assertTaskId:(NSString *)taskId
{
    if ([taskId isEqualToString:@"空对象"]) {
        CCIAssert(self.taskId == nil, @"任务id应为空，实际不为空");
    }
    else {
        CCIAssert(self.taskId != nil, @"任务id应不为空，实际为空");
    }
}
- (NSArray<UPResourceInfo *> *)convertTaskResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [self.resourceHolder convertResourceInfoList:array];
}
- (void)waitForExpectations:(NSString *)desc
{
    [XCTWaiter waitForExpectations:@[ self.callback.expectation ] timeout:self.waitTime enforceOrder:NO];
}
- (void)assertUnintallResult:(NSString *)result
{
    if ([result isEqualToString:@"失败"]) {
        CCIAssert(self.callback.result == NO, @"卸载结果应失败，实际为成功");
    }
    else {
        CCIAssert(self.callback.result == YES, @"卸载结果应成功，实际为失败");
    }
}
- (void)assertInstallResult:(NSString *)result
{
    if ([result isEqualToString:@"失败"]) {
        CCIAssert(self.callback.result == NO, @"安装结果应失败，实际为成功");
    }
    else {
        CCIAssert(self.callback.result == YES, @"安装结果应成功，实际为失败");
    }
}

- (void)assertResourceReportInfo:(NSArray<NSString *> *)args
{
    NSString *resName = args[1];
    NSString *resType = args[2];
    NSString *resVersion = args[3];
    NSString *isPreset = [args[4] isEqualToString:@"YES"] ? @"true" : @"false";
    NSString *userId = args[5];
    NSString *clientId = args[6];
    NSString *appversion = args[7];
    NSString *installTime = args[8];
    NSString *isGray = args[9];
    NSString *grayStr = [isGray isEqualToString:@"NO"] ? @"false" : @"true";
    UPResourceReportInfo *reportInfo = self.reportInfo;
    CCIAssert(isValidStringEqual(resName, reportInfo.res_name), @"资源名称应相等，实际不相等");
    CCIAssert(isValidStringEqual(resType, reportInfo.res_type), @"资源类型应相等，实际不相等");
    CCIAssert(isValidStringEqual(resVersion, reportInfo.res_version), @"资源版本应相等，实际不相等");
    CCIAssert(isValidStringEqual(isPreset, reportInfo.is_preset), @"是否预置资源应相等，实际不相等");
    CCIAssert(isValidStringEqual(appversion, reportInfo.app_version), @"app版本应相等，实际不相等");
    CCIAssert(isValidStringEqual(clientId, reportInfo.client_id), @"客户端id应相等，实际不相等");
    CCIAssert(isValidStringEqual(userId, reportInfo.user_id), @"用户id应相等，实际不相等");
    CCIAssert(isValidStringEqual(installTime, reportInfo.installed_time), @"安装时间应相等，实际不相等");
    CCIAssert(isValidStringEqual(grayStr, reportInfo.is_gray), @"环境设置与实际不相等");
}
@end

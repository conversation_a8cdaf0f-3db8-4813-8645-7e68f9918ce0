//
//  ReportResLoadedSteps.m
//  ResDebuggerTests
//
//  Created by 吴子航 on 2023/3/17.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ReportResLoadedSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import <OCMock/OCMock.h>
#import "UPRLoadedReportInfo.h"
#import "UPRLoadReporterImp.h"

@interface ReportResLoadedSteps ()

@property (nonatomic, strong) ResourceHolder *resourceHolder;
@property (nonatomic, strong) NSMutableArray<NSArray<UPRLoadedReportInfo *> *> *reportInfos;

@end

@implementation ReportResLoadedSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.resourceHolder = [ResourceHolder sharedInstance];
      self.reportInfos = [NSMutableArray array];
    });

    Given(@"^调用上报加载资源信息接口结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *datas;
        [invocation getArgument:&datas atIndex:2];
        [self.reportInfos addObject:(__bridge NSArray *)datas];
        BOOL isUploadSuccess = isValidStringEqual(args[0], @"成功");
        void *callBackPointer;
        [invocation getArgument:&callBackPointer atIndex:3];
        void (^callBack)(NSError *) = (__bridge void (^)(NSError *))callBackPointer;
        callBack(isUploadSuccess ? nil : [NSError errorWithDomain:@"mockFailure" code:-1 userInfo:nil]);
      };
      OCMStub([self.resourceHolder.resourceDataSource reportResLoadedInfo:[OCMArg any] completion:[OCMArg any]]).andDo(proxyBlock);
    });

    Given(@"^调用上报加载资源信息接口等待\"([^\"]*)\"秒后返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *datas;
        [invocation getArgument:&datas atIndex:2];
        [self.reportInfos addObject:(__bridge NSArray *)datas];
        BOOL isUploadSuccess = isValidStringEqual(args[1], @"成功");
        void *callBackPointer;
        [invocation getArgument:&callBackPointer atIndex:3];
        void (^callBack)(NSError *) = (__bridge void (^)(NSError *))callBackPointer;
        dispatch_time_t time = dispatch_time(DISPATCH_TIME_NOW, (int64_t)args[0].integerValue * NSEC_PER_SEC);
        dispatch_after(time, dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
          callBack(isUploadSuccess ? nil : [NSError errorWithDomain:@"mockFailure" code:-1 userInfo:nil]);
        });
      };
      OCMStub([self.resourceHolder.resourceDataSource reportResLoadedInfo:[OCMArg any] completion:[OCMArg any]]).andDo(proxyBlock);
    });

    When(@"^调用上报加载资源信息接口,参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSDictionary *reportInfo = jsonObjectsFromDataTable(userInfo).firstObject;
      NSString *resName = reportInfo[@"Name"];
      NSString *resVersion = reportInfo[@"Version"];
      UPResourceType resType = [UPResourceInfo typeValueOfString:reportInfo[@"Type"]];
      [self.resourceHolder.resourceManager reportResourceLoaded:resName type:resType version:resVersion];
    });

    When(@"^上报\"([^\"]*)\"条资源加载信息$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger reportTimes = args[0].integerValue;
      for (NSInteger i = 0; i < reportTimes; i++) {
          [self.resourceHolder.resourceManager reportResourceLoaded:@"resA" type:UPResourceTypeMPAAS version:@"1.0.0"];
      }
    });

    Then(@"^资源数据源的上报加载资源信息接口被调用\"([^\"]*)\"次,上报数据如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *expectedArray = jsonObjectsFromDataTable(userInfo);
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      NSArray *actualArray = self.reportInfos.count != 0 ? self.reportInfos[expectedInvocationTimes - 1] : nil;
      CCIAssert(compareReportInfo(transformReportInfoFromJson(expectedArray), actualArray), @"上报结果与预期不一致，实际结果为%@--预期结果为%@", actualArray, expectedArray);
      OCMVerify(times(expectedInvocationTimes), [self.resourceHolder.resourceDataSource reportResLoadedInfo:[OCMArg any] completion:[OCMArg any]]);
    });

    Then(@"^资源数据源的上报加载资源信息接口被调用\"([^\"]*)\"次,上报数据条数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      NSUInteger expectedReportCount = args[1].integerValue;
      NSArray *actualArray = self.reportInfos.count != 0 ? self.reportInfos[expectedInvocationTimes - 1] : nil;
      CCIAssert(expectedReportCount == actualArray.count, @"上报条数与预期不一致，实际结果为%@--预期结果为%@", actualArray.count, expectedReportCount);
    });
}

@end

//
//  RelationSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/3/6.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "RelationSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "StepUtils.h"
#import "UPResourceInfo.h"
#import "ResourceHolder.h"
#import "UPRelationDelegate.h"

#define RelationDelegate [ResourceHolder sharedInstance].resourceManager.relationDelegate

@interface RelationModel : NSObject
@property (nonatomic, strong) UPResourceQuery *query;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *infoList;
@property (nonatomic, assign) BOOL isPreset;
@end

@implementation RelationModel

- (instancetype)initWithQuery:(UPResourceQuery *)query isPreset:(BOOL)isPreset infoList:(NSArray *)infoList
{
    if (self = [super init]) {
        _query = query;
        _isPreset = isPreset;
        _infoList = infoList;
    }
    return self;
}

@end

@interface RelationSteps ()

@property (nonatomic, strong) NSMutableArray<RelationModel *> *relationParams;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *relationResult;

@end

@implementation RelationSteps

- (void)defineStepsAndHocks
{

    before(^(CCIScenarioDefinition *scenario) {
      self.relationParams = [NSMutableArray array];
      self.relationResult = nil;
    });
    Given(@"^关联资源关系代理使用模拟的", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      id<UPRelationDelegate> relationDelegate = OCMProtocolMock(@protocol(UPRelationDelegate));
      [[ResourceHolder sharedInstance].resourceManager setValue:relationDelegate forKey:@"relationDelegate"];
    });
    Given(@"^关联资源关系接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL success = isValidStringEqual(result, @"成功");
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        RelationModel *model = [self convertRelationModelWithInvocation:invocation];
        [self.relationParams addObject:model];
        NSArray *result = success ? model.infoList : nil;
        [invocation setReturnValue:&result];
      };
      OCMStub([RelationDelegate relationResList:[OCMArg any] isPreset:YES infoList:[OCMArg any]]).andDo(proxyBlock).ignoringNonObjectArgs;
    });
    Then(@"^资源关联关系接口被调用\"([^\"]*)\"次,传入预置标识为\"([^\"]*)\",参数如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger expectedTime = args[0].integerValue;
      BOOL isPreset = isValidStringEqual(args[1], @"是");
      OCMVerify(times(expectedTime), [RelationDelegate relationResList:[OCMArg any] isPreset:isPreset infoList:[OCMArg any]]);
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *expectedInfoList = [self convertRelationResInfoListByUserInfo:userInfo];
      for (int i = 0; i < array.count; i++) {
          NSString *expectedQueryInfoString = array[i][0];
          UPResourceQuery *expectedQuery = combineQuery(expectedQueryInfoString);
          UPResourceQuery *actualQuery = self.relationParams[i].query;
          CCIAssert(isEqualQuery(expectedQuery, actualQuery), @"资源关联关系接口的查询条件参数与实际不一致");
          NSArray<UPResourceInfo *> *actualInfoList = self.relationParams[i].infoList;
          BOOL isEqual = isEqualResourcesList(actualInfoList, @[ expectedInfoList[i] ]);
          CCIAssert(isEqual, @"资源关联关系接口的资源信息列表参数与预期不一致。");
      }
    });
    Then(@"^资源关联关系接口被调用\"([^\"]*)\"次,传入预置标识为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      BOOL isPreset = isValidStringEqual(args[1], @"是");
      id<UPRelationDelegate> relationDelegate = [[ResourceHolder sharedInstance].resourceManager valueForKey:@"relationDelegate"];
      OCMVerify(times(invocationTimes.integerValue), [relationDelegate relationResList:[OCMArg any] isPreset:isPreset infoList:[OCMArg any]]);
    });
    Then(@"^资源关联关系接口被调用\"([^\"]*)\"次,传入预置标识为\"([^\"]*)\",参数查询条件为\"([^\"]*)\",资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      BOOL isPreset = isValidStringEqual(args[1], @"是");
      OCMVerify(times(invocationTimes.integerValue), [RelationDelegate relationResList:[OCMArg any] isPreset:isPreset infoList:[OCMArg any]]);

    });

    When(@"^调用关联资源列表和查询关系方法,传入预置标识为\"([^\"]*)\",查询关系为\"([^\"]*)\",资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL isPreset = isValidStringEqual(args[0], @"是");
      BOOL isQueryStrNil = isValidStringEqual(args[1], @"空对象");
      NSString *queryStr = args[1];
      NSArray<UPResourceInfo *> *infoList = [self convertResInfoListByUserInfo:userInfo];
      UPResourceQuery *query = isQueryStrNil ? nil : combineQuery(queryStr);
      self.relationResult = [RelationDelegate relationResList:query isPreset:isPreset infoList:infoList];
    });

    When(@"^调用关联资源列表和查询关系方法,传入预置标识为\"([^\"]*)\",查询关系为\"([^\"]*)\",资源列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL isPreset = [args[0] isEqualToString:@"是"];
      NSString *queryStr = args[1];
      NSArray<UPResourceInfo *> *infoList;
      if (isValidStringEqual(args[2], @"空列表")) {
          infoList = [NSArray array];
      }
      else if (isValidStringEqual(args[2], @"空对象")) {
          infoList = nil;
      }
      UPResourceQuery *query = combineQuery(queryStr);
      self.relationResult = [RelationDelegate relationResList:query isPreset:isPreset infoList:infoList];
    });

    Then(@"^关联资源列表和查询关系返回的资源列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      NSArray *infoList = self.relationResult;
      if ([result isEqualToString:@"空对象"]) {
          CCIAssert(infoList == nil, @"关联结果应为空对象，实际不为空对象");
      }
      else {
          CCIAssert(infoList != nil && infoList.count == 0, @"关联结果应为空列表，实际不为空列表");
      }
    });

    Then(@"^关联资源列表和查询关系返回的资源列表如下:", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *infoList = [self convertResInfoListByUserInfo:userInfo];
      NSArray<UPResourceInfo *> *realInfoList = self.relationResult;
      CCIAssert(isEqualResourcesList(realInfoList, infoList), @"关联资源列表和查询关系返回的资源列表与预期不一致,实际为%@,预期为%@", realInfoList, infoList);
    });
}
#pragma mark private Methods
- (NSArray<UPResourceInfo *> *)convertRelationResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
    NSMutableArray *expInfoList = [NSMutableArray array];
    for (NSArray *arr in array) {
        UPResourceInfo *info = [[UPResourceInfo alloc] init];
        info.type = [UPResourceInfo typeValueOfString:arr[1]];
        info.name = arr[2];
        info.version = arr[3];
        [expInfoList addObject:info];
    }
    return expInfoList;
}

- (RelationModel *)convertRelationModelWithInvocation:(NSInvocation *)invocation
{
    [invocation retainArguments];
    void *q;
    [invocation getArgument:&q atIndex:2];
    UPResourceQuery *query = (__bridge UPResourceQuery *)q;
    BOOL isPreset;
    [invocation getArgument:&isPreset atIndex:3];
    void *i;
    [invocation getArgument:&i atIndex:4];
    NSArray *infoList = (__bridge NSArray *)i;
    RelationModel *model = [[RelationModel alloc] initWithQuery:query isPreset:isPreset infoList:infoList];
    return model;
}

- (NSArray<UPResourceInfo *> *)convertResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [[ResourceHolder sharedInstance] convertResourceInfoList:array];
}

@end

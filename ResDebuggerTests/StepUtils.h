//
//  StepUtils.h
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@class UPResourceInfo;
@class UPResourceQuery;
@class UPResourceCondition;
@class UPRLoadedReportInfo;
NS_ASSUME_NONNULL_BEGIN

BOOL isValidString(NSString *str);
BOOL isValidStringEqual(NSString *str, NSString *str2);
BOOL isEqualQuery(UPResourceQuery *query, UPResourceQuery *query2);
BOOL isEqualCondition(UPResourceCondition *condition, UPResourceCondition *condition2);
BOOL isValidArray(NSArray *array);
BOOL isStringArrayEqual(NSArray *array, NSArray *anotherArray);
BOOL isValidArrayOfInstances(NSArray *array, Class instanceClass);
NSArray<NSArray<NSString *> *> *getTableDataListFromExpectUserInfo(NSDictionary *userInfo);
NSArray<NSDictionary<NSString *, NSString *> *> *jsonObjectsFromDataTable(NSDictionary *userInfo);
BOOL isEqualResource(UPResourceInfo *resource, UPResourceInfo *anotherResource);
BOOL isDeviceResourceInfoEqual(UPResourceInfo *resource, UPResourceInfo *anotherResource);
BOOL isResourcesContains(NSArray<UPResourceInfo *> *resources, UPResourceInfo *resource);
BOOL isEqualResourcesList(NSArray<UPResourceInfo *> *resources, NSArray<UPResourceInfo *> *anotherResources);
BOOL isEqualQueryList(NSArray<UPResourceQuery *> *querys, NSArray<UPResourceQuery *> *anotherQuerys);
BOOL isQuerysContains(NSArray<UPResourceQuery *> *querys, UPResourceQuery *query);
BOOL isEqualConditionList(NSArray<UPResourceCondition *> *resourceCondition, NSArray<UPResourceCondition *> *anotherResourceCondition);

NSString *combineStrings(NSArray *strings);
NSString *combineStringsWithSeparator(NSArray *strings, NSString *separator);
NSString *identifierOfResource(UPResourceInfo *info);
NSString *wholeIdentifierOfResource(UPResourceInfo *info);
UPResourceInfo *createResourceInfoFromPropertiesInfoArray(NSArray<NSString *> *propertiesInfoArray);
UPResourceQuery *combineQuery(NSString *queryStr);
UPResourceCondition *combineCondition(NSString *conditionStr);
NSString *conditionStringOfCondition(UPResourceCondition *condition);
NSString *validString(NSString *str);
NSArray<UPResourceQuery *> *getCombineQueryList(NSArray *queryArray);
UPResourceCondition *combineNormalCondition(NSString *conditionStr);
NSArray<UPResourceInfo *> *filterExpectInfos(NSDictionary *userInfo, NSInteger expectedInvocationTimes);
NSArray<UPRLoadedReportInfo *> *transformReportInfoFromJson(NSArray *array);
BOOL compareReportInfo(NSArray<UPRLoadedReportInfo *> *list1, NSArray<UPRLoadedReportInfo *> *list2);
NS_ASSUME_NONNULL_END

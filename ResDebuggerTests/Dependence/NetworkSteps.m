//
//  NetworkSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "NetworkSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "ResourceHolder.h"
#import "StepUtils.h"

@implementation NetworkSteps

- (void)defineStepsAndHocks
{

    Given(@"^设置系统网络连接为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *connection = args.firstObject;
      UPConnectionType connectionType = TYPE_NONE;
      if ([connection isEqualToString:@"wifi"]) {
          connectionType = TYPE_WIFI;
      }
      else if ([connection isEqualToString:@"mobile"]) {
          connectionType = TYPE_MOBILE;
      }
      else {
          connectionType = TYPE_NONE;
      }
      OCMStub([[ResourceHolder sharedInstance].connectionDelegate getConnectionType]).andReturn(connectionType);
    });

    Given(@"^设置系统网络状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL available = isValidStringEqual(args[0], @"可用");
      OCMStub([[ResourceHolder sharedInstance].connectionDelegate isAvailable]).andReturn(available);
    });
}

@end

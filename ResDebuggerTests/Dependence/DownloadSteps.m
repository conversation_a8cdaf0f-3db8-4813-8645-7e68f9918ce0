//
//  DownloadSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/7/3.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DownloadSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import "UPDownloadHandle.h"
#import "UPDownloadCallback.h"

@interface DownloadSteps ()
@property (nonatomic, strong) NSMutableArray<NSString *> *links;
@end

@implementation DownloadSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.links = [NSMutableArray array];
    });
    Given(@"^从网络下载资源链接\"([^\"]*)\"操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [self.links addObject:args[0]];
      NSString *downloadResult = args[1];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *h;
        [invocation getArgument:&h atIndex:2];
        UPDownloadHandle *handle = (__bridge UPDownloadHandle *)h;
        if (isValidStringEqual(downloadResult, @"成功")) {
            [handle.callback onTaskSuccess:handle];
        }
        else if (isValidStringEqual(downloadResult, @"失败")) {
            [handle.callback onTaskFailure:handle error:[NSError errorWithDomain:@"下载失败" code:-1 userInfo:nil]];
        }
        else if (isValidStringEqual(downloadResult, @"取消")) {
            [handle.callback onTaskCancel:handle];
        }
      };
      OCMStub([[ResourceHolder sharedInstance].downloadDelegate start:[OCMArg any]]).andDo(proxyBlock);
    });
    Then(@"^从网络下载资源链接接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].downloadDelegate start:[OCMArg any]]);
    });
    Then(@"^从网络下载资源链接接口被调用\"([^\"]*)\"次,参数链接为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *link = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].downloadDelegate start:[OCMArg any]]);
      if (expectedInvocationTimes > 0) {
          CCIAssert(isValidStringEqual(self.links.firstObject, link), @"网络下载资源链接参数应该为:%@,实际为:%@", link, self.links.firstObject);
      }
    });
    Given(@"^网络下载资源等待\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *durationStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *h;
        [invocation getArgument:&h atIndex:2];
        UPDownloadHandle *handle = (__bridge UPDownloadHandle *)h;
        [self.links addObject:handle.link];
        [NSThread sleepForTimeInterval:durationStr.floatValue];
        [handle.callback onTaskSuccess:handle];
      };
      OCMStub([[ResourceHolder sharedInstance].downloadDelegate start:[OCMArg any]]).andDo(proxyBlock);
    });

    Then(@"^正在下载任务的Link列表为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      CCIAssert(self.links.count == array.count, @"正在下载的任务与期望不一致,期望:%d, 实际:%d", array.count, self.links.count);
      for (NSArray *link in array) {
          CCIAssert([self.links containsObject:link.firstObject], @"期望%@正在下载,实际没有在执行", link.firstObject);
      }
    });
}
@end

//
//  DataBaseSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/15.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DataBaseSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "ResourceHolder.h"
#import "StepUtils.h"
#import <OCMock/OCMock.h>

@interface DataBaseSteps ()

@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *resultInfoList;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *actualSearchList;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *actualInsertList;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *actualUpdateList;
@property (nonatomic, strong) NSMutableArray<UPResourceInfo *> *actualDeleteList;
@property (nonatomic, strong) NSMutableArray<UPResourceQuery *> *actualQueryList;
@property (nonatomic, strong) NSMutableArray<NSArray *> *queryAndResInfoRelations;
@property (nonatomic, strong) NSMutableDictionary *saveMarryInfoList;
@property (nonatomic, assign) BOOL deleteRelationResult;
@property (nonatomic, assign) BOOL isFirstCallSearchResInOnceScenario;

@end

@implementation DataBaseSteps

- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.resultInfoList = [NSMutableArray array];
      self.actualDeleteList = [NSMutableArray array];
      self.actualQueryList = [NSMutableArray array];
      self.actualInsertList = [NSMutableArray array];
      self.actualSearchList = [NSMutableArray array];
      self.actualUpdateList = [NSMutableArray array];
      self.queryAndResInfoRelations = [NSMutableArray array];
      self.saveMarryInfoList = [NSMutableDictionary dictionary];
      self.deleteRelationResult = NO;
      self.isFirstCallSearchResInOnceScenario = YES;

    });

    Given(@"^本地数据存储代理的清空所有数据接口持续时长为\"([^\"]*)\"秒.$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *secondsString = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        if (secondsString.floatValue > 0) {
            [NSThread sleepForTimeInterval:secondsString.floatValue];
        }
        id result = @(YES);
        [invocation setReturnValue:&result];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].databaseDelegate stub] andDo:proxyBlock] emptyAllData];
    });
    Given(@"^本地数据存储代理的清空所有数据接口结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL bResult = isValidStringEqual(resultString, @"成功");
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate emptyAllData]).andReturn(bResult);
    });

    Given(@"^更新资源\"([^\"]*)\"到数据库的操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *updateResult = args[1];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *i;
        [invocation getArgument:&i atIndex:2];
        [self.actualUpdateList addObject:(__bridge UPResourceInfo *)i];
        BOOL result = isValidStringEqual(updateResult, @"成功");
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]).andDo(proxyBlock);
    });
    Given(@"^本地数据库更新资源,操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *i;
        [invocation getArgument:&i atIndex:2];
        [self.actualUpdateList addObject:(__bridge UPResourceInfo *)i];
        BOOL result = isValidStringEqual(resultStr, @"成功");
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]).andDo(proxyBlock);
    });
    Then(@"^本地数据存储代理的清空所有数据接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate emptyAllData]);
    });

    Given(@"^数据库代理根据名字和类型查询所有的资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *infoList = [self convertResInfoListByUserInfo:userInfo];
      [self.resultInfoList addObjectsFromArray:infoList];
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceList:UPResourceTypeAll name:[OCMArg any]]).andReturn(self.resultInfoList);
    });

    Given(@"^数据库代理根据名字\"([^\"]*)\"类型\"([^\"]*)\"查询资源结果如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      NSString *type = args[1];
      UPResourceType resType = [UPResourceInfo typeValueOfString:type];
      NSArray *infoList = [self convertResInfoListByUserInfo:userInfo];
      [self.resultInfoList addObjectsFromArray:infoList];
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceList:resType name:name]).andReturn(infoList);
    });

    Given(@"^数据库代理中所有资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *infoList = [self convertResInfoListByUserInfo:userInfo];
      [self.resultInfoList removeAllObjects];
      [self.resultInfoList addObjectsFromArray:infoList];
    });

    Given(@"^数据库代理根据名字\"([^\"]*)\",类型\"([^\"]*)\",查询资源结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      NSString *typeStr = args[1];
      UPResourceType type = [UPResourceInfo typeValueOfString:typeStr];
      NSString *resultStr = args[2];
      NSArray *result = nil;
      if (isValidStringEqual(resultStr, @"空数组")) {
          result = @[];
      }
      else if (isValidStringEqual(resultStr, @"全部资源")) {
          result = [self.resultInfoList copy];
      }
      else {
          NSMutableArray *filterResult = [NSMutableArray array];
          for (UPResourceInfo *info in self.resultInfoList) {
              if (isValidStringEqual(name, info.name) && type == info.type) {
                  [filterResult addObject:info];
              }
          }
          result = [filterResult copy];
      }
      self.saveMarryInfoList[name] = result;
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *param;
        [invocation getArgument:&param atIndex:3];
        NSString *name = (__bridge NSString *)param ?: @"空对象";
        NSArray *array = self.saveMarryInfoList[name];
        [invocation setReturnValue:&array];
      };
      [[[[(OCMockObject *)[ResourceHolder sharedInstance].databaseDelegate stub] ignoringNonObjectArgs] andDo:proxyBlock] searchResourceList:0 name:[OCMArg any]];
    });

    Given(@"^数据库代理根据名字\"([^\"]*)\",类型\"([^\"]*)\",版本\"([^\"]*)\",查询资源结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      NSString *typeStr = args[1];
      UPResourceType type = [UPResourceInfo typeValueOfString:typeStr];
      NSString *version = args[2];
      NSString *resultStr = args[3];
      UPResourceInfo *result = nil;
      if (isValidStringEqual(resultStr, @"空对象")) {
          result = nil;
      }
      else {
          for (UPResourceInfo *info in self.resultInfoList) {
              if (isValidStringEqual(name, info.name) && type == info.type && isValidStringEqual(version, info.version)) {
                  result = info;
                  break;
              }
          }
      }
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]).andReturn(result).ignoringNonObjectArgs;
    });


    Given(@"^数据库代理根据名字\"([^\"]*)\"类型\"([^\"]*)\"版本\"([^\"]*)\"查询资源结果如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceInfo *info = [self convertResInfoListByUserInfo:userInfo].firstObject;
      info.preset = YES;
      UPResourceType type = [UPResourceInfo typeValueOfString:args[1]];
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:type name:args[0] version:args[2]]).andReturn(info);
    });

    Given(@"^本地数据存储查询资源第一次为空，第二次查询操作结果为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      UPResourceInfo *resInfo = [self convertResInfoListByUserInfo:userInfo].firstObject;
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        UPResourceInfo *actualInfo = [self cacheSearchResourceParam:invocation];
        UPResourceInfo *result = nil;
        if (isValidStringEqual(resInfo.name, actualInfo.name)) {
            result = self.isFirstCallSearchResInOnceScenario ? nil : resInfo;
            self.isFirstCallSearchResInOnceScenario = NO;
        }
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]).andDo(proxyBlock).ignoringNonObjectArgs;
    });

    Given(@"^本地数据存储查询资源第一次为空，第二次查询操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        UPResourceInfo *result = [self cacheSearchResourceParam:invocation];
        result = isValidStringEqual(resultStr, @"成功") ? result : nil;
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]).andDo(proxyBlock).ignoringNonObjectArgs;
    });

    Then(@"^数据库根据名字和类型查询接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceList:UPResourceTypeAll name:[OCMArg any]]);
    });

    Then(@"^数据库根据名字和类型查询接口被调用\"([^\"]*)\"次,参数名字为\"([^\"]*)\",类型为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[1];
      NSString *typeStr = args[2];
      NSString *invocationTimes = args[0];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      UPResourceType type = [UPResourceInfo typeValueOfString:typeStr];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceList:type name:name]);
    });

    Then(@"^数据库根据名字、类型和版本查询接口被调用\"([^\"]*)\"次,参数名字为\"([^\"]*)\",类型为\"([^\"]*)\",版本为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *times = args[0];
      NSString *name = args[1];
      NSString *type = args[2];
      NSString *version = args[3];
      OCMVerify(times(times.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:[UPResourceInfo typeValueOfString:type] name:name version:version]);
    });

    Given(@"^数据库查询请求条件接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *queryStr = args[0];
      UPResourceQuery *query = combineQuery(queryStr);
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any] appVersion:[OCMArg any] conditionStr:[OCMArg any]]).andReturn(query);
    });

    Then(@"数据库查询请求条件接口被调用\"([^\"]*)\"次,参数方法来源为\"([^\"]*)\",App版本号为\"([^\"]*)\",参数组合条件为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *fromFun = args[1];
      NSString *appversion = args[2];
      NSString *conditionStr = args[3];
      UPResourceCondition *condition = combineNormalCondition(conditionStr);
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:fromFun appVersion:appversion conditionStr:condition.combine]);
    });

    Then(@"^数据库查询请求条件接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any] appVersion:[OCMArg any] conditionStr:[OCMArg any]]);
    });
    Then(@"^数据库通过查询条件查询资源列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceList:[OCMArg any]]);
    });
    Then(@"^数据库通过查询条件查询资源列表接口被调用\"([^\"]*)\"次,参数查询条件为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *expectedQueryInfoString = args[1];
      UPResourceQuery *expectQuery = combineQuery(expectedQueryInfoString);
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceList:[OCMArg any]]);
      if (invocationTimes.integerValue > 0) {
          CCIAssert(isEqualQuery(expectQuery, self.actualQueryList.firstObject), @"数据库通过查询条件查询资源列表接口参数应该相同，实际不相同");
      }
    });
    Given(@"^数据库代理查询所有查询条件列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSArray *allQuerys = getCombineQueryList(array);
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchAllQuerys]).andReturn(allQuerys);
    });
    Given(@"^查询条件为\"([^\"]*)\"相关联的资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *result = [self convertResInfoListByUserInfo:userInfo];
      [self.resultInfoList addObjectsFromArray:result];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *l;
        [invocation getArgument:&l atIndex:2];
        UPResourceQuery *query = (__bridge UPResourceQuery *)l;
        [self.actualQueryList addObject:query];
        NSArray *list = result;
        [invocation setReturnValue:&list];
      };

      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceList:[OCMArg any]]).andDo(proxyBlock);
    });
    Given(@"^查询条件为\"([^\"]*)\",查询时间为\"([^\"]*)\"相关联的资源列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *queryStr = args[0];
      NSString *queryTime = args[1];
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      UPResourceQuery *query = combineQuery(queryStr);
      query.createTime = queryTime.longLongValue / 1000;
      query.updateTime = queryTime.longLongValue / 1000;
      [self.resultInfoList addObjectsFromArray:resultInfoList];
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any] appVersion:[OCMArg any] conditionStr:[OCMArg any]]).andReturn(query);
    });
    Given(@"^数据通过查询条件查询资源列表接口执行结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *result = args[0];
      BOOL success = isValidStringEqual(result, @"成功");
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *param;
        [invocation getArgument:&param atIndex:2];
        UPResourceQuery *query = (__bridge UPResourceQuery *)param;
        [self.actualQueryList addObject:query];
        NSArray *list = nil;
        if (success) {
            list = self.resultInfoList;
        }
        [invocation setReturnValue:&list];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].databaseDelegate stub] andDo:proxyBlock] searchResourceList:[OCMArg any]];
    });

    Given(@"^本地数据库存储删除关系接口结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.deleteRelationResult = isValidStringEqual(args[0], @"成功");
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *q;
        [invocation getArgument:&q atIndex:2];
        [self.actualQueryList addObject:(__bridge UPResourceQuery *)q];
        BOOL result = self.deleteRelationResult;
        [invocation setReturnValue:&result];
      };

      OCMStub([[ResourceHolder sharedInstance].databaseDelegate deleteRelation:[OCMArg any]]).andDo(proxyBlock);
    });

    Then(@"^本地数据存储删除关系接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate deleteRelation:[OCMArg any]]);
    });
    Then(@"^本地数据存储插入资源接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate insertResourceInfo:[OCMArg any]]);
    });
    Then(@"^本地数据存储查询资源接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]);
    });
    Then(@"^本地数据存储更新资源接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]);
    });
    Then(@"^本地数据存储插入关系接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate insertRelationBetweenQuery:[OCMArg any] andResourceInfo:[OCMArg any]]);
    });
    Then(@"^本地数据存储事务成功接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //iOS忽略
         });
    Given(@"^本地数据存储删除查询关系为\"([^\"]*)\"的操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL result = isValidStringEqual(args[1], @"成功");
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate deleteRelation:[OCMArg any]]).andReturn(result);
    });
    Then(@"^本地数据存储删除关系接口被调用\"([^\"]*)\"次,查询关系为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      NSString *expectedQueryInfoString = args[1];
      UPResourceQuery *expectedQuery = combineQuery(expectedQueryInfoString);
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate deleteRelation:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      CCIAssert(isEqualQuery(expectedQuery, self.actualQueryList.firstObject), @"本地数据存储删除关系参数应该相同，实际不相同");
    });
    Given(@"^本地数据存储插入资源操作结果为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      NSArray<UPResourceInfo *> *resInfos = [self convertResInfoListByUserInfo:userInfo];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *r;
        [invocation getArgument:&r atIndex:2];
        UPResourceInfo *actualRes = (__bridge UPResourceInfo *)r;
        [self.actualInsertList addObject:actualRes];
        BOOL result = NO;
        if (isValidStringEqual(resultStr, @"失败")) {
            result = !isEqualResource(resInfos.firstObject, actualRes);
        }
        else {
            result = isEqualResource(resInfos.firstObject, actualRes);
        }
        if (resInfos.count >= 2 && isValidStringEqual(resultStr, @"成功")) {
            result = YES;
        }
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate insertResourceInfo:[OCMArg any]]).andDo(proxyBlock);
    });
    Then(@"^本地数据存储插入资源接口被调用\"([^\"]*)\"次,资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate insertResourceInfo:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      CCIAssert(expectedInvocationTimes == self.actualInsertList.count, @"本地数据存储插入接口调用次数与实际不一致,实际为:%ld, 期望是:%ld", self.actualInsertList.count, expectedInvocationTimes);

      NSArray *expectInfos = filterExpectInfos(userInfo, expectedInvocationTimes);
      for (int i = 0; i < expectedInvocationTimes; i++) {
          CCIAssert(isEqualResource(self.actualInsertList[i], expectInfos[i]), @"本地数据存储插入资源与实际不一致，实际为%@--期望是%@", identifierOfResource(self.actualInsertList[i]), identifierOfResource(expectInfos[i]));
      }
    });
    Then(@"^本地数据存储查询资源接口被调用\"([^\"]*)\"次,资源名称为\"([^\"]*)\",资源类型为\"([^\"]*)\",资源版本为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[1];
      NSString *type = args[2];
      NSString *version = args[3];
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:[UPResourceInfo typeValueOfString:type] name:name version:version]);
    });
    Then(@"^本地数据存储查询资源接口被调用\"([^\"]*)\"次,资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      CCIAssert(expectedInvocationTimes == self.actualSearchList.count, @"本地数据存储查询接口调用次数与实际不一致,实际为:%ld, 期望是:%ld", self.actualSearchList.count, expectedInvocationTimes);
      NSArray *expectInfos = filterExpectInfos(userInfo, expectedInvocationTimes);

      for (NSInteger i = 0; i < expectedInvocationTimes; i++) {
          CCIAssert(isEqualResource(self.actualSearchList[i], expectInfos[i]), @"参数与实际不一致，实际为%@,期望是%@", identifierOfResource(self.actualSearchList[i]), identifierOfResource(expectInfos[i]));
      }
    });
    Then(@"^本地数据存储插入关系接口被调用\"([^\"]*)\"次,查询关系为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate insertRelationBetweenQuery:[OCMArg any] andResourceInfo:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      NSString *expectedQueryInfoString = args[1];
      UPResourceQuery *expectedQuery = combineQuery(expectedQueryInfoString);
      CCIAssert(expectedInvocationTimes == self.queryAndResInfoRelations.count, @"本地数据存储插入关系接口调用次数与实际不一致,实际为:%ld, 期望是:%ld", self.queryAndResInfoRelations.count, expectedInvocationTimes);
      NSArray *expectInfos = filterExpectInfos(userInfo, expectedInvocationTimes);
      for (NSInteger i = 0; i < expectedInvocationTimes; i++) {
          CCIAssert(isEqualQuery(expectedQuery, self.queryAndResInfoRelations[i][0]) && isEqualResource(expectInfos[i], self.queryAndResInfoRelations[i][1]), @"参数与实际不一致，实际为%@,期望是%@", identifierOfResource(self.queryAndResInfoRelations[i][1]), identifierOfResource(expectInfos[i]));
      }
    });
    Given(@"^本地数据存储查询资源操作结果为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      if (isValidStringEqual(resultStr, @"失败")) {
          OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]).andReturn(nil).ignoringNonObjectArgs;
      }
      else {
          NSArray<UPResourceInfo *> *resInfos = [self convertResInfoListByUserInfo:userInfo];
          void (^blockProxy)(NSInvocation *) = ^(NSInvocation *invocation) {
            UPResourceInfo *actualInfo = [self cacheSearchResourceParam:invocation];
            UPResourceInfo *result;
            for (UPResourceInfo *info in resInfos) {
                if (isValidStringEqual(info.name, actualInfo.name)) {
                    result = info;
                    break;
                }
            }
            [invocation setReturnValue:&result];
          };
          OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfo:0 name:[OCMArg any] version:[OCMArg any]]).andDo(blockProxy).ignoringNonObjectArgs;
      }
    });
    Given(@"^本地数据存储更新资源操作结果为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      NSArray<UPResourceInfo *> *resInfos = [self convertResInfoListByUserInfo:userInfo];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *i;
        [invocation getArgument:&i atIndex:2];
        UPResourceInfo *actualRes = (__bridge UPResourceInfo *)i;
        [self.actualUpdateList addObject:actualRes];
        BOOL result = isValidStringEqual(actualRes.name, resInfos.firstObject.name) && isValidStringEqual(resultStr, @"成功");
        if (resInfos.count >= 2 && isValidStringEqual(resultStr, @"成功")) {
            result = YES;
        }
        [invocation setReturnValue:&result];
      };

      OCMStub([[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]).andDo(proxyBlock);
    });
    Then(@"^本地数据存储更新资源接口被调用\"([^\"]*)\"次,资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]);
      if (expectedInvocationTimes == 0) {
          return;
      }
      CCIAssert(expectedInvocationTimes == self.actualUpdateList.count, @"本地数据存储更新资源接口调用次数与实际不一致,实际为:%ld, 期望是:%ld", self.actualUpdateList.count, expectedInvocationTimes);
      NSArray *expectedInfos = filterExpectInfos(userInfo, expectedInvocationTimes);
      for (NSInteger i = 0; i < expectedInvocationTimes; i++) {
          CCIAssert(isEqualResource(expectedInfos[i], self.actualUpdateList[i]), @"参数与实际不一致，实际为%@,期望是%@", identifierOfResource(self.actualUpdateList[i]), identifierOfResource(expectedInfos[i]));
      }
    });
    Given(@"^本地数据存储插入关系操作结果为\"([^\"]*)\",查询关系为\"([^\"]*)\",资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      NSArray<UPResourceInfo *> *resInfos = [self convertResInfoListByUserInfo:userInfo];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *q;
        [invocation getArgument:&q atIndex:2];
        UPResourceQuery *actualQuery = (__bridge UPResourceQuery *)q;
        void *i;
        [invocation getArgument:&i atIndex:3];
        UPResourceInfo *actualResInfo = (__bridge UPResourceInfo *)i;
        NSArray *actualParam = @[ actualQuery, actualResInfo ];
        [self.queryAndResInfoRelations addObject:actualParam];

        BOOL result = isValidStringEqual(actualResInfo.name, resInfos.firstObject.name) && isValidStringEqual(resultStr, @"成功");
        if (resInfos.count >= 2 && isValidStringEqual(resultStr, @"成功")) {
            result = YES;
        }
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate insertRelationBetweenQuery:[OCMArg any] andResourceInfo:[OCMArg any]]).andDo(proxyBlock);
    });
    Then(@"^更新资源数据库接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger callTimes = args[0].integerValue;
      OCMVerify(times(callTimes), [[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]);
    });
    Then(@"^更新资源数据库接口被调用\"([^\"]*)\"次,参数资源包如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSUInteger expectedInvocationTimes = args[0].integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].databaseDelegate updateResourceInfo:[OCMArg any]]);
      NSArray<UPResourceInfo *> *resultInfoList = [self convertResInfoListByUserInfo:userInfo];
      CCIAssert(resultInfoList.count == self.actualUpdateList.count, @"更新资源数据库接口调用次数与实际不一致,实际为:%ld,期望是:%ld", self.actualUpdateList.count, resultInfoList.count);
      if (expectedInvocationTimes > 0) {
          CCIAssert(isEqualResourcesList(resultInfoList, self.actualUpdateList), @"参数与实际不一致，实际为%@,期望是%@", self.actualUpdateList, resultInfoList);
      }
    });
    Then(@"^数据库查询所有查询条件接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //iOS忽略
         });
    Then(@"^数据库查询所有关联关系接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchAllQuerys]);
    });
    Then(@"^数据库批量查询资源条件接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceQuery:[OCMArg any]]);
    });
    Given(@"^数据库根据名字和类型查询接口返回\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      NSArray *resourceList;
      if ([resultStr isEqualToString:@"空对象"]) {
          resourceList = nil;
      }
      else if ([resultStr isEqualToString:@"空数组"]) {
          resourceList = @[];
      }
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceList:UPResourceTypeAll name:[OCMArg any]]).andReturn(resourceList);
    });

    Given(@"^数据库批量删除资源列表接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *param;
        [invocation getArgument:&param atIndex:2];
        NSArray *infoList = (__bridge NSArray *)param;
        [self.actualDeleteList addObjectsFromArray:infoList];
        BOOL isExist = [resultStr isEqualToString:@"成功"];
        [invocation setReturnValue:&isExist];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].databaseDelegate stub] andDo:proxyBlock] deleteResourceInfos:[OCMArg any]];
    });
    Given(@"^数据库批量删除资源查询条件接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultStr = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *param;
        [invocation getArgument:&param atIndex:2];
        NSArray *queryList = (__bridge NSArray *)param;
        [self.actualQueryList addObjectsFromArray:queryList];
        BOOL isExist = [resultStr isEqualToString:@"成功"];
        [invocation setReturnValue:&isExist];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].databaseDelegate stub] andDo:proxyBlock] deleteQuerys:[OCMArg any]];
    });
    Then(@"^数据库批量删除资源列表接口被调用\"([^\"]*)\"次,参数列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<UPResourceInfo *> *expectInfoList = [self convertResInfoListByUserInfo:userInfo];
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate deleteResourceInfos:[OCMArg any]]);
      CCIAssert(isEqualResourcesList(expectInfoList, self.actualDeleteList), @"数据库批量删除资源列表接口参数实际为%@，期望为%@", expectInfoList, self.actualDeleteList);
    });
    Then(@"^数据库批量删除资源查询条件接口被调用\"([^\"]*)\"次,参数列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSArray<UPResourceQuery *> *expectQueryList = [[ResourceHolder sharedInstance] convertQueryList:array];
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate deleteQuerys:[OCMArg any]]);
      CCIAssert(isEqualQueryList(expectQueryList, self.actualQueryList), @"数据库批量删除资源查询条件接口参数实际为%@，期望为%@", self.actualQueryList, expectQueryList);

    });
    Then(@"^数据库批量删除关联信息列表接口被调用\"([^\"]*)\"次,参数列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //iOS忽略
         });
    Then(@"^数据库批量删除关联信息列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
             //iOS忽略
         });

    Then(@"^数据库批量删除资源列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate deleteResourceInfos:[OCMArg any]]);
    });

    When(@"^调用清理无用资源方法,传入延时参数为\"([^\"]*)\"秒$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger time = [args[0] integerValue];
      [[ResourceHolder sharedInstance].resourceManager cleanUselessResource:time];
    });

    Given(@"^本地数据存储代理的删除资源查询条件接口结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL bResult = isValidStringEqual(resultString, @"成功");
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate cleanDataKeepPresetRes:[OCMArg any]]).andReturn(bResult);
    });

    Given(@"^数据库代理查询名字为\"([^\"]*)\"类型为\"([^\"]*)\"状态为\"([^\"]*)\"的资源接口返回资源信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *name = args[0];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      NSString *typeStr = args[1];
      UPResourceType type = [UPResourceInfo typeValueOfString:typeStr];
      NSString *statusStr = args[2];
      UPResourceStatus status = UPResourceStatusPublish;
      if ([statusStr isEqualToString:@"已下架"]) {
          status = UPResourceStatusOFF;
      }
      NSArray *infoList = [self convertResInfoListByUserInfo:userInfo];
      OCMStub([[ResourceHolder sharedInstance].databaseDelegate searchResourceInfoByStatus:status name:name type:type version:[OCMArg any]]).andReturn(infoList);
    });

    Then(@"^数据库根据名字和类型查询指定状态资源接口被调用\"([^\"]*)\"次,参数名字为\"([^\"]*)\",类型为\"([^\"]*)\",状态为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *name = args[1];
      if ([name isEqualToString:@"空对象"]) {
          name = nil;
      }
      NSString *typeStr = args[2];
      UPResourceType type = [UPResourceInfo typeValueOfString:typeStr];
      NSString *statusStr = args[3];
      UPResourceStatus status = UPResourceStatusPublish;
      if ([statusStr isEqualToString:@"已下架"]) {
          status = UPResourceStatusOFF;
      }
      OCMVerify(times(invocationTimes.integerValue), [[ResourceHolder sharedInstance].databaseDelegate searchResourceInfoByStatus:status name:name type:type version:[OCMArg any]]);
    });
}

#pragma mark private Methods

- (NSArray<UPResourceInfo *> *)convertResInfoListByUserInfo:(NSDictionary *)userInfo
{
    NSArray *array = jsonObjectsFromDataTable(userInfo);
    return [[ResourceHolder sharedInstance] convertResourceInfoList:array];
}

- (UPResourceInfo *)cacheSearchResourceParam:(NSInvocation *)invocation
{
    UPResourceInfo *param = [[UPResourceInfo alloc] init];
    UPResourceType type;
    [invocation getArgument:&type atIndex:2];
    void *name;
    [invocation getArgument:&name atIndex:3];
    void *version;
    [invocation getArgument:&version atIndex:4];
    param.type = type;
    param.name = (__bridge NSString *)name;
    param.version = (__bridge NSString *)version;
    [self.actualSearchList addObject:param];
    return param;
}

@end

//
//  FileSystemSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/15.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "FileSystemSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import <OCMock/OCMock.h>

@interface FileSystemSteps ()

@property (nonatomic, strong) NSMutableArray<NSString *> *existFiles;
@property (nonatomic, strong) NSMutableArray<NSString *> *existPaths;
//key:目录 value:目录下的子目录
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSArray<NSString *> *> *subPaths;
@property (nonatomic, strong) NSMutableDictionary<NSString *, NSString *> *unzipResults;
@property (nonatomic, strong) NSMutableArray<NSString *> *pathList;

@end

@implementation FileSystemSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.existFiles = [NSMutableArray array];
      self.existPaths = [NSMutableArray array];
      self.subPaths = [NSMutableDictionary dictionary];
      self.unzipResults = [NSMutableDictionary dictionary];
      self.pathList = [NSMutableArray array];
    });

    Given(@"^创建文件夹结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *createFileDirResult = args[0];
      BOOL result = isValidStringEqual(createFileDirResult, @"成功");
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *s;
        [invocation getArgument:&s atIndex:2];
        NSString *path = (__bridge NSString *)s;
        if (result) {
            [self.existPaths addObject:path];
        }
        BOOL isSuccess = result;
        [invocation setReturnValue:&isSuccess];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate mkdirs:[OCMArg any]]).andDo(proxyBlock);
    });

    Given(@"^文件系统中\"([^\"]*)\"的\"([^\"]*)\"路径如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *bExistString = args[0];
      NSString *pathDescription = args[1];
      BOOL bExists = isValidStringEqual(bExistString, @"存在");
      BOOL isDir = isValidStringEqual(pathDescription, @"文件夹");
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *filePaths = [NSMutableArray array];
      for (NSArray *items in array) {
          NSString *path = items.firstObject;
          [filePaths addObject:path];
      }
      NSMutableArray *tempArray = isDir ? self.existPaths : self.existFiles;
      if (bExists) {
          [tempArray addObjectsFromArray:filePaths];
      }
      else {
          [tempArray removeObjectsInArray:filePaths];
      }

      void (^proxyBlock1)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *s;
        [invocation getArgument:&s atIndex:2];
        NSString *file = (__bridge NSString *)s;
        BOOL result = [self.existFiles containsObject:file] || [self.existPaths containsObject:file];
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate exists:[OCMArg any]]).andDo(proxyBlock1);

      void (^proxyBlock2)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *s;
        [invocation getArgument:&s atIndex:2];
        NSString *file = (__bridge NSString *)s;
        BOOL result = [self.existFiles containsObject:file];
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate isFile:[OCMArg any]]).andDo(proxyBlock2);

      void (^proxyBlock3)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *s;
        [invocation getArgument:&s atIndex:2];
        NSString *path = (__bridge NSString *)s;
        BOOL result = [self.existPaths containsObject:path];
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate isDirectory:[OCMArg any]]).andDo(proxyBlock3);

    });
    Then(@"^创建文件夹接口被调用\"([^\"]*)\"次,参数为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *numString = args[0];
      NSString *path = args[1];
      OCMVerify(times(numString.integerValue), [[ResourceHolder sharedInstance].fileDelegate mkdirs:path]);
    });
    Given(@"^设置文件\"([^\"]*)\"的MD5值为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *path = args[0];
      NSString *result = args[1];
      OCMStub([[ResourceHolder sharedInstance].fileDelegate calcMD5:path]).andReturn(result);
    });

    Given(@"文件系统通过文件路径获取文件名称列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
              //适配Android,iOS空实现
          });
    Given(@"文件系统通过文件路径获取父文件路径列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
              //适配Android,iOS空实现
          });

    Given(@"预置文件的根路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo){
              //适配Android,iOS空实现
          });
    Given(@"^判断文件是否存在等待\"([^\"]*)\"秒返回$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSInteger waitTime = args[0].integerValue;
      void (^proxyBlock)(NSInvocation *invocation) = ^(NSInvocation *invocation) {
        [NSThread sleepForTimeInterval:waitTime];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate exists:[OCMArg any]]).andDo(proxyBlock);
    });
    Given(@"^文件系统代理删除文件路径接口返回结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *resultString = args[0];
      BOOL result = isValidStringEqual(resultString, @"成功");
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *param;
        [invocation getArgument:&param atIndex:2];
        NSString *path = (__bridge NSString *)param;
        [self.pathList addObject:path];
        BOOL isExist = result;
        [invocation setReturnValue:&isExist];
      };
      [[[(OCMockObject *)[ResourceHolder sharedInstance].fileDelegate stub] andDo:proxyBlock] deletePath:[OCMArg any]];
    });

    Then(@"^文件系统代理的删除文件路径接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectedString = args[0];
      OCMVerify(times(expectedString.integerValue), [[ResourceHolder sharedInstance].fileDelegate deletePath:[OCMArg any]]);
    });

    Then(@"^文件系统代理的删除文件路径接口被调用\"([^\"]*)\"次,删除的路径为如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectedString = args[0];
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *expectedFilePaths = [NSMutableArray array];
      for (NSArray *items in array) {
          NSString *path = items.firstObject;
          [expectedFilePaths addObject:path];
      }
      OCMVerify(times(expectedString.integerValue), [[ResourceHolder sharedInstance].fileDelegate deletePath:[OCMArg any]]);
      CCIAssert(isStringArrayEqual(expectedFilePaths, self.pathList), @"文件系统代理的删除文件路径接口参数应该为:%@,实际为:%@", expectedFilePaths, self.pathList);
    });

    Given(@"^删除文件\"([^\"]*)\"结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *file = args[0];
      NSString *result = args[1];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        if (result && [self.existFiles containsObject:file]) {
            [self.existFiles removeObject:file];
        }
        BOOL res = isValidStringEqual(result, @"成功");
        [invocation setReturnValue:&res];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate deletePath:file]).andDo(proxyBlock);
    });

    Given(@"^文件系统拷贝资源\"([^\"]*)\"至\"([^\"]*)\"的结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *source = args[0];
      NSString *target = args[1];
      BOOL result = isValidStringEqual(args[2], @"成功");
      OCMStub([[ResourceHolder sharedInstance].fileDelegate copy:source to:target]).andReturn(result);
      //隐含与android差异逻辑
      NSString *name = source.lastPathComponent;
      OCMStub([[ResourceHolder sharedInstance].presetFileLoader openPresetFile:name]).andReturn(source);
    });

    Given(@"^文件系统解压资源\"([^\"]*)\"操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *file = args[0];
      NSString *unZipResult = args[1];
      [self.unzipResults setObject:unZipResult forKey:file];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        void *p;
        [invocation getArgument:&p atIndex:2];
        NSString *path = (__bridge NSString *)p;
        NSString *res = [self.unzipResults objectForKey:path];
        BOOL result = isValidStringEqual(res, @"成功");
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate unzip:file to:[OCMArg any]]).andDo(proxyBlock);
    });

    Given(@"^移动资源包\"([^\"]*)\"至\"([^\"]*)\"操作结果为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *source = args[0];
      NSString *target = args[1];
      NSString *renameResult = args[2];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        BOOL result = isValidStringEqual(renameResult, @"成功");
        if (result && ![self.existPaths containsObject:source]) {
            [self.existPaths addObject:source];
        }
        if (result && ![self.existPaths containsObject:target]) {
            [self.existPaths addObject:target];
        }
        [invocation setReturnValue:&result];
      };
      OCMStub([[ResourceHolder sharedInstance].fileDelegate rename:source to:target]).andDo(proxyBlock);
    });

    Then(@"^文件MD5校验接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate calcMD5:[OCMArg any]]);
    });

    Then(@"^文件MD5校验接口被调用\"([^\"]*)\"次,参数路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *path = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate calcMD5:path]);
    });

    Then(@"^解压资源接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate unzip:[OCMArg any] to:[OCMArg any]]);
    });

    Then(@"^解压资源接口被调用\"([^\"]*)\"次,参数源路径为\"([^\"]*)\",目标路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *sourcePath = args[1];
      NSString *targetPath = args[2];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate unzip:sourcePath to:targetPath]);
    });

    Then(@"^移动资源包接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate rename:[OCMArg any] to:[OCMArg any]]);
    });

    Then(@"^移动资源包接口被调用\"([^\"]*)\"次,参数源路径为\"([^\"]*)\",目标路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *sourcePath = args[1];
      NSString *targetPath = args[2];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate rename:sourcePath to:targetPath]);
    });

    Then(@"^移除资源包接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate deletePath:[OCMArg any]]);
    });

    Then(@"^移除资源包接口被调用\"([^\"]*)\"次,参数路径为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSString *path = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate deletePath:path]);
    });

    Given(@"^设置文件\"([^\"]*)\"的子目录列表为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *path = args[0];
      if (![self.existPaths containsObject:path]) {
          [self.existPaths addObject:path];
      }
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSMutableArray *subPathList = [NSMutableArray array];
      for (NSArray<NSString *> *data in array) {
          if (![data.firstObject isEqualToString:@""] && ![data.lastObject isEqualToString:@""]) {
              NSMutableArray *array = isValidStringEqual(data.lastObject, @"文件夹") ? self.existPaths : self.existFiles;
              if (![array containsObject:data.firstObject]) {
                  [array addObject:data.firstObject];
              }
              [subPathList addObject:data.firstObject];
          }
      }
      [self.subPaths setObject:subPathList forKey:path];
      OCMStub([[ResourceHolder sharedInstance].fileDelegate listSubPaths:path]).andReturn(subPathList);
    });

    Then(@"^文件系统代理的获取文件子路径列表接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate listSubPaths:[OCMArg any]]);
    });

    Then(@"^文件系统代理的判断文件接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].fileDelegate isFile:[OCMArg any]]);
    });

    Then(@"^文件系统代理的获取文件子路径列表接口被调用\"([^\"]*)\"次,参数路径信息如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *invocationTimes = args[0];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSArray *array = getTableDataListFromExpectUserInfo(userInfo);
      NSString *expectedPaths = array[0][0];
      NSArray *expectedPathArray = [expectedPaths componentsSeparatedByString:@";"];
      CCIAssert(invocationTimes.integerValue == expectedPathArray.count, @"文件系统代理获取文件子路径接口期望调用次数与入参不一致,期望调用:%ld,参数个数:%ld", invocationTimes, expectedPathArray.count);
      for (int i = 0; i < expectedInvocationTimes; i++) {
          OCMVerify(times(1), [[ResourceHolder sharedInstance].fileDelegate listSubPaths:expectedPathArray[i]]);
      }
    });

    Given(@"^设置文件\"([^\"]*)\"的子目录列表为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *path = args[0];
      NSArray *array = [args[1] componentsSeparatedByString:@";"];
      NSMutableArray *subPaths = [NSMutableArray array];
      for (NSString *data in array) {
          NSArray *list = [data componentsSeparatedByString:@","];
          NSString *pathType = list[1];
          NSString *subPath = list[0];
          if (isValidStringEqual(pathType, @"文件")) {
              [self.existFiles addObject:subPath];
          }
          else {
              [self.existPaths addObject:subPath];
          }
          [subPaths addObject:subPath];
      }
      [self.subPaths setValue:subPaths forKey:path];
      OCMStub([[ResourceHolder sharedInstance].fileDelegate listSubPaths:path]).andReturn(subPaths);
    });

    Then(@"^文件系统代理的删除文件接口被调用\"([^\"]*)\"次,参数路径信息为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectedString = args[0];
      NSString *expectFilePath = args[1];
      OCMVerify(times(expectedString.integerValue), [[ResourceHolder sharedInstance].fileDelegate deletePath:expectFilePath]);
    });

    Then(@"^文件系统代理的删除文件接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *expectedString = args[0];
      OCMVerify(times(expectedString.integerValue), [[ResourceHolder sharedInstance].fileDelegate deletePath:[OCMArg any]]);
    });
}

@end

//
//  DataSourceSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/5/14.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "DataSourceSteps.h"
#import <Cucumberish/Cucumberish.h>
#import "StepUtils.h"
#import "ResourceHolder.h"
#import <OCMock/OCMock.h>

extern NSString *const FROM_FUNC_COMMON_RESOURCE;
extern NSString *const FROM_FUNC_DEVICE_RESOURCE;
extern NSString *const FROM_FUNC_DEVICE_CONFIG;
extern NSString *const FROM_FUNC_DEVICE_CUSTOM_INFO;

typedef void (^resultBlock)(NSArray<UPResourceInfo *> *infoList, NSError *error);
@interface DataSourceSteps ()
@property (nonatomic, strong) UPResourceCondition *actualCondition;
@end
@implementation DataSourceSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.actualCondition = nil;
    });

    Given(@"^运营平台上部署的\"([^\"]*)\"列表如下:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *searchType = [self searchKeyByType:args[0]];
      NSArray *array = jsonObjectsFromDataTable(userInfo);
      NSArray<UPResourceInfo *> *infoList = [[ResourceHolder sharedInstance] convertResourceInfoList:array];
      [self mockSearchResourceListByType:searchType resourceList:infoList];

    });

    Then(@"^资源数据源的\"([^\"]*)\"接口被调用\"([^\"]*)\"次,参数请求条件为\"([^\"]*)\"", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *searchType = [self searchKeyByType:args[0]];
      NSString *invocationTimes = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      NSString *conditonStr = args[2];
      UPResourceCondition *expectCondition = combineCondition(conditonStr);
      [self assertInvokeTimesByType:searchType expectedInvocationTimes:expectedInvocationTimes];
      CCIAssert(isEqualCondition(expectCondition, self.actualCondition), @"参数请求条件与实际不一致");
    });

    Then(@"^资源数据源的\"([^\"]*)\"接口被调用\"([^\"]*)\"次$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *searchType = [self searchKeyByType:args[0]];
      NSString *invocationTimes = args[1];
      NSUInteger expectedInvocationTimes = invocationTimes.integerValue;
      [self assertInvokeTimesByType:searchType expectedInvocationTimes:expectedInvocationTimes];
    });

    Given(@"^运营平台上查询条件为\"([^\"]*)\"的资源状态为:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray *dataTable = jsonObjectsFromDataTable(userInfo);
      NSArray<UPResourceInfo *> *infoList = [[ResourceHolder sharedInstance] convertResourceInfoList:dataTable];
      [self mockSearchResourceListByType:args[0] resourceList:infoList];
    });
}

#pragma mark - private
- (NSString *)searchKeyByType:(NSString *)type
{
    NSDictionary *typeDic = @{ @"普通资源" : FROM_FUNC_COMMON_RESOURCE,
                               @"设备资源" : FROM_FUNC_DEVICE_RESOURCE,
                               @"设备配置资源" : FROM_FUNC_DEVICE_CONFIG,
                               @"设备自定义资源" : FROM_FUNC_DEVICE_CUSTOM_INFO,
                               @"批量普通资源" : FROM_FUNC_BATCH_NORMAL_RESOURCE,
                               @"查询设备应用配置" : FROM_FUNC_APP_FUNC_MODEL,
    };
    if (!type) {
        return nil;
    }
    return typeDic[type];
}

- (void)assertInvokeTimesByType:(NSString *)type expectedInvocationTimes:(NSUInteger)expectedInvocationTimes
{
    if ([type isEqualToString:FROM_FUNC_COMMON_RESOURCE]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource searchNormalResList:[OCMArg any] completion:[OCMArg any]]);
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_RESOURCE]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource searchDeviceResList:[OCMArg any] completion:[OCMArg any]]);
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_CONFIG]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource searchDeviceConfigResList:[OCMArg any] completion:[OCMArg any]]);
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_CUSTOM_INFO]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource searchDeviceCustomList:[OCMArg any] completion:[OCMArg any]]);
    }
    else if ([type isEqualToString:FROM_FUNC_BATCH_NORMAL_RESOURCE]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource batchSearchNormalResList:[OCMArg any] completion:[OCMArg any]]);
    }
    else if ([type isEqualToString:FROM_FUNC_APP_FUNC_MODEL]) {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource searchAppFuncModelConfig:[OCMArg any] completion:[OCMArg any]]);
    }
    else {
        OCMVerify(times(expectedInvocationTimes), [[ResourceHolder sharedInstance].resourceDataSource checkDynamicResStatusWithCondition:[OCMArg any] completion:[OCMArg any]]);
    }
}

- (void)mockSearchResourceListByType:(NSString *)type resourceList:(NSArray *)resourceList
{
    NSError *error = nil;
    if (resourceList.count == 0) {
        error = [NSError errorWithDomain:@"请求失败" code:-1 userInfo:nil];
        resourceList = nil;
    }
    void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
      [invocation retainArguments];
      void *param;
      [invocation getArgument:&param atIndex:2];
      UPResourceCondition *condition = (__bridge UPResourceCondition *)param;
      self.actualCondition = condition;
      void *blockPointer;
      [invocation getArgument:&blockPointer atIndex:3];
      resultBlock block = (__bridge resultBlock)blockPointer;
      block(resourceList, error);
    };
    if ([type isEqualToString:FROM_FUNC_COMMON_RESOURCE]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource searchNormalResList:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_RESOURCE]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource searchDeviceResList:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_CONFIG]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource searchDeviceConfigResList:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else if ([type isEqualToString:FROM_FUNC_DEVICE_CUSTOM_INFO]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource searchDeviceCustomList:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else if ([type isEqualToString:FROM_FUNC_BATCH_NORMAL_RESOURCE]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource batchSearchNormalResList:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else if ([type isEqualToString:FROM_FUNC_APP_FUNC_MODEL]) {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource searchAppFuncModelConfig:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
    else {
        [OCMStub([[ResourceHolder sharedInstance].resourceDataSource checkDynamicResStatusWithCondition:[OCMArg any] completion:[OCMArg any]]) andDo:proxyBlock];
    }
}

@end

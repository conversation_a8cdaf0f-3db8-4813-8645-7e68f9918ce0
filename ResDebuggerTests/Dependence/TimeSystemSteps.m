//
//  TimeSystemSteps.m
//  ResDebuggerTests
//
//  Created by <PERSON> on 2019/7/3.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "TimeSystemSteps.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import "ResourceHolder.h"
@interface TimeSystemSteps ()

@property (nonatomic, strong) NSString *currentTime;

@end

@implementation TimeSystemSteps
- (void)defineStepsAndHocks
{
    before(^(CCIScenarioDefinition *scenario) {
      self.currentTime = nil;
    });

    Given(@"^设置系统当前时间为\"([^\"]*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      self.currentTime = args[0];
      void (^proxyBlock)(NSInvocation *) = ^(NSInvocation *invocation) {
        [invocation retainArguments];
        NSDateFormatter *formatter = [[NSDateFormatter alloc] init];
        [formatter setDateFormat:@"yyyyMMdd HH:mm:ss"];
        NSDate *currentDate = [formatter dateFromString:self.currentTime];
        NSTimeInterval timeInterval = [currentDate timeIntervalSince1970];
        long time = timeInterval * 1000;
        [invocation setReturnValue:&time];
      };
      OCMStub([[ResourceHolder sharedInstance].timeDelegate currentTimeMillis]).andDo(proxyBlock).ignoringNonObjectArgs;
    });
}
@end

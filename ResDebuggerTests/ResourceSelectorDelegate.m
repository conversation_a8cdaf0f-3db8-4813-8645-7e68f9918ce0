//
//  ResourceSelectorDelegate.m
//  ResDebuggerTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/6/10.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "ResourceSelectorDelegate.h"

@interface ResourceSelectorDelegate ()
/**
 资源名称
 **/
@property (nonatomic, copy) NSString *resName;
/**
 资源类型
 **/
@property (nonatomic, assign) UPResourceType type;
@end

@implementation ResourceSelectorDelegate

+ (ResourceSelectorDelegate *)getInstance
{
    static ResourceSelectorDelegate *instance = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      instance = [[self alloc] init];
      instance.resName = nil;
      instance.type = UPResourceTypeAll;
    });
    return instance;
}

- (UPResourceInfo *)selectFrom:(NSArray<UPResourceInfo *> *)infoList
{
    __block UPResourceInfo *info = nil;
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([obj.name isEqualToString:self.resName] && obj.type == self.type) {
          info = obj;
          *stop = YES;
      }
    }];
    return info;
}

- (void)mockResourceName:(NSString *)name type:(UPResourceType)type
{
    self.resName = name;
    self.type = type;
}

@end

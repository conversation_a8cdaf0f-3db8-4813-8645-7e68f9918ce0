//
//  CommonResCallbackImp.m
//  ResDebuggerTests
//
//  Created by luxu on 2022/11/18.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "CommonResCallbackImp.h"
#import <objc/runtime.h>

@implementation UPResourceInfo (UnitTestPreloadResult)

- (BOOL)success
{
    NSNumber *success = objc_getAssociatedObject(self, @selector(success));
    return [success isKindOfClass:[NSNumber class]] ? success.boolValue : NO;
}

- (void)setSuccess:(BOOL)success
{
    objc_setAssociatedObject(self, @selector(success), @(success), OBJC_ASSOCIATION_RETAIN);
}

@end

@implementation CommonResCallbackImp
#pragma mark - init
- (instancetype)initWithIdentifier:(NSString *)identifier
{
    if (self = [super init]) {
        _identifier = identifier;
        _expectation = [[XCTestExpectation alloc] initWithDescription:@"资源结果回调"];
        _info = nil;
        _infoList = nil;
        _result = NO;
        _preloadResInfoList = [NSMutableArray array];
    }
    return self;
}

#pragma mark -UPResourceCallback
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    _result = success;
    _message = message;
    _info = info;
    if (info == nil) {
        info = [[UPResourceInfo alloc] init];
        info.name = @"";
        info.version = @"";
    }
    info.success = success;
    [_preloadResInfoList addObject:info];
    [self.expectation fulfill];
}

- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
}


#pragma mark - UPResourceListCallback
- (void)onResult:(BOOL)success message:(NSString *)message infoList:(NSArray<UPResourceInfo *> *)infoList
{
    _result = success;
    _message = message;
    _infoList = infoList;
    [self.expectation fulfill];
}

@end

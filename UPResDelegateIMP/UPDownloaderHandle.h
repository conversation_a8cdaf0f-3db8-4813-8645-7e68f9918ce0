//
//  UPDownloaderHandle.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/19.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDownloadHandle.h"


NS_ASSUME_NONNULL_BEGIN

@interface UpDownloadBlockInfo : NSObject

@property (nonatomic, assign, readonly) long blockStartTime;
@property (nonatomic, assign, readonly) long blockEndTime;
@property (nonatomic, assign, readonly) long blockSize;
@property (nonatomic, assign) BOOL blockResult;
@property (nonatomic, copy) NSString *blockFailedReason;

- (void)blockStart;
- (void)blockEnd;
- (void)cumsumSize:(long)size;
@end

@interface UpDownloadNetInfo : NSObject

@property (nonatomic, assign, readonly) long netStateTime;
@property (nonatomic, copy, readonly) NSString *deviceIP;

@end

@interface UpDownloadBackgroundRunInfo : NSObject

@property (nonatomic, assign) BOOL ifBackgroundRun;
@property (nonatomic, assign) long backgroundRunTime;

@end

@interface UPDownloadRecord : NSObject
//文件名称
@property (nonatomic, copy) NSString *fileName;
//文件保存路径
@property (nonatomic, copy) NSString *savePath;
//下载任务
@property (nonatomic, strong) NSURLSessionDataTask *downloadTask;
//下载开始时间
@property (nonatomic, strong) NSDate *startDownloadTime;
//下载结束时间
@property (nonatomic, strong) NSDate *endDownloadTime;
//文件大小,kb
@property (nonatomic, copy) NSString *fileSize;
//循环下载次数
@property (nonatomic, assign) NSInteger reTryCount;
//下载结果
@property (nonatomic, assign) BOOL downloadResult;
//CDN IP
@property (nonatomic, copy, readonly) NSString *resourceIP;
// 添加下载段信息
- (void)addDownloadBlockInfo:(UpDownloadBlockInfo *)blockInfo;
// 添加下载阶段网络变化信息
- (void)addDownloadNetInfo;
// 添加下载阶段前后台变化信息
- (void)addDownloadBackgroundRunInfo:(BOOL)isBackground;
// 下载区段信息集合
- (NSArray<UpDownloadBlockInfo *> *)downloadBlockInfos;
// 下载网络信息集合
- (NSArray<UpDownloadNetInfo *> *)downloadNetInfos;
// 下载前后台信息集合
- (NSArray<UpDownloadBackgroundRunInfo *> *)downloadBackgroundRunInfos;

// 获取CDN IP
- (void)transformSourceURLToCDN:(NSString *)url;

- (instancetype)initRecord:(NSString *)fileName savePath:(NSString *)savePath;

//下载时长
- (long)totalDownloadTimes;

@end

@interface UPDownloaderHandle : UPDownloadHandle
@property (nonatomic, strong) UPDownloadRecord *record;

@end

NS_ASSUME_NONNULL_END

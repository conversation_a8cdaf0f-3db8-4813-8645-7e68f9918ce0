//
//  UPResourceInjection.m
//  UPRes
//
//  Created by <PERSON> on 2019/5/10.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceInjection.h"
#import "UPResourceFileSystem.h"
#import "UPResourceTimeSystem.h"
#import "UPConnectionMonitor.h"
#import "UPDownloaderDelegate.h"
#import "UPOmsResDataSource.h"
#import "UPResourceDatabaseImpl.h"
#import "UpResourceCleanerImpl.h"
#import "SEAOmsResDataSource.h"
#import "UPResourceConfig.h"
#import "UPAssetPresetFileLoader.h"

@interface UPResourceInjection ()
- (instancetype)initWithTestModeOn:(BOOL)testModeOn appPlatform:(UPResourceAppPlatform)appPlatform;
@end

@implementation UPResourceInjection
#pragma mark - Property Methods
- (nullable UPResourceSettings *)settings
{
    if (![_resourceManager isKindOfClass:[UPResourceManager class]]) {
        return nil;
    }
    return self.resourceManager.settings;
}

#pragma mark - Public Methods
static UPResourceInjection *UPResourceInjectionInstance = nil;
+ (UPResourceInjection *)getInstance
{
    return UPResourceInjectionInstance;
}

+ (void)initializeWithTestModeOn:(BOOL)testModeOn
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      UPResourceInjectionInstance = [[self alloc] initWithTestModeOn:testModeOn appPlatform:UPResourceAppPlatformChina];
    });
}
+ (void)initializeWithTestModeOn:(BOOL)testModeOn appPlatform:(UPResourceAppPlatform)appPlatform
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      UPResourceInjectionInstance = [[self alloc] initWithTestModeOn:testModeOn appPlatform:appPlatform];
    });
}

#pragma mark - Non-Public Methods
- (instancetype)initWithTestModeOn:(BOOL)testModeOn appPlatform:(UPResourceAppPlatform)appPlatform;
{
    if (self = [super init]) {
        _isTestModeOn = testModeOn;
        [UPResourceConfig shareInstance].appPlatform = appPlatform;
        NSString *appVersion = [UPResourceConfig shareInstance].appVersion;
        NSString *path = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
        NSString *resRootPath = [path stringByAppendingPathComponent:testModeOn ? UP_RES_ROOT_PATH_TEST : UP_RES_ROOT_PATH];
        id<UPResourceDataSource> dataSource;
        if (appPlatform == UPResourceAppPlatformSoutheasAsia) {
            dataSource = [[SEAOmsResDataSource alloc] initDataSourceWithTestDataEnabled:testModeOn];
        }
        else if (appPlatform == UPResourceAppPlatformChina) {
            dataSource = [[UPOmsResDataSource alloc] initDataSourceWithTestDataEnabled:testModeOn];
        }
        UPResourceFileSystem *fileSystem = [[UPResourceFileSystem alloc] init];
        UPResourceTimeSystem *timeSystem = [[UPResourceTimeSystem alloc] init];
        UPResourceDatabaseImpl *databaseDelegate = [[UPResourceDatabaseImpl alloc] initByCacheFolderPath:resRootPath fileDelegate:fileSystem testModeOn:testModeOn timeDelegate:timeSystem];
        UPConnectionMonitor *connectionDelegate = [[UPConnectionMonitor alloc] init];
        UPDownloaderDelegate *downloadDelegate = [[UPDownloaderDelegate alloc] init];
        UPResourceManager *resManager = [[UPResourceManager alloc] initResourceManagerWithAppVersion:appVersion resRootPath:resRootPath dataSource:dataSource databaseDelegate:databaseDelegate fileDelegate:fileSystem timeDelegate:timeSystem connectionDelegate:connectionDelegate downloadDelegate:downloadDelegate cleaner:nil];
        if ([resManager isKindOfClass:[UPResourceManager class]]) {
            resManager.environment = testModeOn ? UPResEnvironmentTest : UPResEnvironmentProduction;
            resManager.settings.environment = resManager.environment;
            NSString *path = [NSBundle mainBundle].bundlePath;
            NSString *rePath = [path stringByAppendingString:@"/PresetResPkg/"];
            id<UPPresetFileLoader> loader = [[UPAssetPresetFileLoader alloc] initFileLoader:rePath fileDelegate:self.resourceManager.fileDelegate];
            resManager.scanner = loader;
        }
        _resourceManager = resManager;
    }
    return self;
}

- (void)presetResPkg:(id<UPResourceListCallback>)callback
{

    NSString *path = [NSBundle mainBundle].bundlePath;
    NSString *rePath = [path stringByAppendingString:@"/PresetResPkg/"];
    [self presetResPkg:rePath callback:callback];
}

- (void)presetResPkg:(NSString *)filePath callback:(id<UPResourceListCallback>)callback
{

    id<UPPresetFileLoader> loader = [[UPAssetPresetFileLoader alloc] initFileLoader:filePath fileDelegate:self.resourceManager.fileDelegate];
    [self.resourceManager extractPresetResList:loader callback:callback];
}
- (UPResourceResult *)syncPresetResPkg:(NSString *)name type:(UPResourceType)type
{
    NSString *path = [NSBundle mainBundle].bundlePath;
    NSString *rePath = [path stringByAppendingString:@"/PresetResPkg/"];
    id<UPPresetFileLoader> loader = [[UPAssetPresetFileLoader alloc] initFileLoader:rePath fileDelegate:self.resourceManager.fileDelegate];
    return [self.resourceManager syncExtractPresetResInfo:loader name:name type:type];
}
@end

//
//  SEAResourceTransformer.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAResourceTransformer.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPOmsResInfo.h"
#import "UPResourceErrCodes.h"
#import "UPResourceConvertResult.h"
NSString *const kSEAOms_RES_Response_retCode = @"retCode";
NSString *const kSEAOms_RES_Response_retInfo = @"retInfo";
NSString *const kSEAOms_RES_Response_data = @"data";
NSString *const kSEAOms_RES_Response_resource = @"resources";
NSString *const kSEAOms_RES_Response_success = @"00000";

@implementation SEAResourceTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    if (![object isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误!";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *response = (NSDictionary *)object;
    UPLogDebug(@"UPResource", @"%s[%d]资源信息请求成功！response:%@", __PRETTY_FUNCTION__, __LINE__, UPRes_jsonStringValueOfJsonObject(response, NULL));
    NSString *retCode = response[kSEAOms_RES_Response_retCode];
    if (![retCode isKindOfClass:[NSString class]] || ![retCode isEqualToString:kSEAOms_RES_Response_success]) {
        NSString *errMsg = response[kSEAOms_RES_Response_retInfo];
        errMsg = [NSString stringWithFormat:@"资源信息请求接口返回错误！info:%@", errMsg];
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *data = response[kSEAOms_RES_Response_data];
    if (![data isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSArray *resources = data[kSEAOms_RES_Response_resource];
    if ([resources isKindOfClass:[NSNull class]]) {
        NSString *errMsg = @"资源包列表信息请求接口返回数据为空！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:UPResErrorCodeServerResponseEmptyList userInfo:nil];
        return convertResult;
    }
    if (![resources isKindOfClass:[NSArray class]]) {
        NSString *errMsg = @"资源包列表信息请求接口返回数据格式错误！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    return [self convertResouresList:resources];
}
- (id)convertResouresList:(NSArray *)resources
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    NSMutableArray<UPOmsResInfo *> *resourceArray = [NSMutableArray array];
    for (NSDictionary *dict in resources) {
        UPOmsResInfo *resource = [[UPOmsResInfo alloc] initWithDictionary:dict];
        if ([resource isKindOfClass:[UPOmsResInfo class]]) {
            [resourceArray addObject:resource];
        }
    }
    if (resourceArray.count == 0) {
        NSString *errMsg = @"资源包列表信息请求接口返回的资源包个数为0！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:UPResErrorCodeServerResponseEmptyList userInfo:nil];
        return convertResult;
    }
    convertResult.omsResInfoList = resourceArray;
    return convertResult;
}


@end

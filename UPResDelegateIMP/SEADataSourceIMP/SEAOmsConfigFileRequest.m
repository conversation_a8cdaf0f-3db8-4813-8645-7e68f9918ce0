//
//  SEAOmsConfigFileRequest.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAOmsConfigFileRequest.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
static NSString *const SEOMSConfigPathTest = @"/uplussea/resources/v1/gray/conf/list";
static NSString *const SEOMSConfigPath = @"/uplussea/resources/v1/conf/list";

NSString *const kSEAOms_CONFIG_RequestBody_model = @"model";
NSString *const kSEAOms_CONFIG_RequestBody_typeId = @"typeId";
NSString *const kSEAOms_CONFIG_RequestBody_proNo = @"prodNo";

@interface SEAOmsConfigFileRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *appversion;
@end

@implementation SEAOmsConfigFileRequest
- (instancetype)initRequestWithModel:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _testDataEnabled = testDataEnabled;
        _appversion = appversion;
    }
    return self;
}
- (NSString *)path
{
    return _testDataEnabled ? SEOMSConfigPathTest : SEOMSConfigPath;
}
- (NSString *)name
{
    return @"查询配置文件";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    if (!UPRes_isEmptyString(self.model)) {
        [bodyDict setValue:self.model forKey:kSEAOms_CONFIG_RequestBody_model];
    }
    if (!UPRes_isEmptyString(self.typeId)) {
        [bodyDict setValue:self.typeId forKey:kSEAOms_CONFIG_RequestBody_typeId];
    }
    if (!UPRes_isEmptyString(self.prodNo)) {
        [bodyDict setValue:self.prodNo forKey:kSEAOms_CONFIG_RequestBody_proNo];
    }
    return bodyDict;
}

@end

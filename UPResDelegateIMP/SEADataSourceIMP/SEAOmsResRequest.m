//
//  SEAOmsResRequest.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAOmsResRequest.h"
#import "UPResourceInfo.h"
static NSString *const SEOMSCommonPathTest = @"/uplussea/resources/v1/gray/bundle/list";
static NSString *const SEOMSCommonPath = @"/uplussea/resources/v1/bundle/list";

@interface SEAOmsResRequest ()
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *appversion;
@property (nonatomic, copy) NSString *resName;
@end

@implementation SEAOmsResRequest
- (instancetype)initRequestWithResourceType:(UPResourceType)type resName:(NSString *)resName appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _type = type;
        _testDataEnabled = testDataEnabled;
        _appversion = appversion;
        _resName = resName;
    }
    return self;
}
- (NSString *)name
{
    return @"通过类型和名字查询普通资源列表";
}
- (NSString *)path
{
    return _testDataEnabled ? SEOMSCommonPathTest : SEOMSCommonPath;
}
- (NSObject *)requestBody
{
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    NSString *restName = @"";
    if ([self.resName isKindOfClass:[NSString class]]) {
        restName = self.resName;
    }
    return @{ @"resType" : resTypeString,
              @"name" : restName
    };
}

@end

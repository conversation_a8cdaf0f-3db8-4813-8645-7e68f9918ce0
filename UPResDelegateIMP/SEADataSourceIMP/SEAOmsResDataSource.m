//
//  SEAOmsResDataSource.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAOmsResDataSource.h"
#import "UPResourceCondition.h"
#import <UPLog/UPLog.h>
#import "UPResourceQuery.h"
#import <upnetwork/UPNetwork.h>
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "SEAOmsResRequest.h"
#import "UPResCommonFunctions.h"
#import "SEAResourceTransformer.h"
#import "UPResourceConvertResult.h"
#import "SEAOmsDeviceSourceRequest.h"
#import "SEAOmsConfigFileRequest.h"
@implementation SEAOmsResDataSource
#pragma mark - UPResourceDataSource
- (void)searchDeviceResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceResource:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)searchDeviceConfigResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceConfig:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)searchDeviceCustomList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    UPLogError(@"UPResource", @"%s[%d]此接口未实现!", __PRETTY_FUNCTION__, __LINE__);
    if (completion) {
        completion(nil, [NSError errorWithDomain:@"此接口未实现" code:-1 userInfo:nil]);
    }
}

- (void)searchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateNormalResList:condition completion:completion];
}

#pragma mark - Non-Public Methods

- (void)requestWithAPI:(UPRequest *)api completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPLogDebug(@"UPResource", @"%s[%d]开始请求资源列表信息", __PRETTY_FUNCTION__, __LINE__);
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      UPResourceConvertResult *result = (UPResourceConvertResult *)responseObject;
      if (result.error) {
          if (completion) {
              completion(nil, result.error);
          }
          return;
      }
      NSArray<UPResourceInfo *> *infoList = [self convertResourceInfoFromOmsResourceInfoObjects:result.omsResInfoList];
      if (completion) {
          completion(infoList, nil);
      }

    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPLogError(@"UPResource", @"%s[%d]资源包列表信息请求失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
          if (completion) {
              completion(nil, [NSError errorWithDomain:error.description ?: NSCocoaErrorDomain code:error.code userInfo:nil]);
          }
        }];
}

- (void)updateNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    SEAOmsResRequest *request = [[SEAOmsResRequest alloc] initRequestWithResourceType:condition.resourceType resName:condition.resName appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[SEAResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}
- (void)updateDeviceConfig:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    SEAOmsConfigFileRequest *request = [[SEAOmsConfigFileRequest alloc] initRequestWithModel:condition.model typeId:condition.typeId prodNo:condition.prodNo appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[SEAResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (void)updateDeviceResource:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    SEAOmsDeviceSourceRequest *request = [[SEAOmsDeviceSourceRequest alloc] initRequestWithType:condition.resourceType model:condition.model typeId:condition.typeId prodNo:condition.prodNo deviceType:condition.deviceType appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[SEAResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (UPResourceInfo *)convertResourceInfoFromOmsResourceInfo:(UPOmsResInfo *)omsInfo
{
    if (![omsInfo isKindOfClass:[UPOmsResInfo class]]) {
        return nil;
    }
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.name = omsInfo.name;
    info.type = [UPResourceInfo typeValueOfString:omsInfo.resType];
    info.version = omsInfo.resVersion;
    info.link = omsInfo.resUrl;
    info.hashStr = omsInfo.md5;
    info.model = omsInfo.model;
    info.typeId = omsInfo.typeId;
    info.deviceTypeIndex = omsInfo.deviceTypeIndex;
    info.prodNo = omsInfo.prodNo;
    info.isServerLatest = YES;
    info.hideStatusBar = [omsInfo.hideStatusBar isEqualToString:@"1"] ? YES : NO;
    return info;
}

- (NSArray<UPResourceInfo *> *)convertResourceInfoFromOmsResourceInfoObjects:(NSArray<UPOmsResInfo *> *)omsInfoObjects
{
    if (UPRes_isEmptyArray(omsInfoObjects)) {
        return nil;
    }
    NSMutableArray<UPResourceInfo *> *infoArray = [NSMutableArray array];
    for (UPOmsResInfo *omsInfo in omsInfoObjects) {
        UPResourceInfo *info = [self convertResourceInfoFromOmsResourceInfo:omsInfo];
        if ([info isKindOfClass:[UPResourceInfo class]]) {
            [infoArray addObject:info];
        }
    }
    return infoArray;
}

@end

//
//  SEAOmsDeviceSourceRequest.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEAOmsDeviceSourceRequest.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
static NSString *const SEOMSDevicePathTest = @"/uplussea/resources/v1/gray/list";
static NSString *const SEOMSDevicePath = @"/uplussea/resources/v1/list";

NSString *const kSEAOms_SOURCE_RequestBody_resType = @"resType";
NSString *const kSEAOms_SOURCE_RequestBody_model = @"model";
NSString *const kSEAOms_SOURCE_RequestBody_typeId = @"typeId";
NSString *const kSEAOms_SOURCE_RequestBody_proNo = @"prodNo";
NSString *const kSEAOms_SOURCE_RequestBody_deviceType = @"deviceType";

@interface SEAOmsDeviceSourceRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *deviceType;
@property (nonatomic, copy) NSString *appversion;
@end

@implementation SEAOmsDeviceSourceRequest
- (instancetype)initRequestWithType:(UPResourceType)type model:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo deviceType:(NSString *)deviceType appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _type = type;
        _deviceType = deviceType;
        _testDataEnabled = testDataEnabled;
        _appversion = appversion;
    }
    return self;
}
- (NSString *)path
{
    return _testDataEnabled ? SEOMSDevicePathTest : SEOMSDevicePath;
}
- (NSString *)name
{
    return @"查询设备资源列表";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    if (!UPRes_isEmptyString(self.model)) {
        [bodyDict setValue:self.model forKey:kSEAOms_SOURCE_RequestBody_model];
    }
    if (UPRes_isEmptyString(self.typeId)) {
        self.typeId = @"";
    }
    [bodyDict setValue:self.typeId forKey:kSEAOms_SOURCE_RequestBody_typeId];

    if (!UPRes_isEmptyString(self.prodNo)) {
        [bodyDict setValue:self.prodNo forKey:kSEAOms_SOURCE_RequestBody_proNo];
    }
    if (!UPRes_isEmptyString(self.deviceType)) {
        [bodyDict setValue:self.deviceType forKey:kSEAOms_SOURCE_RequestBody_deviceType];
    }
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    [bodyDict setValue:resTypeString forKey:kSEAOms_SOURCE_RequestBody_resType];
    return bodyDict;
}
@end

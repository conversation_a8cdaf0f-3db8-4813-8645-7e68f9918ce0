//
//  SEARequestBase.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SEARequestBase.h"
#import "UPResourceConfig.h"
@implementation SEARequestBase
- (NSString *)baseURL
{
    if (UPRequestEnvTest == [UPResourceConfig shareInstance].requestEnv) {
        return @"https://uhome-sea-yanshou.haieriot.net";
    }
    else {
        return @"https://uhome-sea.haieriot.net";
    }
}
- (NSTimeInterval)retryDelay
{
    return 2;
}
- (NSUInteger)retryTimes
{
    return 2;
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}
- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSDictionary *body = (NSDictionary *)self.requestBody;
    NSMutableDictionary *header = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:body].mutableCopy;
    [header removeObjectForKey:@"appKey"];
    [header removeObjectForKey:@"accessToken"];
    return header;
}
@end

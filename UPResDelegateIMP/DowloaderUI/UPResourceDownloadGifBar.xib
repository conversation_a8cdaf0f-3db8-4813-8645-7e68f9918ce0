<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="22505" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22504"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" id="iN0-l3-epB" customClass="UPResourceDownloadGifBar">
            <rect key="frame" x="0.0" y="0.0" width="195" height="122"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <label opaque="NO" userInteractionEnabled="NO" alpha="0.39000000000000001" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" insetsLayoutMarginsFromSafeArea="NO" text="加载中...66%" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dVq-XM-B4v">
                    <rect key="frame" x="18" y="52" width="159" height="16"/>
                    <fontDescription key="fontDescription" type="system" pointSize="13"/>
                    <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <nil key="highlightedColor"/>
                </label>
                <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="CHZ-Gn-LqL">
                    <rect key="frame" x="73.5" y="0.0" width="48" height="48"/>
                    <constraints>
                        <constraint firstAttribute="width" constant="48" id="amj-CD-3uU"/>
                        <constraint firstAttribute="height" constant="48" id="dbI-7N-Ur9"/>
                    </constraints>
                </imageView>
            </subviews>
            <color key="backgroundColor" red="1" green="1" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
            <constraints>
                <constraint firstItem="CHZ-Gn-LqL" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="B89-83-awS"/>
                <constraint firstItem="dVq-XM-B4v" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" constant="18" id="CYC-4t-FCH"/>
                <constraint firstItem="CHZ-Gn-LqL" firstAttribute="centerX" secondItem="iN0-l3-epB" secondAttribute="centerX" id="Zrq-a8-nVS"/>
                <constraint firstItem="dVq-XM-B4v" firstAttribute="top" secondItem="CHZ-Gn-LqL" secondAttribute="bottom" constant="4" id="lTr-bG-4SK"/>
                <constraint firstAttribute="trailing" secondItem="dVq-XM-B4v" secondAttribute="trailing" constant="18" id="q3g-1l-itl"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <userDefinedRuntimeAttributes>
                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                    <integer key="value" value="12"/>
                </userDefinedRuntimeAttribute>
                <userDefinedRuntimeAttribute type="boolean" keyPath="layer.masksToBounds" value="YES"/>
            </userDefinedRuntimeAttributes>
            <connections>
                <outlet property="imageView" destination="CHZ-Gn-LqL" id="5ap-OL-LQH"/>
                <outlet property="imageViewHeightConstraint" destination="dbI-7N-Ur9" id="Lnm-7S-3YO"/>
                <outlet property="imageViewWidthConstraint" destination="amj-CD-3uU" id="nYZ-Ea-7VK"/>
                <outlet property="loadMessageLabel" destination="dVq-XM-B4v" id="dd3-Ug-yqa"/>
            </connections>
            <point key="canvasLocation" x="-84.057971014492765" y="-147.32142857142856"/>
        </view>
    </objects>
</document>

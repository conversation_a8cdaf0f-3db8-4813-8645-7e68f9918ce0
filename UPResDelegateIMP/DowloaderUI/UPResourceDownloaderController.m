//
//  UPResourceDownloaderController.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//


#import "UPResourceDownloaderController.h"
#import "UPResourceManager.h"
#import <UPVDN/UPVDN.h>
#import <UPVDN/UPVDNManager.h>
#import "UPResourceDownloadProgressBar.h"
#import "UPResourceTask.h"
#import "UPResourceInjection.h"
#import "UPResourceConfig.h"
#import "UPResourceSouthAsiaDownloadProgressBar.h"
#import "UPResourceDownloadGifBar.h"
#import "UPResourceSEADownloadGifBar.h"
#import "UPResourceUIMacros.h"
#import <UPTools/UPPreferLanguage.h>
#import <UPTools/KVNProgressShow.h>
#import <UHWebImage/UHWebImage.h>
#import <UPCore/UPFunctionToggle.h>
#import <UpTrace/UPEventTrace.h>

NSString *const PARAM_RES_TYPE = @"resInfoType";
NSString *const PARAM_RES_NAME = @"resInfoName";
NSString *const PARAM_RES_VERSION = @"resInfoVersion";
NSString *const PARAM_TARGET_URL = @"tarUrl";
NSString *const PARAM_VDNPAGEFLAG = @"VdnPageFlag";
NSString *const PARAM_SERVERRESVERSION = @"serverResourceVersion";
NSString *const KUPResourceShowMessage = @"Loading failed, please try later";
NSString *const MAINPKG = @"isMainPackage";
NSString *const KUPResourceShowNoInternetMessage = @"Internet lost, please try again later";
@interface UPResourceDownloaderController () <UPResourceCallback, UPResourceListener>

/**
 resourceManager
 **/
@property (nonatomic, strong) UPResourceManager *resourceManager;
/**
 targetUrl
 **/
@property (nonatomic, copy) NSString *targetUrl;
// 目标页面是否为设备详情页
@property (nonatomic) BOOL isDevicePage;

@property (nonatomic) BOOL isFirstAppear;

/**
 resTaskId
 **/
@property (nonatomic, copy) NSString *resTaskId;

/**
 downloadBar
 **/
@property (nonatomic, strong) UPResourceDownloadGifBar *downloadGifBar;
@property (nonatomic, strong) UPResourceSEADownloadGifBar *downloadSeaGifBar;

/// 是否主包
@property (nonatomic, assign) BOOL isMainPackage;
/**
 VNDFlag
 **/
@property (nonatomic, assign) VdnPageFlag flag;

/// 下载动画类型
@property (nonatomic, assign) UPResourceDownloadGifType downloadGifType;

/// 标识页面是否已经关闭
@property (nonatomic, assign) BOOL isClosed;
/// 导航栏之前的隐藏状态
@property (nonatomic, assign) BOOL navigationBarHidden;
@property (nonatomic, assign) BOOL popGesuter;
@end

@implementation UPResourceDownloaderController

- (instancetype)init
{
    if (self = [super init]) {
        self.modalPresentationStyle = UIModalPresentationOverFullScreen;
    }
    return self;
}
- (void)viewWillAppear:(BOOL)animated
{
    [super viewWillAppear:animated];
    [self.navigationController setNavigationBarHidden:YES animated:NO];
}
- (void)viewWillDisappear:(BOOL)animated
{
    [super viewWillDisappear:animated];
    [self.navigationController setNavigationBarHidden:self.navigationBarHidden animated:NO];
}

- (void)viewDidAppear:(BOOL)animated
{
    [super viewDidAppear:animated];
    if (self.isFirstAppear && !self.isDevicePage) {
        [self performSelector:@selector(updateLoadingMessageForNoneDevice) withObject:nil afterDelay:4.0];
    }
    self.isFirstAppear = NO;
}

- (void)viewDidDisappear:(BOOL)animated
{
    [super viewDidDisappear:animated];
    [NSObject cancelPreviousPerformRequestsWithTarget:self];
    self.isClosed = YES;
    [self.resourceManager cancel:self.resTaskId];
}

- (UPResourceDownloadGifType)downloadGifType
{
    if (self.resourceManager.gifTypeCallback == nil) {
        _downloadGifType = UPRHaierGif;
    }
    else {
        _downloadGifType = self.resourceManager.gifTypeCallback();
    }

    return _downloadGifType;
}

- (UPResourceDownloadGifBar *)downloadGifBar
{
    if (!_downloadGifBar) {
        _downloadGifBar = [UPResourceDownloadGifBar resBundleNib:CGRectMake((kWidth - 195) / 2.0, (kHeight - 72) / 2.0, 195, 72) type:self.downloadGifType];
        if (self.isDevicePage) {
            [_downloadGifBar setLoadingMessage:@"加载中...0%"];
        }
        else {
            [_downloadGifBar setLoadingMessage:@"加载中"];
        }
    }
    return _downloadGifBar;
}

- (UPResourceSEADownloadGifBar *)downloadSeaGifBar
{
    if (!_downloadSeaGifBar) {
        _downloadSeaGifBar = [[UPResourceSEADownloadGifBar alloc] initWithFrame:CGRectMake((kWidth - 188) / 2.0, (kHeight - 118) / 2.0, 188, 118)];
        _downloadSeaGifBar.percent = 0;
    }
    return _downloadSeaGifBar;
}

- (UPResourceManager *)resourceManager
{
    if (!_resourceManager) {
        _resourceManager = [UPResourceInjection getInstance].resourceManager;
    }
    return _resourceManager;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    self.navigationBarHidden = self.navigationController.navigationBarHidden;
    self.popGesuter = self.navigationController.interactivePopGestureRecognizer.isEnabled;
    self.navigationController.interactivePopGestureRecognizer.enabled = NO;
    self.view.backgroundColor = RGB(0, 0, 0, 0.2);
    self.isFirstAppear = YES;
    self.isDevicePage = [self.parameters[@"isDeviceResource"] boolValue];
    NSString *message = @"网络开小差了，请稍后再试～";
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        message = [UPPreferLanguage up_localizedStringForKey:KUPResourceShowNoInternetMessage withBundleClass:@"" bundleName:@"UPResourceRes"];
    }
    if (!self.resourceManager.connectionDelegate.isAvailable) {
        self.view.backgroundColor = RGB(0, 0, 0, 0);
        [self disMissAlterWithMessage:message];
        return;
    }
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformChina) {
        self.view.backgroundColor = [UIColor whiteColor];
        [self setupFakeNavigationBar];
        [self.view addSubview:self.downloadGifBar];
    }
    else if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        [self.view addSubview:self.downloadSeaGifBar];
    }
    [self checkAndInstallResource];
    [[UPEventTrace getInstance] trace:@"MB41000" withVariable:@{}];
}

- (UIView *)setupFakeNavigationBar
{
    CGFloat safeTop = 20.0;
    if (@available(iOS 11.0, *)) {
        safeTop = [UIApplication sharedApplication].delegate.window.safeAreaInsets.top;
    }
    CGFloat barHeight = safeTop + 44.0;
    CGFloat barWidth = [UIScreen mainScreen].bounds.size.width;
    UIView *bar = [[UIView alloc] initWithFrame:CGRectMake(0, 0, barWidth, barHeight)];
    bar.backgroundColor = self.view.backgroundColor;
    [self.view addSubview:bar];

    UIImage *backImg = [UIImage imageNamed:@"UPResourceRes.bundle/nav_back"];
    UIButton *closeButton = [UIButton buttonWithType:UIButtonTypeCustom];
    [closeButton setImage:backImg forState:UIControlStateNormal];
    CGFloat y = safeTop + ((barHeight - safeTop - backImg.size.height) / 2.0);
    closeButton.frame = CGRectMake(20, y, backImg.size.width, backImg.size.height);
    [closeButton addTarget:self action:@selector(closeButtonTouched:) forControlEvents:UIControlEventTouchUpInside];
    [bar addSubview:closeButton];
    return bar;
}

- (void)checkAndInstallResource
{
    NSDictionary *parameters = self.parameters;
    NSString *type = parameters[PARAM_RES_TYPE];
    NSString *name = parameters[PARAM_RES_NAME];
    NSString *version = parameters[PARAM_SERVERRESVERSION];
    self.targetUrl = parameters[PARAM_TARGET_URL];
    self.flag = [parameters[PARAM_VDNPAGEFLAG] integerValue];
    self.isMainPackage = [parameters[MAINPKG] boolValue];
    UPResourceInfo *info = nil;
    if (version && ![version isEqualToString:@""]) {
        info = [self.resourceManager getResourceInfo:name type:[UPResourceInfo typeValueOfString:type] version:version];
    }
    else {
        info = [self.resourceManager getLatestInfoByName:name type:[UPResourceInfo typeValueOfString:type]];
    }
    if (info == nil) {
        if ([UPResourceInfo typeValueOfString:type] == UPResourceTypeDeviceConfig || [UPResourceInfo typeValueOfString:type] == UPResourceTypeLua || [UPResourceInfo typeValueOfString:type] == UPResourceTypeConfigApp || [UPResourceInfo typeValueOfString:type] == UPResourceTypeAppFuncModel || [UPResourceInfo typeValueOfString:type] == UPResourceTypeRoutes || [UPResourceInfo typeValueOfString:type] == UPResourceTypeConfigFile) {
            [self goback:YES closeSelf:YES completion:nil];
            return;
        }
        void (^completion)(NSArray<UPResourceInfo *> *infoList, NSError *error) = [self updateNormalResListCallback:name type:type];
        [self.resourceManager updateNormalResList:name type:[UPResourceInfo typeValueOfString:type] completion:completion];
    }
    else {

        [self tryDownloadResInfo:info];
    }
}
- (void)goback:(BOOL)animated closeSelf:(BOOL)closeSelf completion:(void (^__nullable)(void))completion
{
    self.navigationController.interactivePopGestureRecognizer.enabled = self.popGesuter;
    UIViewController *vc = [self disMissVC];
    if (vc.presentedViewController == self) {
        [vc dismissViewControllerAnimated:NO completion:completion];
    }
    else {
        if (closeSelf) {
            [self.navigationController popViewControllerAnimated:animated];
        }
        if (completion) {
            completion();
        }
    }
}
- (void)showAlertViewOfError:(NSError *)error
{
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformChina) {
        if (error.code == -1 || error.code == 100001) {
            [self disMissAlterWithMessage:@"未查找到资源信息"];
        }
        else {
            [self disMissAlterWithMessage:@"网络开小差了，稍后再试吧"];
        }
    }
    else if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        [self disMissAlterWithMessage:[UPPreferLanguage up_localizedStringForKey:KUPResourceShowMessage withBundleClass:@"" bundleName:@"UPResourceRes"]];
    }
}
- (void)showAlertView
{
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformChina) {
        [self disMissAlterWithMessage:@"未查找到资源信息"];
    }
    else if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        [self disMissAlterWithMessage:[UPPreferLanguage up_localizedStringForKey:KUPResourceShowMessage withBundleClass:@"" bundleName:@"UPResourceRes"]];
    }
}
- (void)showAlertViewOfCreateTask
{
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformChina) {
        [self disMissAlterWithMessage:@"创建安装资源任务失败"];
    }
    else if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        [self disMissAlterWithMessage:[UPPreferLanguage up_localizedStringForKey:KUPResourceShowMessage withBundleClass:@"" bundleName:@"UPResourceRes"]];
    }
}
- (BOOL)tryDownloadResInfo:(UPResourceInfo *)info
{
    if (info == nil) {
        [self showAlertView];
        return NO;
    }
    NSArray *LoadremoteRes = [[UPFunctionToggle shareInstance] toggleValueForKey:@"UPResource.loadRemoteResource"];
    if ([LoadremoteRes containsObject:info.name] && info.remoteUrl.length) {
        dispatch_async(dispatch_get_main_queue(), ^{
          [self goback:NO
               closeSelf:NO
              completion:^{
                [self goToTargetPage:info.version];
              }];

        });
        return YES;
    }
    else {
        info.priority = UPResourceDownLoadPriorityHeight;
        self.resTaskId = [self.resourceManager install:info callback:self listener:self];
        if (self.resTaskId == nil) {
            [self showAlertViewOfCreateTask];
            return NO;
        }
        return YES;
    }
}
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{
    if (self.isClosed) {
        return;
    }

    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        message = [UPPreferLanguage up_localizedStringForKey:KUPResourceShowMessage withBundleClass:@"" bundleName:@"UPResourceRes"];
    }
    if (success) {
        if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
            self.downloadSeaGifBar.percent = 100;
        }
        else if (self.isDevicePage) {
            [self.downloadGifBar setLoadingMessage:@"加载中...100%"];
        }

        [self goback:NO
             closeSelf:NO
            completion:^{
              [self goToTargetPage:info.version];
              if (self.complete) {
                  self.complete(@{ @"result" : @(info.active),
                                   @"info" : info });
              }
            }];
    }
    else {
        if ([self judgeMainPackageAndShowMessage:message]) {
            return;
        }
        [self goback:YES
             closeSelf:YES
            completion:^{
              if (!info.active) {
                  [self showErrorAlterMessage:message];
              }
              if (self.complete) {
                  self.complete(@{ @"result" : @(info.active),
                                   @"info" : info });
              }
            }];
    }
}
- (BOOL)judgeMainPackageAndShowMessage:(NSString *)message
{
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        if (self.isMainPackage) {
            [self showErrorAlterMessage:message];
            return YES;
        }
    }
    return NO;
}

- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
}

- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
    if (self.isClosed || !self.isDevicePage) {
        return;
    }

    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformSoutheasAsia) {
        self.downloadSeaGifBar.percent = progress;
    }
    else {
        [self.downloadGifBar setLoadingMessage:[NSString stringWithFormat:@"加载中...%lu%%", (unsigned long)progress]];
    }
}

- (void)goToTargetPage:(NSString *)resVersion
{

    if (self.targetUrl) {
        NSData *data = [[NSData alloc] initWithBase64EncodedString:self.targetUrl options:0];
        self.targetUrl = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
        NSString *encodedString = [self urlPersentEncode:self.targetUrl];
        NSURLComponents *urlComponents = [NSURLComponents componentsWithString:encodedString];
        NSMutableDictionary *parameters = self.parameters.mutableCopy;
        if (!parameters) {
            parameters = [NSMutableDictionary dictionary];
        }
        if ([urlComponents.queryParameterMap.allKeys containsObject:PARAM_SERVERRESVERSION]) {
            [urlComponents removeQueryParameter:PARAM_SERVERRESVERSION];
        }
        if (resVersion) {
            parameters[PARAM_SERVERRESVERSION] = resVersion;
        }
        else {
            [parameters removeObjectForKey:PARAM_SERVERRESVERSION];
        }
        if ([UPResourceConfig shareInstance].appPlatform != UPResourceAppPlatformSoutheasAsia) {
            parameters[@"close_current_page"] = resVersion ? @"1" : @"0";
            parameters[@"animated"] = @"0";
        }

        [[UPVDNManager shareManager].vdnDomain goToPage:urlComponents.string flag:self.flag parameters:parameters complete:self.listener error:nil];
    }
}
- (void)showErrorAlterMessage:(NSString *)message
{
    if ([UPResourceConfig shareInstance].appPlatform == UPResourceAppPlatformChina) {
        [KVNProgressShow showText:message];
        return;
    }

    UIAlertView *alterview;
    if ([self.parameters[MAINPKG] isEqualToString:@"1"]) {
        alterview = [[UIAlertView alloc] initWithTitle:[UPPreferLanguage up_localizedStringForKey:@"Tips" withBundleClass:@"" bundleName:@"UPResourceRes"] message:[UPPreferLanguage up_localizedStringForKey:message withBundleClass:@"" bundleName:@"UPResourceRes"] delegate:self cancelButtonTitle:[UPPreferLanguage up_localizedStringForKey:@"Cancel" withBundleClass:@"" bundleName:@"UPResourceRes"] otherButtonTitles:[UPPreferLanguage up_localizedStringForKey:@"Try again" withBundleClass:@"" bundleName:@"UPResourceRes"], nil];
    }
    else {
        alterview = [[UIAlertView alloc] initWithTitle:[UPPreferLanguage up_localizedStringForKey:@"Tips" withBundleClass:@"" bundleName:@"UPResourceRes"] message:[UPPreferLanguage up_localizedStringForKey:message withBundleClass:@"" bundleName:@"UPResourceRes"] delegate:nil cancelButtonTitle:[UPPreferLanguage up_localizedStringForKey:@"OK" withBundleClass:@"" bundleName:@"UPResourceRes"] otherButtonTitles:nil, nil];
    }

    [alterview show];
}
- (void)alertView:(UIAlertView *)alertView clickedButtonAtIndex:(NSInteger)buttonIndex
{
    if ([self.parameters[MAINPKG] isEqualToString:@"1"]) {
        if (buttonIndex == 0) {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wundeclared-selector"
            [self performSelector:@selector(notExistCall)];
            abort();
#pragma clang diagnostic pop
        }
        else {
            [self checkAndInstallResource];
        }
    }
}
- (void)disMissAlterWithMessage:(NSString *)message
{
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
      if (self.isMainPackage) {
          [self showErrorAlterMessage:message];
      }
      else {
          [self goback:YES
               closeSelf:YES
              completion:^{
                [self showErrorAlterMessage:message];
              }];
      }
    });
}
- (UIViewController *)disMissVC
{
    UIViewController *vc = self.presentingViewController;
    if (vc) {
        return vc;
    }
    return self;
}

- (void)closeButtonTouched:(UIButton *)button
{
    /// cancel时会立即收到下载结果回调,先标识isClosed
    self.isClosed = YES;
    [self.resourceManager cancel:self.resTaskId];
    [self goback:YES closeSelf:YES completion:nil];
}

- (void)updateLoadingMessageForNoneDevice
{
    [self.downloadGifBar setLoadingMessage:@"精彩内容正在路上~"];
}

#pragma mark - private methods
- (void (^)(NSArray<UPResourceInfo *> *infoList, NSError *error))updateNormalResListCallback:(NSString *)name type:(NSString *)type
{
    __weak typeof(self) weakSelf = self;
    void (^completion)(NSArray<UPResourceInfo *> *infoList, NSError *error) = ^(NSArray<UPResourceInfo *> *infoList, NSError *error) {
      if (weakSelf.isClosed) {
          return;
      }

      if (error) {
          [weakSelf showAlertViewOfError:error];
          return;
      }
      NSMutableArray<UPResourceInfo *> *infos = [NSMutableArray array];
      [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        if ([name isKindOfClass:[NSString class]] && [obj.name isEqualToString:name] && obj.type == [UPResourceInfo typeValueOfString:type]) {
            [infos addObject:obj];
        }
      }];
      if (infos.count == 0) {
          [self showAlertView];
      }
      else if (infos.count == 1) {
          [self tryDownloadResInfo:infos.firstObject];
      }
      else {
          dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.2 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
            [self goback:NO
                 closeSelf:YES
                completion:^{
                  dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(0.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    [self goToTargetPage:nil];
                  });
                }];
          });
      }
    };

    return completion;
}

- (NSString *)urlPersentEncode:(NSString *)url
{
    if ([url isKindOfClass:NSString.class] && url.length > 0) {
        NSString *encodedString = (NSString *)CFBridgingRelease(CFURLCreateStringByAddingPercentEscapes(kCFAllocatorDefault, (CFStringRef)url, (CFStringRef) @"!$&'()*+,-./:;=?@_~%#[]", NULL, kCFStringEncodingUTF8));
        return encodedString;
    }

    return @"";
}
@end

//
//  UPResourceUIMacros.h
//  UPResDelegateIMP
//
//  Created by luxu on 2022/10/27.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#ifndef UPResourceUIMacros_h
#define UPResourceUIMacros_h

#define kWidth [UIScreen mainScreen].bounds.size.width
#define kHeight [UIScreen mainScreen].bounds.size.height
#define RGB(r, g, b, a) [[UIColor alloc] initWithRed:r / 255.0 green:g / 255.0 blue:b / 255.0 alpha:a * 1.0]

// RGB颜色转换（16进制->10进制）
#define UPRes_UIColorFromRGB(rgbValue)                                   \
    [UIColor colorWithRed:((float)((rgbValue & 0xFF0000) >> 16)) / 255.0 \
                    green:((float)((rgbValue & 0xFF00) >> 8)) / 255.0    \
                     blue:((float)(rgbValue & 0xFF)) / 255.0             \
                    alpha:1.0]

#define UP_LocalizedStringForKey(key) [UPPreferLanguage up_localizedStringForKey:key withBundleClass:@"" bundleName:@"UPResourceRes"]


#endif /* UPResourceUIMacros_h */

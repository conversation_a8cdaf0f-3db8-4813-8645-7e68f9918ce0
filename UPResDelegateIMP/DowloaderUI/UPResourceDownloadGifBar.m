//
//  UPResourceDownloadGifBar.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/5/11.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDownloadGifBar.h"
#import "UPResourceUITool.h"
static NSString *const kDownloadGifBarText = @"kDownloadGifBarText";
static NSString *const kDownloadGifBarMainGif = @"kDownloadGifBarMainGif";
static NSString *const kDownloadGifBarSecondaryGif = @"kDownloadGifBarSecondaryGif";

@interface UPResourceDownloadGifBar ()
@property (unsafe_unretained, nonatomic) IBOutlet UIImageView *imageView;
@property (unsafe_unretained, nonatomic) IBOutlet UIImageView *imageLine;
@property (unsafe_unretained, nonatomic) IBOutlet UILabel *loadMessageLabel;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *imageLineHeightConstraint;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *imageViewWidthConstraint;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *imageViewHeightConstraint;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *loadMessageWidthConstraint;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *loadMessageLeadConstraint;
@property (unsafe_unretained, nonatomic) IBOutlet NSLayoutConstraint *progressTrailConstraint;

@property (nonatomic, strong) NSDictionary<NSNumber *, NSDictionary<NSString *, NSString *> *> *dataSource;
@property (nonatomic, assign) UPResourceDownloadGifType gifType;

@end

@implementation UPResourceDownloadGifBar

- (NSDictionary<NSNumber *, NSDictionary<NSString *, NSString *> *> *)dataSource
{
    if (!_dataSource) {
        _dataSource = @{
            @(UPRHaierGif) : @{
                kDownloadGifBarText : @"加载中...0%",
                kDownloadGifBarMainGif : @"res_loading_animation",
                kDownloadGifBarSecondaryGif : @""
            },
            @(UPRXiaoYouGif) : @{
                kDownloadGifBarText : @"小优正在启动...",
                kDownloadGifBarMainGif : @"xiaoyou",
                kDownloadGifBarSecondaryGif : @""
            }
        };
    }

    return _dataSource;
}

- (void)setGifType:(UPResourceDownloadGifType)gifType
{
    _gifType = gifType;
    switch (_gifType) {
        case UPRHaierGif:
            [self configHaierGif];
            break;
        case UPRXiaoYouGif:
            [self configHaierGif];
            break;
        default:
            break;
    }
}

- (void)awakeFromNib
{
    [super awakeFromNib];
    self.autoresizingMask = UIViewAutoresizingFlexibleWidth;
    _gifType = UPRHaierGif;
}

- (void)setLoadingMessage:(NSString *)message
{
    self.loadMessageLabel.text = message;
}

- (NSString *)resBundleImageNamed:(NSString *)imageName
{
    return [NSString stringWithFormat:@"UPResourceRes.bundle/%@", imageName];
}

+ (UPResourceDownloadGifBar *)resBundleNib:(CGRect)frame type:(UPResourceDownloadGifType)type
{
    UPResourceDownloadGifBar *bar = [[NSBundle mainBundle] loadNibNamed:@"UPResourceDownloadGifBar" owner:self options:nil].firstObject;
    bar.gifType = type;
    bar.frame = frame;
    return bar;
}

#pragma mark - private method
- (void)configHaierGif
{
    NSDictionary<NSString *, NSString *> *gifContext = self.dataSource[@(UPRHaierGif)];
    NSString *bundlePath = [[NSBundle mainBundle] pathForResource:@"UPResourceRes" ofType:@"bundle"];
    NSBundle *resBundle = [NSBundle bundleWithPath:bundlePath];
    NSData *mainGifData = [NSData dataWithContentsOfFile:[resBundle pathForResource:gifContext[kDownloadGifBarMainGif] ofType:@"gif"]];
    NSData *secondaryGifData = [NSData dataWithContentsOfFile:[resBundle pathForResource:gifContext[kDownloadGifBarSecondaryGif] ofType:@"gif"]];
    self.imageView.image = [UPResourceUITool decodeGifImageByData:mainGifData];
    self.imageLine.image = [UPResourceUITool decodeGifImageByData:secondaryGifData];
    self.loadMessageLabel.text = gifContext[kDownloadGifBarText];
}

- (void)configXiaoYouGif
{
    NSDictionary<NSString *, NSString *> *gifContext = self.dataSource[@(UPRXiaoYouGif)];
    NSString *bundlePath = [[NSBundle mainBundle] pathForResource:@"UPResourceRes" ofType:@"bundle"];
    NSBundle *resBundle = [NSBundle bundleWithPath:bundlePath];
    NSData *mainGifData = [NSData dataWithContentsOfFile:[resBundle pathForResource:gifContext[kDownloadGifBarMainGif] ofType:@"gif"]];
    self.imageView.image = [UPResourceUITool decodeGifImageByData:mainGifData];
    self.loadMessageLabel.text = gifContext[kDownloadGifBarText];
    self.loadMessageWidthConstraint.constant = 90.0;
    self.loadMessageLeadConstraint.constant = 33.0;
    self.progressTrailConstraint.constant = 33.0;
    self.imageLineHeightConstraint.constant = 0.0;
    self.imageLine.hidden = YES;
}

@end

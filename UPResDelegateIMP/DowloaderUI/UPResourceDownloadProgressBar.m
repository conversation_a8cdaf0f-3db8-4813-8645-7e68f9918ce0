//
//  UPResourceDownloadProgressBar.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDownloadProgressBar.h"
#import "UPResourceUIMacros.h"

@interface UPResourceDownloadProgressBar ()

/**
 标题
 **/
@property (nonatomic, strong) UILabel *titleLabel;

/**
 进度条背景
 **/
@property (nonatomic, strong) UIView *pregressView;
/**
 进度条视图
 **/
@property (nonatomic, strong) UIView *trackView;
/**
 百分比label
 **/
@property (nonatomic, strong) UILabel *precentlab;


@end

@implementation UPResourceDownloadProgressBar

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        CGFloat viewW = self.frame.size.width;
        CGPathRef path = [UIBezierPath bezierPathWithRoundedRect:self.bounds byRoundingCorners:UIRectCornerTopLeft | UIRectCornerTopRight cornerRadii:CGSizeMake(10, 10)].CGPath;
        CAShapeLayer *lay = [CAShapeLayer layer];
        lay.path = path;
        self.layer.mask = lay;

        self.backgroundColor = [UIColor whiteColor];
        CGFloat titleLabelH = 18 * kScale;
        self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(39 * kScale, 35 * kScale, viewW - (45 + 48) * kScale - 20, titleLabelH)];
        self.titleLabel.font = [UIFont systemFontOfSize:17 * kScale];
        self.titleLabel.backgroundColor = RGB(255, 255, 255, 1);
        self.titleLabel.textAlignment = NSTextAlignmentLeft;
        self.titleLabel.textColor = [UIColor blackColor];
        [self addSubview:self.titleLabel];

        CGFloat precentX = viewW - (39 + 45) * kScale;
        CGFloat precentH = 18 * kScale;
        CGFloat precentY = 35 * kScale;
        self.precentlab = [[UILabel alloc] initWithFrame:CGRectMake(precentX, precentY, 45 * kScale, precentH)];
        self.precentlab.textAlignment = NSTextAlignmentRight;
        self.precentlab.textColor = RGB(34, 135, 227, 1);
        self.precentlab.font = [UIFont systemFontOfSize:17 * kScale];
        [self addSubview:self.precentlab];


        CGFloat pregressViewH = 4 * kScale;
        CGFloat pregressViewY = 80 * kScale;
        CGFloat pregressViewW = viewW - 2 * 39 * kScale;

        self.pregressView = [[UIView alloc] initWithFrame:CGRectMake(39 * kScale, pregressViewY, pregressViewW, pregressViewH)];
        self.pregressView.layer.borderColor = [UIColor clearColor].CGColor;
        self.pregressView.layer.borderWidth = 1;
        self.pregressView.layer.cornerRadius = 4 * kScale / 2.0;
        self.pregressView.layer.masksToBounds = YES;
        self.pregressView.backgroundColor = RGB(0, 0, 0, 0.0831);
        [self addSubview:self.pregressView];

        self.trackView = [[UIView alloc] initWithFrame:CGRectMake(39 * kScale, 79 * kScale, 0, pregressViewH * 1.5)];
        self.trackView.layer.borderColor = [UIColor clearColor].CGColor;
        self.trackView.layer.borderWidth = 1;
        self.trackView.layer.cornerRadius = pregressViewH * 1.5 / 2.0;
        self.trackView.backgroundColor = RGB(34, 131, 226, 1);
        self.trackView.layer.shadowColor = RGB(34, 131, 226, 1).CGColor;
        self.trackView.layer.shadowOffset = CGSizeMake(0, 3);
        self.trackView.layer.shadowOpacity = 0.2;
        self.trackView.layer.shadowRadius = 1;
        [self addSubview:self.trackView];
    }
    return self;
}
- (void)setPercent:(NSInteger)percent
{
    _percent = percent;
    self.precentlab.text = [NSString stringWithFormat:@"%ld%%", percent];
    self.trackView.frame = CGRectMake(39 * kScale, 79 * kScale, (self.frame.size.width - 2 * 39 * kScale) * percent / self.maxValue, 6 * kScale);
}

- (void)setTitleStr:(NSString *)titleStr
{
    _titleStr = titleStr;
    self.titleLabel.text = titleStr;
}
@end

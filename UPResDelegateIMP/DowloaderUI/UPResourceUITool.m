//
//  UPResourceUITool.m
//  UPResDelegateIMP
//
//  Created by luxu on 2022/10/27.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceUITool.h"

@implementation UPResourceUITool

#pragma mark -Public
+ (UIImage *)decodeGifImageByData:(NSData *)data
{
    CGImageSourceRef source = CGImageSourceCreateWithData((__bridge CFDataRef)data, nil);
    size_t count = CGImageSourceGetCount(source);
    UIImage *animationImage;
    if (count <= 1) {
        animationImage = [[UIImage alloc] initWithData:data];
    }
    else {
        NSMutableArray *imageArray = [NSMutableArray arrayWithCapacity:count];
        NSTimeInterval duration = 0.0f;
        for (size_t i = 0; i < count; i++) {
            CGImageRef imageRef = CGImageSourceCreateImageAtIndex(source, i, NULL);
            duration += [self imageDurationAtIndex:i source:source];
            [imageArray addObject:[UIImage imageWithCGImage:imageRef scale:[UIScreen mainScreen].scale orientation:UIImageOrientationUp]];
            CGImageRelease(imageRef);
        }
        if (!duration) {
            duration = (1.0f / 10.0f) * count;
        }
        animationImage = [UIImage animatedImageWithImages:imageArray duration:duration];
    }


    CFRelease(source);
    return animationImage;
}


#pragma mark -Private
+ (CGFloat)imageDurationAtIndex:(NSUInteger)index source:(CGImageSourceRef)source
{
    CGFloat imageDuration = 0.1f;
    CFDictionaryRef cfImageProperties = CGImageSourceCopyPropertiesAtIndex(source, index, nil);
    if (!cfImageProperties) {
        return imageDuration;
    }
    NSDictionary *imageProperties = (__bridge NSDictionary *)cfImageProperties;
    NSDictionary *gifProperties = [imageProperties valueForKey:(NSString *)kCGImagePropertyGIFDictionary];
    NSNumber *delayTime = [gifProperties valueForKey:(NSString *)kCGImagePropertyGIFUnclampedDelayTime];
    if (delayTime != nil) {
        imageDuration = delayTime.floatValue;
    }
    CFRelease(cfImageProperties);
    return imageDuration;
}

@end

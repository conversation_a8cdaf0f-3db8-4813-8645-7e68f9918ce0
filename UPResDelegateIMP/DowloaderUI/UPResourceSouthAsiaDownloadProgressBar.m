//
//  UPResourceSouthAsiaDownloadProgressBar.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/22.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceSouthAsiaDownloadProgressBar.h"
#import "UPResourceUIMacros.h"
#import <UPTools/UPPreferLanguage.h>
#import <UPLog/UPLog.h>

@interface UPResourceSouthAsiaDownloadProgressBar ()

/**
 标题
 **/
@property (nonatomic, strong) UILabel *titleLabel;

/**
 进度条背景
 **/
@property (nonatomic, strong) UIView *progressBackgroundView;
/**
 进度条视图
 **/
@property (nonatomic, strong) UIView *progressView;
/**
 百分比label
 **/
@property (nonatomic, strong) UILabel *percentLabel;


@end

@implementation UPResourceSouthAsiaDownloadProgressBar

- (id)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        CGFloat viewH = self.frame.size.height;
        CGFloat viewW = self.frame.size.width;
        self.backgroundColor = [UIColor whiteColor];
        CGFloat titleLabelH = 44;
        self.titleLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 20, viewW, titleLabelH)];
        self.titleLabel.textAlignment = NSTextAlignmentCenter;
        self.titleLabel.font = [UIFont systemFontOfSize:16];
        self.titleLabel.textColor = UPRes_UIColorFromRGB(0x333333);
        [self addSubview:self.titleLabel];


        CGFloat fProgressViewX = 30;
        NSString *kCurrentLanguage = [UPPreferLanguage preferLanguage];
        UPLogInfo(@"UPResource", @"%s[%d]kCurrentLanguage:%@", __PRETTY_FUNCTION__, __LINE__, kCurrentLanguage);
        if ([kCurrentLanguage isEqualToString:@"ur"]) {
            fProgressViewX = 30 + 40;
        }
        CGFloat fProgressViewHeight = 15;
        CGFloat fProgressViewY = titleLabelH + (viewH - CGRectGetMaxY(self.titleLabel.frame) - fProgressViewHeight) / 2.0;
        CGFloat fPercentLabelWidth = 40;
        CGFloat pregressViewW = viewW - fPercentLabelWidth - fProgressViewX * 2;
        if ([kCurrentLanguage isEqualToString:@"ur"]) {
            pregressViewW = viewW - fPercentLabelWidth - fProgressViewX;
        }
        CGRect rectProgressBackgroundView = CGRectMake(fProgressViewX, fProgressViewY, pregressViewW, fProgressViewHeight);

        self.progressBackgroundView = [[UIView alloc] initWithFrame:rectProgressBackgroundView];
        self.progressBackgroundView.layer.borderColor = [UIColor clearColor].CGColor;
        self.progressBackgroundView.layer.borderWidth = 1;
        self.progressBackgroundView.layer.cornerRadius = 7.5;
        self.progressBackgroundView.layer.masksToBounds = YES;
        self.progressBackgroundView.backgroundColor = RGB(34, 131, 226, 0.2);
        [self addSubview:self.progressBackgroundView];

        self.progressView = [[UIView alloc] initWithFrame:CGRectMake(CGRectGetMinX(rectProgressBackgroundView), CGRectGetMinY(rectProgressBackgroundView), 0, CGRectGetHeight(rectProgressBackgroundView))];
        self.progressView.layer.borderColor = [UIColor clearColor].CGColor;
        self.progressView.layer.borderWidth = 1;
        self.progressView.layer.cornerRadius = 7.5;
        self.progressView.layer.masksToBounds = YES;
        self.progressView.backgroundColor = RGB(34, 131, 226, 1);
        [self addSubview:self.progressView];

        CGFloat fPercentX = CGRectGetMaxX(rectProgressBackgroundView) + 5;
        CGFloat fPercentY = CGRectGetMinY(rectProgressBackgroundView);
        if ([kCurrentLanguage isEqualToString:@"ur"]) {
            fPercentX = CGRectGetMinX(rectProgressBackgroundView) - 45;
            fPercentY = CGRectGetMinY(rectProgressBackgroundView);
        }
        self.percentLabel = [[UILabel alloc] initWithFrame:CGRectMake(fPercentX, fPercentY, fPercentLabelWidth, fProgressViewHeight)];
        self.percentLabel.textAlignment = NSTextAlignmentCenter;
        self.percentLabel.textColor = RGB(34, 135, 227, 1);
        self.percentLabel.font = [UIFont systemFontOfSize:14];
        [self addSubview:self.percentLabel];
        self.backgroundColor = UPRes_UIColorFromRGB(0xFFFFFF);
        self.layer.cornerRadius = 10;
    }
    return self;
}

- (void)setPercent:(NSInteger)percent
{
    _percent = percent;

    self.percentLabel.text = [NSString stringWithFormat:@"%ld%%", percent];
    CGRect rect = self.progressBackgroundView.frame;
    self.progressView.frame = CGRectMake(CGRectGetMinX(rect), CGRectGetMinY(rect), CGRectGetWidth(rect) * percent / self.maxValue, CGRectGetHeight(rect));
}

- (void)setTitleStr:(NSString *)titleStr
{

    _titleStr = titleStr;

    self.titleLabel.text = titleStr;
}
@end

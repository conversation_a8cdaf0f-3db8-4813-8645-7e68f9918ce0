//
//  UPResourceSEADownloadGifBar.m
//  UPResDelegateIMP
//
//  Created by luxu on 2022/10/27.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceSEADownloadGifBar.h"
#import "UPResourceUIMacros.h"
#import "UPResourceUITool.h"
#import <UPTools/UPPreferLanguage.h>

@interface UPResourceSEADownloadGifBar ()

@property (nonatomic, strong) UIImageView *loadingAnimationView;
@property (nonatomic, strong) UILabel *percentLabel;

@end

@implementation UPResourceSEADownloadGifBar

- (instancetype)initWithFrame:(CGRect)frame
{
    self = [super initWithFrame:frame];
    if (self) {
        self.backgroundColor = UPRes_UIColorFromRGB(0x1C1C1E);
        self.layer.cornerRadius = 14.f;
        self.layer.masksToBounds = YES;

        [self setupSubview];
    }
    return self;
}

- (void)setupSubview
{
    UIImage *gifImage = [self loadingAnimationImage];
    CGFloat loadingX = (CGRectGetWidth(self.frame) - 72.f) / 2.f;
    self.loadingAnimationView = [[UIImageView alloc] initWithFrame:CGRectMake(loadingX, 8.f, 72.f, 72.f)];
    self.loadingAnimationView.image = gifImage;
    [self addSubview:self.loadingAnimationView];

    CGFloat percentY = CGRectGetMaxY(self.loadingAnimationView.frame) + 5.f;
    self.percentLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, percentY, CGRectGetWidth(self.frame), 20.f)];
    self.percentLabel.textAlignment = NSTextAlignmentCenter;
    self.percentLabel.textColor = [UIColor whiteColor];
    self.percentLabel.font = [UIFont systemFontOfSize:17.f];
    [self addSubview:self.percentLabel];
}

#pragma mark -Setter
- (void)setPercent:(NSInteger)percent
{
    _percent = percent;
    self.percentLabel.text = [NSString stringWithFormat:@"%@ %ld%%", UP_LocalizedStringForKey(@"Loading…"), percent];
}

#pragma mark -Private
- (UIImage *)loadingAnimationImage
{
    NSString *bundlePath = [[NSBundle mainBundle] pathForResource:@"UPResourceRes" ofType:@"bundle"];
    NSBundle *resBundle = [NSBundle bundleWithPath:bundlePath];
    NSData *gifData = [NSData dataWithContentsOfFile:[resBundle pathForResource:@"sealoading" ofType:@"gif"]];
    UIImage *gifImage = [UPResourceUITool decodeGifImageByData:gifData];
    return gifImage;
}
@end

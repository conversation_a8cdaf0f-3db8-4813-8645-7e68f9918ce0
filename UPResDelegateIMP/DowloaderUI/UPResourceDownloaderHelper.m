//
//  UPResourceDownloaderHelper.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/4/4.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDownloaderHelper.h"
#import "UPResourceDownloaderController.h"
#import "UPResourceInjection.h"
#import <UPVDN/UIViewController+Vdn.h>
#import "UPResourceCondition.h"
#import "UPResourceInjection.h"
#import "UpResourceSelector.h"
#import <UPLog/UPLog.h>
#import <UPVDN/UpVdnUtils.h>
#import "UPResourceListener.h"
#import "UPResourceCallback.h"
@interface UPResourceDownloaderHelper () <UpResourceSelector, UPResourceCallback, UPResourceListener>

/**
 url
 **/
@property (nonatomic, copy) NSString *dowloadUrl;

@end
@implementation UPResourceDownloaderHelper

- (instancetype)initResourceDownloaderHelper:(NSString *)resName type:(UPResourceType)type
{

    if (self = [super init]) {

        _type = type;
        _resName = resName;

        if (resName) {

            _dowloadUrl = [NSString stringWithFormat:@"http://uplus.haier.com/uplusapp/UpResource/download.html?resInfoType=%@&resInfoName=%@", [UPResourceInfo stringValueOfResourceType:type], resName];
        }
    }

    return self;
}

- (void)startDownloader:(void (^)(NSDictionary *_Nonnull))complete
{

    if (!self.dowloadUrl) {

        if (complete) {
            complete(@{ @"result" : @(0),
                        @"message" : @"无可用下载页面地址" });
        }
    }

    UPResourceInfo *info = [[UPResourceInjection getInstance].resourceManager getLatestInfoByName:self.resName type:self.type];
    //    if ([UPResourceInjection getInstance].resourceManager.environment == UPResEnvironmentTest) {
    //        info = nil;
    //    }

    if (info.active) {
        if (complete) {
            complete(@{ @"result" : @(1),
                        @"message" : @"资源已安装",
                        @"info" : info });
        }
        [[UPResourceInjection getInstance].resourceManager getCommonResource:self.resName type:self.type selector:self callback:self listener:self];
    }
    else {

        UPResourceDownloaderController *vc = [[UPResourceDownloaderController alloc] init];
        vc.complete = complete;
        vc.originalURL = self.dowloadUrl;
        vc.parameters = @{ @"resInfoType" : [UPResourceInfo stringValueOfResourceType:self.type],
                           @"resInfoName" : self.resName
        };
        [UpVdnUtils presentToViewController:vc animated:NO completion:nil error:nil];
    }
}

- (UPResourceInfo *)selectFrom:(NSArray<UPResourceInfo *> *)infoList
{
    __block UPResourceInfo *info = nil;
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {

      if ([obj.name isEqualToString:self.resName] && obj.type == self.type) {
          info = obj;
          *stop = YES;
      }
    }];
    return info;
}
- (void)onResult:(BOOL)success message:(NSString *)message resourceInfo:(UPResourceInfo *)info
{

    UPLogInfo(@"UPResource", @"%@", message);
}
- (void)onPrompt:(UPResourceTask *)task action:(NSString *)action processor:(id<UPResourceProcessor>)processor
{
}
- (void)onProgressChanged:(NSString *)processor progress:(NSUInteger)progress
{
}

@end

//
//  UPResourceTransformer.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/20.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceTransformer.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPOmsResInfo.h"
#import "UPResourceErrCodes.h"
#import "UPResourceConvertResult.h"
NSString *const kUPOms_RES_Response_retCode = @"retCode";
NSString *const kUPOms_RES_Response_retInfo = @"retInfo";
NSString *const kUPOms_RES_Response_data = @"data";
NSString *const kUPOms_RES_Response_resource = @"resource";
NSString *const kUPOms_RES_Response_success = @"00000";

@implementation UPResourceTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    if (![object isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误!";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *response = (NSDictionary *)object;
    UPLogDebug(@"UPResource", @"%s[%d]资源信息请求成功！response:%@", __PRETTY_FUNCTION__, __LINE__, UPRes_jsonStringValueOfJsonObject(response, NULL));
    NSString *retCode = response[kUPOms_RES_Response_retCode];
    if (![retCode isKindOfClass:[NSString class]] || ![retCode isEqualToString:kUPOms_RES_Response_success]) {
        NSString *errMsg = response[kUPOms_RES_Response_retInfo];
        errMsg = [NSString stringWithFormat:@"资源信息请求接口返回错误！info:%@", errMsg];
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *data = response[kUPOms_RES_Response_data];
    if (![data isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSArray *resources = data[kUPOms_RES_Response_resource];
    if ([resources isKindOfClass:[NSNull class]]) {
        NSString *errMsg = @"资源包列表信息请求接口返回数据为空！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:UPResErrorCodeServerResponseEmptyList userInfo:nil];
        return convertResult;
    }
    if (![resources isKindOfClass:[NSArray class]]) {
        NSString *errMsg = @"资源包列表信息请求接口返回数据格式错误！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    return [self convertResouresList:resources];
}
- (id)convertResouresList:(NSArray *)resources
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    NSMutableArray<UPOmsResInfo *> *resourceArray = [NSMutableArray array];
    for (NSDictionary *dict in resources) {
        UPOmsResInfo *resource = [[UPOmsResInfo alloc] initWithDictionary:dict];
        if ([resource isKindOfClass:[UPOmsResInfo class]]) {
            [resourceArray addObject:resource];
        }
    }
    if (resourceArray.count == 0) {
        NSString *errMsg = @"资源包列表信息请求接口返回的资源包个数为0！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:UPResErrorCodeServerResponseEmptyList userInfo:nil];
        return convertResult;
    }
    convertResult.omsResInfoList = resourceArray;
    return convertResult;
}
@end

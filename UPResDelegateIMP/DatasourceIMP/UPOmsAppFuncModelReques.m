//
//  UPOmsAppFuncModelReques.m
//  UPResDelegateIMP
//
//  Created by 冉东军 on 2022/12/13.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsAppFuncModelReques.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"

@interface UPOmsAppFuncModelReques ()
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
@end

@implementation UPOmsAppFuncModelReques
- (instancetype)initRequestWithModel:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo type:(UPResourceType)type testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _type = type;
        _testDataEnabled = testDataEnabled;
    }
    return self;
}

- (NSString *)path
{
    return @"/api-gw/zjBaseServer/resource/getResourceConfig";
}
- (NSString *)name
{
    return @"查询资源配置";
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSDictionary *body = (NSDictionary *)self.requestBody;
    NSMutableDictionary *header = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:body].mutableCopy;
    if (UPRes_isEmptyString([header objectForKey:@"accessToken"])) {
        [header removeObjectForKey:@"accessToken"];
    }
    else {
        header[@"accountToken"] = [header objectForKey:@"accessToken"];
    }
    header[@"grayMode"] = self.testDataEnabled ? @"true" : @"false";
    return header;
}
- (NSObject *)requestBody
{
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    bodyDict[@"resType"] = resTypeString ?: @"";
    bodyDict[@"prodNo"] = self.prodNo ?: @"";
    bodyDict[@"typeId"] = self.typeId ?: @"";
    bodyDict[@"model"] = self.model ?: @"";
    return bodyDict;
}

- (NSTimeInterval)retryDelay
{
    return 2;
}

- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}
@end

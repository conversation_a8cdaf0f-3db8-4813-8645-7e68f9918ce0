//
//  UPOmsDeviceSourceRequest.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/1/9.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsDeviceSourceRequest.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
NSString *const UPOms_SOURCE_V3_API = @"/omsappapi/resource/v4/list";
NSString *const UPOms_SOURCE_V3_API_TEST = @"/omsappapi/resource/v4/test/list";


NSString *const kUPOms_SOURCE_RequestBody_resType = @"resType";
NSString *const kUPOms_SOURCE_RequestBody_model = @"model";
NSString *const kUPOms_SOURCE_RequestBody_typeId = @"typeId";
NSString *const kUPOms_SOURCE_RequestBody_proNo = @"prodNo";
NSString *const kUPOms_SOURCE_RequestBody_deviceType = @"deviceType";
NSString *const kUPOms_SOURCE_RequestBody_deviceNetType = @"deviceNetType";
NSString *const kUPOms_SOURCE_RequestBody_localCode = @"localCode";

@interface UPOmsDeviceSourceRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *deviceType;
@property (nonatomic, copy) NSString *appversion;
@property (nonatomic, copy) NSString *deviceNetType;
@property (nonatomic, copy) NSString *localCode;
@end

@implementation UPOmsDeviceSourceRequest
- (instancetype)initRequestWithType:(UPResourceType)type model:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo deviceType:(NSString *)deviceType appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled deviceNetType:(NSString *)deviceNetType localCode:(NSString *)localCode
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _type = type;
        _deviceType = deviceType;
        _testDataEnabled = testDataEnabled;
        _appversion = appversion;
        _deviceNetType = deviceNetType;
        _localCode = localCode;
    }
    return self;
}
- (NSString *)path
{
    return _testDataEnabled ? UPOms_SOURCE_V3_API_TEST : UPOms_SOURCE_V3_API;
}
- (NSString *)name
{
    return @"查询设备资源列表";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    if (!UPRes_isEmptyString(self.model)) {
        [bodyDict setValue:self.model forKey:kUPOms_SOURCE_RequestBody_model];
    }
    if (UPRes_isEmptyString(self.typeId)) {
        self.typeId = @"";
    }
    [bodyDict setValue:self.typeId forKey:kUPOms_SOURCE_RequestBody_typeId];

    if (!UPRes_isEmptyString(self.prodNo)) {
        [bodyDict setValue:self.prodNo forKey:kUPOms_SOURCE_RequestBody_proNo];
    }
    if (!UPRes_isEmptyString(self.deviceType)) {
        [bodyDict setValue:self.deviceType forKey:kUPOms_SOURCE_RequestBody_deviceType];
    }
    if (!UPRes_isEmptyString(self.deviceNetType)) {
        [bodyDict setValue:self.deviceNetType forKey:kUPOms_SOURCE_RequestBody_deviceNetType];
    }
    if (!UPRes_isEmptyString(self.localCode)) {
        [bodyDict setValue:self.localCode forKey:kUPOms_SOURCE_RequestBody_localCode];
    }
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    [bodyDict setValue:resTypeString forKey:kUPOms_SOURCE_RequestBody_resType];
    return bodyDict;
}
@end

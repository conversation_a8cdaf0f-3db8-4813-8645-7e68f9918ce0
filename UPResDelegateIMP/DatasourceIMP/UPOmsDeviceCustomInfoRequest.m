//
//  UPOmsDeviceCustomInfoRequest.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/6.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsDeviceCustomInfoRequest.h"
#import "UPResCommonFunctions.h"

NSString *const UPOms_SOURCE_DeviceCustomInfo = @"/adc/device/release/getDeviceCustomizationInfo";
NSString *const UPOms_SOURCE_DeviceCustomInfo_TEST = @"/adc/device/debug/getDeviceCustomizationInfo";

@interface UPOmsDeviceCustomInfoRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *prodNo;
@end

@implementation UPOmsDeviceCustomInfoRequest
- (instancetype)initRequestWithProdNo:(NSString *)prodNo testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _prodNo = prodNo;
        _testDataEnabled = testDataEnabled;
    }
    return self;
}
- (NSString *)name
{
    return @"查询型号定制信息接口";
}
- (NSString *)baseURL
{
    return @"https://api.haigeek.com";
}
- (NSTimeInterval)retryDelay
{
    return 2;
}
- (NSUInteger)retryTimes
{
    return 2;
}
- (NSString *)path
{
    return self.testDataEnabled ? UPOms_SOURCE_DeviceCustomInfo_TEST : UPOms_SOURCE_DeviceCustomInfo;
}
- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}
- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSDictionary *body = (NSDictionary *)self.requestBody;
    NSMutableDictionary *header = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:body].mutableCopy;
    [header removeObjectForKey:@"accessToken"];
    return header;
}
- (NSObject *)requestBody
{
    return @{ @"productCode" : self.prodNo ?: @"" };
}
@end

//
//  UPOmsResListRequest.h
//  UPResDelegateIMP
//
//  Created by 景彦铭 on 2022/9/26.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRequestBase.h"
#import "UPResourceType.h"
NS_ASSUME_NONNULL_BEGIN

@interface UPOmsResListRequest : UPResourceRequestBase

/// 初始化
/// @param type 资源类型
/// @param resList 资源列表 eg: [{"name": "xxx", "version": "1.0.0"}]
/// 若不存在资源版本传空字符串 eg: [{"name": "xxx", "version": ""}]
/// @param testDataEnabled 是否灰度模式
- (instancetype)initRequestWithResourceType:(UPResourceType)type resList:(NSArray<NSDictionary<NSString *, NSString *> *> *)resList testDataEnabled:(BOOL)testDataEnabled;

@end

NS_ASSUME_NONNULL_END

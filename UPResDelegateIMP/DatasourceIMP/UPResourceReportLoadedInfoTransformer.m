//
//  UPResourceReportLoadedInfoTransformer.m
//  UPResDelegateIMP
//
//  Created by 吴子航 on 2023/3/24.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceReportLoadedInfoTransformer.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPResourceErrCodes.h"
#import "UPResourceConvertResult.h"

@implementation UPResourceReportLoadedInfoTransformer

- (NSObject *)parseResponseObject:(NSObject *)responseObject
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    if (![responseObject isKindOfClass:[NSDictionary class]]) {
        UPLogError(@"UPResource", @"%s[%d]资源触达上报接口返回数据格式错误！", __PRETTY_FUNCTION__, __LINE__);
        convertResult.error = [NSError errorWithDomain:@"response object invalidate" code:0 userInfo:nil];
    }
    NSDictionary *response = (NSDictionary *)responseObject;
    if (![response[@"retCode"] isEqualToString:@"00000"]) {
        UPLogError(@"UPResource", @"%s[%d]资源触达上报请求失败！error:", __PRETTY_FUNCTION__, __LINE__, response[@"retInfo"]);
        convertResult.error = [NSError errorWithDomain:@"response failure" code:0 userInfo:nil];
    }

    return convertResult;
}

@end

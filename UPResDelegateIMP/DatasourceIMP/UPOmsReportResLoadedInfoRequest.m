//
//  UPOmsReportResLoadedInfoRequest.m
//  UPResDelegateIMP
//
//  Created by 吴子航 on 2023/3/22.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsReportResLoadedInfoRequest.h"

@interface UPOmsReportResLoadedInfoRequest ()

@property (nonatomic, strong) NSArray *infos;

@end

@implementation UPOmsReportResLoadedInfoRequest

- (instancetype)initRequestWithInfos:(NSArray *)infos
{
    if (self = [super init]) {
        _infos = infos;
    }

    return self;
}

- (NSString *)path
{
    return @"/api-gw/upmapi/appmanage/resource/v2/touchingList";
}
- (NSString *)name
{
    return @"资源触达接口";
}

- (NSObject *)requestBody
{
    return @{ @"resourceList" : self.infos ?: @[] };
}

- (NSTimeInterval)retryDelay
{
    return 1;
}
- (NSUInteger)retryTimes
{
    return 1;
}

@end

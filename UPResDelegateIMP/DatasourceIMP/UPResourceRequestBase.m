//
//  UPResourceRequestBase.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/21.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRequestBase.h"
#import "UPResourceConfig.h"
#import "UPResCommonFunctions.h"
@implementation UPResourceRequestBase
- (NSString *)baseURL
{
    if (UPRequestEnvProduction == [UPResourceConfig shareInstance].requestEnv)
        return @"https://zj.haier.net";
    else if (UPRequestEnvTest == [UPResourceConfig shareInstance].requestEnv)
        return @"https://zj-yanshou.haier.net";
    else
        return @"https://zj.haier.net";
}

- (UPRequestMethod)method
{
    return UPRequestMethodPOST;
}

- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSDictionary *body = (NSDictionary *)self.requestBody;
    NSMutableDictionary *header = [UPCommonServerHeader uwsHeaderWithUrlString:self.path body:body].mutableCopy;
    if (UPRes_isEmptyString([header objectForKey:@"accessToken"])) {
        [header removeObjectForKey:@"accessToken"];
    }
    return header;
}


@end

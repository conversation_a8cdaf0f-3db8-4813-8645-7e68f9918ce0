//
//  UPResourceDeviceCustomInfoTransformer.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/7/6.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDeviceCustomInfoTransformer.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>
#import "UPOmsResInfo.h"
#import "UPResourceErrCodes.h"
#import "UPResourceConvertResult.h"
@implementation UPResourceDeviceCustomInfoTransformer
- (NSObject *)parseResponseObject:(NSObject *)object
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    if (![object isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误!";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *response = (NSDictionary *)object;
    UPLogDebug(@"UPResource", @"%s[%d]资源信息请求成功！response:%@", __PRETTY_FUNCTION__, __LINE__, UPRes_jsonStringValueOfJsonObject(response, NULL));
    NSString *retCode = response[@"retCode"];
    if (![retCode isKindOfClass:[NSString class]] || ![retCode isEqualToString:@"00000"]) {
        NSString *errMsg = response[@"retInfo"];
        errMsg = [NSString stringWithFormat:@"资源信息请求接口返回错误！info:%@", errMsg];
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    NSDictionary *data = response[@"data"];
    if (![data isKindOfClass:[NSDictionary class]]) {
        NSString *errMsg = @"资源信息请求接口返回数据格式错误！";
        UPLogError(@"UPResource", @"%s[%d]%@", __PRETTY_FUNCTION__, __LINE__, errMsg);
        convertResult.error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
        return convertResult;
    }
    return [self convertResouresList:data];
}
- (id)convertResouresList:(NSDictionary *)resourceDic
{
    UPResourceConvertResult *convertResult = [[UPResourceConvertResult alloc] init];
    UPOmsResInfo *resInfo = [[UPOmsResInfo alloc] init];
    resInfo.name = UPRes_stringValueOfDictionaryForKey(resourceDic, @"resourceName");
    resInfo.resType = UPRes_stringValueOfDictionaryForKey(resourceDic, @"resourceType");
    resInfo.resUrl = UPRes_stringValueOfDictionaryForKey(resourceDic, @"resourceUrl");
    resInfo.md5 = UPRes_stringValueOfDictionaryForKey(resourceDic, @"resourceSign");
    resInfo.prodNo = UPRes_stringValueOfDictionaryForKey(resourceDic, @"productCode");
    resInfo.resVersion = UPRes_stringValueOfDictionaryForKey(resourceDic, @"resourceVersion");
    convertResult.omsResInfoList = @[ resInfo ];
    return convertResult;
}
@end

//
//  UPOmsResRequest.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/17.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsResRequest.h"
#import "UPResourceInfo.h"
NSString *const UPOms_RES_API = @"/omsappapi/resource/bag/v1/list";
NSString *const UPOms_RES_API_TEST = @"/omsappapi/resource/bag/v1/test/list";

@interface UPOmsResRequest ()
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *appversion;
@end

@implementation UPOmsResRequest
- (instancetype)initRequestWithResourceType:(UPResourceType)type appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _type = type;
        _testDataEnabled = testD<PERSON>nabled;
        _appversion = appversion;
    }
    return self;
}
- (NSString *)name
{
    return @"通过类型查询普通资源列表";
}
- (NSString *)path
{
    return _testDataEnabled ? UPOms_RES_API_TEST : UPOms_RES_API;
}
- (NSObject *)requestBody
{
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    return @{ @"resType" : resTypeString };
}
@end

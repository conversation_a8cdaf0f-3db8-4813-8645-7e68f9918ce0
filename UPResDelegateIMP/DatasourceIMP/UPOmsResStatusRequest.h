//
//  UPOmsResStatusRequest.h
//  UPResDelegateIMP
//
//  Created by 景彦铭 on 2022/12/7.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceRequestBase.h"
#import "UPResourceType.h"

NS_ASSUME_NONNULL_BEGIN

@interface UPOmsResStatusRequest : UPResourceRequestBase

/// 初始化
/// 内部会将资源列表中版本号为空的资源进行过滤，如果过滤之后，资源列表为空则此方法返回 nil
/// @param type 资源类型
/// @param resList 资源列表 eg: [{"name": "xxx", "version": "1.0.0"}]
/// @param testDataEnabled 是否灰度模式
- (instancetype _Nullable)initRequestWithResourceType:(UPResourceType)type resList:(NSArray<NSDictionary<NSString *, NSString *> *> *)resList testDataEnabled:(BOOL)testDataEnabled;

@end

NS_ASSUME_NONNULL_END

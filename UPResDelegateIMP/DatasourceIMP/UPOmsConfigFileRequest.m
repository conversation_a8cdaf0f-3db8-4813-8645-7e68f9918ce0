//
//  UPOmsCfgReqBody.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsConfigFileRequest.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
NSString *const UPOms_CONFIG_API = @"/omsappapi/resource/conf/list";
NSString *const UPOms_CONFIG_API_TEST = @"/omsappapi/resource/conf/test/list";

NSString *const kUPOms_CONFIG_RequestBody_model = @"model";
NSString *const kUPOms_CONFIG_RequestBody_typeId = @"typeId";
NSString *const kUPOms_CONFIG_RequestBody_proNo = @"prodNo";

@interface UPOmsConfigFileRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *appversion;
@end

@implementation UPOmsConfigFileRequest
- (instancetype)initRequestWithModel:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled;
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _testDataEnabled = testDataEnabled;
        _appversion = appversion;
    }
    return self;
}
- (NSString *)path
{
    return _testDataEnabled ? UPOms_CONFIG_API_TEST : UPOms_CONFIG_API;
}
- (NSString *)name
{
    return @"查询配置文件";
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    if (!UPRes_isEmptyString(self.model)) {
        [bodyDict setValue:self.model forKey:kUPOms_CONFIG_RequestBody_model];
    }
    if (!UPRes_isEmptyString(self.typeId)) {
        [bodyDict setValue:self.typeId forKey:kUPOms_CONFIG_RequestBody_typeId];
    }
    if (!UPRes_isEmptyString(self.prodNo)) {
        [bodyDict setValue:self.prodNo forKey:kUPOms_CONFIG_RequestBody_proNo];
    }
    return bodyDict;
}

- (NSTimeInterval)retryDelay
{
    return 2;
}
- (NSUInteger)retryTimes
{
    return 2;
}
- (NSTimeInterval)timeoutInterval
{
    return 15.0;
}
@end

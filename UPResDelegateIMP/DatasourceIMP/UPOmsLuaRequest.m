//
//  UPOmsLuaRequest.m
//  UPResDelegateIMP
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/7/14.
//  Copyright © 2025 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsLuaRequest.h"

#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
static NSString *const UPOms_LUALIST_API = @"/api-gw/upmapi/appmanage/resource/v1/match/lua";
static NSString *const kUPOms_LUALIST_RequestHeader_grayMode = @"grayMode";


static NSString *const kUPOms_LUALIST_RequestBody_resType = @"resType";
static NSString *const kUPOms_LUALIST_RequestBody_model = @"model";
static NSString *const kUPOms_LUALIST_RequestBody_typeId = @"typeId";
static NSString *const kUPOms_LUALIST_RequestBody_proNo = @"prodNo";
static NSString *const kUPOms_LUALIST_RequestBody_deviceType = @"deviceType";
static NSString *const kUPOms_LUALIST_RequestBody_accountToken = @"accountToken";
@interface UPOmsLuaRequest ()
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *model;
@property (nonatomic, copy) NSString *typeId;
@property (nonatomic, copy) NSString *prodNo;
@property (nonatomic, copy) NSString *deviceType;
@property (nonatomic, copy) NSString *appversion;
@end

@implementation UPOmsLuaRequest
- (instancetype)initRequestWithModel:(NSString *)model typeId:(NSString *)typeId prodNo:(NSString *)prodNo deviceType:(NSString *)deviceType testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _model = model;
        _typeId = typeId;
        _prodNo = prodNo;
        _deviceType = deviceType;
        _testDataEnabled = testDataEnabled;
    }
    return self;
}
- (NSString *)path
{
    return UPOms_LUALIST_API;
}
- (NSString *)name
{
    return @"查询lua资源列表";
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headers = [super requestHeaders].mutableCopy;
    headers[kUPOms_LUALIST_RequestHeader_grayMode] = _testDataEnabled ? @"true" : @"false";
    NSString *accessToken = [UPNetworkSettings sharedSettings].accessToken;
    if (!UPRes_isEmptyString(accessToken)) {
        headers[kUPOms_LUALIST_RequestBody_accountToken] = accessToken;
    }
    return headers.copy;
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    bodyDict[kUPOms_LUALIST_RequestBody_resType] = @"lua";
    bodyDict[kUPOms_LUALIST_RequestBody_model] = self.model;
    bodyDict[kUPOms_LUALIST_RequestBody_typeId] = self.typeId;
    bodyDict[kUPOms_LUALIST_RequestBody_proNo] = self.prodNo;
    bodyDict[kUPOms_LUALIST_RequestBody_deviceType] = self.deviceType;
    return bodyDict;
}

@end

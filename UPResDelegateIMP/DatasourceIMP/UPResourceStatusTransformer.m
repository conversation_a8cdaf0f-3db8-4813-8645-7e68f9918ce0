//
//  UPResourceStatusTransformer.m
//  UPResDelegateIMP
//
//  Created by 景彦铭 on 2023/2/1.
//  Copyright © 2023 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceStatusTransformer.h"
#import <UPLog/UPLog.h>
#import "UPResourceStatusConvertResult.h"
@implementation UPResourceStatusTransformer

- (NSObject *)parseResponseObject:(NSObject *)responseObject
{
    UPResourceStatusConvertResult *result = [[UPResourceStatusConvertResult alloc] init];
    if (![responseObject isKindOfClass:[NSDictionary class]]) {
        UPLogError(@"UPResource", @"%s[%d]资源包状态接口返回数据格式错误！", __PRETTY_FUNCTION__, __LINE__);
        result.error = [NSError errorWithDomain:@"response object invalidate" code:0 userInfo:nil];
        return result;
    }
    NSDictionary *response = (NSDictionary *)responseObject;
    if (![response[@"retCode"] isEqualToString:@"00000"]) {
        UPLogError(@"UPResource", @"%s[%d]资源包状态请求失败！error:", __PRETTY_FUNCTION__, __LINE__, response[@"retInfo"]);
        result.error = [NSError errorWithDomain:@"response failure" code:0 userInfo:nil];
        return result;
    }
    NSArray *resStatusList = response[@"data"];
    if (![resStatusList isKindOfClass:[NSArray class]] || resStatusList.count == 0) {
        UPLogInfo(@"UPResource", @"%s[%d]资源包状态接口返回数据为空！", __PRETTY_FUNCTION__, __LINE__);
        result.error = [NSError errorWithDomain:@"response object empty" code:0 userInfo:nil];
        return result;
    }
    result.resStatus = resStatusList;
    return result;
}

@end

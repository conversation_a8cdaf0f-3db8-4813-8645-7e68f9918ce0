//
//  UPOmsResDataSource.m
//  UPRes
//
//  Created by <PERSON> on 2019/5/13.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsResDataSource.h"
#import "UPOmsResRequest.h"
#import "UPOmsConfigFileRequest.h"
#import "UPOmsDeviceSourceRequest.h"
#import "UPResourceCondition.h"
#import <UPLog/UPLog.h>
#import "UPResourceQuery.h"
#import <upnetwork/UPNetwork.h>
#import <MJExtension/MJExtension.h>
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import "UPOmsNormalRequest.h"
#import "UPResourceTransformer.h"
#import "UPResourceConvertResult.h"
#import "UPOmsDeviceCustomInfoRequest.h"
#import "UPResourceDeviceCustomInfoTransformer.h"
#import "UPResourceConfig.h"
#import "UPOmsResListRequest.h"
#import "UPResourceInjection.h"
#import "UPResourceErrCodes.h"
#import "UPOmsResStatusRequest.h"
#import "UPOmsAppFuncModelReques.h"
#import "UPOmsReportResLoadedInfoRequest.h"
#import "UPResourceStatusTransformer.h"
#import "UPResourceReportLoadedInfoTransformer.h"
#import "UPResourceStatusConvertResult.h"
#import "UPOmsLuaRequest.h"
@implementation UPOmsResDataSource
#pragma mark - UPResourceDataSource
- (void)searchDeviceResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceResource:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)searchDeviceConfigResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceConfig:(UPResourceDeviceCondition *)condition completion:completion];
}
- (void)searchDeviceLuaResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceLua:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)searchDeviceCustomList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self updateDeviceCustomInfo:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)searchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    if (condition.resourceType == UPResourceTypeConfigApp) {
        [self updateConfigAPPResList:condition completion:completion];
        return;
    }
    [self updateDynamicResList:condition completion:completion];
}
- (void)batchSearchNormalResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *resources, NSError *error))completion
{
    [self updateDynamicResList:condition completion:completion];
}
- (void)searchAppFuncModelConfig:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *_Nonnull, NSError *_Nonnull))completion
{
    [self getAppFuncModelConfigList:(UPResourceDeviceCondition *)condition completion:completion];
}

- (void)reportResLoadedInfo:(NSArray<UPRLoadedReportInfo *> *)infos completion:(void (^)(NSError *_Nonnull))completion
{
    [self reportResourceLoadedInfo:infos completion:completion];
}
#pragma mark - Non-Public Methods

- (void)requestWithAPI:(UPRequest *)api completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPLogDebug(@"UPResource", @"%s[%d]开始请求资源列表信息", __PRETTY_FUNCTION__, __LINE__);
    [api startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      UPResourceConvertResult *result = (UPResourceConvertResult *)responseObject;
      if (result.error) {
          if (completion) {
              completion(nil, result.error);
          }
          return;
      }
      NSArray<UPResourceInfo *> *infoList = [self convertResourceInfoFromOmsResourceInfoObjects:result.omsResInfoList];
      if (completion) {
          completion(infoList, nil);
      }

    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPLogError(@"UPResource", @"%s[%d]资源包列表信息请求失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
          if (completion) {
              completion(nil, [NSError errorWithDomain:error.description ?: NSCocoaErrorDomain code:error.code userInfo:nil]);
          }
        }];
}

- (void)updateCommonResource:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsResRequest *request = [[UPOmsResRequest alloc] initRequestWithResourceType:condition.resourceType appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (void)updateConfigAPPResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsNormalRequest *request = [[UPOmsNormalRequest alloc] initRequestWithResourceType:condition.resourceType resName:condition.resName appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled localCode:[UPResourceConfig shareInstance].localCode];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (void)updateDynamicResList:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    // 获取需要更新的资源名字列表
    NSArray *resNames = [condition.resName componentsSeparatedByString:@","];
    // 获取本地已安装最新资源列表
    NSArray<UPResourceInfo *> *installedList = [[UPResourceInjection getInstance]
                                                    .resourceManager getLatestInstalledList:^BOOL(UPResourceInfo *info) {
      BOOL accept = NO;
      if (info.type == condition.resourceType) {
          accept = [resNames containsObject:info.name];
      }
      return accept;
    }];
    // 获取本地已安装最新资源名称列表
    NSArray *installedNames = [installedList valueForKeyPath:@"name"] ?: @[];
    NSMutableArray *resList = [NSMutableArray array];
    /**
    组装待更新资源信息，如果本地已安装资源则同时上报本地安装资源版本
    如：请求更新 resA、resB、resC ,本地安装 resB@1.0.0
    请求参数为
    [{
        "name": "resA",
        "version": @""
    },
    {
     "name": "resB",
     "version": @"1.0.0"
    },
    {
     "name": "resC",
     "version": @""
    }]
     */
    [resNames enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSString *version = @"";
      NSInteger index = [installedNames indexOfObject:obj];
      if (index != NSNotFound) {
          version = installedList[index].version;
      }
      [resList addObject:@{
          @"name" : obj,
          @"version" : UPRes_validStringValue(version),
      }];
    }];
    UPOmsResListRequest *request = [[UPOmsResListRequest alloc] initRequestWithResourceType:condition.resourceType resList:resList.copy testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request
              completion:^(NSArray<UPResourceInfo *> *resList, NSError *error) {
                if (completion) {
                    completion(resList, error);
                }
              }];
}

- (void)checkDynamicResStatusWithCondition:(UPResourceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    if (condition.resourceType == UPResourceTypeConfigApp) {
        if (completion) {
            completion(@[], nil);
        }
        return;
    }
    NSArray *resNames = [condition.resName componentsSeparatedByString:@","];
    NSArray<UPResourceInfo *> *resList = [[UPResourceInjection getInstance]
                                              .resourceManager getLatestInstalledList:^BOOL(UPResourceInfo *info) {
      BOOL accept = NO;
      if (info.type == condition.resourceType) {
          accept = [resNames containsObject:info.name];
      }
      return accept;
    }];
    NSMutableArray *checkResList = [NSMutableArray array];
    [resList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [checkResList addObject:@{
          @"name" : obj.name,
          @"version" : UPRes_validStringValue(obj.version)
      }];
    }];
    UPOmsResStatusRequest *request = [[UPOmsResStatusRequest alloc] initRequestWithResourceType:condition.resourceType resList:checkResList.copy testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceStatusTransformer alloc] init];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      UPResourceStatusConvertResult *response = (UPResourceStatusConvertResult *)responseObject;
      if (response.error) {
          if (completion) {
              completion(nil, response.error);
          }
      }
      else {
          [self updateLocalDynamicResStatusIfNeeds:resList remoteStatus:response.resStatus completion:completion];
      }
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPLogError(@"UPResource", @"%s[%d]资源包状态请求失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
          if (completion) {
              completion(nil, error);
          }
        }];
}

- (void)updateLocalDynamicResStatusIfNeeds:(NSArray<UPResourceInfo *> *)resList remoteStatus:(NSArray<NSDictionary *> *)status completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    NSMutableArray *needsUpdateInfo = [NSMutableArray array];
    NSMutableDictionary *statusMap = [NSMutableDictionary dictionary];
    [status enumerateObjectsUsingBlock:^(NSDictionary *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [statusMap setObject:obj forKey:obj[@"name"]];
    }];
    [resList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      NSDictionary *status = statusMap[obj.name];
      if (!status || ![status[@"version"] isEqualToString:obj.version] || [status[@"resStatus"] integerValue] == obj.resStatus) {
          return;
      }
      obj.resStatus = [status[@"resStatus"] integerValue];
      [needsUpdateInfo addObject:obj];
    }];
    if (completion) {
        completion(needsUpdateInfo, nil);
    }
}

- (void)updateDeviceConfig:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsConfigFileRequest *request = [[UPOmsConfigFileRequest alloc] initRequestWithModel:condition.model typeId:condition.typeId prodNo:condition.prodNo appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}
- (void)updateDeviceLua:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsLuaRequest *request = [[UPOmsLuaRequest alloc] initRequestWithModel:condition.model typeId:condition.typeId prodNo:condition.prodNo deviceType:condition.deviceType testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (void)updateDeviceResource:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsDeviceSourceRequest *request = [[UPOmsDeviceSourceRequest alloc] initRequestWithType:condition.resourceType model:condition.model typeId:condition.typeId prodNo:condition.prodNo deviceType:condition.deviceType appversion:condition.appVersion testDataEnabled:self.isTestDataEnabled deviceNetType:condition.deviceNetType localCode:[UPResourceConfig shareInstance].localCode];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}
- (void)updateDeviceCustomInfo:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsDeviceCustomInfoRequest *request = [[UPOmsDeviceCustomInfoRequest alloc] initRequestWithProdNo:condition.prodNo testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceDeviceCustomInfoTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}


- (void)getAppFuncModelConfigList:(UPResourceDeviceCondition *)condition completion:(void (^)(NSArray<UPResourceInfo *> *, NSError *))completion
{
    UPOmsAppFuncModelReques *request = [[UPOmsAppFuncModelReques alloc] initRequestWithModel:condition.model typeId:condition.typeId prodNo:condition.prodNo type:condition.resourceType testDataEnabled:self.isTestDataEnabled];
    request.responseParser = [[UPResourceTransformer alloc] init];
    [self requestWithAPI:request completion:completion];
}

- (void)reportResourceLoadedInfo:(NSArray<UPRLoadedReportInfo *> *)infos completion:(void (^)(NSError *_Nonnull))completion
{
    if (![infos isKindOfClass:[NSArray class]] || infos.count == 0) {
        return;
    }
    NSArray *jsonData = [UPRLoadedReportInfo mj_keyValuesArrayWithObjectArray:infos];
    UPOmsReportResLoadedInfoRequest *request = [[UPOmsReportResLoadedInfoRequest alloc] initRequestWithInfos:jsonData];
    request.responseParser = [[UPResourceReportLoadedInfoTransformer alloc] init];
    [request startRequestWithSuccess:^(NSObject *_Nonnull responseObject) {
      UPResourceConvertResult *response = (UPResourceConvertResult *)responseObject;
      completion ? completion(response.error) : nil;
    }
        failure:^(NSError *_Nonnull error, NSDictionary *_Nullable info) {
          UPLogError(@"UPResource", @"%s[%d]上报资源触达请求失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
          completion ? completion(error) : nil;
        }];
}

- (UPResourceInfo *)convertResourceInfoFromOmsResourceInfo:(UPOmsResInfo *)omsInfo
{
    if (![omsInfo isKindOfClass:[UPOmsResInfo class]]) {
        return nil;
    }
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.name = omsInfo.name;
    info.type = [UPResourceInfo typeValueOfString:omsInfo.resType];
    info.version = omsInfo.resVersion;
    info.link = omsInfo.resUrl;
    info.hashStr = omsInfo.md5;
    info.model = omsInfo.model;
    info.typeId = omsInfo.typeId;
    info.deviceTypeIndex = omsInfo.deviceTypeIndex;
    info.prodNo = omsInfo.prodNo;
    info.isServerLatest = YES;
    info.resourceType = omsInfo.resourceType;
    info.hideStatusBar = [omsInfo.hideStatusBar isEqualToString:@"1"];
    info.forceUpgrade = [omsInfo.forceUpgrade isEqualToString:@"1"];
    info.resRules = omsInfo.resRules;
    info.resStatus = omsInfo.resStatus.integerValue;
    info.remoteUrl = omsInfo.remoteUrl;
    return info;
}

- (NSArray<UPResourceInfo *> *)convertResourceInfoFromOmsResourceInfoObjects:(NSArray<UPOmsResInfo *> *)omsInfoObjects
{
    if (UPRes_isEmptyArray(omsInfoObjects)) {
        return nil;
    }
    NSMutableArray<UPResourceInfo *> *infoArray = [NSMutableArray array];
    for (UPOmsResInfo *omsInfo in omsInfoObjects) {
        UPResourceInfo *info = [self convertResourceInfoFromOmsResourceInfo:omsInfo];
        if ([info isKindOfClass:[UPResourceInfo class]]) {
            [infoArray addObject:info];
        }
    }
    return infoArray;
}

@end

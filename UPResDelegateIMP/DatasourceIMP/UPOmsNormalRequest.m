//
//  UPOmsNormalRequest.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/9/20.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsNormalRequest.h"
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
NSString *const UPOms_NORMAL_API = @"/omsappapi/resource/v2/get/resourceBag";
NSString *const UPOms_NORMAL_API_TEST = @"/omsappapi/resource/v2/get/test/resourceBag";

NSString *const kUPOms_NORMAL_RequestBody_resType = @"resType";
NSString *const kUPOms_NORMAL_RequestBody_resName = @"name";
NSString *const kUPOms_NORMAL_RequestBody_localCode = @"localCode";

@interface UPOmsNormalRequest ()
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
@property (nonatomic, copy) NSString *resName;
@property (nonatomic, copy) NSString *appverison;
@property (nonatomic, copy) NSString *localCode;
@end

@implementation UPOmsNormalRequest
- (instancetype)initRequestWithResourceType:(UPResourceType)type resName:(NSString *)resName appversion:(NSString *)appversion testDataEnabled:(BOOL)testDataEnabled localCode:(NSString *)localCode
{
    if (self = [super init]) {
        _type = type;
        _testDataEnabled = testDataEnabled;
        _resName = resName;
        _appverison = appversion;
        _localCode = localCode;
    }
    return self;
}
- (NSString *)name
{
    return @"通过名字和类型查询普通资源列表";
}
- (NSString *)path
{
    return _testDataEnabled ? UPOms_NORMAL_API_TEST : UPOms_NORMAL_API;
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    [bodyDict setValue:resTypeString forKey:kUPOms_NORMAL_RequestBody_resType];
    if ([self.resName isKindOfClass:[NSString class]]) {
        [bodyDict setValue:self.resName forKey:kUPOms_NORMAL_RequestBody_resName];
    }
    if (!UPRes_isEmptyString(self.localCode)) {
        [bodyDict setValue:self.localCode forKey:kUPOms_NORMAL_RequestBody_localCode];
    }
    return bodyDict;
}
@end

//
//  UPOmsResListRequest.m
//  UPResDelegateIMP
//
//  Created by 景彦铭 on 2022/9/26.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsResListRequest.h"
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import <Foundation/Foundation.h>

static NSString *const UPOms_RESLIST_API = @"/api-gw/upmapi/appmanage/resource/v2/resList";
static NSString *const kUPOms_RESLIST_RequestHeader_grayMode = @"grayMode";

static NSString *const kUPOms_RESLIST_RequestBody_resType = @"resType";
static NSString *const kUPOms_RESLIST_RequestBody_resList = @"resList";
static NSString *const kUPOms_RESLIST_RequestBody_name = @"name";
static NSString *const kUPOms_RESLIST_RequestBody_version = @"version";
static NSString *const kUPOms_RESLIST_RequestBody_accountToken = @"accountToken";

@interface UPOmsResListRequest ()
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
/// 资源列表 [{"name": "xxx", "version": "xxx"}]
@property (nonatomic, strong) NSArray<NSDictionary<NSString *, NSString *> *> *resList;
@end

@implementation UPOmsResListRequest
- (instancetype)initRequestWithResourceType:(UPResourceType)type resList:(NSArray<NSDictionary<NSString *, NSString *> *> *)resList testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _type = type;
        _testDataEnabled = testDataEnabled;
        // 检查resList是否合法（接口要求 version 不存在时传空字符串）
        NSMutableArray *resListM = [NSMutableArray array];
        [resList enumerateObjectsUsingBlock:^(NSDictionary<NSString *, NSString *> *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *name = [obj objectForKey:kUPOms_RESLIST_RequestBody_name];
          NSString *version = [obj objectForKey:kUPOms_RESLIST_RequestBody_version] ?: @"";
          if (UPRes_isEmptyString(name)) {
              return;
          }
          [resListM addObject:@{
              kUPOms_RESLIST_RequestBody_name : name,
              kUPOms_RESLIST_RequestBody_version : version,
          }];
        }];
        _resList = resListM.copy;
    }
    return self;
}
- (NSString *)name
{
    return @"通过类型查询指定名称版本资源列表";
}
- (NSString *)path
{
    return UPOms_RESLIST_API;
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headers = [super requestHeaders].mutableCopy;
    headers[kUPOms_RESLIST_RequestHeader_grayMode] = _testDataEnabled ? @"true" : @"false";
    NSString *accessToken = [UPNetworkSettings sharedSettings].accessToken;
    if (!UPRes_isEmptyString(accessToken)) {
        headers[kUPOms_RESLIST_RequestBody_accountToken] = accessToken;
    }
    return headers.copy;
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    bodyDict[kUPOms_RESLIST_RequestBody_resType] = resTypeString;
    bodyDict[kUPOms_RESLIST_RequestBody_resList] = self.resList;
    return bodyDict;
}
@end

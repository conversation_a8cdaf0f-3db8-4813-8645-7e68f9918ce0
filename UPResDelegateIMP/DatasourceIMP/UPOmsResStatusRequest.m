//
//  UPOmsResStatusRequest.m
//  UPResDelegateIMP
//
//  Created by 景彦铭 on 2022/12/7.
//  Copyright © 2022 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsResStatusRequest.h"
#import "UPOmsResInfo.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import <Foundation/Foundation.h>

static NSString *const UPOms_RESSTATUS_API = @"/api-gw/upmapi/appmanage/resource/status";
static NSString *const kUPOms_RESSTATUS_RequestHeader_grayMode = @"grayMode";

static NSString *const kUPOms_RESSTATUS_RequestBody_resType = @"resType";
static NSString *const kUPOms_RESSTATUS_RequestBody_resList = @"resList";
static NSString *const kUPOms_RESSTATUS_RequestBody_name = @"name";
static NSString *const kUPOms_RESSTATUS_RequestBody_version = @"version";

@interface UPOmsResStatusRequest ()
@property (nonatomic, assign) UPResourceType type;
@property (nonatomic, assign) BOOL testDataEnabled;
/// 资源列表 [{"name": "xxx", "version": "xxx"}]
@property (nonatomic, strong) NSArray<NSDictionary<NSString *, NSString *> *> *resList;
@end

@implementation UPOmsResStatusRequest

- (instancetype)initRequestWithResourceType:(UPResourceType)type resList:(NSArray<NSDictionary<NSString *, NSString *> *> *)resList testDataEnabled:(BOOL)testDataEnabled
{
    if (self = [super init]) {
        _type = type;
        _testDataEnabled = testDataEnabled;
        /**
         检查resList是否合法 version 为空后台当做异常数据处理
         后台兜底逻辑：
         若请求参数中有资源版本号为空，如下：
         resList : [
            {
                "name": "appmine",
                "version": "1.0.0"
            },
            {
                "name": "customServers",
                "version": "2.0.0"
            },
            {
                "name": "usercenter",
                "version": ""
            }
         ]
         后台返回数据结果集中不包含版本号为空的资源，如下:
         data : [
            {
                "name": "appmine",
                "version": "1.0.0",
                "resStatus": 0
            },
            {
                "name": "customServers",
                "version": "2.0.0",
                "resStatus": 0
            }
         ]
         */
        NSMutableArray *resListM = [NSMutableArray array];
        [resList enumerateObjectsUsingBlock:^(NSDictionary<NSString *, NSString *> *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *name = [obj objectForKey:kUPOms_RESSTATUS_RequestBody_name];
          NSString *version = [obj objectForKey:kUPOms_RESSTATUS_RequestBody_version] ?: @"";
          if (UPRes_isEmptyString(name) || UPRes_isEmptyString(version)) {
              return;
          }
          [resListM addObject:@{
              kUPOms_RESSTATUS_RequestBody_name : name,
              kUPOms_RESSTATUS_RequestBody_version : version,
          }];
        }];
        if (resListM.count == 0) {
            return nil;
        }
        _resList = resListM.copy;
    }
    return self;
}
- (NSString *)name
{
    // api文档: https://stp.haier.net/project/400/interface/api/144543
    return @"查询动态资源状态";
}
- (NSString *)path
{
    return UPOms_RESSTATUS_API;
}
- (NSDictionary<NSString *, NSString *> *)requestHeaders
{
    NSMutableDictionary *headers = [super requestHeaders].mutableCopy;
    headers[kUPOms_RESSTATUS_RequestHeader_grayMode] = _testDataEnabled ? @"true" : @"false";
    return headers.copy;
}
- (NSObject *)requestBody
{
    NSMutableDictionary *bodyDict = [NSMutableDictionary dictionary];
    NSString *resTypeString = [UPResourceInfo stringValueOfResourceType:self.type];
    bodyDict[kUPOms_RESSTATUS_RequestBody_resType] = resTypeString;
    bodyDict[kUPOms_RESSTATUS_RequestBody_resList] = self.resList;
    return bodyDict;
}

@end

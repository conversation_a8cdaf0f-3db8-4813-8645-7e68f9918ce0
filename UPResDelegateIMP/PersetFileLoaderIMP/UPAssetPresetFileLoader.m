//
//  UPAssetPresetFileLoader.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/5/21.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPAssetPresetFileLoader.h"
#import <UPLog/UPLog.h>
#import "UPResourceHelper.h"

@interface UPAssetPresetFileLoader ()
@property (nonatomic, copy) NSString *resPath;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) NSArray *presetInfos;
@end

@implementation UPAssetPresetFileLoader

- (instancetype)initFileLoader:(NSString *)resPath fileDelegate:(id<UPFileDelegate>)fileDelegate
{

    if (self = [super init]) {
        _resPath = resPath;
        _fileDelegate = fileDelegate;
        _presetInfos = @[];
    }
    return self;
}

- (nullable NSArray<NSDictionary *> *)scanPresetFilenameList
{
    if (self.presetInfos.count) {
        return self.presetInfos;
    }
    if ([UPResourceHelper isBlank:self.resPath]) {
        return nil;
    }
    NSMutableArray *infoList = @[].mutableCopy;
    NSArray *presetResInfoList = [self getPresetHainerFileNameList:@"presetResInfoList.json"];
    NSArray *hainerNameList = [self getPresetHainerFileNameList:@"localPreRes.json"];
    if (presetResInfoList) {
        [infoList addObjectsFromArray:presetResInfoList];
    }
    if (hainerNameList) {
        [infoList addObjectsFromArray:hainerNameList];
    }
    self.presetInfos = [infoList copy];
    return self.presetInfos;
}

- (nullable NSArray<NSDictionary *> *)getPresetHainerFileNameList:(NSString *)jsonName
{
    if ([UPResourceHelper isBlank:jsonName]) {
        return nil;
    }
    NSError *error = nil;
    NSData *data = [NSData dataWithContentsOfFile:[NSString stringWithFormat:@"%@%@", self.resPath, jsonName] options:NSDataReadingMappedIfSafe error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]扫描出错！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
        return nil;
    }
    NSDictionary *dict = [NSJSONSerialization JSONObjectWithData:data options:NSJSONReadingMutableContainers error:&error];
    if (![dict isKindOfClass:NSDictionary.class]) {
        UPLogError(@"UPResource", @"%s[%d]读取json文件失败！error:%@", __PRETTY_FUNCTION__, __LINE__, error);
        return nil;
    }
    return dict[@"infoList"];
}

- (nullable NSString *)openPresetFile:(NSString *)fileName
{
    if ([UPResourceHelper isBlank:self.resPath] || [UPResourceHelper isBlank:fileName]) {
        return nil;
    }
    return [self.resPath stringByAppendingPathComponent:fileName];
}

- (BOOL)existFile:(NSString *)fileName
{
    if ([UPResourceHelper isBlank:self.resPath] || [UPResourceHelper isBlank:fileName]) {
        return NO;
    }
    NSString *filePath = [self.resPath stringByAppendingPathComponent:fileName];
    return [self.fileDelegate exists:filePath];
}
@end

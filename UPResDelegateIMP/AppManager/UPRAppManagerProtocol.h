//
//  UPRAppManagerProtocol.h
//  UPR
//
//  Created by osiris on 2020/10/19.
//

#import <UIKit/UIkit.h>

NS_ASSUME_NONNULL_BEGIN

/**
 @enum App生命周期变化动作
 */
typedef NS_ENUM(NSInteger, UPRAppLifeCycleAction) {
    UPR_LifeCycle_Action_DidBecomeActive = 0,
    UPR_LifeCycle_Action_DidEnterBackground,
    UPR_LifeCycle_Action_WillEnterForeground,
    UPR_LifeCycle_Action_WillResignActive,
    UPR_LifeCycle_Action_WillTerminate
};

/**
 资源模块App系统通知观察者协议
 */
@protocol UPRAppLifeCycleManagerObserver <NSObject>

@property (nonatomic, copy) void (^lifeCycleChange)(UPRAppLifeCycleAction action, UIApplicationState appState, UIApplicationState lastAppState);

@end

/**
 资源模块系统通知观察者管理器协议
 */
@protocol UPRAppManagerProtocol <NSObject>

/**
 当前App系统声明周期状态
 */
@property (nonatomic, assign, readonly) UIApplicationState appState;

/**
 添加观察者
 @param observer 观察者
 */
- (void)addObserver:(id<UPRAppLifeCycleManagerObserver>)observer;

/**
 删除观察者
 @param observer 观察者
 */
- (void)removeObserver:(id<UPRAppLifeCycleManagerObserver>)observer;

@end

NS_ASSUME_NONNULL_END

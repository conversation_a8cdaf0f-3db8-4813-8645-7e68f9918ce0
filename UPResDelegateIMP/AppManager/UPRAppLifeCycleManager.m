//
//  UPRAppManager.m
//  UPR
//
//  Created by osiris on 2020/10/19.
//

#import "UPRAppLifeCycleManager.h"

@interface UPRAppLifeCycleManager ()

@property (nonatomic, strong) NSHashTable<id<UPRAppLifeCycleManagerObserver>> *observers;
@property (nonatomic, strong) NSDictionary<NSString *, id> *appLifeCycleSelectorMap;
@property (nonatomic, assign) UIApplicationState lastAppState;

@end

@implementation UPRAppLifeCycleManager

+ (instancetype)instance
{
    static dispatch_once_t onceToken;
    static UPRAppLifeCycleManager *appManager;
    dispatch_once(&onceToken, ^{
      if (!appManager) {
          appManager = [[UPRAppLifeCycleManager alloc] init];
      }
    });

    return appManager;
}

- (instancetype)init
{
    if (self = [super init]) {
        _observers = [NSHashTable weakObjectsHashTable];
        [self assembleInvocationMap];
        [self registerAppLifeCycleNotification];
        self.lastAppState = self.appState;
    }

    return self;
}


#pragma mark - property
- (UIApplicationState)appState
{
    return [UIApplication sharedApplication].applicationState;
}

#pragma mark - public method
- (void)addObserver:(id<UPRAppLifeCycleManagerObserver>)observer
{
    if (![observer conformsToProtocol:@protocol(UPRAppLifeCycleManagerObserver)]) {
        return;
    }
    @synchronized(_observers)
    {
        [_observers addObject:observer];
    }
}

- (void)removeObserver:(id<UPRAppLifeCycleManagerObserver>)observer
{
    if (![observer conformsToProtocol:@protocol(UPRAppLifeCycleManagerObserver)]) {
        return;
    }
    @synchronized(_observers)
    {
        [_observers removeObject:observer];
    }
}

#pragma mark - private methods
- (void)assembleInvocationMap
{
    _appLifeCycleSelectorMap = @{
        UIApplicationDidBecomeActiveNotification : @(UPR_LifeCycle_Action_DidBecomeActive),
        UIApplicationDidEnterBackgroundNotification : @(UPR_LifeCycle_Action_DidEnterBackground),
        UIApplicationWillEnterForegroundNotification : @(UPR_LifeCycle_Action_WillEnterForeground),
        UIApplicationWillResignActiveNotification : @(UPR_LifeCycle_Action_WillResignActive),
        UIApplicationWillTerminateNotification : @(UPR_LifeCycle_Action_WillTerminate)
    };
}

- (void)registerAppLifeCycleNotification
{
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationDidBecomeActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationDidEnterBackgroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillEnterForegroundNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillResignActiveNotification object:nil];
    [[NSNotificationCenter defaultCenter] addObserver:self selector:@selector(appLifeCycleChangeHandler:) name:UIApplicationWillTerminateNotification object:nil];
}

#pragma mark - actions
- (void)appLifeCycleChangeHandler:(NSNotification *)notification
{
    for (id<UPRAppLifeCycleManagerObserver> observer in _observers) {
        observer.lifeCycleChange ? observer.lifeCycleChange([_appLifeCycleSelectorMap[notification.name] integerValue], self.appState, self.lastAppState) : nil;
    }
    self.lastAppState = self.appState;
}

@end

//
//  UPDownloaderHandle.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/19.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDownloaderHandle.h"
#import <UPLog/UPLog.h>
#import "UPConnectionMonitor.h"
#import "UPResourceConfig.h"

@interface UpDownloadBlockInfo ()

@property (nonatomic, assign) long blockStartTime;
@property (nonatomic, assign) long blockEndTime;
@property (nonatomic, assign) long blockSize;

@end

@implementation UpDownloadBlockInfo

- (instancetype)init
{
    if (self = [super init]) {
        _blockStartTime = 0L;
        _blockEndTime = 0L;
        _blockSize = 0L;
        _blockResult = NO;
        _blockFailedReason = @"";
    }

    return self;
}

- (void)blockStart
{
    _blockStartTime = [NSDate date].timeIntervalSince1970 * 1000;
}

- (void)blockEnd
{
    _blockEndTime = [NSDate date].timeIntervalSince1970 * 1000;
}

- (void)cumsumSize:(long)size
{
    _blockSize += size;
}
@end

@interface UpDownloadNetInfo ()

@property (nonatomic, assign) long netStateTime;
@property (nonatomic, copy) NSString *deviceIP;

@end

@implementation UpDownloadNetInfo

- (instancetype)init
{
    if (self = [super init]) {
        _netStateTime = 0L;
        _deviceIP = @"";
    }

    return self;
}

@end

@implementation UpDownloadBackgroundRunInfo

- (instancetype)init
{
    if (self = [super init]) {
        _ifBackgroundRun = NO;
        _backgroundRunTime = 0L;
    }

    return self;
}

@end

@interface UPDownloadRecord ()

@property (nonatomic, copy) NSString *resourceIP;
@property (nonatomic, strong) NSMutableArray<UpDownloadBlockInfo *> *downloadBlockInfos;
@property (nonatomic, strong) NSMutableArray<UpDownloadNetInfo *> *downloadNetInfos;
@property (nonatomic, strong) NSMutableArray<UpDownloadBackgroundRunInfo *> *downloadBackgroundRunInfos;
@property (nonatomic, strong) dispatch_queue_t readWriteQuene;

@end

@implementation UPDownloadRecord

- (instancetype)initRecord:(NSString *)fileName savePath:(NSString *)savePath
{
    if (self = [super init]) {
        self.fileName = fileName;
        self.savePath = savePath;
        self.downloadBlockInfos = [NSMutableArray array];
        self.downloadNetInfos = [NSMutableArray array];
        self.downloadBackgroundRunInfos = [NSMutableArray array];
        self.readWriteQuene = dispatch_queue_create("com.haier.upresource.downloadRecord", DISPATCH_QUEUE_CONCURRENT);
    }
    return self;
}

- (long)totalDownloadTimes
{
    if (self.startDownloadTime) {
        long totalTime = (long)([self.endDownloadTime timeIntervalSinceDate:self.startDownloadTime] * 1000);
        return totalTime;
    }
    UPLogError(@"UPResource", @"%s[%d]资源下载时长统计错误 开始时间:%@ 结束时间:%@", __PRETTY_FUNCTION__, __LINE__, self.startDownloadTime, self.endDownloadTime);
    return 0;
}

- (void)addDownloadBlockInfo:(UpDownloadBlockInfo *)blockInfo
{
    [_downloadBlockInfos addObject:blockInfo];
}

- (void)addDownloadNetInfo
{
    dispatch_barrier_async(self.readWriteQuene, ^{
      UpDownloadNetInfo *downloadNetInfo = UpDownloadNetInfo.new;
      downloadNetInfo.deviceIP = [UPResourceConfig shareInstance].enablePrivacyAgreement ? UPConnectionMonitor.getWANIPAddress : @"";
      downloadNetInfo.netStateTime = [NSDate date].timeIntervalSince1970 * 1000;
      [self->_downloadNetInfos addObject:downloadNetInfo];
    });
}

- (void)addDownloadBackgroundRunInfo:(BOOL)isBackground
{
    UpDownloadBackgroundRunInfo *backgroundRunInfo = UpDownloadBackgroundRunInfo.new;
    backgroundRunInfo.ifBackgroundRun = isBackground;
    backgroundRunInfo.backgroundRunTime = [NSDate date].timeIntervalSince1970 * 1000;
    [_downloadBackgroundRunInfos addObject:backgroundRunInfo];
}

- (NSArray<UpDownloadNetInfo *> *)downloadNetInfos
{
    __block NSArray<UpDownloadNetInfo *> *array = @[];
    dispatch_sync(self.readWriteQuene, ^{
      array = self->_downloadNetInfos.copy;
    });
    return array;
}

- (NSArray<UpDownloadBlockInfo *> *)downloadBlockInfos
{
    return _downloadBlockInfos.copy;
}

- (NSArray<UpDownloadBackgroundRunInfo *> *)downloadBackgroundRunInfos
{
    return _downloadBackgroundRunInfos.copy;
}

- (void)transformSourceURLToCDN:(NSString *)url
{
    NSURLComponents *urlComponents = [NSURLComponents componentsWithString:url];
    _resourceIP = !urlComponents ? @"" : urlComponents.host ?: @"";
}

@end

@implementation UPDownloaderHandle

@end

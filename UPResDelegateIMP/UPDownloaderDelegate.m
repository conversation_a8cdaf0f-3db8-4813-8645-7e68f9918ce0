//
//  UPDownloaderDelegate.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/3/12.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPDownloaderDelegate.h"
#import <AFNetworking/AFNetworking.h>
#import <UPLog/UPLog.h>
#import "UPDownloaderHandle.h"
#import "UPDownloadCallback.h"
#import <upnetwork/UPNetwork.h>
#import "UPRAppLifeCycleManager.h"
#import "UPConnectionDelegate.h"

static CGFloat const DOWNLOADTIMEOUTINTERVAL = 10.f;
@interface UPDownloaderDelegate () <UPConnectionListener, UPRAppLifeCycleManagerObserver>
@property (atomic, strong) NSMutableDictionary<NSString *, UPDownloadHandle *> *handles;
@property (nonatomic, assign) NSInteger retryCount;
@property (nonatomic, assign) NSInteger retryDelay;
@property (nonatomic, assign) NSInteger downloadRetryTimes;
@property (nonatomic, weak) id<UPConnectionDelegate> connectionMonitor;
@end

@implementation UPDownloaderDelegate
@synthesize lifeCycleChange, connectionChangedBlock;
- (instancetype)init
{
    if (self = [super init]) {
        self.handles = [NSMutableDictionary dictionary];
        self.downloadRetryTimes = 0;
        [UPRAppLifeCycleManager.instance addObserver:self];
    }
    return self;
}

#pragma mark UPDownloadDelegate
- (void)setConnectionMonitor:(id<UPConnectionDelegate>)connectionMonitor
{
    _connectionMonitor = connectionMonitor;
    [_connectionMonitor addConnectionListener:self];
}

- (UPDownloadHandle *)createUPDownloadHandle:(NSString *)link folder:(NSString *)folder fileName:(NSString *)fileName callback:(id<UPDownloadCallback>)callback
{
    UPDownloaderHandle *handle = [[UPDownloaderHandle alloc] initHandle:link callback:callback];
    UPDownloadRecord *record = [[UPDownloadRecord alloc] initRecord:fileName savePath:folder];
    handle.record = record;
    if (handle) {
        @synchronized(self.handles)
        {
            [self.handles setValue:handle forKey:link];
        }
    }

    return handle;
}

- (void)start:(UPDownloadHandle *)handle
{
    self.downloadRetryTimes = 0;
    UPDownloaderHandle *downloaderHandle = (UPDownloaderHandle *)handle;
    [downloaderHandle.record addDownloadNetInfo];
    [downloaderHandle.record addDownloadBackgroundRunInfo:UPRAppLifeCycleManager.instance.appState == UIApplicationStateBackground];
    self.connectionChangedBlock = ^(BOOL isOnline, UPConnectionType type) {
      isOnline ? [downloaderHandle.record addDownloadNetInfo] : nil;
    };
    self.lifeCycleChange = ^(UPRAppLifeCycleAction action, UIApplicationState appState, UIApplicationState lastAppState) {
      if ((appState == UIApplicationStateBackground || lastAppState == UIApplicationStateBackground) && appState != lastAppState) {
          [downloaderHandle.record addDownloadBackgroundRunInfo:appState == UIApplicationStateBackground];
      }
    };
    downloaderHandle.record.startDownloadTime = [NSDate date];
    UPLogDebug(@"UPResource", @"%s[%d]开始下载资源包:%@,下载地址:%@,保存地址:%@", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName, downloaderHandle.link, downloaderHandle.record.savePath);
    NSString *url = [[UPNetworkHTTPDns sharedHTTPDns] dnsHostURL:downloaderHandle.link];
    UPLogInfo(@"UPResource", @"%s[%d]原始url为%@处理过的url为%@", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.link, url);
    [downloaderHandle.record transformSourceURLToCDN:url];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:[NSURL URLWithString:url]];
    NSURLSessionConfiguration *config = [NSURLSessionConfiguration defaultSessionConfiguration];
    config.timeoutIntervalForRequest = DOWNLOADTIMEOUTINTERVAL;
    AFHTTPSessionManager *manager = [[AFHTTPSessionManager alloc] initWithSessionConfiguration:config];
    manager.responseSerializer = [AFHTTPResponseSerializer serializer];
    manager.responseSerializer.acceptableContentTypes = [NSSet setWithObjects:@"text/plain", @"application/json", @"text/json", @"text/javascript", @"text/html", @"application/zip", @"application/x-javascript", @"text/css", @"application/x-zip-compressed", @"application/octet-stream", nil];
    manager.securityPolicy.validatesDomainName = NO;
    NSURL *hostURL = [NSURL URLWithString:downloaderHandle.link];
    NSNumber *port = hostURL.port;
    if (port == nil) {
        [request setValue:hostURL.host forHTTPHeaderField:@"host"];
    }
    else {
        NSInteger iPort = port.integerValue;
        NSString *hostHeaderValue = [NSString stringWithFormat:@"%@:%ld", hostURL.host, (long)iPort];
        [request setValue:hostHeaderValue forHTTPHeaderField:@"host"];
    }
    [self retryDownloadRes:self.retryCount request:request manager:manager downloaderHandle:downloaderHandle];
    [downloaderHandle.callback onTaskStart:downloaderHandle];
}
- (void)setDownloadRetryDealy:(NSInteger)retryDealy
{
    _retryDelay = retryDealy;
}
- (void)setDownloadRetryCount:(NSInteger)retryCount
{
    _retryCount = retryCount;
}
- (void)retryDownloadRes:(NSInteger)reryCount request:(NSMutableURLRequest *)request manager:(AFHTTPSessionManager *)manager downloaderHandle:(UPDownloaderHandle *)downloaderHandle
{
    reryCount--;
    UpDownloadBlockInfo *blockInfo = [UpDownloadBlockInfo new];
    [blockInfo blockStart];
    NSString *savePath = downloaderHandle.record.savePath;
    __block NSInteger currentLength = [self fileLengthForPath:downloaderHandle.record.savePath];
    NSString *range = [NSString stringWithFormat:@"bytes=%ld-", currentLength];
    [request setValue:range forHTTPHeaderField:@"Range"];
    NSMutableDictionary *fileHandles = [NSMutableDictionary dictionary];
    void (^completionHandler)(NSURLResponse *_Nonnull response, id _Nullable responseObject, NSError *_Nullable error) = ^(NSURLResponse *_Nonnull response, id _Nullable responseObject, NSError *_Nullable error) {
      [blockInfo blockEnd];
      blockInfo.blockResult = error == nil;
      currentLength = 0;
      NSFileHandle *fileHandle = savePath ? fileHandles[savePath] : nil;
      if (fileHandle) {
          [fileHandle closeFile];
      }
      downloaderHandle.record.reTryCount = self.downloadRetryTimes;
      if (error) {
          blockInfo.blockFailedReason = error.description;
          if (reryCount > 0 && error.code == -1005) {
              self.downloadRetryTimes++;
              dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(self.retryDelay * NSEC_PER_SEC)), dispatch_get_global_queue(0, 0), ^{
                [self retryDownloadRes:reryCount request:request manager:manager downloaderHandle:downloaderHandle];
              });
              [downloaderHandle.record addDownloadBlockInfo:blockInfo];
              return;
          }
          NSHTTPURLResponse *resp = (NSHTTPURLResponse *)response;
          if (resp.statusCode == 404) {
              UPLogError(@"UPResource", @"%s[%d]资源包(%@)下载失败错误404!需删除目标文件", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName)
                  [[NSFileManager defaultManager] removeItemAtPath:savePath ?: @""
                                                             error:nil];
          }
          if (resp.statusCode == 416) {
              blockInfo.blockResult = YES;
              UPLogDebug(@"UPResource", @"%s[%d]资源包(%@)下载完成!保存地址:%@", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName, savePath);
              [downloaderHandle.callback onTaskSuccess:downloaderHandle];
              [downloaderHandle.record addDownloadBlockInfo:blockInfo];
              return;
          }
          UPLogError(@"UPResource", @"%s[%d]资源包(%@)下载失败!error:%@", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName, error);
          [downloaderHandle.callback onTaskFailure:downloaderHandle error:error];
          [downloaderHandle.record addDownloadBlockInfo:blockInfo];
          return;
      }
      UPLogDebug(@"UPResource", @"%s[%d]资源包(%@)下载完成!保存地址:%@", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName, downloaderHandle.record.savePath);
      [downloaderHandle.callback onTaskSuccess:downloaderHandle];
      [downloaderHandle.record addDownloadBlockInfo:blockInfo];
    };
    NSURLSessionDataTask *task = [manager dataTaskWithRequest:request uploadProgress:nil downloadProgress:nil completionHandler:completionHandler];
    __block NSInteger fileLength = 0;
    NSURLSessionResponseDisposition (^receiveResponseBlock)(NSURLSession *_Nonnull session, NSURLSessionDataTask *_Nonnull dataTask, NSURLResponse *_Nonnull response) = ^NSURLSessionResponseDisposition(NSURLSession *_Nonnull session, NSURLSessionDataTask *_Nonnull dataTask, NSURLResponse *_Nonnull response) {
      NSDictionary *dict = [(NSHTTPURLResponse *)response allHeaderFields];
      if (![dict[@"Content-Type"] isEqualToString:@"text/html"]) {
          fileLength = response.expectedContentLength + currentLength;
      }
      else {
          fileLength = currentLength;
      }
      NSFileManager *fileManager = [NSFileManager defaultManager];
      if (savePath) {
          if (![fileManager fileExistsAtPath:savePath]) {
              [fileManager createFileAtPath:savePath contents:nil attributes:nil];
          }
          NSFileHandle *fileHandle = [NSFileHandle fileHandleForWritingAtPath:savePath];
          fileHandles[savePath] = fileHandle;
      }

      return NSURLSessionResponseAllow;
    };
    [manager setDataTaskDidReceiveResponseBlock:receiveResponseBlock];
    void (^receiveDataBlock)(NSURLSession *_Nonnull session, NSURLSessionDataTask *_Nonnull dataTask, NSData *_Nonnull data) = ^(NSURLSession *_Nonnull session, NSURLSessionDataTask *_Nonnull dataTask, NSData *_Nonnull data) {
      NSFileHandle *fileHandle;
      if (savePath) {
          fileHandle = fileHandles[savePath];
      }
      if (fileLength != currentLength) {
          [fileHandle seekToEndOfFile];
          [fileHandle writeData:data];
          currentLength += data.length;
          [blockInfo cumsumSize:data.length];
      }
      downloaderHandle.record.fileSize = [NSString stringWithFormat:@"%0.2f", fileLength / 1024.0];
      CGFloat fProgress = currentLength * 100.0 / fileLength;
      UPLogDebug(@"UPResource", @"%s[%d]资源包:%@,下载进度:%.2f%%", __PRETTY_FUNCTION__, __LINE__, downloaderHandle.record.fileName, fProgress);
      dispatch_async(dispatch_get_main_queue(), ^{
        [downloaderHandle.callback onProgressChanged:downloaderHandle progress:fProgress];
      });
    };
    [manager setDataTaskDidReceiveDataBlock:receiveDataBlock];
    [task resume];
    downloaderHandle.record.downloadTask = task;
}

- (void)cancel:(UPDownloadHandle *)handle
{
    UPDownloaderHandle *downloaderHandle = (UPDownloaderHandle *)handle;
    [downloaderHandle.record.downloadTask cancel];
    if (handle) {
        @synchronized(self.handles)
        {
            [self.handles removeObjectForKey:handle.link];
        }
    }
}
- (NSInteger)fileLengthForPath:(NSString *)path
{
    NSInteger fileLength = 0;
    NSFileManager *fileManager = [[NSFileManager alloc] init];
    if ([fileManager fileExistsAtPath:path]) {
        NSError *error = nil;
        NSDictionary *fileDict = [fileManager attributesOfItemAtPath:path error:&error];
        if (!error && fileDict) {
            fileLength = [fileDict fileSize];
        }
    }
    return fileLength;
}

#pragma mark - UPConnectionListener
- (void)onConnectionChanged:(BOOL)isOnline type:(UPConnectionType)type
{
}

@end

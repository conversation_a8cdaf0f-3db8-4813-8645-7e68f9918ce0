//
//  UPResourceInjection.h
//  UPRes
//
//  Created by <PERSON> on 2019/5/10.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPResourceManager.h"
#import "UPResourceConfig.h"
@class UPResourceResult;
NS_ASSUME_NONNULL_BEGIN

@interface UPResourceInjection : NSObject
@property (atomic, readonly, strong) UPResourceManager *resourceManager;
@property (atomic, readonly, strong, nullable) UPResourceSettings *settings;
@property (atomic, readonly, assign) BOOL isTestModeOn;

+ (void)initializeWithTestModeOn:(BOOL)testModeOn;
+ (void)initializeWithTestModeOn:(BOOL)testModeOn appPlatform:(UPResourceAppPlatform)appPlatform;
+ (UPResourceInjection *)getInstance;
- (void)presetResPkg:(id<UPResourceListCallback>)callback;
- (void)presetResPkg:(NSString *)filePath callback:(id<UPResourceListCallback>)callback;
- (UPResourceResult *)syncPresetResPkg:(NSString *)name type:(UPResourceType)type;

@end

NS_ASSUME_NONNULL_END

//
//  SpecifyResourceVersionPatcher.m
//  UPResDelegateIMP
//
//  Created by osiris on 2021/2/8.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SpecifyResourceVersionPatcher.h"
#import "UPResourceInjection.h"
#import "SpecifyResourceVersionModel.h"

@interface SpecifyResourceVersionPatcher ()

@property (nonatomic, strong) SpecifyResourceVersionModel *model;

@end

@implementation SpecifyResourceVersionPatcher
#pragma mark - LogicPatch
- (NSString *)name
{
    return NSStringFromClass(self.class);
}

- (NSInteger)priority
{
    return 2;
}

- (BOOL)isNeedPatch:(id<Page>)page
{
    return [SpecifyResourceVersionModel.needParchResrouce.allKeys containsObject:page.uri.scheme] && page.uri.host && ![page.uri.queryParameterMap.allKeys containsObject:PARAM_SERVERRESVERSION];
}

- (BOOL)patch:(id<Page>)page
{
    _model = [[SpecifyResourceVersionModel alloc] initWithPage:page];
    if ([page.uri.queryParameterMap.allKeys containsObject:PARAM_ISDEVICERESOURCE]) {
        [_model deviceResourceHandler];
    }
    else {
        [_model normalResourceHandler];
    }

    return YES;
}

- (void)removeTrigger:(id<Page>)page
{
    /* TODO */
}
@end

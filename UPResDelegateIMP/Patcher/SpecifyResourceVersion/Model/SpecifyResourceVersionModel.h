//
//  SpecifyResourceVersionModel.h
//  UPResDelegateIMP
//
//  Created by osiris on 2021/2/9.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

extern NSString *const PARAM_SERVERRESVERSION;
extern NSString *const PARAM_TARGET_URL;
extern NSString *const PARAM_ISDEVICERESOURCE;
extern NSString *const PARAM_PAGE_FLAG;
extern NSString *const PARAM_RES_VERSION;

@protocol Page;
@class UPResourceInfo;
@interface SpecifyResourceVersionModel : NSObject

- (instancetype)initWithPage:(id<Page>)page;

@property (nonatomic, strong, readonly, class) NSDictionary *needParchResrouce;
@property (nonatomic, strong, readonly) id<Page> page;

- (void)deviceResourceHandler;
- (void)normalResourceHandler;

- (NSArray<UPResourceInfo *> *)deviceResource;
- (NSArray<UPResourceInfo *> *)normalResource;

+ (NSString *)decodeTargetURL:(NSString *)base64;
@end

NS_ASSUME_NONNULL_END

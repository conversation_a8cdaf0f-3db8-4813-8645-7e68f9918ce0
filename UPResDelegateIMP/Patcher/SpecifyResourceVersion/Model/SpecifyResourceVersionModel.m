//
//  SpecifyResourceVersionModel.m
//  UPResDelegateIMP
//
//  Created by o<PERSON><PERSON> on 2021/2/9.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SpecifyResourceVersionModel.h"
#import <UPVDN/Page.h>
#import "UPResourceInjection.h"

NSString *const PARAM_ISDEVICERESOURCE = @"isDeviceResource";
NSString *const PARAM_PAGE_FLAG = @"flag";

@interface SpecifyResourceVersionModel ()

@property (nonatomic, strong) id<Page> page;

@end

@implementation SpecifyResourceVersionModel

- (instancetype)initWithPage:(id<Page>)page
{
    if (self = [super init]) {
        if (self = [super init]) {
            _page = page;
        }
    }

    return self;
}

#pragma mark - getter, setter
+ (NSDictionary *)needParchResrouce
{
    return @{
        @"mpaas" : @(UPResourceTypeMPAAS),
    };
    ;
}

#pragma mark - public methods
- (void)deviceResourceHandler
{
    NSArray<UPResourceInfo *> *resources = [self multiResourceFilter:[self deviceResource]];
    if (resources.count == 0) {
        [self normalResourceHandler];
    }
    else if (resources.count == 1) {
        [self singleResourceHandler:_page resource:resources.firstObject];
    }
    else {
        [self multipleResourceHandler:_page resources:resources];
    }
}
- (void)normalResourceHandler
{
    NSArray<UPResourceInfo *> *resources = [self multiResourceFilter:[self normalResource]];
    if (resources.count == 0) {
        [self blankResourceHandler:_page];
    }
    else if (resources.count == 1) {
        [self singleResourceHandler:_page resource:resources.firstObject];
    }
    else {
        [self multipleResourceHandler:_page resources:resources];
    }
}

- (NSArray<UPResourceInfo *> *)deviceResource
{
    UPResourceDeviceCondition *deviceCondition = [[UPResourceDeviceCondition alloc] initResourceType:UPResourceTypeMPAAS];
    deviceCondition.model = _page.uri.queryParameterMap[@"model"] ?: @"";
    deviceCondition.typeId = _page.uri.queryParameterMap[@"typeid"] ?: @"";
    deviceCondition.prodNo = _page.uri.queryParameterMap[@"devNo"] ?: @"";
    deviceCondition.deviceType = _page.uri.queryParameterMap[@"deviceType"] ?: @"";
    deviceCondition.deviceNetType = _page.uri.queryParameterMap[@"deviceNetType"] ?: @"";

    return [[UPResourceInjection getInstance].resourceManager searchDeviceResList:deviceCondition];
}

- (NSArray<UPResourceInfo *> *)normalResource
{
    NSArray<UPResourceInfo *> *resources = [[UPResourceInjection getInstance].resourceManager searchNormalResList:_page.uri.host type:[SpecifyResourceVersionModel.needParchResrouce[_page.uri.scheme] integerValue]];
    NSMutableArray<UPResourceInfo *> *resourceList = [NSMutableArray arrayWithArray:resources];
    [resources enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if (obj.preset) {
          [resourceList removeObject:obj];
      }
    }];
    return [resourceList copy];
}

+ (NSString *)decodeTargetURL:(NSString *)base64
{
    NSData *data = [[NSData alloc] initWithBase64EncodedString:base64 options:0];
    return [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
}

#pragma mark - private methods
- (void)blankResourceHandler:(id<Page>)page
{
    [page.uri addQueryParameter:[[NSURLQueryItem alloc] initWithName:PARAM_SERVERRESVERSION value:@""]];
    [page resetURL:page.uri.URL.absoluteString];
    [page moveToStage:VdnPageStageVdns];
}

- (void)singleResourceHandler:(id<Page>)page resource:(UPResourceInfo *)resource
{
    [page.uri addQueryParameter:[[NSURLQueryItem alloc] initWithName:PARAM_SERVERRESVERSION value:resource.version]];
    [page resetURL:page.uri.URL.absoluteString];
    [page moveToStage:VdnPageStageVdns];
}

- (void)multipleResourceHandler:(id<Page>)page resources:(NSArray<UPResourceInfo *> *)resources
{
    NSString *targetURL = [self createTargetURL:page];
    [page resetURL:targetURL];
    [page setFlag:VdnPageFlagPresent];
    [page moveToStage:VdnPageStageVdns];
}

- (NSString *)createTargetURL:(id<Page>)page
{
    return [NSString stringWithFormat:@"https://uplus.haier.com/uplusapp/UpResource/specifyResourceVersion.html?%@=%@&%@=%ld", PARAM_TARGET_URL, [[page.uri.string dataUsingEncoding:NSUTF8StringEncoding] base64EncodedStringWithOptions:0] ?: @"", PARAM_PAGE_FLAG, (long)page.flag];
}

- (NSArray<UPResourceInfo *> *)multiResourceFilter:(NSArray<UPResourceInfo *> *)resourceList
{
    if (resourceList.count > 1 && [UPResourceInjection getInstance].resourceManager.environment == UPResEnvironmentProduction) {
        UPResourceInfo *latest = [[UPResourceInjection getInstance].resourceManager getLatestInfoByName:resourceList.firstObject.name type:UPResourceTypeMPAAS];
        return latest ? @[ latest ] : @[];
    }

    return resourceList;
}
@end

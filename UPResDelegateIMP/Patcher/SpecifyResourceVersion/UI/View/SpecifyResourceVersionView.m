//
//  SpecifyResourceVersionView.m
//  UPResDelegateIMP
//
//  Created by osiris on 2021/7/6.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SpecifyResourceVersionView.h"

@interface SpecifyResourceVersionView () <UIPickerViewDataSource, UIPickerViewDelegate>

@property (nonatomic, strong) UIView *contentView;
@property (nonatomic, strong) UIButton *cancelButton;
@property (nonatomic, strong) UILabel *titleLabel;
@property (nonatomic, strong) UIButton *confirmButton;
@property (nonatomic, strong) UIView *divider;
@property (nonatomic, strong) UIPickerView *picker;

@property (nonatomic, strong) NSArray<NSString *> *options;
@property (nonatomic, assign) NSInteger index;
@end

@implementation SpecifyResourceVersionView

- (instancetype)initWithOptions:(NSArray<NSString *> *)options
{
    if (self = [super initWithFrame:CGRectZero]) {
        self.options = options;
        self.index = 0;
        [self configView];
        [self configConstraint];
    }

    return self;
}

- (void)configView
{
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    self.cancelButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.confirmButton.translatesAutoresizingMaskIntoConstraints = NO;
    self.divider.translatesAutoresizingMaskIntoConstraints = NO;
    self.picker.translatesAutoresizingMaskIntoConstraints = NO;
    self.titleLabel.translatesAutoresizingMaskIntoConstraints = NO;
    [self addSubview:self.contentView];
    [self.contentView addSubview:self.cancelButton];
    [self.contentView addSubview:self.titleLabel];
    [self.contentView addSubview:self.confirmButton];
    [self.contentView addSubview:self.divider];
    [self.contentView addSubview:self.picker];
}

- (void)configConstraint
{
    [self addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[contentView]|" options:0 metrics:nil views:@{ @"contentView" : self.contentView }]];
    [self addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|[contentView]|" options:0 metrics:nil views:@{ @"contentView" : self.contentView }]];
    [self.contentView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:|-15-[titleLabel]" options:0 metrics:nil views:@{ @"titleLabel" : self.titleLabel }]];
    [self.contentView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:[cancelButton]-[divider(==1)][picker(==216.0)]-|"
                                                                             options:0
                                                                             metrics:nil
                                                                               views:@{
                                                                                   @"cancelButton" : self.cancelButton,
                                                                                   @"divider" : self.divider,
                                                                                   @"picker" : self.picker
                                                                               }]];
    [self.contentView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|-[cancelButton]" options:0 metrics:nil views:@{ @"cancelButton" : self.cancelButton }]];
    [self.contentView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:[confirmButton]-|" options:0 metrics:nil views:@{ @"confirmButton" : self.confirmButton }]];
    [self.contentView addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[divider]|" options:0 metrics:nil views:@{ @"divider" : self.divider }]];
    [self.contentView addConstraint:[NSLayoutConstraint constraintWithItem:self.titleLabel attribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:self.contentView attribute:NSLayoutAttributeCenterX multiplier:1.0 constant:0.0]];
    [self.contentView addConstraint:[NSLayoutConstraint constraintWithItem:self.cancelButton attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:self.titleLabel attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0.0]];
    [self.contentView addConstraint:[NSLayoutConstraint constraintWithItem:self.confirmButton attribute:NSLayoutAttributeCenterY relatedBy:NSLayoutRelationEqual toItem:self.titleLabel attribute:NSLayoutAttributeCenterY multiplier:1.0 constant:0.0]];
    [self.contentView addConstraint:[NSLayoutConstraint constraintWithItem:self.picker attribute:NSLayoutAttributeCenterX relatedBy:NSLayoutRelationEqual toItem:self.contentView attribute:NSLayoutAttributeCenterX multiplier:1.0 constant:0.0]];
}

#pragma mark - getter, setter
- (UIView *)contentView
{
    if (!_contentView) {
        _contentView = [[UIView alloc] init];
        _contentView.backgroundColor = [UIColor whiteColor];
    }

    return _contentView;
}

- (UIButton *)cancelButton
{
    if (!_cancelButton) {
        _cancelButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_cancelButton setTitle:@"取消" forState:UIControlStateNormal];
        [_cancelButton setTitleColor:[UIColor colorWithRed:34.0 / 255.0 green:131.0 / 255.0 blue:226.0 / 255.0 alpha:1.0] forState:UIControlStateNormal];
        [_cancelButton addTarget:self action:@selector(cancelAction) forControlEvents:UIControlEventTouchUpInside];
    }

    return _cancelButton;
}

- (UILabel *)titleLabel
{
    if (!_titleLabel) {
        _titleLabel = [[UILabel alloc] init];
        _titleLabel.text = @"请选择内测版本";
        _titleLabel.font = [UIFont systemFontOfSize:18.0];
        _titleLabel.textColor = [UIColor darkTextColor];
    }

    return _titleLabel;
}
- (UIButton *)confirmButton
{
    if (!_confirmButton) {
        _confirmButton = [UIButton buttonWithType:UIButtonTypeCustom];
        [_confirmButton setTitle:@"确定" forState:UIControlStateNormal];
        [_confirmButton setTitleColor:[UIColor colorWithRed:34.0 / 255.0 green:131.0 / 255.0 blue:226.0 / 255.0 alpha:1.0] forState:UIControlStateNormal];
        [_confirmButton addTarget:self action:@selector(confirmAction) forControlEvents:UIControlEventTouchUpInside];
    }

    return _confirmButton;
}

- (UIView *)divider
{
    if (!_divider) {
        _divider = [[UIView alloc] init];
        _divider.backgroundColor = [UIColor colorWithRed:238.0 / 255.0 green:238.0 / 255.0 blue:238.0 / 255.0 alpha:0.6];
    }

    return _divider;
}

- (UIPickerView *)picker
{
    if (!_picker) {
        _picker = [[UIPickerView alloc] init];
        _picker.dataSource = self;
        _picker.delegate = self;
    }

    return _picker;
}

#pragma mark - Action
- (void)cancelAction
{
    self.cancel ? self.cancel() : nil;
}

- (void)confirmAction
{
    self.confirm ? self.confirm(self.index) : nil;
}

#pragma mark - UIPickerViewDataSource
- (NSInteger)numberOfComponentsInPickerView:(UIPickerView *)pickerView
{
    return 1;
}

- (NSInteger)pickerView:(UIPickerView *)pickerView numberOfRowsInComponent:(NSInteger)component
{
    return self.options.count;
}

#pragma mark - UIPickerViewDelegate
- (NSString *)pickerView:(UIPickerView *)pickerView titleForRow:(NSInteger)row forComponent:(NSInteger)component
{
    return self.options[row];
}

- (void)pickerView:(UIPickerView *)pickerView didSelectRow:(NSInteger)row inComponent:(NSInteger)component
{
    self.index = row;
}

@end

//
//  SpecifyResourceVersionController.m
//  UPResDelegateIMP
//
//  Created by osiris on 2021/2/8.
//  Copyright © 2021 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "SpecifyResourceVersionController.h"
#import <UPVDN/UIViewController+Vdn.h>
#import <UPVDN/UpPage.h>
#import <UPVDN/UPVDNManager.h>
#import <UPVDN/UPVDN.h>
#import "UPResourceInfo.h"
#import "SpecifyResourceVersionModel.h"
#import "SpecifyResourceVersionAlertAnimator.h"
#import "SpecifyResourceVersionView.h"

@interface SpecifyResourceVersionController ()

@property (nonatomic, strong) SpecifyResourceVersionView *contentView;
@property (nonatomic, strong) SpecifyResourceVersionModel *model;
@property (nonatomic, strong) NSArray<UPResourceInfo *> *resources;
@property (nonatomic, strong) SpecifyResourceVersionAlertAnimator *animator;

@end

@implementation SpecifyResourceVersionController

- (instancetype)initWithNibName:(NSString *)nibNameOrNil bundle:(NSBundle *)nibBundleOrNil
{
    if (self = [super initWithNibName:nibNameOrNil bundle:nibBundleOrNil]) {
        self.modalPresentationStyle = UIModalPresentationCustom;
        self.animator = [[SpecifyResourceVersionAlertAnimator alloc] init];
        self.transitioningDelegate = self.animator;
    }

    return self;
}

- (void)viewDidLoad
{
    [super viewDidLoad];
    [self configData];
    [self configView];
    [self configConstraint];
}

- (void)configData
{
    NSString *decodeTargetURL = [SpecifyResourceVersionModel decodeTargetURL:self.parameters[PARAM_TARGET_URL]];
    _model = [[SpecifyResourceVersionModel alloc] initWithPage:[[UpPage alloc] initWithURL:decodeTargetURL flag:[self.parameters[PARAM_PAGE_FLAG] integerValue]]];
    if ([_model.page.uri.queryParameterMap.allKeys containsObject:PARAM_ISDEVICERESOURCE]) {
        _resources = [_model deviceResource];
    }

    if (!_resources || _resources.count == 0) {
        _resources = [_model normalResource];
    }
}

- (void)configView
{
    self.contentView.translatesAutoresizingMaskIntoConstraints = NO;
    [self.view addSubview:self.contentView];
}

- (void)configConstraint
{
    [self.view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"H:|[contentView]|" options:0 metrics:nil views:@{ @"contentView" : self.contentView }]];
    [self.view addConstraints:[NSLayoutConstraint constraintsWithVisualFormat:@"V:[contentView(>=300)]|" options:0 metrics:nil views:@{ @"contentView" : self.contentView }]];
}

#pragma mark - getter, setter
- (SpecifyResourceVersionView *)contentView
{
    if (!_contentView) {
        NSMutableArray<NSString *> *versions = [NSMutableArray array];
        [self.resources enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
          NSString *iVersion = obj.version;
          if (iVersion) {
              [versions addObject:iVersion];
          }
        }];
        _contentView = [[SpecifyResourceVersionView alloc] initWithOptions:[versions copy]];
        __weak typeof(self) weakSelf = self;
        _contentView.cancel = ^{
          [weakSelf dismissViewControllerAnimated:YES
                                       completion:^{
                                       }];
        };

        _contentView.confirm = ^(NSInteger index) {
          [weakSelf dismissViewControllerAnimated:YES
                                       completion:^{
                                         if (index > weakSelf.resources.count - 1) {
                                             return;
                                         }
                                         [weakSelf.model.page.uri addQueryParameter:[NSURLQueryItem queryItemWithName:PARAM_SERVERRESVERSION value:weakSelf.resources[index].version]];
                                         [weakSelf.model.page resetURL:weakSelf.model.page.uri.URL.absoluteString];
                                         [UPVDNManager.shareManager.vdnDomain goToPage:weakSelf.model.page.originURL
                                                                                  flag:weakSelf.model.page.flag
                                                                            parameters:@{ @"hidesBottomBarWhenPushed" : @"1" }
                                                                              complete:weakSelf.listener
                                                                                 error:nil];
                                       }];
        };
    }

    return _contentView;
}

@end

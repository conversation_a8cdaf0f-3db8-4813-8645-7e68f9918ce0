//
//  SSHomeCompoundAlertAnimator.m
//  scene
//
//  Created by osiris on 2019/1/22.
//  Copyright © 2019 haierguook. All rights reserved.
//

#import "SpecifyResourceVersionAlertAnimator.h"

@interface SpecifyResourceVersionAlertAnimator ()

@property (nonatomic, assign) BOOL isPresent;

@end

@implementation SpecifyResourceVersionAlertAnimator

- (instancetype)init
{
    self = [super init];
    if (self) {
        _isPresent = NO;
    }
    return self;
}

#pragma mark - UIViewControllerAnimatedTransitioning
- (NSTimeInterval)transitionDuration:(id<UIViewControllerContextTransitioning>)transitionContext
{
    return 0.3;
}

- (void)animateTransition:(id<UIViewControllerContextTransitioning>)transitionContext
{
    UIViewController *toViewController = [transitionContext viewControllerForKey:UITransitionContextToViewControllerKey];
    UIViewController *fromViewController = [transitionContext viewControllerForKey:UITransitionContextFromViewControllerKey];
    CGRect finalFrame = [transitionContext finalFrameForViewController:toViewController];
    if (_isPresent) {
        toViewController.view.alpha = 0.0;
        toViewController.view.frame = finalFrame;
        [transitionContext.containerView addSubview:toViewController.view];
    }
    NSTimeInterval duration = [self transitionDuration:transitionContext];
    [UIView animateWithDuration:duration
        delay:0.0
        options:UIViewAnimationOptionCurveLinear
        animations:^{
          if (self.isPresent) {
              toViewController.view.alpha = 1.0;
          }
          else {
              fromViewController.view.alpha = 0.0;
          }
        }
        completion:^(BOOL finished) {
          [transitionContext completeTransition:YES];
        }];
}

#pragma mark - UIViewControllerTransitioningDelegate
- (id<UIViewControllerAnimatedTransitioning>)animationControllerForDismissedController:(UIViewController *)dismissed
{
    _isPresent = NO;
    return self;
}

- (id<UIViewControllerAnimatedTransitioning>)animationControllerForPresentedController:(UIViewController *)presented presentingController:(UIViewController *)presenting sourceController:(UIViewController *)source
{
    _isPresent = YES;
    return self;
}

@end

//
//  UPResourceFileSystem.m
//  UPRes
//
//  Created by <PERSON> on 2019/4/29.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceFileSystem.h"
#import <UPLog/UPLog.h>
#import <ZipArchive/ZipArchive.h>
#import <AWFileHash/AWFileHash.h>
#import <CommonCrypto/CommonDigest.h>
#import <CommonCrypto/CommonHMAC.h>
@interface UPResourceFileSystem ()
@property (nonatomic, readonly, weak) NSFileManager *fileManager;
- (BOOL)arrayObject:(NSArray *)object writeTo:(NSString *)target error:(NSError **)error;
- (BOOL)dictionaryObject:(NSDictionary *)object writeTo:(NSString *)target error:(NSError **)error;
@end

@implementation UPResourceFileSystem
#pragma mark - Property Methods
- (NSFileManager *)fileManager
{
    return [NSFileManager defaultManager];
}

#pragma mark - UPFileDelegate
- (BOOL)exists:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    BOOL bResult = [self.fileManager fileExistsAtPath:path];
    return bResult;
}

- (BOOL)mkdirs:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    NSError *error = NULL;
    BOOL bResult = [self.fileManager createDirectoryAtPath:path withIntermediateDirectories:YES attributes:nil error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]文件夹路径创建失败！path:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, path, error);
    }
    return bResult;
}

- (BOOL)deletePath:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]] || path.length == 0) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    NSError *error = NULL;
    BOOL bResult = [self.fileManager removeItemAtPath:path error:&error];
    UPLogInfo(@"UPResource", @"%s[%d]要删除的目标路径是%@", __PRETTY_FUNCTION__, __LINE__, path);
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]文件夹路径删除失败！path:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, path, error);
    }
    return bResult;
}

- (BOOL)rename:(NSString *)source to:(NSString *)target
{
    if (![source isKindOfClass:[NSString class]] || ![target isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！src:%@,tgt:%@", __PRETTY_FUNCTION__, __LINE__, source, target);
        return NO;
    }
    NSError *error = NULL;
    BOOL bResult = [self.fileManager moveItemAtPath:source toPath:target error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]重命名/移动文件操作失败！src:%@,tgt:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, source, target, error);
    }
    return bResult;
}

- (BOOL)isFile:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    BOOL bIsDirectory = NO;
    BOOL bResult = [self.fileManager fileExistsAtPath:path isDirectory:&bIsDirectory];
    if (bIsDirectory) {
        UPLogError(@"UPResource", @"%s[%d]指定路径为非文件！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    return bResult;
}

- (BOOL)isDirectory:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    BOOL bIsDirectory = NO;
    BOOL bResult = [self.fileManager fileExistsAtPath:path isDirectory:&bIsDirectory];
    if (!bIsDirectory) {
        UPLogError(@"UPResource", @"%s[%d]指定路径为非目录！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return NO;
    }
    return bResult;
}

- (NSString *)getParentPath:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return nil;
    }
    NSArray *pathComponents = [path pathComponents];
    if (![pathComponents isKindOfClass:[NSArray class]]) {
        return nil;
    }
    NSMutableArray *components = [pathComponents mutableCopy];
    [components removeLastObject];
    return components.lastObject;
}

- (NSString *)getFilename:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return nil;
    }
    return path.lastPathComponent;
}

- (NSArray<NSString *> *)listSubNames:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return nil;
    }
    NSError *error = NULL;
    NSArray *subNames = [self.fileManager contentsOfDirectoryAtPath:path error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]指定路径下所有文件的名称获取失败！path:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, path, error);
    }
    return subNames;
}

- (NSArray<NSString *> *)listSubPaths:(NSString *)path
{
    if (![path isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！path:%@", __PRETTY_FUNCTION__, __LINE__, path);
        return nil;
    }
    NSError *error = NULL;
    NSArray *subNames = [self.fileManager contentsOfDirectoryAtPath:path error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]指定路径下所有文件的名称获取失败！path:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, path, error);
        return nil;
    }
    NSMutableArray *subPaths = [NSMutableArray array];
    for (NSString *subName in subNames) {
        NSString *subPath = [path stringByAppendingPathComponent:subName];
        if (![subPath isKindOfClass:[NSString class]]) {
            UPLogError(@"UPResource", @"%s[%d]子路径拼接失败！path:%@,subName:%@", __PRETTY_FUNCTION__, __LINE__, path, subName);
            continue;
        }
        [subPaths addObject:subPath];
    }
    return subPaths;
}

- (BOOL)unzip:(NSString *)source to:(NSString *)target
{
    if (![self isFile:source]) {
        UPLogError(@"UPResource", @"%s[%d]待解压的源路径文件错误！path:%@,error:该路径为非文件路径!", __PRETTY_FUNCTION__, __LINE__, source);
        return NO;
    }
    ZipArchive *archive = [[ZipArchive alloc] initWithFileManager:self.fileManager];
    if (![archive UnzipOpenFile:source]) {
        UPLogError(@"UPResource", @"%s[%d]待解压的源路径文件错误！path:%@,error:压缩文件打开失败!", __PRETTY_FUNCTION__, __LINE__, source);
        return NO;
    }
    BOOL result = [archive UnzipFileTo:target overWrite:YES];
    [archive UnzipCloseFile];
    return result;
}

- (BOOL)copy:(NSString *)source to:(NSString *)target
{
    if (![source isKindOfClass:[NSString class]] || ![target isKindOfClass:[NSString class]]) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！src:%@,tgt:%@", __PRETTY_FUNCTION__, __LINE__, source, target);
        return NO;
    }
    BOOL bResult = [self.fileManager fileExistsAtPath:target];
    if (bResult) {
        [self.fileManager removeItemAtPath:target error:nil];
        UPLogInfo(@"UPResource", @"%s[%d]要删除的目标路径是%@", __PRETTY_FUNCTION__, __LINE__, target);
    }
    NSError *error = NULL;
    bResult = [self.fileManager copyItemAtPath:source toPath:target error:&error];
    if (error) {
        UPLogError(@"UPResource", @"%s[%d]拷贝文件操作失败！src:%@,tgt:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, source, target, error);
    }
    return bResult;
}

- (BOOL)writeObject:(NSObject *)object to:(NSString *)target
{
    if (![target isKindOfClass:[NSString class]] || target.length == 0) {
        UPLogError(@"UPResource", @"%s[%d]路径格式有误！target:%@", __PRETTY_FUNCTION__, __LINE__, target);
        return NO;
    }
    BOOL bResult = NO;
    NSError *error = NULL;
    if ([object isKindOfClass:[NSArray class]]) {
        bResult = [self arrayObject:(NSArray *)object writeTo:target error:&error];
    }
    else if ([object isKindOfClass:[NSDictionary class]]) {
        bResult = [self dictionaryObject:(NSDictionary *)object writeTo:target error:&error];
    }
    else if ([object isKindOfClass:[NSData class]]) {
        bResult = [(NSData *)object writeToFile:target options:NSDataWritingAtomic error:&error];
    }
    else if ([object isKindOfClass:[NSString class]]) {
        bResult = [(NSString *)object writeToFile:target atomically:YES encoding:NSUTF8StringEncoding error:&error];
    }
    if (!bResult || error) {
        UPLogError(@"UPResource", @"%s[%d]对象写入操作失败！target:%@,error:%@", __PRETTY_FUNCTION__, __LINE__, target, error);
    }
    return bResult;
}

- (NSString *)calcHash:(NSString *)path algorithm:(NSString *)algorithm
{
    if (![algorithm isKindOfClass:[NSString class]]) {
        return [self calcMD5:path];
    }
    if ([[algorithm uppercaseString] isEqualToString:HASH_ALGORITHM_MD5]) {
        return [self calcMD5:path];
    }
    else if ([[algorithm uppercaseString] isEqualToString:HASH_ALGORITHM_SHA1]) {
        return [self calcSHA1:path];
    }
    UPLogError(@"UPResource", @"%s[%d]哈希值计算失败！string:%@,error:不支持%@算法。", __PRETTY_FUNCTION__, __LINE__, path, algorithm);
    return nil;
}

- (NSString *)calcMD5:(NSString *)path
{
    return [self md5HashOfFileAtPath:path];
}

- (NSString *)calcSHA1:(NSString *)path
{
    return [AWFileHash sha1HashOfFileAtPath:path];
}
- (unsigned long long)getFileSize:(NSString *)path
{

    if ([self exists:path]) {
        NSEnumerator *childFilesEnumerator = [[self.fileManager subpathsAtPath:path] objectEnumerator];
        NSString *fileName;
        unsigned long long folderSize = 0;
        while ((fileName = [childFilesEnumerator nextObject]) != nil) {
            NSString *fileAbsolutePath = [path stringByAppendingPathComponent:fileName];
            if (![self checkFileNeedIgnore:fileAbsolutePath resPath:path]) {
                folderSize += [self fileSizeAtPath:fileAbsolutePath];
            }
        }
        return folderSize;
    }
    return 0;
}
#pragma mark - Non-Public Methods
- (unsigned long long)fileSizeAtPath:(NSString *)filePath
{
    if ([self exists:filePath]) {
        NSError *error;
        unsigned long long size = [[self.fileManager attributesOfItemAtPath:filePath error:&error] fileSize];
        if (error) {
            UPLogError(@"UPResource", @"%s[%d]计算文件路出错！path:%@---error:%@", __PRETTY_FUNCTION__, __LINE__, filePath, error);
            return 0;
        }
        return size;
    }
    return 0;
}
- (BOOL)arrayObject:(NSArray *)object writeTo:(NSString *)target error:(NSError **)error
{
    if (@available(iOS 11.0, *)) {
        NSURL *url = [NSURL URLWithString:target];
        if (![url isKindOfClass:[NSURL class]]) {
            NSString *errMsg = @"待写入路径对象创建失败！";
            *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
            return NO;
        }
        return [object writeToURL:url error:error];
    }
    BOOL bResult = [object writeToFile:target atomically:YES];
    if (!bResult) {
        NSString *errMsg = @"对象写入失败！";
        *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
    }
    return bResult;
}

- (BOOL)dictionaryObject:(NSDictionary *)object writeTo:(NSString *)target error:(NSError **)error
{
    if (@available(iOS 11.0, *)) {
        NSURL *url = [NSURL URLWithString:target];
        if (![url isKindOfClass:[NSURL class]]) {
            NSString *errMsg = @"待写入路径对象创建失败！";
            *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
            return NO;
        }
        return [object writeToURL:url error:error];
    }
    BOOL bResult = [object writeToFile:target atomically:YES];
    if (!bResult) {
        NSString *errMsg = @"对象写入失败！";
        *error = [NSError errorWithDomain:errMsg code:-1 userInfo:nil];
    }
    return bResult;
}
- (NSString *)md5HashOfFileAtPath:(NSString *)filePath
{
    NSString *fileContentsHash;
    if ([[NSFileManager defaultManager] fileExistsAtPath:filePath]) {
        NSData *fileContents = [NSData dataWithContentsOfFile:filePath];
        fileContentsHash = [self computeHashForData:fileContents];
    }
    return fileContentsHash;
}
- (NSString *)computeHashForData:(NSData *)inputData
{
    uint8_t digest[CC_MD5_DIGEST_LENGTH];
    CC_MD5(inputData.bytes, (CC_LONG)inputData.length, digest);
    NSMutableString *inputHash = [NSMutableString stringWithCapacity:CC_MD5_DIGEST_LENGTH * 2];
    for (int i = 0; i < CC_MD5_DIGEST_LENGTH; i++) {
        [inputHash appendFormat:@"%02x", digest[i]];
    }
    UPLogDebug(@"UPResource", @"计算hash值为--%@", inputHash);
    return inputHash;
}

// 判断哪些文件不被计算缓存大小
- (BOOL)checkFileNeedIgnore:(NSString *)path resPath:(NSString *)resPath
{
    NSMutableArray *reverseResoucePaths = [NSMutableArray array];
    // 资源包文件夹
    [reverseResoucePaths addObject:[resPath stringByAppendingPathComponent:@"routes"]];
    [reverseResoucePaths addObject:[resPath stringByAppendingPathComponent:@"video"]];
    [reverseResoucePaths addObject:[resPath stringByAppendingPathComponent:@"other"]];

    BOOL __block should = NO; //计算缓存大小时，是否要忽略此文件
    [reverseResoucePaths enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      if ([path containsString:obj]) {
          should = YES;
          *stop = YES;
      }
    }];

    return should;
}

@end

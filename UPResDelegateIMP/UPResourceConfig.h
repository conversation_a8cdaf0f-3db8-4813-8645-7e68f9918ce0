//
//  UPResourceConfig.h
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/6.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
typedef enum : NSInteger {
    //国内
    UPResourceAppPlatformChina = 0,
    //东南亚
    UPResourceAppPlatformSoutheasAsia,
    //未知
    UPResourceAppPlatformUnknown,
} UPResourceAppPlatform;

typedef enum : NSInteger {
    //生产
    UPRequestEnvProduction = 0,
    //验收
    UPRequestEnvTest,
    //未知
    UPRequestEnvUnknown,
} UPRequestEnv;

@interface UPResourceConfig : NSObject
/**
 appversion
 */
@property (nonatomic, copy) NSString *appVersion;
/**
 appId
 */
@property (nonatomic, copy) NSString *appID;
/**
 appKey
 */
@property (nonatomic, copy) NSString *appKey;
/**
 accessToken
 */
@property (nonatomic, copy) NSString *accessToken;
/**
 clientId
 */
@property (nonatomic, copy) NSString *clientID;
/**
 用户中心用户id 
 */
@property (nonatomic, copy) NSString *user_id;
/**
app平台
*/
@property (nonatomic, assign) UPResourceAppPlatform appPlatform;
/**
 生产或者验收环境
 */
@property (nonatomic, assign) UPRequestEnv requestEnv;

/*
 区域（市编码）
 */
@property (nonatomic, copy) NSString *localCode;

/// 是否游客模式
@property (nonatomic, assign) BOOL enablePrivacyAgreement;

+ (instancetype)shareInstance;
@end

NS_ASSUME_NONNULL_END

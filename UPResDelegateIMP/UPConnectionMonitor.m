//
//  UPConnectionMonitor.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPConnectionMonitor.h"
#import <AFNetworking/AFNetworkReachabilityManager.h>
#import <UPLog/UPLog.h>

@interface UPConnectionMonitor ()
@property (nonatomic, strong) AFNetworkReachabilityManager *manager;
@property (nonatomic, copy) void (^receiver)(AFNetworkReachabilityStatus status);
@property (nonatomic, strong) NSHashTable<id<UPConnectionListener>> *listeners;
- (void)notifyCurrentNetworkConnectionTypeToListeners;
- (void)notifyCurrentNetworkConnectionTypeToListener:(id<UPConnectionListener>)listener;
@end

@implementation UPConnectionMonitor
#pragma mark - Non-Public Methods
- (void)registerMonitor
{
    if (self.receiver != nil) {
        return;
    }
    __weak UPConnectionMonitor *weakSelf = self;
    self.receiver = ^(AFNetworkReachabilityStatus status) {
      __strong UPConnectionMonitor *strongSelf = weakSelf;
      UPLogDebug(@"UPResource", @"%s[%d]Network status:%@", __PRETTY_FUNCTION__, __LINE__, strongSelf.manager.localizedNetworkReachabilityStatusString);
      [strongSelf notifyCurrentNetworkConnectionTypeToListeners];
    };

    [self.manager setReachabilityStatusChangeBlock:self.receiver];
    [self.manager startMonitoring];
}

- (void)unregisterMonitor
{
    if (self.receiver == nil) {
        return;
    }
    [self.manager stopMonitoring];
    self.receiver = nil;
}

- (void)notifyCurrentNetworkConnectionTypeToListeners
{
    NSHashTable<id<UPConnectionListener>> *listeners = self.listeners.copy;
    for (id<UPConnectionListener> listener in listeners) {
        [self notifyCurrentNetworkConnectionTypeToListener:listener];
    }
}

- (void)notifyCurrentNetworkConnectionTypeToListener:(id<UPConnectionListener>)listener
{
    UPConnectionType connectionType = [self getConnectionType];
    BOOL isAvailable = [self isAvailable];
    listener.connectionChangedBlock ? listener.connectionChangedBlock(isAvailable, connectionType) : nil;
    if (![listener conformsToProtocol:@protocol(UPConnectionListener)] || ![listener respondsToSelector:@selector(onConnectionChanged:type:)]) {
        return;
    }
    [listener onConnectionChanged:isAvailable type:connectionType];
}

#pragma mark - Override
- (instancetype)init
{
    if (self = [super init]) {
        _listeners = [NSHashTable weakObjectsHashTable];
        _manager = [AFNetworkReachabilityManager manager];
        [self registerMonitor];
    }
    return self;
}

- (void)dealloc
{
    [self unregisterMonitor];
}

#pragma mark - ConnectionDelegate
- (BOOL)isAvailable
{
    return [self getConnectionType] == TYPE_WIFI || [self getConnectionType] == TYPE_MOBILE;
}

- (UPConnectionType)getConnectionType
{
    UPConnectionType type = TYPE_NONE;
    switch (self.manager.networkReachabilityStatus) {
        case AFNetworkReachabilityStatusReachableViaWWAN:
            type = TYPE_MOBILE;
            break;
        case AFNetworkReachabilityStatusReachableViaWiFi:
            type = TYPE_WIFI;
            break;
        case AFNetworkReachabilityStatusUnknown:
            type = TYPE_WIFI;
        default:
            break;
    }
    return type;
}

+ (NSString *)getWANIPAddress
{
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    NSURL *ipURL = [NSURL URLWithString:@"https://ifconfig.me/ip"];
    NSMutableURLRequest *request = [NSMutableURLRequest requestWithURL:ipURL];
    [request setTimeoutInterval:3];
    NSURLSession *session = [NSURLSession sharedSession];
    __block NSString *ip = @"";
    NSURLSessionDataTask *task = [session dataTaskWithRequest:request
                                            completionHandler:^(NSData *data, NSURLResponse *response, NSError *error) {
                                              if (!error) {
                                                  ip = [[NSString alloc] initWithData:data encoding:NSUTF8StringEncoding];
                                              }
                                              dispatch_semaphore_signal(semaphore);
                                            }];
    [task resume];
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    return ip;
}

#pragma mark - Public Methods
- (void)addConnectionListener:(id<UPConnectionListener>)listener
{
    if (![listener conformsToProtocol:@protocol(UPConnectionListener)]) {
        return;
    }
    @synchronized(self.listeners)
    {
        if (![self.listeners containsObject:listener]) {
            [self.listeners addObject:listener];
        }
    }
}

- (void)removeConnectionListener:(id<UPConnectionListener>)listener
{
    if (![listener conformsToProtocol:@protocol(UPConnectionListener)]) {
        return;
    }
    @synchronized(self.listeners)
    {
        if ([self.listeners containsObject:listener]) {
            [self.listeners removeObject:listener];
        }
    }
}

- (void)clearConnectionListeners
{
    @synchronized(self.listeners)
    {
        [self.listeners removeAllObjects];
    }
}

@end

//
//  UPOmsResInfo.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface UPOmsResInfo : NSObject

/**
 * @brief 名称，可做唯一标识。
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *name;

/**
 * @brief 资源版本。
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *resVersion;

/**
 * @brief 资源类型
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *resType;

/**
 * @brief 资源下载地址
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *resUrl;

/**
 * @brief md5校验码
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *md5;

/**
 * @brief 设备型号
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *model;

/**
 * @brief 设备类型唯一标识。
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *typeId;

/**
 * @brief 设备类别
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *deviceTypeIndex;

/**
 * @brief 产品编码
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *prodNo;

/**
 * @brief 资源类型
 */
@property (nonatomic, copy) NSString *resourceType;

/**
 * @brief 是否全屏 0非全屏 1全屏
 * @since 1.0.0
 */
@property (nonatomic, copy) NSString *hideStatusBar;

/**
 * @brief 是否强制升级 1强制 0不强制优先使用已安装版本
 * @since 2.20.0
 */
@property (nonatomic, copy) NSString *forceUpgrade;

/**
 * @brief 资源规则（仅资源是插件包时，才有loadMode、enableMode、blockingMode三个属性；其他资源，resRules为null）
 * @since 2.20.0
 */
@property (nonatomic, strong) NSDictionary *resRules;

/// 资源包状态 0发布中  1下架
@property (nonatomic, copy) NSString *resStatus;

/**
  远端地址
 */
@property (nonatomic, copy) NSString *remoteUrl;

/**
 * @brief 通过字典对象初始化资源信息对象的方法。
 * @param dict 包含资源信息对象数据的字典对象。
 * @return 若初始化成功则返回资源信息对象，否则返回nil。
 * @since 1.0.0
 */
- (instancetype)initWithDictionary:(NSDictionary *)dict;

/**
 * @brief 资源信息对象的字典数据格式。
 * @return 返回包含资源信息对象数据的字典。
 * @since 1.0.0
 */
- (NSDictionary *)dictionary;

/**
 * @brief 资源信息对象的JSON格式字符串
 * @return 返回当前资源信息对象的JSON格式字符串。
 * @since 1.0.0
 */
- (NSString *)jsonString;

@end

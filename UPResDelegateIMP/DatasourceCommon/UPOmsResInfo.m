//
//  UPOmsResInfo.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/13.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPOmsResInfo.h"
#import "UPResCommonFunctions.h"
#import <UPLog/UPLog.h>

NSString *const kLERemoteConfigInfoName = @"name";
NSString *const kLERemoteConfigInfoResVersion = @"resVersion";
NSString *const kLERemoteConfigInfoResType = @"resType";
NSString *const kLERemoteConfigInfoResUrl = @"resUrl";
NSString *const kLERemoteConfigInfoMd5 = @"md5";
NSString *const kLERemoteConfigInfoModel = @"model";
NSString *const kLERemoteConfigInfoTypeId = @"typeId";
NSString *const kLERemoteConfigInfoDeviceTypeIndex = @"deviceTypeIndex";
NSString *const kLERemoteConfigInfoProdNo = @"prodNo";
NSString *const kLERemoteConfigInfoResourceType = @"resourceType";
NSString *const kLERemoteConfigInfoHideStatusBar = @"hideStatusBar";
NSString *const kLERemoteConfigInfoForceUpgrade = @"forceUpgrade";
NSString *const kLERemoteConfigInfoResRules = @"resRules";
NSString *const kLERemoteConfigInfoResStatus = @"resStatus";
NSString *const kLERemoteConfigInfoResRemoteUrl = @"remoteUrl";

@implementation UPOmsResInfo
#pragma mark - Public Methods
- (instancetype)initWithDictionary:(NSDictionary *)dict
{
    if (![dict isKindOfClass:[NSDictionary class]]) {
        return nil;
    }
    if (self = [super init]) {
        _name = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoName);
        _resVersion = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResVersion);
        _resType = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResType);
        _resUrl = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResUrl);
        _md5 = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoMd5);
        _model = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoModel);
        _typeId = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoTypeId);
        _deviceTypeIndex = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoDeviceTypeIndex);
        _prodNo = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoProdNo);
        _resourceType = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResourceType);
        _hideStatusBar = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoHideStatusBar);
        _forceUpgrade = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoForceUpgrade);
        _resRules = UPRes_dictionaryValueOfDictionaryForKey(dict, kLERemoteConfigInfoResRules);
        _resStatus = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResStatus);
        _remoteUrl = UPRes_stringValueOfDictionaryForKey(dict, kLERemoteConfigInfoResRemoteUrl);
    }
    return self;
}

- (NSDictionary *)dictionary
{
    NSMutableDictionary *dict = [NSMutableDictionary dictionary];
    [dict setValue:UPRes_validStringValue(_name) forKey:kLERemoteConfigInfoName];
    [dict setValue:UPRes_validStringValue(_resVersion) forKey:kLERemoteConfigInfoResVersion];
    [dict setValue:UPRes_validStringValue(_resType) forKey:kLERemoteConfigInfoResType];
    [dict setValue:UPRes_validStringValue(_resUrl) forKey:kLERemoteConfigInfoResUrl];
    [dict setValue:UPRes_validStringValue(_md5) forKey:kLERemoteConfigInfoMd5];
    [dict setValue:UPRes_validStringValue(_model) forKey:kLERemoteConfigInfoModel];
    [dict setValue:UPRes_validStringValue(_typeId) forKey:kLERemoteConfigInfoTypeId];
    [dict setValue:UPRes_validStringValue(_deviceTypeIndex) forKey:kLERemoteConfigInfoDeviceTypeIndex];
    [dict setValue:UPRes_validStringValue(_prodNo) forKey:kLERemoteConfigInfoProdNo];
    [dict setValue:UPRes_validStringValue(_resourceType) forKey:kLERemoteConfigInfoResourceType];
    [dict setValue:UPRes_validStringValue(_hideStatusBar) forKey:kLERemoteConfigInfoHideStatusBar];
    [dict setValue:UPRes_validStringValue(_forceUpgrade) forKey:kLERemoteConfigInfoForceUpgrade];
    [dict setValue:UPRes_validDictionaryValue(_resRules) forKey:kLERemoteConfigInfoResRules];
    [dict setValue:UPRes_validStringValue(_resStatus) forKey:kLERemoteConfigInfoResStatus];
    [dict setValue:UPRes_validStringValue(_remoteUrl) forKey:kLERemoteConfigInfoResRemoteUrl];
    return dict;
}

- (NSString *)jsonString
{
    NSError *error = NULL;
    NSString *str = UPRes_jsonStringValueOfJsonObject(self.dictionary, &error);
    if (error) {
        UPLogWarning(@"UPResource", @"%s[%d]配置文件(%@)的JSON格式字符串转换失败!error:%@", __PRETTY_FUNCTION__, __LINE__, _name, error);
    }
    return str;
}
@end

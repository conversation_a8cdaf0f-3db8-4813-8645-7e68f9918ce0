//
//  UPResourceConfig.m
//  UPRes
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2019/12/6.
//  Copyright © 2019 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceConfig.h"

@implementation UPResourceConfig
+ (instancetype)shareInstance
{
    static UPResourceConfig *handle = nil;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      handle = [[UPResourceConfig alloc] init];
    });
    return handle;
}
@end

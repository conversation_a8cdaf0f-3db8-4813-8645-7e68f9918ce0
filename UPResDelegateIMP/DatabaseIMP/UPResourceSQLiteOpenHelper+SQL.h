//
//  UPResourceSQLiteOpenHelper+SQL.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/19.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceSQLiteOpenHelper.h"
#import "UPResourceType.h"
#import <fmdb/FMDB.h>

@class UPResourceInfo, UPResourceQuery;
@interface UPResourceSQLiteOpenHelper (SQL)
- (UPResourceInfo *)SQL_queryInfoFromFMResultSet:(FMResultSet *)set;
- (NSString *)SQL_createTableWithName:(NSString *)tableName;
- (NSString *)SQL_createIndexOfTable:(NSString *)tableName;
- (NSString *)SQL_addColumn:(NSString *)column type:(NSString *)type inTable:(NSString *)tableName;
- (NSString *)SQL_dropTableWithName:(NSString *)tableName;
- (NSString *)SQL_deleteTableWithName:(NSString *)tableName;
- (NSString *)SQL_deleteIndexOfTable:(NSString *)tableName;
- (NSString *)SQL_table:(NSString *)tableName insertInfo:(UPResourceInfo *)info;
- (NSString *)SQL_table:(NSString *)tableName deleteResourceInfo:(UPResourceInfo *)info;
- (NSString *)SQL_table:(NSString *)tableName updateResourceInfo:(UPResourceInfo *)info;

- (NSString *)SQL_table:(NSString *)tableName queryByName:(NSString *)name type:(UPResourceType)type excludeStatusOff:(BOOL)excludeStatusOff;

- (NSString *)SQL_table:(NSString *)tableName queryByName:(NSString *)name type:(UPResourceType)type version:(NSString *)version excludeStatusOff:(BOOL)excludeStatusOff;

- (NSString *)SQL_table:(NSString *)tableName queryDeviceConditionByStr:(NSString *)conditionStr appVersion:(NSString *)appVersion fromFunc:(NSString *)fromFunc;
- (NSString *)SQL_table:(NSString *)tableName queryResourceInfoByItemIds:(NSString *)items excludeStatusOff:(BOOL)excludeStatusOff;
- (UPResourceQuery *)SQL_queryConditionInfoFromFMResultSet:(FMResultSet *)set;
- (NSString *)SQL_createQueryConditionTableWithName:(NSString *)tableName;
- (NSString *)SQL_table:(NSString *)tableName insertResourceQuery:(UPResourceQuery *)query;
- (NSString *)SQL_table:(NSString *)tableName deleteResourceQuery:(UPResourceQuery *)query;
- (NSString *)SQL_table:(NSString *)tableName deleteResourceQueryAndRelation:(UPResourceQuery *)query;
- (NSString *)SQL_table:(NSString *)tableName updateResourceQuery:(UPResourceQuery *)query;

- (NSString *)SQL_table:(NSString *)tableName queryResourceQueryListByItemIds:(NSString *)items;
- (NSString *)SQL_table:(NSString *)tableName deleteQueryListByItemIds:(NSString *)items;
- (NSString *)SQL_table:(NSString *)tableName deleteResListByItemIds:(NSString *)items;

/// 查询指定状态资源
- (NSString *)SQL_table:(NSString *)tableName queryResourceInfoByStatus:(NSInteger)status name:(NSString *)name type:(UPResourceType)type version:(NSString *)version;
@end

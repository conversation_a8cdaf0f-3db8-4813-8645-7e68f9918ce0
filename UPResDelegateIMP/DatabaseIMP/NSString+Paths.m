//
//  NSString+Paths.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/20.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "NSString+Paths.h"

@implementation NSString (Paths)
- (NSString *)UPRes_relativePath
{
    NSString *rootPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    return [self stringByReplacingOccurrencesOfString:rootPath withString:@""];
}

- (NSString *)UPRes_fullAbsolutePath
{
    NSString *rootPath = [NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES) firstObject];
    if ([self containsString:rootPath]) {
        return self;
    }
    return [rootPath stringByAppendingPathComponent:self];
}

@end

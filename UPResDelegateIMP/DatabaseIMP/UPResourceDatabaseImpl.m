//
//  UPResourceDatabaseImpl.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/14.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceDatabaseImpl.h"
#import "UPResourceInfo.h"
#import "UPResourceSQLiteOpenHelper.h"
#import "UPResourceSQLiteOpenHelper+SQL.h"
#import <UPLog/UPLog.h>
#import "UPResCommonFunctions.h"
#import "UPResourceCondition.h"
#import "UPResourceHelper.h"

NSString *const UPRES_DATABASE_FILE_NAME = @"uplus-resource-database.db";
NSString *const UPRES_TABLE_NAME = @"ur_resources";
NSString *const UPRES_QUERY_CONDITION = @"ur_query_condition";

@interface UPResourceDatabaseImpl ()
@property (nonatomic, strong) UPResourceSQLiteOpenHelper *sqliteHelper;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@end

@implementation UPResourceDatabaseImpl

#pragma mark UPDatabaseDelegate
- (BOOL)insertResourceQuery:(UPResourceQuery *)query
{
    if (![query isKindOfClass:[UPResourceQuery class]]) {
        return NO;
    }
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION insertResourceQuery:query];
    return [self.sqliteHelper executeSQL:sql];
}

- (BOOL)deleteResourceQuery:(UPResourceQuery *)query
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION deleteResourceQuery:query];
    return [self.sqliteHelper executeSQL:sql];
}
- (BOOL)updateResourceQuery:(UPResourceQuery *)query
{
    if (![query isKindOfClass:[UPResourceQuery class]]) {
        return NO;
    }
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION updateResourceQuery:query];
    return [self.sqliteHelper executeSQL:sql];
}

- (UPResourceQuery *)searchResourceQuery:(NSString *)fromFucn appVersion:(NSString *)appVersion conditionStr:(NSString *)conditionStr
{
    NSArray<UPResourceQuery *> *queryList = [self getResourceQueryBy:appVersion fromFunc:fromFucn conditonStr:conditionStr];
    return queryList.firstObject;
}

- (BOOL)insertResourceInfo:(UPResourceInfo *)info
{
    if (![info isKindOfClass:[UPResourceInfo class]]) {
        return NO;
    }
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME insertInfo:info];
    return [self.sqliteHelper executeSQL:sql];
}

- (NSArray<UPResourceInfo *> *)searchResourceList:(UPResourceQuery *)query
{
    return [self queryResourceInfoListByQuery:query];
}

- (NSArray<UPResourceInfo *> *)searchResourceList:(UPResourceType)type name:(NSString *__nullable)name
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME queryByName:name type:type excludeStatusOff:YES];
    NSArray<UPResourceInfo *> *infoArray = [self.sqliteHelper exexuteQuerySQL:sql];
    return infoArray;
}

- (nullable UPResourceInfo *)searchResourceInfo:(UPResourceType)type name:(NSString *)name version:(NSString *)version
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME queryByName:name type:type version:version excludeStatusOff:YES];
    NSArray<UPResourceInfo *> *infoArray = [self.sqliteHelper exexuteQuerySQL:sql];
    if (UPRes_isEmptyArray(infoArray)) {
        return nil;
    }
    return [infoArray firstObject];
}

- (BOOL)updateResourceInfo:(UPResourceInfo *)info
{
    if (![info isKindOfClass:[UPResourceInfo class]]) {
        return NO;
    }
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME updateResourceInfo:info];
    return [self.sqliteHelper executeSQL:sql];
}

- (BOOL)deleteResourceInfo:(UPResourceInfo *)info
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME deleteResourceInfo:info];
    return [self.sqliteHelper executeSQL:sql];
}

- (BOOL)emptyAllData
{
    BOOL bResult = [self.sqliteHelper deleteTable:UPRES_TABLE_NAME];
    BOOL bQueryResult = [self.sqliteHelper deleteTable:UPRES_QUERY_CONDITION];
    return bResult && bQueryResult;
}


- (BOOL)insertRelationBetweenQuery:(UPResourceQuery *)query andResourceInfo:(UPResourceInfo *)info
{

    if (!query) {
        UPLogInfo(@"UPResource", @"%s[%d]查询条件不能为空", __PRETTY_FUNCTION__, __LINE__);
        return NO;
    }
    if ([info isKindOfClass:[UPResourceInfo class]]) {
        query.correlationId = [NSString stringWithFormat:@"%ld", info.itemId];
    }
    return [self insertResourceQuery:query];
}

- (BOOL)deleteRelation:(UPResourceQuery *)query
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION deleteResourceQueryAndRelation:query];
    return [self.sqliteHelper executeSQL:sql];
}
- (NSArray<UPResourceQuery *> *)searchResourceQuery:(NSArray<UPResourceInfo *> *)infoList
{
    __block NSString *string = @"";

    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      string = [NSString stringWithFormat:@"%@,%ld", string, obj.itemId];
    }];
    string = string.length ? [string substringFromIndex:1] : string;
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION queryResourceQueryListByItemIds:string];

    NSArray<UPResourceQuery *> *infoArray = [self.sqliteHelper exexuteQueryConditionSQL:sql];

    return infoArray;
}
- (BOOL)deleteQuerys:(NSArray<UPResourceQuery *> *)queryList
{
    if (queryList.count == 0) {
        return YES;
    }
    __block NSString *string = @"";
    [queryList enumerateObjectsUsingBlock:^(UPResourceQuery *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      string = [NSString stringWithFormat:@"%@,%ld", string, obj.itemId];
    }];
    string = string.length ? [string substringFromIndex:1] : string;
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION deleteQueryListByItemIds:string];
    return [self.sqliteHelper executeSQL:sql];
}
- (BOOL)deleteResourceInfos:(NSArray<UPResourceInfo *> *)infoList
{
    if (infoList.count == 0) {
        return YES;
    }
    __block NSString *string = @"";
    [infoList enumerateObjectsUsingBlock:^(UPResourceInfo *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      string = [NSString stringWithFormat:@"%@,%ld", string, obj.itemId];
    }];
    string = string.length ? [string substringFromIndex:1] : string;
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME deleteResListByItemIds:string];
    return [self.sqliteHelper executeSQL:sql];
}

- (NSArray<UPResourceQuery *> *)searchAllQuerys
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION queryByName:nil type:UPResourceTypeAll excludeStatusOff:NO];
    return [self.sqliteHelper exexuteQueryConditionSQL:sql];
}

#pragma mark public Method
- (instancetype)initByCacheFolderPath:(NSString *)path fileDelegate:(id<UPFileDelegate>)fileDelegate testModeOn:(BOOL)testModeOn timeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    if ([UPResourceHelper isBlank:path] ||
        ![UPResourceHelper isNonNullObject:fileDelegate conformToProtocol:@protocol(UPFileDelegate)]) {
        UPLogError(@"UPResource", @"%s[%d]UPDatabaseDelegate实现对象初始化失败！error:输入参数为空或不符合协议要求！", __PRETTY_FUNCTION__, __LINE__);
        return nil;
    }
    if (self = [super init]) {
        _fileDelegate = fileDelegate;
        NSString *dbFilePath = [self dbFilePathWithName:UPRES_DATABASE_FILE_NAME inFolder:path];
        _sqliteHelper = [[UPResourceSQLiteOpenHelper alloc] initSQLiteOpenHelperWithDatabaseFilePath:dbFilePath fileDelegate:fileDelegate timeDelegate:timeDelegate];
        [_sqliteHelper createTableIfNotExistsWithName:UPRES_TABLE_NAME testModeOn:testModeOn];
        //创建资源查询条件表
        [_sqliteHelper createTableIfNotExistsWithName:UPRES_QUERY_CONDITION testModeOn:testModeOn];
    }

    return self;
}

#pragma mark - UPResourceReporterSetter
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate
{
    [_sqliteHelper setReporterDelegate:reporterDelegate];
}

#pragma mark private Methods

- (NSString *)dbFilePathWithName:(NSString *)dbFileName inFolder:(NSString *)folderPath
{
    id<UPFileDelegate> fileDelegate = self.fileDelegate;
    BOOL bExists = [fileDelegate exists:folderPath];
    BOOL bIsDirectory = [fileDelegate isDirectory:folderPath];
    if (bExists && !bIsDirectory) {
        [fileDelegate deletePath:folderPath];
    }
    else if (!bExists) {
        [fileDelegate mkdirs:folderPath];
    }
    NSString *dbFilePath = [folderPath stringByAppendingPathComponent:dbFileName];
    return dbFilePath;
}
- (NSArray<UPResourceInfo *> *)queryResourceInfoListByQuery:(UPResourceQuery *)query
{

    NSString *itemIds = [self.sqliteHelper executeSQLWithConditionStr:query.conditionStr fromFunc:query.fromFunc appVersion:query.appVersion];

    if (UPRes_isEmptyString(itemIds)) {

        return nil;
    }

    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME queryResourceInfoByItemIds:itemIds excludeStatusOff:YES];

    NSArray<UPResourceInfo *> *infoArray = [self.sqliteHelper exexuteQuerySQL:sql];

    return infoArray;
}

- (NSArray<UPResourceQuery *> *)getResourceQueryBy:(NSString *)appVersion fromFunc:(NSString *)fromFunc conditonStr:(NSString *)conditionStr
{

    NSString *sql = [self.sqliteHelper SQL_table:UPRES_QUERY_CONDITION queryDeviceConditionByStr:conditionStr appVersion:appVersion fromFunc:fromFunc];

    NSArray<UPResourceQuery *> *infoArray = [self.sqliteHelper exexuteQueryConditionSQL:sql];

    return infoArray;
}

- (NSArray<UPResourceInfo *> *)searchResourceInfoByStatus:(UPResourceStatus)status name:(NSString *)name type:(UPResourceType)type version:(NSString *)version
{
    NSString *sql = [self.sqliteHelper SQL_table:UPRES_TABLE_NAME queryResourceInfoByStatus:status name:name type:type version:version];
    NSArray<UPResourceInfo *> *infoArray = [self.sqliteHelper exexuteQuerySQL:sql];
    return infoArray;
}


@end

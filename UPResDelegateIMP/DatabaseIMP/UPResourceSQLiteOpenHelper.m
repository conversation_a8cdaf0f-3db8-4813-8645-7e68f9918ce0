//
//  UPResourceSQLiteOpenHelper.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/18.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceSQLiteOpenHelper.h"
#import "UPResourceSQLiteOpenHelper+SQL.h"
#import "UPResourceInfo.h"
#import <UPLog/UPLog.h>
#import "UPResCommonFunctions.h"
#import "UPResourceQuery.h"
#import <fmdb/FMDB.h>

// 数据库版本。当此版本发生变化时，会删表重建。
NSInteger const K_UPRESOURCETABLE_Version = 7;
@interface UPResourceSQLiteOpenHelper ()
@property (nonatomic, strong) FMDatabaseQueue *databaseQueue;
@property (nonatomic, copy) NSString *dbFilePath;
@property (nonatomic, strong) id<UPFileDelegate> fileDelegate;
@property (nonatomic, strong) id<UPTimeDelegate> timeDelegate;
@property (nonatomic, strong) id<UPResourceReporter> reporterDelegate;

- (FMDatabaseQueue *)initializeDatabaseQueueWithPath:(NSString *)dbFilePath fileDelegate:(id<UPFileDelegate>)fileDelegate;
@end

@implementation UPResourceSQLiteOpenHelper
#pragma mark - Non-Public Methods

- (FMDatabaseQueue *)initializeDatabaseQueueWithPath:(NSString *)dbFilePath fileDelegate:(id<UPFileDelegate>)fileDelegate
{
    NSString *folderPath = [dbFilePath stringByDeletingLastPathComponent];
    BOOL bExists = [fileDelegate exists:folderPath];
    BOOL bIsDirectory = [fileDelegate isDirectory:folderPath];

    if (bExists && !bIsDirectory) {
        UPLogError(@"UPResource", @"%s[%d]数据库存储文件夹创建失败！该目录已存在同名文件:%@", __PRETTY_FUNCTION__, __LINE__, folderPath);
    }
    else if (!bExists) {
        BOOL mkdirs = [fileDelegate mkdirs:folderPath];
        if (mkdirs == NO) {

            return nil;
        }
    }

    return [FMDatabaseQueue databaseQueueWithPath:dbFilePath];
}

#pragma mark - Override Methods
- (instancetype)initSQLiteOpenHelperWithDatabaseFilePath:(NSString *)dbFilePath fileDelegate:(id<UPFileDelegate>)fileDelegate timeDelegate:(id<UPTimeDelegate>)timeDelegate
{
    if (self = [super init]) {
        FMDatabaseQueue *queue = [self initializeDatabaseQueueWithPath:dbFilePath fileDelegate:fileDelegate];
        _databaseQueue = queue;
        _dbFilePath = dbFilePath;
        _fileDelegate = fileDelegate;
        _timeDelegate = timeDelegate;
    }
    return self;
}

#pragma mark - getter, setter
- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate
{
    _reporterDelegate = reporterDelegate;
}

- (BOOL)createTableIfNotExistsWithName:(NSString *)tableName testModeOn:(BOOL)testModeOn
{
    if (UPRes_isEmptyString(tableName)) {
        return NO;
    }
    __block BOOL result = YES;
    NSString *versionKey = [NSString stringWithFormat:@"%d-%@", testModeOn, tableName];
    NSInteger version = [[NSUserDefaults standardUserDefaults] integerForKey:versionKey];
    if (version <= 4) {
        // 覆盖安装App首次启动，本地数据库版本小于 5 走旧逻辑删除表重新创建,将本地数据库版本升至 5
        // 兼容数据库版本从 1-4 直接升级至版本 5
        // 当前版本必定比 5 大，此处无需判断新旧版本是否相同，直接引用旧逻辑删表
        if (![self dropTableIfExistsWithName:tableName]) {
            return NO;
        }
    }
    // 版本 5 之后，数据库升级不删除表仅添加字段
    result = [self createAndUpgradeTableWithName:tableName oldVersion:version];
    if (!result) {
        UPLogError(@"UPResource", @"%s[%d]表信息创建失败！", __PRETTY_FUNCTION__, __LINE__);
    }
    else {
        [[NSUserDefaults standardUserDefaults] setInteger:K_UPRESOURCETABLE_Version forKey:versionKey];
    }
    return result;
}

- (BOOL)createAndUpgradeTableWithName:(NSString *)tableName oldVersion:(NSInteger)version
{
    __block BOOL result = YES;
    if ([tableName isEqualToString:@"ur_query_condition"]) {
        // 创建条件表
        NSString *tableCreationSQL = [self SQL_createQueryConditionTableWithName:tableName];
        [self.databaseQueue inDatabase:^(FMDatabase *_Nonnull db) {
          result = [db executeUpdate:tableCreationSQL];
          if (!result) {
              UPLogError(@"UPResource", @"%s[%d]数据库操作失败!Error:%@", __PRETTY_FUNCTION__, __LINE__, db.lastError);
          }
        }];
        return result;
    }
    else {
        // 创建资源信息表
        NSString *tableCreationSQL = [self SQL_createTableWithName:tableName];
        NSString *indexCreationSQL = [self SQL_createIndexOfTable:tableName];
        [self.databaseQueue inDatabase:^(FMDatabase *_Nonnull db) {
          result = [db executeUpdate:tableCreationSQL];
          if (result) {
              result = [db executeUpdate:indexCreationSQL];
          }
          if (!result) {
              UPLogError(@"UPResource", @"%s[%d]数据库操作失败!Error:%@", __PRETTY_FUNCTION__, __LINE__, db.lastError);
          }
        }];
        if (!result) {
            return result;
        }
        return [self addColumnIfNotExists:@{
            @"r_forceUpgrade" : @"INTEGER(1) NOT NULL DEFAULT 0",
            @"r_resRules" : @"TEXT DEFAULT ''",
            @"r_resStatus" : @"INTEGER(1) NOT NULL DEFAULT 0",
            @"r_remoteUrl" : @"TEXT"
        }
                                  inTable:tableName];
    }
}

- (BOOL)addColumnIfNotExists:(NSDictionary<NSString *, NSString *> *)columns inTable:(NSString *)tableName
{
    __block BOOL result = YES;
    [columns enumerateKeysAndObjectsUsingBlock:^(NSString *_Nonnull key, NSString *_Nonnull obj, BOOL *_Nonnull stop) {
      if ([self columnExists:key inTableWithName:tableName]) {
          UPLogInfo(@"UPResource", @"%s[%d]数据库已存在字段 %@", __PRETTY_FUNCTION__, __LINE__, key);
          return;
      }
      NSString *sql = [self SQL_addColumn:key type:obj inTable:tableName];
      [self.databaseQueue inDatabase:^(FMDatabase *_Nonnull db) {
        result = [db executeUpdate:sql];
        if (!result) {
            UPLogError(@"UPResource", @"%s[%d]SQL语句(%@)执行失败!Error:%@", __PRETTY_FUNCTION__, __LINE__, sql, db.lastError);
        }
      }];
      *stop = !result;
    }];
    return result;
}

- (BOOL)columnExists:(NSString *)columnName inTableWithName:(NSString *)tableName
{
    __block BOOL exist = NO;
    [self.databaseQueue inDatabase:^(FMDatabase *_Nonnull db) {
      exist = [db columnExists:columnName inTableWithName:tableName];
    }];
    return exist;
}

- (BOOL)dropTableIfExistsWithName:(NSString *)tableName
{
    if (UPRes_isEmptyString(tableName)) {
        return NO;
    }

    __block BOOL result = NO;
    NSString *tableCreationSQL = [self SQL_dropTableWithName:tableName];
    NSString *indexCreationSQL = [self SQL_deleteIndexOfTable:tableName];
    void (^databaseExecutionBlock)(FMDatabase *db) = ^(FMDatabase *db) {
      if ((result = [db executeUpdate:tableCreationSQL])) {

          if (!UPRes_isEqualString(tableName, @"ur_query_condition")) {

              result = [db executeUpdate:indexCreationSQL];
          }
      }
      if (!result) {
          UPLogError(@"UPResource", @"%s[%d]数据库操作失败!Error:%@", __PRETTY_FUNCTION__, __LINE__, db.lastError);
      }
    };
    [self.databaseQueue inDatabase:databaseExecutionBlock];
    if (!result) {
        UPLogError(@"UPResource", @"%s[%d]表删除失败！", __PRETTY_FUNCTION__, __LINE__);
    }
    return result;
}
- (BOOL)deleteTable:(NSString *)tableName
{

    if (UPRes_isEmptyString(tableName)) {
        return NO;
    }
    __block BOOL result = NO;
    NSString *sql = [self SQL_deleteTableWithName:tableName];

    [self.databaseQueue inDatabase:^(FMDatabase *_Nonnull db) {
      result = [db executeUpdate:sql];
    }];
    if (!result) {
        UPLogError(@"UPResource", @"%s[%d]清空表失败！", __PRETTY_FUNCTION__, __LINE__);
    }
    return result;
}

- (BOOL)executeSQL:(NSString *)sql
{
    __block BOOL result = NO;
    void (^databaseExecutionBlock)(FMDatabase *db) = ^(FMDatabase *db) {
      result = [db executeUpdate:sql];
      if (!result) {
          UPLogError(@"UPResource", @"%s[%d]数据库操作失败!Error:%@", __PRETTY_FUNCTION__, __LINE__, db.lastError);
      }
    };
    [self.databaseQueue inDatabase:databaseExecutionBlock];
    if (!result) {
        UPLogError(@"UPResource", @"%s[%d]SQL执行失败:%@", __PRETTY_FUNCTION__, __LINE__, sql);
    }
    return result;
}

- (NSArray<UPResourceInfo *> *)exexuteQuerySQL:(NSString *)sql
{
    __block NSMutableArray *infoArray = [NSMutableArray array];
    void (^databaseExecutionBlock)(FMDatabase *db) = ^(FMDatabase *db) {
      FMResultSet *set = [db executeQuery:sql];
      if (!set) {
          UPLogError(@"UPResource", @"%s[%d]SQL execute query fail:%@", __PRETTY_FUNCTION__, __LINE__, sql);
      }
      while ([set next]) {
          UPResourceInfo *info = [self SQL_queryInfoFromFMResultSet:set];
          info.fileDelegate = self.fileDelegate;
          info.timeDelegate = self.timeDelegate;
          info.reporterDelegate = self.reporterDelegate;
          if (info) {
              [infoArray addObject:info];
          }
      }
    };
    [self.databaseQueue inDatabase:databaseExecutionBlock];
    return infoArray;
}
- (NSArray<UPResourceQuery *> *)exexuteQueryConditionSQL:(NSString *)sql
{

    __block NSMutableArray *infoArray = [NSMutableArray array];
    void (^databaseExecutionBlock)(FMDatabase *db) = ^(FMDatabase *db) {
      FMResultSet *set = [db executeQuery:sql];
      if (!set) {
          UPLogError(@"UPResource", @"%s[%d]SQL execute query fail:%@", __PRETTY_FUNCTION__, __LINE__, sql);
      }
      while ([set next]) {
          UPResourceQuery *info = [self SQL_queryConditionInfoFromFMResultSet:set];
          if (info) {
              [infoArray addObject:info];
          }
      }
    };
    [self.databaseQueue inDatabase:databaseExecutionBlock];
    return infoArray;
}

- (NSString *)executeSQLWithConditionStr:(NSString *)conditionStr fromFunc:(NSString *)fromFunc appVersion:(NSString *)appVersion
{

    NSString *sqlFormat = @"SELECT r_itemId FROM %@ WHERE r_condition ='%@' and r_fromfunc = '%@' and r_appVersion = '%@'";
    NSString *sql = [NSString stringWithFormat:sqlFormat, @"ur_query_condition", conditionStr, fromFunc, appVersion];
    __block NSString *string = @"";

    void (^databaseExecutionBlock)(FMDatabase *db) = ^(FMDatabase *db) {
      FMResultSet *set = [db executeQuery:sql];
      if (!set) {
          UPLogError(@"UPResource", @"%s[%d]SQL execute query fail:%@", __PRETTY_FUNCTION__, __LINE__, sql);
      }
      while ([set next]) {

          string = [NSString stringWithFormat:@"%@,%@", string, [set stringForColumn:@"r_itemId"]];
      }
    };
    [self.databaseQueue inDatabase:databaseExecutionBlock];

    return string.length ? [string substringFromIndex:1] : string;
}
@end

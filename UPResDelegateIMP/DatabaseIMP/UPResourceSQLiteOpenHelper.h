//
//  UPResourceSQLiteOpenHelper.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/18.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPFileDelegate.h"
#import "UPTimeDelegate.h"
#import "UPResourceReporter.h"

@class UPResourceInfo, UPResourceQuery;

// 异步操作完成回调
typedef void(^UPDatabaseCompletionBlock)(BOOL success, NSError * _Nullable error);
typedef void(^UPDatabaseQueryCompletionBlock)(NSArray * _Nullable results, NSError * _Nullable error);
typedef void(^UPDatabaseStringCompletionBlock)(NSString * _Nullable result, NSError * _Nullable error);

@interface UPResourceSQLiteOpenHelper : NSObject

- (instancetype)initSQLiteOpenHelperWithDatabaseFilePath:(NSString *)dbFilePath fileDelegate:(id<UPFileDelegate>)fileDelegate timeDelegate:(id<UPTimeDelegate>)timeDelegate;

// 原有同步方法保持兼容性
- (BOOL)createTableIfNotExistsWithName:(NSString *)tableName testModeOn:(BOOL)testModeOn;
- (BOOL)dropTableIfExistsWithName:(NSString *)tableName;
- (BOOL)deleteTable:(NSString *)tableName;
- (BOOL)executeSQL:(NSString *)sql;
- (NSArray<UPResourceInfo *> *)exexuteQuerySQL:(NSString *)sql;
- (NSArray<UPResourceQuery *> *)exexuteQueryConditionSQL:(NSString *)sql;
- (NSString *)executeSQLWithConditionStr:(NSString *)conditionStr fromFunc:(NSString *)fromFunc appVersion:(NSString *)appVersion;

// 新增异步方法
- (void)executeSQLAsync:(NSString *)sql completion:(UPDatabaseCompletionBlock)completion;
- (void)executeQuerySQLAsync:(NSString *)sql completion:(UPDatabaseQueryCompletionBlock)completion;
- (void)executeQueryConditionSQLAsync:(NSString *)sql completion:(UPDatabaseQueryCompletionBlock)completion;
- (void)executeSQLWithConditionStrAsync:(NSString *)conditionStr fromFunc:(NSString *)fromFunc appVersion:(NSString *)appVersion completion:(UPDatabaseStringCompletionBlock)completion;

// 批量操作方法
- (BOOL)executeBatchSQL:(NSArray<NSString *> *)sqlArray;
- (void)executeBatchSQLAsync:(NSArray<NSString *> *)sqlArray completion:(UPDatabaseCompletionBlock)completion;

// 事务操作方法
- (BOOL)executeInTransaction:(BOOL(^)(FMDatabase *db, BOOL *rollback))block;
- (void)executeInTransactionAsync:(BOOL(^)(FMDatabase *db, BOOL *rollback))block completion:(UPDatabaseCompletionBlock)completion;

- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate;

@end

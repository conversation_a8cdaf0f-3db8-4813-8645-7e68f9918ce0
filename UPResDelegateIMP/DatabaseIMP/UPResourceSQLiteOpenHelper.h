//
//  UPResourceSQLiteOpenHelper.h
//  UPRes
//
//  Created by <PERSON> on 2018/9/18.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "UPFileDelegate.h"
#import "UPTimeDelegate.h"
#import "UPResourceReporter.h"

@class UPResourceInfo, UPResourceQuery;
@interface UPResourceSQLiteOpenHelper : NSObject
- (instancetype)initSQLiteOpenHelperWithDatabaseFilePath:(NSString *)dbFilePath fileDelegate:(id<UPFileDelegate>)fileDelegate timeDelegate:(id<UPTimeDelegate>)timeDelegate;
- (BOOL)createTableIfNotExistsWithName:(NSString *)tableName testModeOn:(BOOL)testModeOn;
- (BOOL)dropTableIfExistsWithName:(NSString *)tableName;
- (BOOL)deleteTable:(NSString *)tableName;
- (BOOL)executeSQL:(NSString *)sql;
- (NSArray<UPResourceInfo *> *)exexuteQuerySQL:(NSString *)sql;

/**
 执行sql返回查询条件对象数组

 @param sql sql语句
 @return 查询条件对象数组
 */
- (NSArray<UPResourceQuery *> *)exexuteQueryConditionSQL:(NSString *)sql;

- (NSString *)executeSQLWithConditionStr:(NSString *)conditionStr fromFunc:(NSString *)fromFunc appVersion:(NSString *)appVersion;

- (void)setReporterDelegate:(id<UPResourceReporter>)reporterDelegate;
@end

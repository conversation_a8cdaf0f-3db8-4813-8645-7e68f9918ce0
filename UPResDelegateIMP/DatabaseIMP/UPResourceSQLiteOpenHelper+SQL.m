//
//  UPResourceSQLiteOpenHelper+SQL.m
//  UPRes
//
//  Created by <PERSON> on 2018/9/19.
//  Copyright © 2018 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "UPResourceSQLiteOpenHelper+SQL.h"
#import "UPResourceInfo.h"
#import "UPResCommonFunctions.h"
#import "NSString+Paths.h"
#import "UPResourceQuery.h"
NSString *const UPRES_ITEM_ID = @"r_id";
NSString *const UPRES_ITEM_CREATE_TIME = @"r_create_time";
NSString *const UPRES_ITEM_UPDATE_TIME = @"r_update_time";
NSString *const UPRES_ITEM_ACTIVE = @"r_active";
NSString *const UPRES_ITEM_PATH = @"r_path";

NSString *const UPRES_ITEM_NAME = @"r_name";
NSString *const UPRES_ITEM_TYPE = @"r_type";
NSString *const UPRES_ITEM_VERSION = @"r_version";
NSString *const UPRES_ITEM_SERVER_LATEST = @"r_server_latest";
NSString *const UPRES_ITEM_LINK = @"r_link";
NSString *const UPRES_ITEM_HASH = @"r_hash";

NSString *const UPRES_ITEM_MODEL = @"rd_model";
NSString *const UPRES_ITEM_TYPE_ID = @"rd_type_id";
NSString *const UPRES_ITEM_PROD_NO = @"rd_prod_no";
NSString *const UPRES_ITEM_TYPE_NO = @"rd_type_no";
NSString *const UPRES_ITEM_PRESET = @"r_preset";
NSString *const UPRES_ITEM_INDEX_PATH = @"r_indexPath";
NSString *const UPRES_ITEM_RESOURCE_TYPE = @"r_resourceType";
NSString *const UPRES_ITEM_HIDE_STATUSBAR = @"r_hideStatusBar";
NSString *const UPRES_ITEM_FORCE_UPGRADE = @"r_forceUpgrade";
NSString *const UPRES_ITEM_RESOURCE_RULES = @"r_resRules";
NSString *const UPRES_ITEM_RESOURCE_STATUS = @"r_resStatus";
NSString *const UPRES_ITEM_RESOURCE_REMOTE_URL = @"r_remoteUrl";

//为查询条件表增加的特定字段
NSString *const UPRES_ITEM_CONDITION = @"r_condition";
NSString *const UPRES_ITEM_ITEMID = @"r_itemId";
NSString *const UPRES_ITEM_APPVERSION = @"r_appVersion";
NSString *const UPRES_ITEM_FROMFUNC = @"r_fromfunc";

@implementation UPResourceSQLiteOpenHelper (SQL)
- (UPResourceInfo *)SQL_queryInfoFromFMResultSet:(FMResultSet *)set
{
    UPResourceInfo *info = [[UPResourceInfo alloc] init];
    info.itemId = [set longForColumn:UPRES_ITEM_ID];
    info.createTime = [set doubleForColumn:UPRES_ITEM_CREATE_TIME];
    info.updateTime = [set doubleForColumn:UPRES_ITEM_UPDATE_TIME];
    info.active = [set boolForColumn:UPRES_ITEM_ACTIVE];
    info.preset = [set boolForColumn:UPRES_ITEM_PRESET];
    NSString *path = [set stringForColumn:UPRES_ITEM_PATH];
    if (!UPRes_isEmptyString(path)) {
        path = [path UPRes_fullAbsolutePath];
    }
    info.path = path;
    info.name = [set stringForColumn:UPRES_ITEM_NAME];
    info.type = [UPResourceInfo typeValueOfString:[set stringForColumn:UPRES_ITEM_TYPE]];
    info.version = [set stringForColumn:UPRES_ITEM_VERSION];
    info.link = [set stringForColumn:UPRES_ITEM_LINK];
    info.hashStr = [set stringForColumn:UPRES_ITEM_HASH];
    info.model = [set stringForColumn:UPRES_ITEM_MODEL];
    info.typeId = [set stringForColumn:UPRES_ITEM_TYPE_ID];
    info.prodNo = [set stringForColumn:UPRES_ITEM_PROD_NO];
    info.deviceTypeIndex = [set stringForColumn:UPRES_ITEM_TYPE_NO];
    info.indexPath = [set stringForColumn:UPRES_ITEM_INDEX_PATH];
    info.isServerLatest = [set boolForColumn:UPRES_ITEM_SERVER_LATEST];
    info.resourceType = [set stringForColumn:UPRES_ITEM_RESOURCE_TYPE];
    info.hideStatusBar = [set boolForColumn:UPRES_ITEM_HIDE_STATUSBAR];
    info.forceUpgrade = [set boolForColumn:UPRES_ITEM_FORCE_UPGRADE];
    info.remoteUrl = [set stringForColumn:UPRES_ITEM_RESOURCE_REMOTE_URL];
    NSString *resRules = [set stringForColumn:UPRES_ITEM_RESOURCE_RULES];
    info.resRules = UPRes_jsonObjectValueOfJsonString(resRules, nil);
    info.resStatus = [set longForColumn:UPRES_ITEM_RESOURCE_STATUS];
    return info;
}

- (UPResourceQuery *)SQL_queryConditionInfoFromFMResultSet:(FMResultSet *)set
{
    UPResourceQuery *info = [[UPResourceQuery alloc] init];
    info.itemId = [set longForColumn:UPRES_ITEM_ID];
    info.createTime = [set doubleForColumn:UPRES_ITEM_CREATE_TIME];
    info.updateTime = [set doubleForColumn:UPRES_ITEM_UPDATE_TIME];
    info.conditionStr = [set stringForColumn:UPRES_ITEM_CONDITION];
    info.correlationId = [set stringForColumn:UPRES_ITEM_ITEMID];
    info.appVersion = [set stringForColumn:UPRES_ITEM_APPVERSION];
    info.fromFunc = [set stringForColumn:UPRES_ITEM_FROMFUNC];
    return info;
}

NSString *const UPRES_SQL_CMD_FORMAT_CREATE_TABLE =
    @"CREATE TABLE IF NOT EXISTS %@ \
(%@ INTEGER PRIMARY KEY AUTOINCREMENT,\
 %@ INTEGER NOT NULL,\
 %@ INTEGER NOT NULL,\
 %@ INTEGER(1) NOT NULL DEFAULT 0,\
 %@ TEXT,\
 %@ TEXT(256) NOT NULL,\
 %@ TEXT(64) NOT NULL,\
 %@ TEXT(64) NOT NULL,\
 %@ INTEGER(1) NOT NULL DEFAULT 0,\
 %@ TEXT,\
 %@ TEXT,\
 %@ TEXT,\
 %@ TEXT,\
 %@ TEXT,\
 %@ TEXT,\
 %@ INTEGER(1) NOT NULL DEFAULT 0,\
 %@ TEXT,\
 %@ TEXT,\
 %@ INTEGER(1) NOT NULL DEFAULT 0);";

- (NSString *)SQL_createTableWithName:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_CREATE_TABLE, tableName,
                                      UPRES_ITEM_ID,
                                      UPRES_ITEM_CREATE_TIME,
                                      UPRES_ITEM_UPDATE_TIME,
                                      UPRES_ITEM_ACTIVE,
                                      UPRES_ITEM_PATH,
                                      UPRES_ITEM_NAME,
                                      UPRES_ITEM_TYPE,
                                      UPRES_ITEM_VERSION,
                                      UPRES_ITEM_SERVER_LATEST,
                                      UPRES_ITEM_LINK,
                                      UPRES_ITEM_HASH,
                                      UPRES_ITEM_MODEL,
                                      UPRES_ITEM_TYPE_ID,
                                      UPRES_ITEM_PROD_NO,
                                      UPRES_ITEM_TYPE_NO,
                                      UPRES_ITEM_PRESET,
                                      UPRES_ITEM_INDEX_PATH,
                                      UPRES_ITEM_RESOURCE_TYPE,
                                      UPRES_ITEM_HIDE_STATUSBAR];
}

NSString *const UPRES_SQL_CMD_FORMAT_CREATE_TABLE_INDEX = @"CREATE UNIQUE INDEX IF NOT EXISTS %@_idx ON %@ (%@, %@, %@)";

- (NSString *)SQL_createIndexOfTable:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_CREATE_TABLE_INDEX, tableName, tableName,
                                      UPRES_ITEM_NAME, UPRES_ITEM_TYPE, UPRES_ITEM_VERSION];
}

NSString *const UPRES_SQL_CMD_FORMAT_ADD_COLUMN = @"ALTER TABLE %@ ADD %@ %@";
- (NSString *)SQL_addColumn:(NSString *)column type:(NSString *)type inTable:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_ADD_COLUMN, tableName, column, type];
}

NSString *const UPRES_SQL_CMD_FORMAT_DROP_TABLE = @"DROP TABLE IF EXISTS %@";

- (NSString *)SQL_dropTableWithName:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_DROP_TABLE, tableName];
}
NSString *const UPRES_SQL_CMD_FORMAT_DELETE_TABLE = @"DELETE FROM %@";
- (NSString *)SQL_deleteTableWithName:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_DELETE_TABLE, tableName];
}

NSString *const UPRES_SQL_CMD_FORMAT_DELETE_TABLE_INDEX = @"DROP INDEX IF EXISTS %@_idx";
- (NSString *)SQL_deleteIndexOfTable:(NSString *)tableName
{
    return [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_DELETE_TABLE_INDEX, tableName];
}

NSString *const UPRES_SQL_CMD_FORMAT_INSERT_ROW = @"INSERT INTO %@ (%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@,%@) VALUES (%ld,%ld,%ld,'%@','%@','%@','%@',%ld,'%@','%@','%@','%@','%@','%@',%ld,'%@','%@',%ld,%ld,'%@',%ld,'%@')";

- (NSString *)SQL_table:(NSString *)tableName insertInfo:(UPResourceInfo *)info
{
    NSString *sql = [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_INSERT_ROW, tableName,
                                               UPRES_ITEM_CREATE_TIME,
                                               UPRES_ITEM_UPDATE_TIME,
                                               UPRES_ITEM_ACTIVE,
                                               UPRES_ITEM_PATH,
                                               UPRES_ITEM_NAME,
                                               UPRES_ITEM_TYPE,
                                               UPRES_ITEM_VERSION,
                                               UPRES_ITEM_SERVER_LATEST,
                                               UPRES_ITEM_LINK,
                                               UPRES_ITEM_HASH,
                                               UPRES_ITEM_MODEL,
                                               UPRES_ITEM_TYPE_ID,
                                               UPRES_ITEM_PROD_NO,
                                               UPRES_ITEM_TYPE_NO,
                                               UPRES_ITEM_PRESET,
                                               UPRES_ITEM_INDEX_PATH,
                                               UPRES_ITEM_RESOURCE_TYPE,
                                               UPRES_ITEM_HIDE_STATUSBAR,
                                               UPRES_ITEM_FORCE_UPGRADE,
                                               UPRES_ITEM_RESOURCE_RULES,
                                               UPRES_ITEM_RESOURCE_STATUS,
                                               UPRES_ITEM_RESOURCE_REMOTE_URL,
                                               (long)info.createTime,
                                               (long)info.updateTime,
                                               (long)info.active,
                                               UPRes_validStringValue([info.path UPRes_relativePath]),
                                               UPRes_validStringValue(info.name),
                                               UPRes_validStringValue([UPResourceInfo stringValueOfResourceType:info.type]),
                                               UPRes_validStringValue(info.version),
                                               (long)info.isServerLatest,
                                               UPRes_validStringValue(info.link),
                                               UPRes_validStringValue(info.hashStr),
                                               UPRes_validStringValue(info.model),
                                               UPRes_validStringValue(info.typeId),
                                               UPRes_validStringValue(info.prodNo),
                                               UPRes_validStringValue(info.deviceTypeIndex),
                                               (long)info.preset,
                                               UPRes_validStringValue(info.indexPath),
                                               UPRes_validStringValue(info.resourceType),
                                               (long)info.hideStatusBar,
                                               (long)info.forceUpgrade,
                                               UPRes_validStringValue(UPRes_jsonStringValueOfJsonObject(info.resRules, nil)),
                                               (long)info.resStatus,
                                               UPRes_validStringValue(info.remoteUrl)];

    return sql;
}

NSString *const UPRES_SQL_CMD_FORMAT_DELETE_ROW = @"DELETE FROM %@ WHERE %@=%ld";
- (NSString *)SQL_table:(NSString *)tableName deleteResourceInfo:(UPResourceInfo *)info
{
    NSString *sql = [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_DELETE_ROW, tableName, UPRES_ITEM_ID, info.itemId];
    return sql;
}

NSString *const UPRES_SQL_CMD_FORMAT_UPDATE_ROW = @"UPDATE %@ SET \
%@=%ld, \
%@=%ld, \
%@=%ld, \
%@='%@', \
%@='%@', \
%@='%@', \
%@='%@', \
%@=%ld, \
%@='%@', \
%@='%@', \
%@='%@', \
%@='%@', \
%@='%@', \
%@='%@',\
%@='%ld',\
%@='%@',\
%@='%@',\
%@='%ld',\
%@='%ld',\
%@='%@', \
%@='%ld',\
%@='%@'\
WHERE %@=%ld";

- (NSString *)SQL_table:(NSString *)tableName updateResourceInfo:(UPResourceInfo *)info
{
    NSString *sql = [NSString stringWithFormat:UPRES_SQL_CMD_FORMAT_UPDATE_ROW, tableName,
                                               UPRES_ITEM_CREATE_TIME, (long)info.createTime,
                                               UPRES_ITEM_UPDATE_TIME, (long)info.updateTime,
                                               UPRES_ITEM_ACTIVE, (long)info.active,
                                               UPRES_ITEM_PATH, UPRes_validStringValue([info.path UPRes_relativePath]),
                                               UPRES_ITEM_NAME, UPRes_validStringValue(info.name),
                                               UPRES_ITEM_TYPE, UPRes_validStringValue([UPResourceInfo stringValueOfResourceType:info.type]),
                                               UPRES_ITEM_VERSION, UPRes_validStringValue(info.version),
                                               UPRES_ITEM_SERVER_LATEST, (long)info.isServerLatest,
                                               UPRES_ITEM_LINK, UPRes_validStringValue(info.link),
                                               UPRES_ITEM_HASH, UPRes_validStringValue(info.hashStr),
                                               UPRES_ITEM_MODEL, UPRes_validStringValue(info.model),
                                               UPRES_ITEM_TYPE_ID, UPRes_validStringValue(info.typeId),
                                               UPRES_ITEM_PROD_NO, UPRes_validStringValue(info.prodNo),
                                               UPRES_ITEM_TYPE_NO, UPRes_validStringValue(info.deviceTypeIndex),
                                               UPRES_ITEM_PRESET, (long)info.preset,
                                               UPRES_ITEM_INDEX_PATH, UPRes_validStringValue(info.indexPath),
                                               UPRES_ITEM_RESOURCE_TYPE, UPRes_validStringValue(info.resourceType),
                                               UPRES_ITEM_HIDE_STATUSBAR, (long)info.hideStatusBar,
                                               UPRES_ITEM_FORCE_UPGRADE, (long)info.forceUpgrade,
                                               UPRES_ITEM_RESOURCE_RULES, UPRes_validStringValue(UPRes_jsonStringValueOfJsonObject(info.resRules, nil)),
                                               UPRES_ITEM_RESOURCE_STATUS, info.resStatus,
                                               UPRES_ITEM_RESOURCE_REMOTE_URL,
                                               UPRes_validStringValue(info.remoteUrl),
                                               UPRES_ITEM_ID, (long)info.itemId];
    return sql;
}

- (NSString *)SQL_table:(NSString *)tableName queryByName:(NSString *)name type:(UPResourceType)type excludeStatusOff:(BOOL)excludeStatusOff
{
    NSString *sql = @"";
    NSString *typeStr = [UPResourceInfo stringValueOfResourceType:type];
    NSString *nameStr = UPRes_validStringValue(name);

    if (UPRes_isEmptyString(typeStr) && UPRes_isEmptyString(nameStr)) {
        sql = [NSString stringWithFormat:@"SELECT * FROM %@", tableName];
    }
    else if (UPRes_isEmptyString(typeStr)) {
        sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE %@='%@'", tableName, UPRES_ITEM_NAME, nameStr];
    }
    else if (UPRes_isEmptyString(nameStr)) {
        sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE %@='%@'", tableName, UPRES_ITEM_TYPE, typeStr];
    }
    else {
        sql = [NSString stringWithFormat:@"SELECT * FROM %@ WHERE %@='%@' AND  %@='%@'", tableName, UPRES_ITEM_NAME, nameStr, UPRES_ITEM_TYPE, typeStr];
    }

    if (excludeStatusOff) {
        if (UPRes_isEmptyString(typeStr) && UPRes_isEmptyString(nameStr)) {
            sql = [sql stringByAppendingFormat:@" WHERE %@!=%zd", UPRES_ITEM_RESOURCE_STATUS, UPResourceStatusOFF];
        }
        else {
            sql = [sql stringByAppendingFormat:@" AND %@!=%zd", UPRES_ITEM_RESOURCE_STATUS, UPResourceStatusOFF];
        }
    }
    return sql;
}

- (NSString *)SQL_table:(NSString *)tableName queryByName:(NSString *)name type:(UPResourceType)type version:(NSString *)version excludeStatusOff:(BOOL)excludeStatusOff
{
    NSString *sqlFormat = @"SELECT * FROM %@ WHERE %@ ='%@' AND %@ LIKE '%@' AND %@ ='%@'";
    NSString *typeStr = [UPResourceInfo stringValueOfResourceType:type];
    NSString *nameStr = UPRes_validStringValue(name);
    NSString *verStr = UPRes_validStringValue(version);
    if (UPRes_isEmptyString(typeStr)) {
        typeStr = @"%";
    }
    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_NAME, nameStr, UPRES_ITEM_TYPE, typeStr, UPRES_ITEM_VERSION, verStr];
    if (excludeStatusOff) {
        sql = [sql stringByAppendingFormat:@" AND %@!=%zd", UPRES_ITEM_RESOURCE_STATUS, UPResourceStatusOFF];
    }
    return sql;
}
- (NSString *)SQL_table:(NSString *)tableName queryDeviceConditionByStr:(NSString *)conditionStr appVersion:(NSString *)appVersion fromFunc:(NSString *)fromFunc
{

    NSString *sqlFormat = @"SELECT * FROM %@ WHERE %@='%@' AND %@='%@' AND %@='%@'";

    NSString *conStr = UPRes_validStringValue(conditionStr);
    NSString *appVersionStr = UPRes_validStringValue(appVersion);
    NSString *fromFuncStr = UPRes_validStringValue(fromFunc);
    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_CONDITION, conStr, UPRES_ITEM_APPVERSION, appVersionStr, UPRES_ITEM_FROMFUNC, fromFuncStr];

    return sql;
}

- (NSString *)SQL_table:(NSString *)tableName queryResourceInfoByItemIds:(NSString *)items excludeStatusOff:(BOOL)excludeStatusOff
{

    NSString *sqlFormat = @"SELECT * FROM %@ WHERE %@ IN (%@)";

    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_ID, items];

    if (excludeStatusOff) {
        sql = [sql stringByAppendingFormat:@" AND %@!=%zd", UPRES_ITEM_RESOURCE_STATUS, UPResourceStatusOFF];
    }
    return sql;
}
NSString *const UPRES_SQL_CREATE_QUERYCONDITION_TABLE =
    @"CREATE TABLE IF NOT EXISTS %@ \
(%@ INTEGER PRIMARY KEY AUTOINCREMENT,\
%@ INTEGER NOT NULL,\
%@ INTEGER NOT NULL,\
%@ TEXT,\
%@ TEXT,\
%@ TEXT,\
%@ TEXT,\
FOREIGN KEY (%@) REFERENCES ur_resources(%@) ON DELETE CASCADE);";
- (NSString *)SQL_createQueryConditionTableWithName:(NSString *)tableName
{

    return [NSString stringWithFormat:UPRES_SQL_CREATE_QUERYCONDITION_TABLE, tableName,
                                      UPRES_ITEM_ID,
                                      UPRES_ITEM_CREATE_TIME,
                                      UPRES_ITEM_UPDATE_TIME,
                                      UPRES_ITEM_CONDITION,
                                      UPRES_ITEM_ITEMID,
                                      UPRES_ITEM_APPVERSION,
                                      UPRES_ITEM_FROMFUNC,
                                      UPRES_ITEM_ITEMID,
                                      UPRES_ITEM_ID];
}
NSString *const UPRES_SQL_INSERT_QUERYCONDITION_ROW = @"INSERT INTO %@ (%@,%@,%@,%@,%@,%@) VALUES (%ld,%ld,'%@','%@','%@','%@')";
- (NSString *)SQL_table:(NSString *)tableName insertResourceQuery:(UPResourceQuery *)query
{

    NSString *sql = [NSString stringWithFormat:UPRES_SQL_INSERT_QUERYCONDITION_ROW, tableName,
                                               UPRES_ITEM_CREATE_TIME,
                                               UPRES_ITEM_UPDATE_TIME,
                                               UPRES_ITEM_CONDITION,
                                               UPRES_ITEM_ITEMID,
                                               UPRES_ITEM_APPVERSION,
                                               UPRES_ITEM_FROMFUNC,
                                               (long)query.createTime,
                                               (long)query.updateTime, UPRes_validStringValue(query.conditionStr), UPRes_validStringValue(query.correlationId),
                                               UPRes_validStringValue(query.appVersion),
                                               UPRes_validStringValue(query.fromFunc)];

    return sql;
}
NSString *const UPRES_SQL_DELETE_QUERYCONDITION = @"DELETE FROM %@ WHERE %@='%@' AND %@='%@' AND %@='%@' AND %@=''";
- (NSString *)SQL_table:(NSString *)tableName deleteResourceQuery:(UPResourceQuery *)query
{

    NSString *sql = [NSString stringWithFormat:UPRES_SQL_DELETE_QUERYCONDITION, tableName, UPRES_ITEM_FROMFUNC, query.fromFunc, UPRES_ITEM_APPVERSION, query.appVersion, UPRES_ITEM_CONDITION, query.conditionStr, UPRES_ITEM_ITEMID];
    return sql;
}
NSString *const UPRES_SQL_DELETE_QUERYCONDITION_AND_RELATION = @"DELETE FROM %@ WHERE %@='%@' AND %@='%@' AND %@='%@' AND %@!=''";
- (NSString *)SQL_table:(NSString *)tableName deleteResourceQueryAndRelation:(UPResourceQuery *)query
{
    NSString *sql = [NSString stringWithFormat:UPRES_SQL_DELETE_QUERYCONDITION_AND_RELATION, tableName, UPRES_ITEM_FROMFUNC, query.fromFunc, UPRES_ITEM_APPVERSION, query.appVersion, UPRES_ITEM_CONDITION, query.conditionStr, UPRES_ITEM_ITEMID];
    return sql;
}
NSString *const UPRES_SQL_UPDATE_QUERYCONDITION = @"UPDATE %@ SET \
%@=%ld, \
%@=%ld, \
%@='%@', \
%@='%@', \
%@='%@', \
%@='%@', \
WHERE %@=%ld";
- (NSString *)SQL_table:(NSString *)tableName updateResourceQuery:(UPResourceQuery *)query
{
    NSString *sql = [NSString stringWithFormat:UPRES_SQL_UPDATE_QUERYCONDITION, tableName, UPRES_ITEM_CREATE_TIME, (long)query.createTime, UPRES_ITEM_UPDATE_TIME, (long)query.updateTime, UPRES_ITEM_CONDITION, UPRes_validStringValue(query.conditionStr), UPRES_ITEM_ITEMID, UPRes_validStringValue(query.correlationId), UPRES_ITEM_APPVERSION, UPRes_validStringValue(query.appVersion), UPRES_ITEM_FROMFUNC, UPRes_validStringValue(query.fromFunc), UPRES_ITEM_ID, query.itemId];
    return sql;
}
- (NSString *)SQL_table:(NSString *)tableName queryResourceQueryListByItemIds:(NSString *)items
{

    NSString *sqlFormat = @"SELECT * FROM %@ WHERE %@ IN (%@)";

    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_ITEMID, items];

    return sql;
}
- (NSString *)SQL_table:(NSString *)tableName deleteResListByItemIds:(NSString *)items
{
    NSString *sqlFormat = @"DELETE  FROM %@ WHERE %@ IN (%@)";
    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_ID, items];
    return sql;
}
- (NSString *)SQL_table:(NSString *)tableName deleteQueryListByItemIds:(NSString *)items
{
    NSString *sqlFormat = @"DELETE  FROM %@ WHERE %@ IN (%@)";
    NSString *sql = [NSString stringWithFormat:sqlFormat, tableName, UPRES_ITEM_ID, items];
    return sql;
}

- (NSString *)SQL_table:(NSString *)tableName queryResourceInfoByStatus:(NSInteger)status name:(NSString *)name type:(UPResourceType)type version:(NSString *)version
{
    NSMutableString *sql = [NSMutableString stringWithFormat:@"SELECT * FROM %@ WHERE %@=%zd", tableName, UPRES_ITEM_RESOURCE_STATUS, status];
    NSString *typeStr = [UPResourceInfo stringValueOfResourceType:type];
    NSString *nameStr = UPRes_validStringValue(name);
    NSString *versionStr = UPRes_validStringValue(version);
    if (!UPRes_isEmptyString(nameStr)) {
        [sql appendFormat:@" AND %@='%@'", UPRES_ITEM_NAME, nameStr];
    }
    if (!UPRes_isEmptyString(typeStr)) {
        [sql appendFormat:@" AND  %@='%@'", UPRES_ITEM_TYPE, typeStr];
    }
    if (!UPRes_isEmptyString(versionStr)) {
        [sql appendFormat:@" AND %@='%@'", UPRES_ITEM_VERSION, versionStr];
    }
    return sql.copy;
}
@end

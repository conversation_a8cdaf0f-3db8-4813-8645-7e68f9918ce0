# UPResource

#### 介绍 
U+App 资源管理组件,其代码仓库地址为:http://144.123.47.148:8091/admin/repos/uplus/ios/resource 
先将其clone下来，命令如下：
```
git clone "ssh://username@10.138.228.89:29418/uplus/ios/resource" && scp -p -P 29418 username@10.138.228.89:hooks/commit-msg "resource/.git/hooks/""
```
克隆完成后，进入工程所在目录的跟目录，会发现有一个名称为Podfile的文件。
然后，打开Terminal，进入工程所在目录，执行pod install。
pod install完成之后，会生成一个UPRes.xcworkspace的文件，双击打开它。
在执行单元测试之前，还需要clone该库的单元测试用例库，如下：
```
git clone "ssh://username@10.138.228.89:29418/uplus/features/resource" && scp -p -P 29418 username@10.138.228.89:hooks/commit-msg "resource/.git/hooks/""
```
克隆下来后，将该库放置与UPRes库文件夹同级目录，
然后，将单元测试用例的仓库以文件形式引入进UPRes的单元测试target中即可运行单元测试用例。

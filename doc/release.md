#  UPRes 发布记录

## 当前版本: 2.19.0

### 2.19.0
1.资源下载适配印尼语和马来西亚语
### 2.15.19
1.资源信息添加全屏幕显示属性
### 2.15.18
1.ABTest资源包下载埋点添加
### 2.15.17
1.预置安装资源包时，将资源包指定为最新资源包
### 2.15.14
1.切换server域名为zj.haier.net
### 2.15.13
1.添加ABTest资源选择功能
### 2.15.12
2021-07-26
1.查询资源删除灰度情况下返回空对象判断
### 2.15.10
2021-07-16
1.资源管理库代码优化
### 2.15.9
1.新增configAPP(数据配置)类型资源并适配测试用例
### 2.15.7
1.适配头文件
### 2.15.6
1.添加gitsubmodule
### 2.15.3
基于tag2.15重新切换为静态库源码依赖,供切换源码依赖调试
### 2.15.1
1.优化UPResourceManager新增自动升级本地已安装的资源包接口
2.优化UPResourceInfo是否过期接口isExpiresIn代码
### 2.15.0
1.UPResourceManager新增自动升级本地已安装的资源包接口
2.UPResourceInfo新增是否过期接口isExpiresIn
### 2.14.2
1.合并2.14.x分支：解决【概率】覆盖安装后点击生活家帖子提示资源下载失败问题；支持资源管理支持验收环境iOS侧开发
### 2.14.1
1.tag 2.14.0未使用，设备资源接口升级:新增deviceNetType类型
### 2.14.0
1.设备资源接口升级:新增deviceNetType类型
### 2.13.0
1.适配AFNetworking 4.0
### 2.12.0
1.合并东南亚分支 
2.隔离上报资源到达率

### 2.11.7
1.预置资源包机制升级
### 2.11.6
1.配合CI处理Xcode12的framework合包问题
### 2.11.5
1.适配Xcode12的framework
### 2.11.4
1.添加查找最新已安装资源日志
### 2.11.3
1.修复高版本资源已安装时，升级App后。清除无用资源将预置包进行清除
### 2.11.2
1.升级GrowingIO版本为2.8.22
### 2.11.1
1.下载器支持HttpDns接口改造
### 2.11.0
1.增加统计资源文件存储空间大小接口
### 2.10.1
1.修复预置资源拷贝文件失败问题
### 2.10.0
1.增加删除无用资源接口
### 2.9.9
1.修改bundle集成方式
### 2.9.8
1.完善多线程单元测试
### 2.9.7
1.去掉同步预置的callback回调，添加同步返回。并完成多线程单元测试
### 2.9.6
1.添加自定义类型资源并修改其单元测试
### 2.9.5
1.添加异步插入资源信息且安装接口
2.增加查询本地普通、设备资源列表接口
2.增加查询服务器普通、设备资源列表接口
### 2.9.4
1.修复数据库表升级版本号存储key
### 2.9.3
1.添加预置资源error日志
### 2.9.1
1.修改framework集成后错误问题
### 2.8.7
1.修改工程为framework
### 2.8.5
1.扫描首页地址功能添加
2.设备资源v3接口添加调试
### 2.8.4
1.下载进度显示宽度比例调整
### 2.8.3
1.下载git图片替换
### 2.8.1
1.加载资源动效开发
### 2.8.0
1.东南亚分支代码与国内合并,并且均适配UPNetwork3.2.1
### 2.7.4
1.文件hash计算算法优化
### 2.7.3
1.资源到达增加是否灰度字段
### 2.7.2
1.修复下载downloadpolicy无法修改问题
### 2.7.1
1.兼容预置资源包类型错误导致无法预置资源问题
### 2.7.0
1.单元测试代码调整
2.清除缓存接口修改
3.增加资源到达率统计功能

### 2.6.0
1.资源管理支持断点下载
### 2.5.1
1.修复弱网下载资源包失败问题

### 2.5.0
1.支持自动升级资源包
2.删除无用接口

### 2.4.5
1.去掉灰度下不进行预置资源相关逻辑
### 2.4.4
1.优化预加载资源包逻辑
### 2.4.3
1.修复预置包计算hashstr时间过长导致闪退问题
### 2.4.2
1.修复预置资源包hashstr和服务器不一致时，未安装服务器资源包问题
### 2.4.1
1. 新增mPaaS资源类型

### 2.4.0
1.增加预加载普通资源接口，优化下载资源逻辑
2.增加同步预置资源接口
3.增加获取已安装最新资源接口
4.适配UPLog,解除UPCompents的依赖

### 2.2.5
1.修改网络请求空结果结果判断
### 2.2.4 
1.资源包加载进度条样式调整
### 2.2.3
1.修改下载页面样式

### 2.2.2
1.修改资源安装过程的提示语

### 2.2.1
1.修改下载页面进度条偶现无法消失的问题
### 2.2.0
1.优化灰度下下载页面的下载流程,增加获取资源包列表失败重试机制为3次
2.优化下载资源包的失败提示语
3.增加updateNormalResList，batchInstallNormalResList方法
4.删除updateCommonResList接口
5.修改任务取消时的结果回调逻辑

### 2.1.10
1.设置下载超时时长为10秒
### 2.1.9
1.去掉下载进度条中间样式

### 2.1.8
1.修改下载进度条样式
2.添加并发任务个数为5个
### 2.1.7
1.修复下载页面VDN跳转回调相关问题
### 2.1.6
1.修复重复资源下载时进度条无法消失的问题
### 2.1.5
1.修复网络重新连接后无法恢复下载功能
### 2.1.4
1.修改injection初始化
### 2.1.3
1.区分查询资源与预置资源条件
### 2.1.2
1.优化网络结果处理卡主线程问题

### 2.1.1
1.解决创建下载任务时导致闪退问题

### 2.1.0
1.调整manager接口,修改预置资源逻辑
2.调整数据库接口，修改实现方法
3.隔离文件系统、时间代理。
4.调整清除缓存逻辑

### 2.0.4
1.修复获取最新资源问题
2.修复已安装资源的后台更新列表问题


### 2.0.3
1.去掉无网时不更新资源列表限制条件

### 2.0.2
1.修改网络问题时，导致下载失败，下载页面无法消失的问题

### 2.0.1
1.修改资源找不到时，下载页面无法消失的问题

### 2.0.0
1.升级UPResource至2.0.0
2.增加预置资源安装，批量安装，批量卸载等方法
3.增加下载进度页面


### 1.4.6
1.修复资源包更新时网络异常，导致的下载未开始的循环问题

### 1.4.5
1.新增获取本地配置文件API:  getLocalDevCfgInfo:
2.优化从服务器更新资源和配置文件的逻辑

### 1.4.4
1.新增获取普通资源包API:  updateNormalResource
2.新增获取本地所有资源包API:getLocalFormerInfoList

### 1.4.3
1.优化查询条件存储和查找资源信息时对于nil的处理
### 1.4.2
1.修改根据查询条件查询资源的sql错误
2.删除根据type获取正在使用的资源列表API: getActiveInfoList

### 1.4.1
1.删除根据名称获取正在使用的资源信息API : getActiveInfoByName

### 1.4.0
1.添加从本地读取普通资源和设备资源API ：getLocalNormalInfoLis，getLocalDeviceInfoList


### 1.3.1
1.添加服务器返回错误从本地取数据逻辑
2.删除searchActiveDeviceConfig、searchLatestDeviceConfig两个API

### 1.3.0
1.增加根据 type 资源类型 model 型号 typeID 类型标识prodNo 产品编码 deviceType 设备类型 查找资源API
2.增加清除下载文件和数据的API
3.更换原有请求资源列表接口地址
4.优化原有更新资源列表和配置文件的逻辑
5.删除强制服务器更新配置文件API


### 1.2.3
1. 调整设备配置文件更新的默认的时间间隔数值。

### 1.2.2
1. 更新依赖的FMDB版本配置为>=2.7.5。

### 1.2.1
1. 修复对于版本号元素中大于10的版本号比较出错的问题。
2. 修复当传入资源类型为UPResourceTypeAll时，获取现有已安装资源包列表的结果不正确的问题。

### 1.2.0
1. 更新配置文件接口的返回中，添加服务器返回列表为空的错误码。
2. 增加查询当前正在进行的任务对象和任务ID的接口方法。
3. 优化按型号和typeID查询配置文件数据库的查询语句。

### 1.1.1
1. 添加根据资源名称、类型和版本号查询资源信息对象的接口(getResourceInfo:type:version:)方法;
2. 修复当资源类型传入UPResourceTypeAll类型时，数据库查询语句错误导致查询结果错误的问题。

### 1.1.0
1. 添加查询所有H5资源包列表信息的枚举值UPResourceTypeAll。
2. 添加对APICloud类型资源包列表的查询逻辑支持。

### 1.0.7
1. 修复更新配置文件时，因缓存引起数据错误。

### 1.0.6
1. 修改通过设备型号和typeId查询配置文件信息的匹配逻辑。修复查询出的配置文件信息型号与入参设备型号不匹配的问题。

### 1.0.5
1. 添加资源包的临时文件清理逻辑。

### 1.0.4
1. 优化签名逻辑，修复因空格、回车、制表符等特殊字符导致签名失败的问题。

### 1.0.3
1. 将resType的默认值修改为UPResourceTypeDeviceConfig。
2. 优化包含HAVING MAX数据库语句的逻辑。

### 1.0.2
1. 添加配置文件更新间隔设置属性。

### 1.0.1
1. 修复在资源包升级时的BUG。

### 1.0.0
1. 完成资源管理组件基本功能开发和调试。

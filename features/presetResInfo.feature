Feature: 预置资源包
  功能范围:
  主要用于测试组件中预置资源相关接口及功能，用户可通过设置预置资源加载器，通过预置资源加载器提供的预置资源信息，进行预置资源包的安装，预置安装的结果会通知给指定的回调对象。
  预置资源包安装成功后，可通过资源包查询接口，查询到被安装的资源包信息。
  对于同一预置资源加载器扫描结果，预置完成一次后，再次进行预置，则会收到成功的回调结果，查询已安装的资源包与第一次结果相同。
  对于不同的预置资源加载器扫描结果，每次预置，仅安装变化了的未预置的资源包。
  - 资源包压缩文件命名规则为"类型@名称@版本号.zip"。
  - 目前仅支持对h5、apicloud和mPaaS类型的资源包进行预置。
  - 名称不能为空。
  - 版本号格式为x.y.z，且只能为数字。
  - 压缩包格式仅支持zip格式。文件的压缩格式通过其文件扩展名判断。
  - 新增资源文件夹预置，预置文件夹名称为资源名称

  外部依赖:
  - 预置资源加载器
  - 资源清理器
  - 本地数据存储代理
  - 文件系统代理
  - 关联资源关系代理

  接口说明
  1.异步预置资源列表
  预置资源加载器扫描预置路径失败时，接口回调失败结果。否则检测需要安装的预置资源信息列表是否为空，为空时接口回调成功结果，否则异步批量安装资源，接口回调批量安装资源的结果。
  2.同步预置资源信息
  根据指定的资源类型和资源名称，从预置资源加载器提供的预置资源中匹配预置资源信息，如果未匹配到则接口回调失败结果，否则同步安装资源，接口回调安装资源的结果。

  Background:
    Given 初始化资源管理器,清理器为"模拟的",调度器为"同步的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
    Given 关联资源关系代理使用模拟的
    Given 预置文件的根路径为"/path/to/resource"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
      | /path/to/resource/Temp     | 文件夹 |

  Scenario:[2000]当资源管理器正在清空缓存时，异步预置资源列表结果应失败.
    Given 正在执行清除本地缓存操作
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then "CallbackA"收到异步预置资源列表的结果为"失败"

  Scenario:[2001]当资源管理器正在预置资源列表时，再次异步预置资源列表结果应失败.
    Given 预置资源加载器扫描预置路径接口持续时长为"3"秒
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 调用异步预置资源列表接口,参数为"CallbackB"
    When 等待"5"秒
    Then "CallbackB"收到异步预置资源列表的结果为"失败"


  Scenario:[2002]当预置资源扫描路径下无json文件时，异步预置资源列表结果应失败.
    Given 预置资源加载器扫描的json文件的列表为空对象
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then "CallbackA"收到异步预置资源列表的结果为"失败",预置的资源信息列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |

  Scenario:[2003]当预置资源扫描路径下的json文件内容为空时,异步预置资源列表结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType | version | filename | hashMD5 |
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then "CallbackA"收到异步预置资源列表的结果为"成功",预置的资源信息列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |

  Scenario:[2004]当预置资源扫描路径下的json文件内容为不为空时,但是文件系统下没有对应的资源压缩包，则异步预置资源列表结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then "CallbackA"收到异步预置资源列表的结果为"成功",预置的资源信息列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |

  Scenario:[2005]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当关联资源关系失败时，异步预置资源列表结果应失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 关联资源关系接口返回结果为"失败"
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then "CallbackA"收到异步预置资源列表的结果为"失败"
    Then 资源关联关系接口被调用"1"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |

  Scenario:[2006]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，待预置资源已经预置过了(resInfo.active判断有path)，异步预置资源列表结果应成功，并且资源关联关系接口应被调用。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 数据库代理根据名字"ResB"类型"apicloud"版本"1.0.0"查询资源结果如下:
      | Id | Type     | Name | Version | Link                    | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 2  | apicloud | ResB | 1.0.0   | apicloud@<EMAIL> | hashB |       |        |        |          | /path/to/resource/APICloud/ResB@1.0.0 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
      | /path/to/resource/APICloud/ResB@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then 资源关联关系接口被调用"4"次,传入预置标识为"是",参数如下:
      | Query                                            | Type     | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA       | h5       | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=ResB | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResB         | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA         | h5       | ResA | 1.0.0   |
    Then "CallbackA"收到异步预置资源列表的结果为"成功",预置的资源信息列表如下:
      | Id | Type     | Name | Version | Link                    | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5       | ResA | 1.0.0   | h5@<EMAIL>       | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |
      | 2  | apicloud | ResB | 1.0.0   | apicloud@<EMAIL> | hashB |       |        |        |          | /path/to/resource/APICloud/ResB@1.0.0 |

  Scenario:[2007]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，资源包安装成功的的个数为大于0时，异步预置资源列表结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 关联资源关系接口返回结果为"成功"
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |
    Given 数据库代理根据名字"ResB"类型"apicloud"版本"1.0.0"查询资源结果如下:
      | Id | Type     | Name | Version | Link                    | MD5   |
      | 2  | apicloud | ResB | 1.0.0   | apicloud@<EMAIL> | hashB |
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/apicloud@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"失败"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/ResA@1.0.0     |
      | /path/to/resource/Temp/ResB@1.0.0     |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
      | /path/to/resource/ApiCloud/ResB@1.0.0 | ResB@1.0.0 |
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/index.html | 文件   |
    Given 本地数据库更新资源,操作结果为"成功"
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then 资源关联关系接口被调用"4"次,传入预置标识为"是",参数如下:
      | Query                                            | Type     | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA       | h5       | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=ResB | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResB         | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA         | h5       | ResA | 1.0.0   |
    Then "CallbackA"收到异步预置资源列表的结果为"成功",预置的资源信息列表如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |

  Scenario:[2007-1]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的预置资源文件夹和预置zip，异步预置资源列表结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename          | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL> | hashA   |
      | ResB    | apicloud | 1.0.0   | ResB              | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename          |
      | h5@<EMAIL> |
      | ResB              |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
      | /path/to/resource/ApiCloud/ResB@1.0.0 | ResB@1.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/ResA@1.0.0     |
      | /path/to/resource/Temp/ResB@1.0.0     |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
      | /path/to/resource/H5ResPkg            |
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js         | 文件夹  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/css        | 文件夹  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/index.html | 文件   |
    Given 设置文件"/path/to/resource/ApiCloud/ResB@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type |
      | /path/to/resource/ApiCloud/ResB@1.0.0/js         | 文件夹  |
      | /path/to/resource/ApiCloud/ResB@1.0.0/css        | 文件夹  |
      | /path/to/resource/ApiCloud/ResB@1.0.0/index.html | 文件   |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |
    Given 数据库代理根据名字"ResB"类型"apicloud"版本"1.0.0"查询资源结果如下:
      | Id | Type     | Name | Version | Link | MD5   |
      | 2  | apicloud | ResB | 1.0.0   | ResB | hashB |
    Given 关联资源关系接口返回结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/ResB"至"/path/to/resource/Temp/ResB@1.0.0/ResB"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResB@1.0.0"至"/path/to/resource/ApiCloud/ResB@1.0.0"操作结果为"成功"
    Given 本地数据库更新资源,操作结果为"成功"
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"3"秒
    Then 资源关联关系接口被调用"4"次,传入预置标识为"是"
    Then "CallbackA"收到异步预置资源列表的结果为"成功",预置的资源信息列表如下:
      | Id | Type     | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5       | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |
      | 2  | apicloud | ResB | 1.0.0   | ResB              | hashB |       |        |        |          | /path/to/resource/ApiCloud/ResB@1.0.0 |

  Scenario:[2008]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，所有资源包均安装失败，则异步预置资源列表结果应失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 关联资源关系接口返回结果为"成功"
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |
    Given 数据库代理根据名字"ResB"类型"apicloud"版本"1.0.0"查询资源结果如下:
      | Id | Type     | Name | Version | Link                    | MD5   |
      | 2  | apicloud | ResB | 1.0.0   | apicloud@<EMAIL> | hashB |
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/apicloud@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/ResA@1.0.0     |
      | /path/to/resource/Temp/ResB@1.0.0     |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"失败"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"失败"
    When 调用异步预置资源列表接口,参数为"CallbackA"
    When 等待"5"秒
    Then 资源关联关系接口被调用"4"次,传入预置标识为"是",参数如下:
      | Query                                            | Type     | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA       | h5       | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=ResB | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResB         | apicloud | ResB | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA         | h5       | ResA | 1.0.0   |
    Then "CallbackA"收到异步预置资源列表的结果为"失败"

  Scenario Outline:[2009]当资源名称无效时，同步预置资源信息结果应为失败。
    When 使用者调用同步预置资源信息接口,资源名称为"<Name>",资源类型为"<Type>"
    Then 同步预置资源信息的结果为"<Result>"
    Examples:
      | Name     | Type     | Result |
      | 空对象   | h5       | 失败   |
      | 空字符串 | apicloud | 失败   |

  Scenario Outline:[2010]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当资源类型不匹配或不支持时，同步预置资源信息结果应为失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    When 使用者调用同步预置资源信息接口,资源名称为"<Name>",资源类型为"<Type>"
    Then 同步预置资源信息的结果为"<Result>"
    Examples:
      | Name | Type   | Result |
      | ResA | config | 失败   |
      | ResB | react  | 失败   |
      | ResC | h5     | 失败   |

  Scenario:[2011]当资源管理器正在清空缓存时，同步预置资源信息结果应为失败。
    Given 正在执行清除本地缓存操作
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2012]当预置资源扫描路径下无json文件时，同步预置资源信息结果应失败.
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2013]当预置资源扫描路径下json文件内容为空时，同步预置资源信息结果应失败.
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType | version | filename | hashMD5 |
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2014]当预置资源扫描路径下的json文件内容为不为空时,但是文件系统下没有对应的资源压缩包，同步预置资源信息结果应失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2015]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当关联资源关系失败时，同步预置资源信息结果为失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 关联资源关系接口返回结果为"失败"
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"1"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2016]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当待预置的资源已安装时，同步预置资源信息结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType | version | filename          | hashMD5 |
      | ResA    | h5      | 1.0.0   | h5@<EMAIL> | hashA   |
    Given 扫描路径下的文件列表如下:
      | filename          |
      | h5@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"2"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA   | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"成功",预置的资源信息如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |

  Scenario:[2017]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当待预置的资源安装成功时，同步预置资源信息结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/index.html | 文件   |
    Given 本地数据库更新资源,操作结果为"成功"
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"2"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA   | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"成功",预置的资源信息如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |

  Scenario Outline:[2017-1]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当资源路径的子目录列表为空或不包含index.html文件时，同步预置资源信息结果应失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath    |   Type      |
      | <SubFilePaths> | <FileType>  |           
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"2"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA   | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"失败"
    Examples:
      | SubFilePaths                             | FileType |
      |                                          |          |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js |  文件     |

  Scenario:[2018]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当待预置的资源安装失败时，同步预置资源信息结果应失败。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          |      |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"失败"
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"2"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA   | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"失败"

  Scenario:[2019]预置资源扫描路径下的json文件内容为不为空时,且文件系统下存在相应的资源包，当目标文件夹不存在时，同步预置资源信息结果应成功。
    Given 预置资源加载器扫描的json文件的列表如下:
      | resName | resType  | version | filename                | hashMD5 |
      | ResA    | h5       | 1.0.0   | h5@<EMAIL>       | hashA   |
      | ResB    | apicloud | 1.0.0   | apicloud@<EMAIL> | hashB   |
    Given 扫描路径下的文件列表如下:
      | filename                |
      | h5@<EMAIL>       |
      | apicloud@<EMAIL> |
    Given 数据库代理根据名字"ResA"类型"h5"版本"1.0.0"查询资源结果如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          |      |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
    Given 关联资源关系接口返回结果为"成功"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/index.html | 文件   |
    Given 本地数据库更新资源,操作结果为"成功"
    When 使用者调用同步预置资源信息接口,资源名称为"ResA",资源类型为"h5"
    Then 资源关联关系接口被调用"2"次,传入预置标识为"是",参数如下:
      | Query                                      | Type | Name | Version |
      | appversion=6.9.0,fromFun=0,rt=h5,name=ResA | h5   | ResA | 1.0.0   |
      | appversion=6.9.0,fromFun=0,rt=,name=ResA   | h5   | ResA | 1.0.0   |
    Then 同步预置资源信息的结果为"成功",预置的资源信息如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |

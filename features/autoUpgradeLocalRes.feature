Feature: 自动更新本地已安装的资源信息

  功能范围:
  主要用于测试自动更新本地已安装的所有资源，除了配置资源。提供自动查询所有已安装资源，根据查询条件更新设备资源或者批量更新普通资源到本地。

  外部依赖:
  - 本地数据存储代理
  - 文件系统代理
  - 运营平台部署的资源包信息
  - 资源数据源代理

  接口说明:
  1.自动更新本地所有已安装资源（除了配置文件类型资源）
  自动获取本地所有已安装资源信息和资源条件，如果设备资源条件不为空，则遍历请求更新设备资源信息并更新到数据库；
  如果普通资源不为空，则将批量请求普通资源信息并更新到数据库。

  Background:
    Given 初始化资源管理器,清理器为"模拟的",APP版本号为"7.0.4",数据存储的根路径为"/path/to/resource"
    Given 资源请求代理使用模拟的
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type  | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                               | isServerLatest | CreateTime | UpdateTime |
      | 1  | mPaaS | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/mPaaS/resA@1.0.0 | true           | 1622424929 | 1622424929 |
      | 2  | mPaaS | resB | 2.0.0   | http://resB.zip | hashB | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/mPaaS/resB@2.0.0 | true           | 1622424930 | 1622424930 |
      | 3  | mPaaS | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/mPaaS/resC@1.0.1 | true           | 1622424933 | 1622424933 |
      | 4  | mPaaS | resD | 2.0.1   | http://resD.zip | hashD | modelD | typeIdD | prodNoD | typeCodeE | /path/to/resource/mPaaS/ResD@2.0.1 | false          | 1622424934 | 1622424934 |
      | 5  | mPaaS | resE | 1.0.0   | http://resE.zip | hashE |        |         |         |           | /path/to/resource/mPaaS/resE@1.0.0 | true           | 1622424931 | 1622424931 |
      | 6  | flutter | resF | 1.0.0   | http://resF.zip | hashF |        |         |         |           | /path/to/resource/flutter/resF@1.0.0 | true           | 1622424935 | 1622424935 |

    Given 文件系统中"存在"的"文件"路径如下:
      | Path                               |
      | /path/to/resource/mPaaS/resA@2.0.0 |
      | /path/to/resource/mPaaS/resB@2.0.0 |
      | /path/to/resource/mPaaS/ResD@2.0.1 |
      | /path/to/resource/mPaaS/resE@1.0.0 |
      | /path/to/resource/flutter/resF@1.0.0 |

  Scenario:[14000] 自动更新本地所有已安装资源，查询已安装资源，有设备资源，则进行遍历更新设备资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name | md     | ti      | pn      | tc        | dnt            |
      | 1           | 2     | 7.0.4      | 2       | mPaaS | resB | modelB | typeIdB | prodNoB | typeCodeB | deviceNetTypeB |
      | 2           | 4     | 7.0.4      | 2       | mPaaS | resD | modelD | typeIdD | prodNoD | typeCodeD | deviceNetTypeD |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 用户调用自动更新本地已安装资源接口
    When 等待"2"秒
    Then 数据库批量查询资源条件接口被调用"1"次
    Then 资源请求代理接口被调用"2"次

  Scenario:[14001-1] 自动更新本地所有已安装资源，查询已安装资源，有普通资源，则进行批量更新普通资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name |
      | 3           | 1     | 7.0.4      | 0       | mPaaS | resA |
      | 4           | 5     | 7.0.4      | 0       | mPaaS | resE |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"1"次
    Then 资源请求代理接口被调用"1"次

  Scenario:[14001-2] 自动更新本地所有已安装资源，查询已安装资源，有动态资源，则进行批量更新动态资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt      | name |
      | 6           | 6     | 7.0.4      | 0       | flutter | resF |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"1"次
    Then 资源请求代理接口被调用"1"次

  @ios_ignore
  Scenario:[14001-3] 自动更新本地所有已安装资源，查询已安装资源，有普通资源，也有设备资源，则进行批量更新普通资源，进行遍历更新设备资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name | md     | ti      | pn      | tc        | dnt            |
      | 3           | 1     | 7.0.4      | 0       | mPaaS | resA |        |         |         |           |                |
      | 4           | 5     | 7.0.4      | 0       | mPaaS | resE |        |         |         |           |                |
      | 1           | 2     | 7.0.4      | 2       | mPaaS | resB | modelB | typeIdB | prodNoB | typeCodeB | deviceNetTypeB |
      | 2           | 4     | 7.0.4      | 2       | mPaaS | resD | modelD | typeIdD | prodNoD | typeCodeD | deviceNetTypeD |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 升级后首次启动自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"2"次
    Then 资源请求代理接口被调用"6"次

  @ios_ignore
  Scenario:[14001-4] 自动更新本地所有已安装资源，查询已安装资源，只有普通资源，则进行批量更新普通资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name | md     | ti      | pn      | tc        | dnt            |
      | 3           | 1     | 7.0.4      | 0       | mPaaS | resA |        |         |         |           |                |
      | 4           | 5     | 7.0.4      | 0       | mPaaS | resE |        |         |         |           |                |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 升级后首次启动自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"2"次
    Then 资源请求代理接口被调用"2"次

  @ios_ignore
  Scenario:[14001-5] 自动更新本地所有已安装资源，查询已安装资源，只有设备资源，则进行遍历更新设备资源
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name | md     | ti      | pn      | tc        | dnt            |
      | 1           | 2     | 7.0.4      | 2       | mPaaS | resB | modelB | typeIdB | prodNoB | typeCodeB | deviceNetTypeB |
      | 2           | 4     | 7.0.4      | 2       | mPaaS | resD | modelD | typeIdD | prodNoD | typeCodeD | deviceNetTypeD |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 升级后首次启动自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"2"次
    Then 资源请求代理接口被调用"4"次

  Scenario:[14002-1] 自动更新本地所有已安装资源，查询已安装资源，有普通资源，但是资源查询类型为批量查询资源，则跳过遍历
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name |
      | 5           | 5     | 7.0.4      | 4       | mPaaS | resE |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"1"次
    Then 资源请求代理接口被调用"0"次

  Scenario:[14002-2] 自动更新本地所有已安装资源，查询已安装资源，有动态资源，但是资源查询类型为批量查询资源，则跳过遍历
    Given 自动更新数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt      | name |
      | 7           | 6     | 7.0.4      | 4       | flutter | resF |
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
    
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"1"次
    Then 资源请求代理接口被调用"0"次

  Scenario:[14003] 自动更新本地所有已安装资源，查询已安装资源，没有查到已安装的资源，则直接返回
    Given 文件系统中"不存在"的"文件"路径如下:
      | Path                               |
      | /path/to/resource/mPaaS/resA@2.0.0 |
      | /path/to/resource/mPaaS/resB@2.0.0 |
      | /path/to/resource/mPaaS/ResD@2.0.1 |
      | /path/to/resource/mPaaS/resE@1.0.0 |
      | /path/to/resource/flutter/resF@1.0.0 |
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"0"次
    Then 资源请求代理接口被调用"0"次

  Scenario: [14004]处于执行清理本地缓存操作时，自动更新不执行
    Given 正在执行清除本地缓存操作
    When 用户调用自动更新本地已安装资源接口
    Then 数据库批量查询资源条件接口被调用"0"次
    Then 资源请求代理接口被调用"0"次

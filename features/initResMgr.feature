Feature: 初始化资源管理器
  功能范围：
  主要是提供初始化资源管理器功能，资源管理器是资源管理组件对外的主要接口单元，通过资源管理器提供的各类接口，调用者可实现对资源的安装、卸载、查询、更新等操作。
  提供初始化资源管理器接口，初始化资源管理器所需要的主要参数如下：
  - APP版本号(appVersion)，APP应用的版本号。
  - 资源存储路径(resourceRootPath)，用来保存资源信息相关文件的根目录。
  - 资源数据来源(dataSource)，资源信息的数据源。
  - 本地数据存储代理(databaseDelegate)，管理器通过该参数实现数据存储功能。
  - 文件系统代理(fileDelegate)，管理器通过该代理完成文件系统的相关功能。
  - 系统时间代理(timeDelegate)，管理器通过该代理实现系统时间的相关操作。
  - 网络状态代理(connectionDelegate)，为管理器提供网络状态的查询、监听等功能。
  - 下载功能代理(downloadDelegate)，管理器通过该参数实现对资源包的下载功能。
  - 资源清理器(cleaner)，管理器通过该参数实现对资源包的清理功能，初始化时，若不传该参数，则会使用默认实现。
  - 线程调度器(scheduler)，避免在主线程执行耗时操作(该参数仅android版本需要)。

  接口说明：
  1.初始化资源管理器
  根据指定的参数，初始化资源管理器，资源清理器参数为空时，会使用默认实现，其余参数不为空对象情况，初始化成功，
  除过资源清理器参数外，任一参数为空对象，则初始化失败。

  Scenario Outline: [1000]传入资源管理器的初始化方法中除清理器之外的其他任一参数为空对象，则初始化失败。
    When 初始化资源管理器对象,各参数是否为空对象的情况如下:
      | appVersion   | resourceRootPath   | dataSource   | databaseDelegate   | fileDelegate   | timeDelegate   | connectionDelegate   | downloadDelegate   | scheduler   |
      | <appVersion> | <resourceRootPath> | <dataSource> | <databaseDelegate> | <fileDelegate> | <timeDelegate> | <connectionDelegate> | <downloadDelegate> | <scheduler> |
    Then 资源管理器初始化"失败"
    Examples:
      | appVersion | resourceRootPath | dataSource | databaseDelegate | fileDelegate | timeDelegate | connectionDelegate | downloadDelegate | scheduler |
      | 是         | 是               | 是         | 是               | 是           | 是           | 是                 | 是               | 是        |
      | 是         | 否               | 否         | 否               | 否           | 否           | 否                 | 否               | 否        |
      | 否         | 是               | 否         | 否               | 否           | 否           | 否                 | 否               | 否        |
      | 否         | 否               | 是         | 否               | 否           | 否           | 否                 | 否               | 否        |
      | 否         | 否               | 否         | 是               | 否           | 否           | 否                 | 否               | 否        |
      | 否         | 否               | 否         | 否               | 是           | 否           | 否                 | 否               | 否        |
      | 否         | 否               | 否         | 否               | 否           | 是           | 否                 | 否               | 否        |
      | 否         | 否               | 否         | 否               | 否           | 否           | 是                 | 否               | 否        |
      | 否         | 否               | 否         | 否               | 否           | 否           | 否                 | 是               | 否        |

  Scenario Outline: [1001]初始化方法中的除清理器之外所有参数均不为空对象，则初始化成功。
    When 初始化资源管理器对象,各参数是否为空对象的情况如下:
      | appVersion   | resourceRootPath   | dataSource   | databaseDelegate   | fileDelegate   | timeDelegate   | connectionDelegate   | downloadDelegate   | cleaner   | scheduler   |
      | <appVersion> | <resourceRootPath> | <dataSource> | <databaseDelegate> | <fileDelegate> | <timeDelegate> | <connectionDelegate> | <downloadDelegate> | <cleaner> | <scheduler> |
    Then 资源管理器初始化"成功"
    Examples:
      | appVersion | resourceRootPath | dataSource | databaseDelegate | fileDelegate | timeDelegate | connectionDelegate | downloadDelegate | cleaner | scheduler |
      | 否         | 否               | 否         | 否               | 否           | 否           | 否                 | 否               | 否      | 否        |
      | 否         | 否               | 否         | 否               | 否           | 否           | 否                 | 否               | 是      | 否        |

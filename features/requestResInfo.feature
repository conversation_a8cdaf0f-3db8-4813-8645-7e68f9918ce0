Feature: 请求资源信息
    功能范围：
    主要提供请求资源信息功能，资源管理器通过运营平台远程请求与APP版本相关的资源数据，并将数据更新到本地数据库
    在使用资源管理器的请求功能时，若已经请求过数据，则请求接口会先返回缓存数据，否则返回空列表；然后再通过异步回调返回从远程请求后的最新数据
    主要提供更请求资源列表接口
    - 运营平台的请求接口需要使用APP的版本作为参数，以返回对应APP版本的资源数据，当用户更新本地缓存时，应返回与运营平台一致的数据
    - 支持根据设置的请求时间阀值来控制从运营平台请求数据的频率，间隔时间的单位为：毫秒，默认为：10分钟；每次向运营平台请求成功时，会将当前系统时间保存在请求结果中
    若第二次请求时取到的当前系统时间与上一次请求记录的系统时间间隔大于等于设置的间隔时间，则需要去运营平台请求新数据，否则只返回缓存数据；当间隔时间设置为小于等于0的值，表示立即从运营平台请求新数据
    - 支持通过给请求方法传入立即请求的参数来强制从运营平台获取数据
    - 当运营平台请求接口返回成功代码，但资源列表为空时，认为此次请求成功，在时间阀值内再次执行请求操作，应直接返回缓存数据，而不去远程请求

    外部依赖：
    1.运营平台部署的资源包信息
    2.关联资源关系代理
    3.资源数据源代理
    4.本地数据存储代理


    接口说明：
    1.请求资源列表
    根据名字和类型、APP版本等组成的请求条件，同步返回与请求条件一致的上次缓存在本地的数据，如果首次请求则同步返回空列表。然后异步返回运营平台数据，当立即请求参数为YES时
    不比较时间阀值，异步返回当前请求条件下匹配运营平台的资源信息列表。反之，若没有请求过，请求逻辑与立即请求参数为YES时逻辑一致，若请求过，则比较上次请求时间与当前时间的差值是否小于设置的时间阀值，
    若小于，返回与请求条件一致的上次缓存在本地的数据。大于等于则去运营平台请求，请求逻辑与立即请求参数为YES时逻辑一致。
    2.请求资源状态
    请求资源列表时，并发请求相关资源状态，若资源状态与本地数据库不一致，则更新本地数据库资源状态信息。

    Background:
        Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
        Given 关联资源关系代理使用模拟的

    Scenario Outline:[5000]初次请求资源时，当运营平台的资源查询接口出错时，则请求资源接口返回空对象，异步回调接口返回失败。
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Condition                                                                                              | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |


    Scenario Outline:[5001]初次请求资源时，运营平台正常返回数据后，关联资源关系失败，则请求资源接口返回空对象，异步回调接口返回失败。
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | Query                                                                                                  | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 2.0.0      | http://resJ.json        | hashJ   | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.1      | http://resD.zip         | hashD   | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.1.0      | http://cfg0.signed.json | hashC   | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 2.0.0      | http://resJ.json        | hashJ   | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.1      | http://resD.zip         | hashD   | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.1.0      | http://cfg0.signed.json | hashC   | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |


    Scenario Outline:[5002]初次请求资源时，请求资源接口先返回空对象，然后再异步返回从运营平台请求后的列表。
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Examples:
            | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | Query                                                                                                  | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 2.0.0      | http://resJ.json        | hashJ   | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.1      | http://resD.zip         | hashD   | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.1.0      | http://cfg0.signed.json | hashC   | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 2.0.0      | http://resJ.json        | hashJ   | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.1      | http://resD.zip         | hashD   | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.1.0      | http://cfg0.signed.json | hashC   | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |


    Scenario Outline:[5003]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，接口返回和异步回调返回结果均为缓存的资源列表。
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"0"次
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5004]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，数据库查询请求条件失败时,请求资源接口先返回空对象,再异步返回从运营平台请求后的列表
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"空对象",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resA    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5005]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，数据库查询关联资源列表失败时,则请求资源接口返回空列表，异步回调接口返回失败。
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"0"次
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5006]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，应先返回本地缓存的资源列表，再异步返回从运营平台请求后的列表。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | SyncTypeName     | SyncResName | SyncResVersion | SyncResLink             | SyncHashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA        | 1.0.0          | http://resA1.zip        | hashA1      | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ        | 1.0.0          | http://resJ1.json       | hashJ1      | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD        | 1.1.0          | http://resD1.zip        | hashD1      | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0        | 1.0.1          | http://cfg0.signed.json | hashC1      | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ        | 1.0.1          | http://resZ.zip         | hashZ       | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5007]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，数据库查询关联资源列表失败时，则请求资源接口返回空列表，再异步返回从运营平台请求后的列表。
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | SyncTypeName     | SyncResName | SyncResVersion | SyncResLink             | SyncHashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA        | 1.0.0          | http://resA1.zip        | hashA1      | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ        | 1.0.0          | http://resJ1.json       | hashJ1      | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD        | 1.1.0          | http://resD1.zip        | hashD1      | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0        | 1.0.1          | http://cfg0.signed.json | hashC1      | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ        | 1.0.1          | http://resZ.zip         | hashZ       | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5008]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，运营平台的资源查询接口出错时，则请求资源接口返回上一次请求结果，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:30:00"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5009]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，数据库查询关联资源列表失败时，则请求资源接口返回空列表,运营平台的资源查询接口出错时，异步回调接口返回失败。
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:30:00"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5010]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，关联资源关系失败，则请求资源接口返回上一次请求结果，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5011]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，数据库查询关联资源列表失败时，则请求资源接口返回空列表,关联资源关系失败，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5012]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，运营平台的资源查询接口出错时，则请求资源接口返回上一次请求结果，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |



    Scenario Outline:[5013]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，数据库查询关联资源列表失败时，则请求资源接口返回空列表,运营平台的资源查询接口出错时，异步回调接口返回失败。
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    Scenario Outline:[5014]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，关联资源关系失败，则请求资源接口返回上一次请求结果，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    Scenario Outline:[5015]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，数据库查询关联资源列表失败时，则请求资源接口返回空列表,关联资源关系失败，异步回调接口返回失败。
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    Scenario Outline:[5016]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，应先返回本地缓存的资源列表，异步返回从运营平台请求后的列表。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    Scenario Outline:[5017]已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，请求时传入立即请求标识，数据库查询关联资源列表失败时，则请求资源接口返回空列表，异步返回从运营平台请求后的列表。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"YES"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ    | 1.0.0      | http://resJ1.json       | hashJ1  | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD    | 1.1.0      | http://resD1.zip        | hashD1  | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | config           | cfg0         | 1.1.0           | http://cfg0.signed.json | hashC        | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    Scenario Outline:[5018]初次请求资源时，请求资源接口先返回空对象，然后再异步返回从运营平台请求后的列表。
        Given 关联资源关系接口返回结果为"成功"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type  | Name | Version | Link            | MD5   |
            | mPaaS | resA | 2.0.0   | http://resA.zip | hashA |
            | mPaaS | resB | 2.0.1   | http://resB.zip | hashB |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"No"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type  | Name | Version | Link            | MD5   |
            | mPaaS | resA | 2.0.0   | http://resA.zip | hashA |
            | mPaaS | resB | 2.0.1   | http://resB.zip | hashB |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type  | Name | Version | Link            | MD5   |
            | mPaaS | resA | 2.0.0   | http://resA.zip | hashA |
            | mPaaS | resB | 2.0.1   | http://resB.zip | hashB |
        Examples:
            | Query                                              | Condition                                          | FromFun | AppVersion | ConditionStr            | Interface    |
            | appversion=6.9.0,fromFun=4,rt=mPaaS,name=resA/resB | appversion=6.9.0,fromFun=4,rt=mPaaS,name=resA/resB | 4       | 6.9.0      | rt=mPaaS,name=resA/resB | 批量普通资源   |

    Scenario Outline:[5019]初次请求资源时，当运营平台的批量查询普通资源接口出错时，则请求资源接口返回空对象，异步回调接口返回失败。
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"No"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Condition                                          | FromFun | AppVersion | ConditionStr            | Interface    |
            | appversion=6.9.0,fromFun=4,rt=mPaaS,name=resA/resB | 4       | 6.9.0      | rt=mPaaS,name=resA/resB | 批量普通资源 |

    Scenario Outline:[5020]初次请求资源时，运营平台正常返回数据后，关联资源关系失败，则请求批量普通资源接口返回空对象，异步回调接口返回失败。
        Given 关联资源关系接口返回结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type  | Name | Version | Link            | MD5   |
            | mPaaS | resA | 2.0.0   | http://resA.zip | hashA |
            | mPaaS | resB | 2.0.1   | http://resB.zip | hashB |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"No"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type  | Name | Version | Link            | MD5   |
            | mPaaS | resA | 2.0.0   | http://resA.zip | hashA |
            | mPaaS | resB | 2.0.1   | http://resB.zip | hashB |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                              | Condition                                          | FromFun | AppVersion | ConditionStr            | Interface    |
            | appversion=6.9.0,fromFun=4,rt=mPaaS,name=resA/resB | appversion=6.9.0,fromFun=4,rt=mPaaS,name=resA/resB | 4       | 6.9.0      | rt=mPaaS,name=resA/resB | 批量普通资源 |

    Scenario Outline:[5021]初次请求资源时，请求资源接口先返回空对象，然后再异步返回从运营平台请求后的列表。
        Given 关联资源关系接口返回结果为"成功"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type           | Name | Version | Link            | MD5   |
            | app-func-model | resY | 1.3.0   | http://resY.zip | hashY |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"No"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type           | Name | Version | Link            | MD5   |
            | app-func-model | resY | 1.3.0   | http://resY.zip | hashY |
        Then 请求资源列表返回的资源列表为"空对象"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type           | Name | Version | Link            | MD5   |
            | app-func-model | resY | 1.3.0   | http://resY.zip | hashY |
        Examples:
            | Query                                                                                                | Condition                                                                                            | FromFun | AppVersion | ConditionStr                                                              | Interface |
            | appversion=6.9.0,fromFun=6,rt=app-func-model,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt= | appversion=6.9.0,fromFun=6,rt=app-func-model,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt= | 6       | 6.9.0      | rt=app-func-model,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt= | 查询设备应用配置  |

    Scenario Outline:[5022]初次请求资源时，请求资源接口先返回空对象，然后再异步返回从运营平台请求后的列表，并根据返回数据情况更新本地数据中ServerLatest标识。
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 数据库代理根据名字"resA"类型"h5"查询资源结果如下:
            | Id | Type | Name | Version | Link          | MD5   | isServerLatest |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA | <ServerLatest>           |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 关联资源关系接口返回结果为"成功"
        Given 本地数据库更新资源,操作结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link          | MD5   |  isServerLatest |
            | 1     | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |  <AssertServerLatest>          |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Examples:
            | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | Query                                                                                                  | FromFun | AppVersion | ConditionStr                                                                | Interface     |ServerLatest| AssertServerLatest|
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |  true  | false|
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |  false  | true |

    Scenario Outline:[5023]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，当请求资源状态为已下架且无其他版本资源，首次请求应先同步返回本地缓存的资源列表，再异步返回空列表。
      Given 运营平台上部署的"<Interface>"列表如下:
        | Type | Name | Version | Link | MD5 |
      Given 运营平台上查询条件为"<Condition>"的资源状态为:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus        |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |  true          | 1480802243000 | 1480802243000 | 否       | <AsyncResStatus> |
      Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      Given 数据通过查询条件查询资源列表接口执行结果为"成功"
      Given 设置系统当前时间为"20200225 12:30:00"
      Given 关联资源关系接口返回结果为"成功"
      Given 数据库代理根据名字"<SyncResName>"类型"<SyncTypeName>"版本"<SyncResVersion>"查询资源结果如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
      Then 资源数据源的"资源状态"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
      Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
      Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
      Then 请求资源列表返回的资源列表如下:
        | Type           | Name          | Version          | Link          | MD5           |
        | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
      Then 请求资源列表异步操作结果为失败
      Then 更新资源数据库接口被调用"1"次,参数资源包如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus        |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |  true          | 1480802243000 | 1480802243000 | 否        | <AsyncResStatus> |
      Examples:
        | Interface  | Query                                           | SearchTime    | Condition                                       | SyncTypeName | SyncResName | SyncResVersion | SyncResLink       | SyncHashMD5 | SyncPath                             | AsyncResStatus | FromFun | AppVersion | ConditionStr         |
        | 普通资源     | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | 1582603200000 | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | flutter      | resA        | 2.0.0          | http://resA1.zip  | hashA1      | /path/to/resource/flutter/resA@2.0.0 | 1              | 0       | 6.9.0      | rt=flutter,name=resA |
        | 批量普通资源 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | 1582603200000 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | flutter      | resJ        | 2.0.0          | http://resJ1.json | hashJ1      | /path/to/resource/flutter/resJ@2.0.0 | 1              | 4       | 6.9.0      | rt=flutter,name=resJ |

    Scenario Outline:[5024]已成功请求过资源列表，且再次请求时间超过请求时间阈值时，当请求资源状态为已下架且运营平台有其他版本资源，首次请求应先同步返回本地缓存的资源列表，再异步返回从运营平台请求后的列表。
      Given 运营平台上部署的"<Interface>"列表如下:
       | Id | Type            | Name           | Version           | Link           | MD5            |Model | TypeId | ProdNo | TypeCode | Path       | isServerLatest | CreateTime    | UpdateTime    | isPreset |
       | 1  | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |      |        |        |          | <SyncPath> |  true          | 1480802243000 | 1480802243000 | 否        |
      Given 运营平台上查询条件为"<Condition>"的资源状态为:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus        |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> | true          | 1480802243000 | 1480802243000 | 否        | <AsyncResStatus> |
      Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 4  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      Given 数据通过查询条件查询资源列表接口执行结果为"成功"
      Given 设置系统当前时间为"20200225 12:30:00"
      Given 关联资源关系接口返回结果为"成功"
      Given 数据库代理根据名字"<SyncResName>"类型"<SyncTypeName>"版本"<SyncResVersion>"查询资源结果如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |
      When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
      Then 资源数据源的"资源状态"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
      Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
      Then 资源关联关系接口被调用"1"次,传入预置标识为"否"
      Then 请求资源列表返回的资源列表如下:
        | Type           | Name          | Version          | Link          | MD5           |
        | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
      Then 请求资源列表异步操作结果为成功,资源列表如下:
        | Type            | Name           | Version           | Link           | MD5            |
        | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
      Then 更新资源数据库接口被调用"1"次,参数资源包如下:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus        |
        | 1  | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |       |        |        |          | <SyncPath> |  true          | 1480802243000 | 1480802243000 | 否        | <AsyncResStatus> |
      Examples:
        | Interface  | Query                                           | SearchTime    | Condition                                       | SyncTypeName | SyncResName | SyncResVersion | SyncResLink       | SyncHashMD5 | SyncPath                             | AsyncTypeName | AsyncResName | AsyncResVersion | AsyncResLink     | AsyncHashMD5 | AsyncResStatus | FromFun | AppVersion | ConditionStr         |
        | 普通资源     | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | 1582603200000 | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | flutter      | resA        | 2.0.0          | http://resA1.zip  | hashA1      | /path/to/resource/flutter/resA@2.0.0 | flutter       | resA         | 1.0.0           | http://resA.zip  | hashA        | 1              | 0       | 6.9.0      | rt=flutter,name=resA |
        | 批量普通资源 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | 1582603200000 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | flutter      | resJ        | 2.0.0          | http://resJ1.json | hashJ1      | /path/to/resource/flutter/resJ@2.0.0 | flutter       | resJ         | 1.0.0           | http://resJ.json | hashJ        | 1              | 4       | 6.9.0      | rt=flutter,name=resJ |

    Scenario Outline:[5025]已成功请求过资源列表，上一次请求时资源状态为已下架且运营平台无其他版本资源，再次请求应先同步返回空列表，再异步返回从运营平台请求返回空列表。
      Given 运营平台上部署的"<Interface>"列表如下:
        | Type | Name | Version | Link | MD5 |
      Given 运营平台上查询条件为"<Condition>"的资源状态为:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
      Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
        | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
      Given 数据通过查询条件查询资源列表接口执行结果为"成功"
      Given 设置系统当前时间为"20200225 12:30:00"
      Given 关联资源关系接口返回结果为"成功"
      When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
      Then 资源数据源的"资源状态"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
      Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
      Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
      Then 请求资源列表返回的资源列表为"空列表"
      Then 请求资源列表异步操作结果为失败
      Then 更新资源数据库接口被调用"0"次
      Examples:
        | Interface  | Query                                           | SearchTime    | Condition                                       | FromFun | AppVersion | ConditionStr         |
        | 普通资源     | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | 1582603200000 | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | 0       | 6.9.0      | rt=flutter,name=resA |
        | 批量普通资源 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | 1582603200000 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | 4       | 6.9.0      | rt=flutter,name=resJ |

    Scenario Outline:[5026]已成功请求过资源列表，且上一次请求时资源状态为已下架且运营平台有其他版本资源，再次请求应先同步返回空列表，再异步返回从运营平台请求后的列表。
      Given 运营平台上部署的"<Interface>"列表如下:
        | Type            | Name           | Version           | Link           | MD5            |
        | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
      Given 运营平台上查询条件为"<Condition>"的资源状态为:
        | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
      Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
        | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
      Given 数据通过查询条件查询资源列表接口执行结果为"成功"
      Given 设置系统当前时间为"20200225 12:30:00"
      Given 关联资源关系接口返回结果为"成功"
      When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
      Then 资源数据源的"资源状态"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
      Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
      Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
      Then 资源关联关系接口被调用"1"次,传入预置标识为"否"
      Then 请求资源列表返回的资源列表为"空列表"
      Then 请求资源列表异步操作结果为成功,资源列表如下:
        | Type            | Name           | Version           | Link           | MD5            |
        | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
      Then 更新资源数据库接口被调用"0"次
      Examples:
        | Interface  | Query                                           | SearchTime    | Condition                                       | AsyncTypeName | AsyncResName | AsyncResVersion | AsyncResLink     | AsyncHashMD5 | FromFun | AppVersion | ConditionStr         |
        | 普通资源     | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | 1582603200000 | appversion=6.9.0,fromFun=0,rt=flutter,name=resA | flutter       | resA         | 1.0.0           | http://resA.zip  | hashA        | 0       | 6.9.0      | rt=flutter,name=resA |
        | 批量普通资源 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | 1582603200000 | appversion=6.9.0,fromFun=4,rt=flutter,name=resJ | flutter       | resJ         | 1.0.0           | http://resJ.json | hashJ        | 4       | 6.9.0      | rt=flutter,name=resJ |

    @ios_ignore
    Scenario Outline:[5027]当加锁功能开关状态为关闭，初次请求资源，当运营平台的资源查询接口出错时，则请求资源接口返回空对象，异步回调接口返回失败。
        Given 设置移除锁功能开关状态为"开启"
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type | Name | Version | Link | MD5 |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Condition                                                                                              | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5028]当加锁功能开关状态为关闭，初次请求资源时，运营平台正常返回数据后，关联资源关系失败，则请求资源接口返回空对象，异步回调接口返回失败。
        Given 设置移除锁功能开关状态为"开启"
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 关联资源关系接口返回结果为"失败"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | Query                                                                                                  | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5029]当加锁功能开关状态为关闭，初次请求资源时，请求资源接口先返回空对象，然后再异步返回从运营平台请求后的列表。
        Given 设置移除锁功能开关状态为"开启"
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Examples:
            | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | Query                                                                                                  | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 2.0.0      | http://resA.zip         | hashA   | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5030]当加锁功能开关状态为关闭，已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，接口返回和异步回调返回结果均为缓存的资源列表。
        Given 设置移除锁功能开关状态为"开启"
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"0"次
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5031]当加锁功能开关状态为关闭，已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，数据库查询请求条件失败时,请求资源接口先返回空对象,再异步返回从运营平台请求后的列表
        Given 设置移除锁功能开关状态为"开启"
        Given 数据库查询请求条件接口执行结果为"空对象"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"空对象",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA    | 1.0.0      | http://resA1.zip        | hashA1  | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | deviceCustomInfo | resZ         | 1.2.0           | http://resZ.zip         | hashZ        | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5032]当加锁功能开关状态为关闭，已成功请求过资源列表，且再次请求时间未超过请求时间阈值时，数据库查询关联资源列表失败时,则请求资源接口返回空列表，异步回调接口返回失败。
        Given 设置移除锁功能开关状态为"开启"
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type       | Name      | Version      | Link      | MD5       |
            | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> |
        Given 设置系统当前时间为"20200225 12:00:05"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"0"次
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"0"次,传入预置标识为"否"
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为失败
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 1582603200000 | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | config           | cfg0    | 1.0.1      | http://cfg0.signed.json | hashC1  | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                    | 设备配置资源   |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 1582603200000 | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | deviceCustomInfo | resZ    | 1.0.1      | http://resZ.zip         | hashZ   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                       | 设备自定义资源 |

    @ios_ignore
    Scenario Outline:[5033]当加锁功能开关状态为关闭，已成功请求过资源列表，且再次请求时间超过请求时间阈值时，应先返回本地缓存的资源列表，再异步返回从运营平台请求后的列表。
        Given 设置移除锁功能开关状态为"开启"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Given 数据通过查询条件查询资源列表接口执行结果为"成功"
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | SyncTypeName     | SyncResName | SyncResVersion | SyncResLink             | SyncHashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA        | 1.0.0          | http://resA1.zip        | hashA1      | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | 1582603200000 | appversion=6.9.0,fromFun=0,rt=configAPP,name=resJ                                                      | configAPP        | resJ        | 1.0.0          | http://resJ1.json       | hashJ1      | configAPP        | resJ         | 2.0.0           | http://resJ.json        | hashJ        | 0       | 6.9.0      | rt=configAPP,name=resJ                                                      | 普通资源       |

    @ios_ignore
    Scenario Outline:[5034]当加锁功能开关状态为关闭，已成功请求过资源列表，且再次请求时间超过请求时间阈值时，数据库查询关联资源列表失败时，则请求资源接口返回空列表，再异步返回从运营平台请求后的列表。
        Given 设置移除锁功能开关状态为"开启"
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        Given 运营平台上部署的"<Interface>"列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Given 运营平台上查询条件为"<Condition>"的资源状态为:
            | Id | Type           | Name          | Version          | Link          | MD5           | Model | TypeId | ProdNo | TypeCode | Path       | ResStatus        |
        Given 查询条件为"<Query>",查询时间为"<SearchTime>"相关联的资源列表如下:
            | Type           | Name          | Version          | Link          | MD5           |
            | <SyncTypeName> | <SyncResName> | <SyncResVersion> | <SyncResLink> | <SyncHashMD5> |
        Given 设置系统当前时间为"20200225 12:30:00"
        Given 关联资源关系接口返回结果为"成功"
        When 调用请求资源列表方法,参数请求条件为"<Condition>",立即请求参数为"NO"
        Then 资源数据源的"<Interface>"接口被调用"1"次,参数请求条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 资源关联关系接口被调用"1"次,传入预置标识为"否",参数查询条件为"<Query>",资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Then 请求资源列表返回的资源列表为"空列表"
        Then 请求资源列表异步操作结果为成功,资源列表如下:
            | Type            | Name           | Version           | Link           | MD5            |
            | <AsyncTypeName> | <AsyncResName> | <AsyncResVersion> | <AsyncResLink> | <AsyncHashMD5> |
        Examples:
            | Query                                                                                                  | SearchTime    | Condition                                                                                              | SyncTypeName     | SyncResName | SyncResVersion | SyncResLink             | SyncHashMD5 | AsyncTypeName    | AsyncResName | AsyncResVersion | AsyncResLink            | AsyncHashMD5 | FromFun | AppVersion | ConditionStr                                                                | Interface      |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | 1582603200000 | appversion=6.9.0,fromFun=0,rt=h5,name=resA                                                             | h5               | resA        | 1.0.0          | http://resA1.zip        | hashA1      | h5               | resA         | 2.0.0           | http://resA.zip         | hashA        | 0       | 6.9.0      | rt=h5,name=resA                                                             | 普通资源       |
            | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1582603200000 | appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | h5               | resD        | 1.1.0          | http://resD1.zip        | hashD1      | h5               | resD         | 1.1.1           | http://resD.zip         | hashD        | 2       | 6.9.0      | rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 设备资源       |

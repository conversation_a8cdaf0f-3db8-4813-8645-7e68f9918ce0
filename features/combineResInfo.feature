Feature: combineResInfo
    功能范围：
    主要提供普通资源和设备资源自动升级，批量预加载、自动安装最新资源功能
    单资源自动升级指安装指定资源与运营平台提供的版本一致，整个业务过程是从运营平台查询指定资源，然后通过选择器选择单个资源，对选择后的资源进行升级。
    提供单资源自动升级接口
    - 如果单资源自动升级未完成、或失败，旧的资源包依旧存在且能正常使用
    - 自动升级普通资源，指定资源类型和名称，通过选择器选择单个资源信息，当选择器为空时，直接返回被安装资源信息为空，有选择器时，直接返回根据选择器选择后的已安装资源信息，异步更新完普通资源列表后，对通过选择器选择的资源信息进行安装，安装完成后将安装结果反馈。
    - 自动升级设备资源，指定资源类型，指定型号、类型ID、类型代码、产品编码，通过选择器选择单个资源信息，当选择器为空时，直接返回被安装资源信息为空，有选择器时，直接返回根据选择器选择后的已安装资源信息，异步更新完设备资源列表后，对通过选择器选择的资源信息进行安装，安装完成后将安装结果反馈。

    批量预加载普通资源指从运营平台查询所有指定资源类型和资源名称的资源信息，然后逐个安装已查询到的资源信息
    提供批量预加载普通资源接口
    - 预加载资源信息，指包含资源名称和资源类型的数据结构
    - 当请求资源中有一个失败，回调失败结果，继续更新，完成更新后开始逐个安装

    自动安装最新资源指从本地获取到未安装的各类型最新资源列表，根据传入的资源安装顺序对查询到的最新资源列表进行排序，最后逐个进行安装
    提供自动安装最新资源接口
    - 需要优先安装的资源列表，指安装资源时需要按照优先资源列表顺序进行安装

    外部依赖：
    1.资源请求代理
    2.资源安装代理
    3.本地数据存储代理
    4.文件系统代理

    接口说明：
    1.自动升级普通资源
    根据资源类型和名称获取当前的资源信息，并自动升级到运营平台提供的资源；
    如果正在清空数据，返回"不能在清空数据时执行其他操作"失败结果；
    如果选择器为空对象，返回"未设置选择器"失败结果，反之同步返回选择器匹配的已安装的资源；
    如果运营平台请求资源失败，返回"请求资源信息错误"失败结果；
    如果运营平台返回的资源数据根据选择器选择后，不存在数据，返回"从列表中筛选信息失败"失败结果；
    资源更新完成后，对选择器匹配后的资源进行安装，回调安装结果。
    2.自动升级设备资源
    根据设备更新条件，获取当前的设备资源信息，并自动升级到运营平台提供的资源；
    如果正在清空数据，返回"不能在清空数据时执行其他操作"失败结果；
    如果选择器为空对象，返回"未设置选择器"失败结果，反之同步返回选择器匹配的已安装的资源；
    如果运营平台请求资源失败，返回"请求资源信息错误"失败结果；
    如果运营平台返回的资源数据根据选择器匹配后，不存在数据，返回"从列表中筛选信息失败"失败结果；
    资源更新完成后，对选择器匹配后的资源进行安装，回调安装结果。
    3.批量预加载资源
    根据需要预加载的资源列表，进行更新，更新完成后逐个安装资源；
    如果预加载资源列表为空，返回"预加载资源列表不能为空"失败结果；
    逐个请求资源，每个资源更新结果通过回调返回，当请求资源中有一个失败，回调失败结果，继续更新；
    更新完成后，逐个安装资源，每个资源安装结果通过回调返回。
    4.自动安装最新资源
    根据缓存数据获取到各个类型最新未安装的资源列表，根据传入的资源安装顺序对查询到的最新资源列表进行排序，逐个安装资源；
    如果资源列表为空列表，返回"没有需要更新的资源"成功结果；
    安装传入的资源安装顺序列表，逐个安装资源，每个资源安装结果通过回调返回。

    Background:
        Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
        Given 资源请求代理使用模拟的
        Given 资源安装代理使用模拟的
        Given 关联资源关系代理使用模拟的
        Given 资源请求代理请求资源接口同步返回资源列表如下:
            | Id | Type           | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                      |
            | 1  | h5             | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0     |
            | 2  | h5             | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1     |
            | 3  | app-func-model | resD | 1.0.2   | http://resD.zip | hashD | modelD | typeIdD | prodNoD | typeCodeD | /path/to/resource/appFuncModel/resD@1.0.2 |

    Scenario Outline: [7000]处于执行清空操作时,设置类型和名字匹配的选择器,执行自动升级资源操作,自动升级资源结果为失败,返回资源包为空对象
        Given 正在执行清除本地缓存操作
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"<SelectInfo>"
        Then 资源请求代理接口被调用"0"次
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为空对象
        Then 自动升级"<APIType>"回调结果为"失败",资源包为空对象
        Examples:
            | APIType  | DeviceInfo                       | Type | Name | SelectInfo |
            | 普通资源 |                                  | h5   | resA | h5,resA    |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | h5   | resC | h5,resC    |

    Scenario Outline: [7001]已经成功执行完清除缓存操作,执行自动升级资源操作,设置选择器为空对象,自动升级资源结果为失败,返回资源包为空对象
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"空对象"
        Then 资源请求代理接口被调用"0"次
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为空对象
        Then 自动升级"<APIType>"回调结果为"失败",资源包为空对象
        Examples:
            | APIType  | DeviceInfo                       | Type | Name |
            | 普通资源 |                                  | h5   | resA |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | h5   | resC |

    Scenario Outline: [7002]在资源请求代理直接返回对象后,当选择器匹配到已安装资源,返回资源包不为空对象,当资源请求代理回调失败结果时,自动升级回调结果为失败
        Given 文件系统中"存在"的"文件"路径如下:
            | Path                                  |
            | /path/to/resource/H5ResPkg/resA@1.0.0 |
            | /path/to/resource/H5ResPkg/resC@1.0.1 |
        Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
            | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"<SelectInfo>"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type   | Name   | DeviceInfo   |
            | <Type> | <Name> | <DeviceInfo> |
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为
            | Id   | Type   | Name   | Version   | Link   | MD5   | Path   | DeviceInfo   |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Path> | <DeviceInfo> |
        Then 自动升级"<APIType>"回调结果为"失败",资源包为空对象
        Examples:
            | APIType  | DeviceInfo                       | Id | Type | Name | Version | Link            | MD5   | Path                                  | SelectInfo |
            | 普通资源 |                                  | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/H5ResPkg/resA@1.0.0 | h5,resA    |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | /path/to/resource/H5ResPkg/resC@1.0.1 | h5,resC    |

    Scenario Outline: [7003]在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调失败结果时,自动升级回调结果为失败
        Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
            | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path |
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"<SelectInfo>"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type   | Name   | DeviceInfo   |
            | <Type> | <Name> | <DeviceInfo> |
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为空对象
        Then 自动升级"<APIType>"回调结果为"失败",资源包为空对象
        Examples:
            | APIType  | DeviceInfo                       | Type     | Name | SelectInfo |
            | 普通资源 |                                  | apicloud | resA | h5,resA    |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | apicloud | resC | h5,resC    |

    Scenario Outline: [7004]在资源请求代理直接返回对象后,当选择器未匹配到资源,返回空对象,当资源请求代理回调成功结果时,如果选择器未匹配到资源,自动升级回调结果为失败
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1 |
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"<SelectInfo>"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type   | Name   | DeviceInfo   |
            | <Type> | <Name> | <DeviceInfo> |
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为空对象
        Then 自动升级"<APIType>"回调结果为"失败",资源包为空对象
        Examples:
            | APIType  | DeviceInfo                       | Type             | Name | SelectInfo            |
            | 普通资源 |                                  | h5               | resA | h5,resB               |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | h5               | resC | h5,resB               |
            | 设备资源 | modelD,typeIdD,typeCodeD,prodNoD | deviceCustomInfo | resD | deviceCustomInfo,resB |
            | 设备资源 | modelE,typeIdE,typeCodeE,prodNoE | config           | resE | config,resB           |

    Scenario Outline: [7005]在资源请求代理直接返回对象后,当选择器匹配到已安装资源,返回资源包不为空对象,当资源请求代理回调成功结果时,如果选择器匹配到已安装资源,自动升级回调结果为成功
        Given 文件系统中"存在"的"文件"路径如下:
            | Path                                      |
            | /path/to/resource/H5ResPkg/resA@1.0.0     |
            | /path/to/resource/H5ResPkg/resC@1.0.1     |
            | /path/to/resource/appFuncModel/resD@1.0.2 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type           | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                      |
            | 1  | h5             | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0     |
            | 2  | h5             | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1     |
            | 3  | app-func-model | resD | 1.0.2   | http://resD.zip | hashD | modelD | typeIdD | prodNoD | typeCodeD | /path/to/resource/appFuncModel/resD@1.0.2 |
        When 执行自动升级"<APIType>"操作,资源类型为"<Type>",资源名称为"<Name>",设备信息为"<DeviceInfo>",选择器信息为"<SelectInfo>"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type   | Name   | DeviceInfo   | FromFunc   |
            | <Type> | <Name> | <DeviceInfo> | <FromFunc> |
        Then 资源安装代理接口被调用"0"次
        Then 自动升级"<APIType>"直接返回已安装资源为
            | Id   | Type   | Name   | Version   | Link   | MD5   | Path   | DeviceInfo   |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Path> | <DeviceInfo> |
        Then 自动升级"<APIType>"回调结果为"成功",资源包为
            | Id   | Type   | Name   | Version   | Link   | MD5   | Path   | DeviceInfo   |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Path> | <DeviceInfo> |
        Examples:
            | APIType  | DeviceInfo                       | FromFunc | Id | Type           | Name | Version | Link            | MD5   | Path                                      | SelectInfo          |
            | 普通资源 |                                  | 0        | 1  | h5             | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/H5ResPkg/resA@1.0.0     | h5,resA             |
            | 设备资源 | modelC,typeIdC,typeCodeC,prodNoC | 2        | 2  | h5             | resC | 1.0.1   | http://resC.zip | hashC | /path/to/resource/H5ResPkg/resC@1.0.1     | h5,resC             |
            | 设备资源 | modelD,typeIdD,typeCodeD,prodNoD | 6        | 3  | app-func-model | resD | 1.0.2   | http://resD.zip | hashD | /path/to/resource/appFuncModel/resD@1.0.2 | app-func-model,resD |

    Scenario:[7006]普通资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源成功,自动升级回调结果为成功
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        When 执行自动升级"普通资源"操作,资源类型为"h5",资源名称为"resA",设备信息为"",选择器信息为"h5,resA"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type | Name | DeviceInfo |
            | h5   | resA |            |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |      |            |
        Then 自动升级"普通资源"直接返回已安装资源为空对象
        Then 自动升级"普通资源"回调结果为"成功",资源包为
            | Id | Type | Name | Version | Link            | MD5   | Path                                  | DeviceInfo |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/H5ResPkg/resA@1.0.0 |            |

    Scenario:[7006-1]设备资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源成功,自动升级回调结果为成功
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC |      |
        When 执行自动升级"设备资源"操作,资源类型为"h5",资源名称为"resC",设备信息为"modelC,typeIdC,typeCodeC,prodNoC",选择器信息为"h5,resC"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type | Name | DeviceInfo                       |
            | h5   | resC | modelC,typeIdC,typeCodeC,prodNoC |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo                       |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC |      | modelC,typeIdC,typeCodeC,prodNoC |
        Then 自动升级"设备资源"直接返回已安装资源为空对象
        Then 自动升级"设备资源"回调结果为"成功",资源包为
            | Id | Type | Name | Version | Link            | MD5   | Path                                  | DeviceInfo                       |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | /path/to/resource/H5ResPkg/resC@1.0.1 | modelC,typeIdC,typeCodeC,prodNoC |


    Scenario:[7006-2]普通资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源成功,自动升级回调结果为成功
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type   | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                |
            | 1  | routes | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/routes/resA@1.0.0 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type   | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | routes | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        When 执行自动升级"普通资源"操作,资源类型为"routes",资源名称为"resA",设备信息为"",选择器信息为"routes,resA"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type   | Name | DeviceInfo |
            | routes | resA |            |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type   | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | routes | resA | 1.0.0   | http://resA.zip | hashA |      |            |
        Then 自动升级"普通资源"直接返回已安装资源为空对象
        Then 自动升级"普通资源"回调结果为"成功",资源包为
            | Id | Type   | Name | Version | Link            | MD5   | Path                                | DeviceInfo |
            | 1  | routes | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/routes/resA@1.0.0 |            |

    Scenario:[7006-3]普通资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源成功,自动升级回调结果为成功
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type  | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                               |
            | 1  | audio | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/audio/resA@1.0.0 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type  | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | audio | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        When 执行自动升级"普通资源"操作,资源类型为"audio",资源名称为"resA",设备信息为"",选择器信息为"audio,resA"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type  | Name | DeviceInfo |
            | audio | resA |            |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type  | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | audio | resA | 1.0.0   | http://resA.zip | hashA |      |            |
        Then 自动升级"普通资源"直接返回已安装资源为空对象
        Then 自动升级"普通资源"回调结果为"成功",资源包为
            | Id | Type  | Name | Version | Link            | MD5   | Path                               | DeviceInfo |
            | 1  | audio | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/audio/resA@1.0.0 |            |

    Scenario:[7006-4]普通资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源成功,自动升级回调结果为成功
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type    | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                 |
            | 1  | picture | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/picture/resA@1.0.0 |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type    | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | picture | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        When 执行自动升级"普通资源"操作,资源类型为"picture",资源名称为"resA",设备信息为"",选择器信息为"picture,resA"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type    | Name | DeviceInfo |
            | picture | resA |            |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type    | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | picture | resA | 1.0.0   | http://resA.zip | hashA |      |            |
        Then 自动升级"普通资源"直接返回已安装资源为空对象
        Then 自动升级"普通资源"回调结果为"成功",资源包为
            | Id | Type    | Name | Version | Link            | MD5   | Path                                 | DeviceInfo |
            | 1  | picture | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/picture/resA@1.0.0 |            |

    Scenario: [7007]普通在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源失败,自动升级回调结果为失败。
        Given 资源安装代理接口回调"失败"，资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        When 执行自动升级"普通资源"操作,资源类型为"h5",资源名称为"resA",设备信息为"",选择器信息为"h5,resA"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type | Name | DeviceInfo |
            | h5   | resA |            |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |      |            |
        Then 自动升级"普通资源"直接返回已安装资源为空对象
        Then 自动升级"普通资源"回调结果为"失败",资源包为
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |      |            |

    Scenario: [7007-1]设备资源在资源请求代理直接返回对象后,当选择器未匹配到已安装资源,返回空对象,当资源请求代理回调成功结果时,安装匹配到的资源失败,自动升级回调结果为失败。
        Given 资源安装代理接口回调"失败"，资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC |      |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC |      |
        When 执行自动升级"设备资源"操作,资源类型为"h5",资源名称为"resC",设备信息为"modelC,typeIdC,typeCodeC,prodNoC",选择器信息为"h5,resC"
        Then 资源请求代理接口被调用"1"次,参数如下:
            | Type | Name | DeviceInfo                       |
            | h5   | resC | modelC,typeIdC,typeCodeC,prodNoC |
        Then 资源安装代理接口被调用"1"次,参数如下:
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo                       |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC |      | modelC,typeIdC,typeCodeC,prodNoC |
        Then 自动升级"设备资源"直接返回已安装资源为空对象
        Then 自动升级"设备资源"回调结果为"失败",资源包为
            | Id | Type | Name | Version | Link            | MD5   | Path | DeviceInfo                       |
            | 2  | h5   | resC | 1.0.1   | http://resC.zip | hashC |      | modelC,typeIdC,typeCodeC,prodNoC |

    Scenario: [7008]处于执行清空操作时,执行批量预加载资源操作,批量预加载资源结果为失败
        Given 正在执行清除本地缓存操作
        When 执行批量预加载资源操作,预加载资源列表为
            | Type     | Name |
            | h5       | resA |
            | apicloud | resB |
            | mPaaS    | resM |
        Then 资源请求代理接口被调用"0"次
        Then 资源安装代理接口被调用"0"次
        Then 批量预加载资源结果为"失败"

    Scenario Outline: [7009]已经成功执行完清除缓存操作,执行批量预加载资源操作,预加载资源列表为空对象或空数组,批量预加载资源结果为失败
        When 执行批量预加载资源操作,预加载资源列表为"<Data>"
        Then 资源请求代理接口被调用"0"次
        Then 资源安装代理接口被调用"0"次
        Then 批量预加载资源结果为"失败"
        Examples:
            | Data   |
            | 空对象 |
            | 空数组 |

    Scenario: [7010]已经成功执行完清除缓存操作,执行批量预加载资源操作,资源请求代理部分成功,请求失败资源回调失败结果，更新完成后开始安装,安装资源部分成功,批量预加载资源结果为每个安装资源回调结果
        Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
        Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 3  | apicloud | resB | 1.0.3   | http://resB.zip | hashB |       |        |        |          |      |
            | 4  | mPaaS    | resE | 1.0.4   | http://resE.zip | hashE |       |        |        |          |      |
        Given 资源安装代理接口回调"失败"，资源信息如下:
            | Id | Type  | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 4  | mPaaS | resE | 1.0.4   | http://resE.zip | hashE |       |        |        |          |      |
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
            | 3  | apicloud | resB | 1.0.3   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/apicloud/resB@1.0.3 |
        When 执行批量预加载资源操作,预加载资源列表为
            | Type     | Name |
            | h5       | resA |
            | apicloud | resB |
            | mPaaS    | resE |
        Then 资源请求代理接口被调用"3"次,参数如下:
            | Type     | Name | DeviceInfo |
            | h5       | resA |            |
            | apicloud | resB |            |
            | mPaaS    | resE |            |
        Then 资源安装代理接口被调用"2"次,参数如下:
            | Id | Type     | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 3  | apicloud | resB | 1.0.3   | http://resB.zip | hashB |      |            |
            | 4  | mPaaS    | resE | 1.0.4   | http://resE.zip | hashE |      |            |
        Then 批量预加载资源结果为
            | Id | Type     | Name | Version | Link            | MD5   | Path                                  | Result |
            |    |          |      |         |                 |       |                                       | 失败   |
            | 3  | apicloud | resB | 1.0.3   | http://resB.zip | hashB | /path/to/resource/apicloud/resB@1.0.3 | 成功   |
            | 4  | mPaaS    | resE | 1.0.4   | http://resE.zip | hashE | /path/to/resource/mPaaS/resE@1.0.4    | 失败   |

    Scenario: [7011]处于执行清空操作时,执行自动安装最新资源操作,自动安装最新资源结果为失败
        Given 正在执行清除本地缓存操作
        When 执行自动安装最新资源操作,需要优先安装的资源列表为"空对象"
        Then 数据库根据名字和类型查询接口被调用"0"次
        Then 资源安装代理接口被调用"0"次
        Then 自动安装最新资源结果为"失败"

    Scenario: [7012]已经成功执行完清除缓存操作,执行自动安装最新资源操作,获取到本地未安装资源列表为空数组,自动安装最新资源结果为成功
        Given 文件系统中"存在"的"文件"路径如下:
            | Path                                  |
            | /path/to/resource/H5ResPkg/resA@1.0.0 |
            | /path/to/resource/H5ResPkg/resC@1.0.1 |
            | /path/to/resource/ApiCloud/cfg1@1.2.0 |
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | ResId | Type   | Name | Version | Link                    | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest | CreateTime | UpdateTime |
            | 1     | h5     | resA | 1.0.0   | http://resA.zip         | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           | 1622424933 | 1622424933 |
            | 2     | h5     | resC | 1.0.1   | http://resC.zip         | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1 | true           | 1622424933 | 1622424933 |
            | 3     | config | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1 | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           | 1622424933 | 1622424933 |
        When 执行自动安装最新资源操作,需要优先安装的资源列表如下:
            | Type     | Name |
            | h5       | resA |
            | apicloud | resB |
            | mPaaS    | resM |
        Then 数据库根据名字和类型查询接口被调用"1"次
        Then 资源安装代理接口被调用"0"次
        Then 自动安装最新资源结果为"成功"

    Scenario Outline: [7013]已经成功执行完清除缓存操作,执行自动安装最新资源操作,需要优先安装的资源列表为空对象或者数组,当获取到本地未安装资源列表不为空数组,自动安装最新资源结果为成功
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 9  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |       |        |        |          |      |
            | 10 | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |       |        |        |          |      |
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
            | 9  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/apicloud/resB@2.0.1 |
            | 10 | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |       |        |        |          | /path/to/resource/mPaaS/resM@1.0.1    |
        When 执行自动安装最新资源操作,需要优先安装的资源列表为"<Data>"
        Then 数据库根据名字和类型查询接口被调用"1"次
        Then 资源安装代理接口被调用"2"次,参数如下:
            | Id | Type     | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 9  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |      |            |
            | 10 | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |      |            |
        Then 自动安装最新资源结果为
            | Id | Type     | Name | Version | Link            | MD5   | Path                                  | Result |
            | 9  | apicloud | resB | 2.0.1   | http://resB.zip | hashB | /path/to/resource/apicloud/resB@2.0.1 | 成功   |
            | 10 | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM | /path/to/resource/mPaaS/resM@1.0.1    | 成功   |
        Examples:
            | Data   |
            | 空对象 |
            | 空数组 |

    Scenario: [7014]已经成功执行完清除缓存操作,执行自动安装最新资源操作,获取到本地未安装资源列表不为空,根据传入的资源安装顺序对查询到的最新资源列表进行排序,逐个安装资源,自动安装最新资源结果为安装资源的回调结果
        Given 文件系统中"存在"的"文件"路径如下:
            | Path                                  |
            | /path/to/resource/H5ResPkg/resA@1.0.0 |
            | /path/to/resource/H5ResPkg/resC@1.0.1 |
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  |
            | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 |
            | 2  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |        |         |         |           |                                       |
            | 3  | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |        |         |         |           |                                       |
            | 4  | h5       | resC | 1.0.1   | http://resC.zip | hashC | modelC | typeIdC | prodNoC | typeCodeC | /path/to/resource/H5ResPkg/resC@1.0.1 |
        Given 资源安装代理接口回调"失败"，资源信息如下:
            | Id | Type  | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
            | 3  | mPaaS | resM | 1.0.1   | http://resM.zip | hashM |       |        |        |          |      |
        Given 资源安装代理接口回调"成功"，资源信息如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
            | 2  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/apicloud/resB@2.0.1 |
        When 执行自动安装最新资源操作,需要优先安装的资源列表如下:
            | Type     | Name |
            | h5       | resA |
            | mPaaS    | resM |
            | apicloud | resB |
        Then 数据库根据名字和类型查询接口被调用"1"次
        Then 资源安装代理接口被调用"2"次,参数如下:
            | Id | Type     | Name | Version | Link            | MD5   | Path | DeviceInfo |
            | 3  | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |      |            |
            | 2  | apicloud | resB | 2.0.1   | http://resB.zip | hashB |      |            |
        Then 自动安装最新资源结果为
            | Id | Type     | Name | Version | Link            | MD5   | Path                                  | Result |
            | 3  | mPaaS    | resM | 1.0.1   | http://resM.zip | hashM |                                       | 失败   |
            | 2  | apicloud | resB | 2.0.1   | http://resB.zip | hashB | /path/to/resource/apicloud/resB@2.0.1 | 成功   |


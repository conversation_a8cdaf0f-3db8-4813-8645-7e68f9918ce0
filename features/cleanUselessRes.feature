Feature: 清理无用资源
  功能范围:
  主要用于测试组件中清理无用资源相关的功能。主要提供清理无用资源接口
  - 无用资源指的是
  - 非当前最新已安装资源信息
  - 无用资源所对应的关联关系
  - 无资源所对应的查询条件
  - 数据存储路径下的文件没有关联到本地数据存储的资源信息

  外部依赖:
  - 本地数据存储代理
  - 文件系统代理
  - 线程调度器

  接口说明:
  1.清理无用资源接口
  该接口接收延时时间参数，用于在子线程中延时开启清理无用资源；
  如果延时时间参数小于零，将延时时间参数重置为零；
  如果本地已安装的资源信息为空时，删除数据存储的根目录；
  如果数据存储的根路径下的文件夹没有关联到本地数据存储中的资源信息，那么删除该文件夹；
  如果查询到的关联列表为空，删除本地数据存储中的资源信息和数据存储中对应的文件夹；
  如果资源信息无匹配的查询条件，则删除该资源，同时删除与之相关的条件信息；
  如果已安装资源列表中，存在非最新已安装资源，且非主题资源的资源。则删除该资源，同时删除与之相关的条件信息；

  Background:
    Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"

  Scenario:[11000]正在执行清除缓存操作时，执行清理无用资源操作，接口无操作直接返回
    Given 正在执行清除本地缓存操作
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"0"次
    Then 数据库查询所有关联关系接口被调用"0"次

  Scenario Outline:[11001]本地数据存储没有已安装的资源信息时，执行清理无用资源操作，删除数据存储根目录下的子目录
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/H5ResPkg | 文件夹  |
    Given 数据库根据名字和类型查询接口返回"<Result>"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"2"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"1"次,参数路径信息为"/path/to/resource/H5ResPkg"
    Then 数据库查询所有查询条件接口被调用"2"次
    Then 数据库查询所有关联关系接口被调用"2"次
    Examples:
      | Result |
      | 空对象    |
      | 空数组    |

  Scenario Outline:[11002]本地数据存储没有已安装的资源信息时，执行清理无用资源操作，删除数据存储根目录下的子目录
    Given 数据库根据名字和类型查询接口返回"<Result>"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"2"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"2"次
    Then 数据库查询所有关联关系接口被调用"2"次
    Examples:
      | Result |
      | 空对象    |
      | 空数组    |

  Scenario:[11003]本地数据存储没有已安装的资源信息时，执行清理无用资源操作，删除数据存储的根目录
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           |      | true           | 1480802243000 | 1480802243000 | 否        |
      | 4  | h5               | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | true           | 1480802246000 | 1480802246000 | 否        |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |        |         | prodNoB |           |      | true           | 1480802250000 | 1480802250000 | 否        |
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"2"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"2"次
    Then 数据库查询所有关联关系接口被调用"2"次

  Scenario:[11004] 执行清理无用资源操作，数据存储目录下的文件没有关联的资源信息时，删除相对应的文件目录
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否        |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否        |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.9.0      | 0       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/H5ResPkg | 文件夹  |
      | /path/to/resource/ApiCloud | 文件夹  |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹  |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹  |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg            | H5ResPkg   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | resD@1.0.2 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"1"次,参数路径信息为"/path/to/resource/H5ResPkg/resD@1.0.2"
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次

  Scenario:[11005] 执行清理无用资源操作，当已安装资源出现同一名称资源多个不同版本，删除非最新已安装资源，同时删除无用资源对应的条件和关联关系
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否        |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否        |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 否        |
      | 4  | h5       | resI | 2.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@2.0.0 | true           | 1480802249000 | 1480802249000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.8.0      | 0       | h5       | resI |
      | 3           | 4     | 6.8.0      | 4       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/H5ResPkg | 文件夹  |
      | /path/to/resource/ApiCloud | 文件夹  |
      | /path/to/resource/Temp     | 文件夹  |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹  |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg            | H5ResPkg   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/Temp                | Temp       |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | resI@2.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"1"次,参数路径信息为"/path/to/resource/H5ResPkg/resI@1.0.0"
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | h5   | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 否        |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                              |
      | appversion=6.8.0,fromFun=0,rt=h5,name=resI,resId=3 |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |
      | 3     |

  Scenario:[11006] 本地已缓存资源都未标识为最新资源，执行清理无用资源操作，如果同一名称和类型的多个已安装资源都未标识为最新资源，保留一个最新版本资源。
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | false          | 1480802243000 | 1480802243000 | 否        |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | false          | 1480802244000 | 1480802244000 | 否        |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 否        |
      | 4  | h5       | resI | 2.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@2.0.0 | false          | 1480802250000 | 1480802250000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.9.0      | 0       | h5       | resI |
      | 3           | 4     | 6.9.0      | 4       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/H5ResPkg | 文件夹  |
      | /path/to/resource/ApiCloud | 文件夹  |
      | /path/to/resource/Temp     | 文件夹  |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹  |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg            | H5ResPkg   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/Temp                | Temp       |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | resI@2.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"1"次,参数路径信息为"/path/to/resource/H5ResPkg/resI@1.0.0"
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | h5   | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 否        |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                              |
      | appversion=6.9.0,fromFun=0,rt=h5,name=resI,resId=3 |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |
      | 3     |

  Scenario:[11007] 执行清理无用资源操作，同一名称和类型资源信息，有多个已安装版本，删除未标识为最新资源的版本
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否        |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否        |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否        |
      | 4  | h5       | resI | 2.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@2.0.0 | false          | 1480802250000 | 1480802250000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
      | 3           | 3     | 6.9.0      | 4       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/H5ResPkg | 文件夹  |
      | /path/to/resource/ApiCloud | 文件夹  |
      | /path/to/resource/Temp     | 文件夹  |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹  |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg            | H5ResPkg   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/Temp                | Temp       |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 | resI@2.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@2.0.0 |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"1"次,参数路径信息为"/path/to/resource/H5ResPkg/resI@2.0.0"
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 4  | h5   | resI | 2.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@2.0.0 | false          | 1480802250000 | 1480802250000 | 否        |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                              |
      | appversion=6.9.0,fromFun=0,rt=h5,name=resI,resId=4 |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |
      | 4     |

  Scenario:[11008] 执行清理无用资源操作，一个资源关联多个查询条件，该资源为已安装的有用资源，资源所对应的所有查询条件不被删除
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 1     | 6.8.0      | 0       | h5       | resB |
      | 2           | 1     | 6.9.0      | 0       | apicloud | resB |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/ApiCloud | 文件夹  |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | resB@1.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
    Given 数据库批量删除资源列表接口返回结果为"失败"
    Given 数据库批量删除资源查询条件接口返回结果为"失败"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime | isPreset |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |

  Scenario:[11009] 执行清理无用资源操作，资源信息的类型为配置文件，且未关联到当前APP版本时，不会删除本地存储资源信息以及资源相对应的文件目录
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type   | Name | Version | Link             | MD5   | Model  | TypeId  | ProdNo  | TypeCode | Path                                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 6  | config | resE | 1.0.0   | http://resE.json | hashE | modelE | typeIdE | prodNoE |          | /path/to/resource/DeviceConfig/<EMAIL> | true           | 1480802244000 | 1480802244000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt     | name | md     | ti      | pn      | tc | dnt |
      | 1           | 6     | 6.8.0      | 1       | config | resE | modelE | typeIdE | prodNoE |    |     |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                    | Type |
      | /path/to/resource/DeviceConfig | 文件夹  |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                                  |
      | /path/to/resource/DeviceConfig/<EMAIL> |
    Given 数据库批量删除资源列表接口返回结果为"失败"
    Given 数据库批量删除资源查询条件接口返回结果为"失败"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime | isPreset |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |

  Scenario:[11010] 执行清理无用资源操作，查询到的所有关联信息列表为空时，不会删除临时目录下的文件
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath            | Type |
      | /path/to/resource/Temp | 文件夹  |
    Given 数据库批量删除资源查询条件接口返回结果为"失败"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"2"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"2"次
    Then 数据库查询所有关联关系接口被调用"2"次
    Then 数据库批量删除资源列表接口被调用"0"次
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |

  Scenario:[11011] 执行清理无用资源操作，资源信息的类型为配置App文件，且未关联到当前APP版本时，不会删除本地存储资源信息以及资源相对应的文件目录
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type      | Name | Version | Link             | MD5   | Model  | TypeId  | ProdNo  | TypeCode | Path                                               | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 6  | configAPP | resE | 1.0.0   | http://resE.json | hashE | modelE | typeIdE | prodNoE |          | /path/to/resource/configAPP/<EMAIL> | true           | 1480802244000 | 1480802244000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt     | name | md     | ti      | pn      | tc | dnt |
      | 1           | 6     | 6.8.0      | 1       | config |      | modelE | typeIdE | prodNoE |    |     |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                 | Type |
      | /path/to/resource/configAPP | 文件夹  |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                               |
      | /path/to/resource/configAPP/<EMAIL> |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime | isPreset |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |

  Scenario: [11012] 执行清理无用资源操作，查询关联信息列表，删除没有资源信息对应的关联信息
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt    | name |
      | 1           | 1     | 6.9.0      | 0       | mPaaS | resA |
      | 2           | 2     | 6.9.0      | 0       | mPaaS | resB |
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库查询所有查询条件接口被调用"2"次
    Then 数据库查询所有关联关系接口被调用"2"次
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                                 |
      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=resA,resId=1 |
      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=resB,resId=2 |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |
      | 1,2   |

  Scenario:[11013] 执行清理无用资源操作，资源信息是没有关联到当前APP版本的主题资源时，不会删除本地存储资源信息以及资源相对应的文件目录，不会删除临时目录下的文件
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否        |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否        |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | false          | 1480802249000 | 1480802249000 | 否        |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802249000 | 1480802249000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.8.0      | 0       | mPaaS    | appComResI |
      | 3           | 4     | 6.9.0      | 0       | mPaaS    | appComResI |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"0"次
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime | isPreset |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |

  Scenario:[11014] 如果本地存在已下架资源且有已安装的有用资源，执行清理无用资源操作，已下架资源会被删除
    Given 数据库代理查询名字为"空对象"类型为"AllType"状态为"已下架"的资源接口返回资源信息如下:
      | Id | Type    | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                 | isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus |
      | 1  | flutter | resC | 1.0.0   | http://resC.zip| hashC |       |        |        |          | /path/to/resource/flutter/resC@1.0.0 | true           | 1480802243000 | 1480802243000 | 否        | 1         |
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip| hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否        |
    Given 数据库代理查询所有查询条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 1     | 6.9.0      | 0       | flutter  | resC |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type |
      | /path/to/resource/ApiCloud | 文件夹  |
      | /path/to/resource/flutter  | 文件夹  |
    Given 设置文件"/path/to/resource/flutter"的子目录列表为:
      | SubFilePath                          | Type |
      | /path/to/resource/flutter/resC@1.0.0 | 文件夹  |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹  |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/ApiCloud            | ApiCloud   |
      | /path/to/resource/flutter             | flutter    |
      | /path/to/resource/ApiCloud/resB@1.0.0 | resB@1.0.0 |
      | /path/to/resource/flutter/resC@1.0.0  | resC@1.0.0 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/flutter/resC@1.0.0  |
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    When 调用清理无用资源方法,传入延时参数为"3"秒
    When 等待"4"秒
    Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
    Then 文件系统代理的删除文件接口被调用"2"次,参数路径信息为"/path/to/resource/flutter/resC@1.0.0"
    Then 数据库查询所有查询条件接口被调用"1"次
    Then 数据库查询所有关联关系接口被调用"1"次
    Then 数据库根据名字和类型查询指定状态资源接口被调用"1"次,参数名字为"空对象",类型为"AllType",状态为"已下架"
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type    | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                 | isServerLatest | CreateTime    | UpdateTime    | isPreset | ResStatus |
      | 1  | flutter | resC | 1.0.0   | http://resC.zip| hashC |       |        |        |          | /path/to/resource/flutter/resC@1.0.0 | true           | 1480802243000 | 1480802243000 | 否        | 1         |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                                   |
      | appversion=6.9.0,fromFun=0,rt=flutter,name=resC,resId=1 |
    Then 数据库批量删除关联信息列表接口被调用"1"次,参数列表如下:
      | resId |
      | 1     |

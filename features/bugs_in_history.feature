Feature: 历史Bug雷区
  记录了在资源管理上线后发生的线上问题的还原用例

  Background:
    Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
    Given 关联资源关系代理使用模拟的

  # 问题现象描述：mPaaS类型预置资源usercenter版本为5.0.0_202105310预置失败后，数据库中记录的 link 字段为 mPaaS@usercenter@5.0.0_2021053101.zip，进入安装流程下载失败。
  # 现象：预置资源预置失败后，进入安装流程，安装失败。
  # 原因：预置失败后，数据库中记录的 link 字段为类似 mPaaS@usercenter@5.0.0_2021053101.zip 的值，而非 http 连接，故下载失败。
  # 解决：安装资源时，原有逻辑为根据资源信息直接下载，修改后，添加本地下载器，根据资源的link 字段的值做判断，如果是http类连接则从网络下载，如果是本地连接则走本地下载器。
  Scenario:[15000] 资源包信息已存在，被安装资源为h5类型未安装，执行安装资源包的操作，从本地下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息且其安装路径下有已安装的资源包文件,可以查询到该资源已被安装
    Given 预置文件的根路径为"/path/to/resource"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
      | /path/to/resource/Temp     | 文件夹 |
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 | ResA@1.0.0 |
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashA"
    Given 文件系统拷贝资源"/path/to/resource/h5@<EMAIL>"至"/path/to/resource/Temp/<EMAIL>"的结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/ResA@1.0.0"至"/path/to/resource/H5ResPkg/ResA@1.0.0"操作结果为"成功"
    Given 设置文件"/path/to/resource/H5ResPkg/ResA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/ResA@1.0.0/index.html | 文件   |
    Given 本地数据库更新资源,操作结果为"成功"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          |      |
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link              | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | ResA | 1.0.0   | h5@<EMAIL> | hashA |       |        |        |          | /path/to/resource/H5ResPkg/ResA@1.0.0 |

Feature: 更新资源列表
  功能范围：
  主要提供更新资源功能，若已经更新过数据，则更新接口会先返回请求结果同步数据，否则返回空对象；然后再通过异步回调返回请求后的最新数据
  主要提供更新普通资源列表和更新设备资源列表接口

  外部依赖：
  1.资源请求代理

  接口说明：
  1.更新普通资源列表
  根据资源名称和资源类型获取运营平台部署的普通资源信息，同步返回上一次请求数据，异步返回请求后的数据，请求失败，则更新失败，反之更新成功
  2.更新设备资源列表
  更新配置文件时，根据资源类型、型号、类型ID、产品编号获取运营平台部署的配置文件信息，更新逻辑与1中描述的一致
  更新设备资源时，根据资源类型、型号、类型ID、类型代码、产品编号、网器标识获取运营平台部署的设备资源信息，更新逻辑与1中描述的一致


  Background:
    Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
    Given 资源请求代理使用模拟的

  Scenario Outline:[9000]更新资源列表时，若正在进行清空操作，更新资源接口返回失败。
    Given 正在执行清除本地缓存操作
    When 调用更新"<APIType>"列表方法,资源类型为"<ResType>",资源名称为"<ResName>",设备信息为"<DeviceInfo>"
    Then 资源请求代理接口被调用"0"次
    Then 更新"<APIType>"列表返回的资源列表为"空对象"
    Then 更新"<APIType>"列表异步操作结果为失败
    Examples:
      | APIType             | ResType          | ResName | DeviceInfo                                      |
      | updateNormalResList | h5               | resA    |                                                 |
      | updateNormalResList | configAPP        | resJ    |                                                 |
      | updateDeviceResList | h5               |         | modelB,typeIdB,typeCodeB,prodNoB,deviceNetTypeB |
      | updateDeviceResList | config           |         | modelB,typeIdB,typeCodeB,prodNoB                |
      | updateDeviceResList | deviceCustomInfo |         | modelB,typeIdB,typeCodeB,prodNoB                |
      | updateDeviceResList | app-func-model   |         | modelB,typeIdB,typeCodeB,prodNoB                |

  Scenario Outline:[9001]更新资源列表，请求资源列表返回空，更新资源列表返回失败。
    Given 资源请求代理请求资源接口异步回调"失败"，资源列表如下:
      | Id   | Type      | Name      | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path |
      | <Id> | <ResType> | <ResName> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> |      |
    When 调用更新"<APIType>"列表方法,资源类型为"<ResType>",资源名称为"<ResName>",设备信息为"<DeviceInfo>"
    Then 资源请求代理接口被调用"1"次,参数如下:
      | Type      | Name      | DeviceInfo   | FromFunc   |
      | <ResType> | <ResName> | <DeviceInfo> | <FromFunc> |
    Then 更新"<APIType>"列表返回的资源列表为"空对象"
    Then 更新"<APIType>"列表异步操作结果为"失败"，资源列表如下：
      | Id   | Type      | Name      | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path |
      | <Id> | <ResType> | <ResName> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> |      |
    Examples:
      | APIType             | Id | ResType          | FromFunc | ResName | Version | Link            | MD5   | Model  | TypeId  | ProdNo | TypeCode  | DeviceInfo                       |
      | updateNormalResList | 1  | h5               | 0        | resA    | 1.0.0   | http://resA.zip | hashA |        |         |        |           |                                  |
      | updateNormalResList | 2  | configAPP        | 0        | resB    | 1.0.1   | http://resB.zip | hashB |        |         |        |           |                                  |
      | updateDeviceResList | 3  | h5               | 2        | resC    | 1.0.2   | http://resC.zip | hashC | modelC | typeIdC | proNoC | typeCodeC | modelC,typeIdC,typeCodeC,prodNoC |
      | updateDeviceResList | 4  | config           | 1        | resD    | 1.0.3   | http://resD.zip | hashD | modelD | typeIdD | proNoD | typeCodeD | modelD,typeIdD,,prodNoD          |
      | updateDeviceResList | 5  | deviceCustomInfo | 3        | resE    | 1.0.4   | http://resE.zip | hashE | modelE | typeIdE | proNoE | typeCodeE | modelE,typeIdE,typeCodeE,proNoE  |
      | updateDeviceResList | 6  | app-func-model     | 6        | resF    | 1.0.5   | http://resF.zip | hashF | modelF | typeIdF | proNoF | typeCodeF | modelF,typeIdF,typeCodeF,proNoF  |

  Scenario Outline:[9002]更新资源列表，请求资源列表返回不为空，更新资源列表成功。
    Given 资源请求代理请求资源接口异步回调"成功"，资源列表如下:
      | Id   | Type      | Name      | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path |
      | <Id> | <ResType> | <ResName> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> |      |
    When 调用更新"<APIType>"列表方法,资源类型为"<ResType>",资源名称为"<ResName>",设备信息为"<DeviceInfo>"
    Then 资源请求代理接口被调用"1"次,参数如下:
      | Type      | Name      | DeviceInfo   | FromFunc   |
      | <ResType> | <ResName> | <DeviceInfo> | <FromFunc> |
    Then 更新"<APIType>"列表返回的资源列表为"空对象"
    Then 更新"<APIType>"列表异步操作结果为"成功"，资源列表如下：
      | Id   | Type      | Name      | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path |
      | <Id> | <ResType> | <ResName> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> |      |
    Examples:
      | APIType             | Id | ResType          | FromFunc|  ResName | Version | Link            | MD5   | Model  | TypeId  | ProdNo | TypeCode  | DeviceInfo                       |
      | updateNormalResList | 1  | h5               | 0        | resA    | 1.0.0   | http://resA.zip | hashA |        |         |        |           |                                  |
      | updateNormalResList | 2  | configAPP        | 0        | resB    | 1.0.1   | http://resB.zip | hashB |        |         |        |           |                                  |
      | updateDeviceResList | 3  | h5               | 2        | resC    | 1.0.2   | http://resC.zip | hashC | modelC | typeIdC | proNoC | typeCodeC | modelC,typeIdC,typeCodeC,prodNoC |
      | updateDeviceResList | 4  | config           | 1        | resD    | 1.0.3   | http://resD.zip | hashD | modelD | typeIdD | proNoD | typeCodeD | modelD,typeIdD,,prodNoD          |
      | updateDeviceResList | 5  | deviceCustomInfo | 3        | resE    | 1.0.4   | http://resE.zip | hashE | modelE | typeIdE | proNoE | typeCodeE | modelE,typeIdE,proNoE,typeCodeE  |
      | updateDeviceResList | 6  | app-func-model   | 6        | resF    | 1.0.5   | http://resF.zip | hashF | modelF | typeIdF | proNoF | typeCodeF | modelF,typeIdF,proNoF,typeCodeF  |

Scenario Outline: [9003]更新资源列表，请求参数非法时,更新资源失败，返回资源列表为空对象
    When 调用更新"<APIType>"列表方法,资源类型为"<ResType>",资源名称为"<ResName>",设备信息为"<DeviceInfo>"
    Then 资源请求代理接口被调用"0"次
    Then 更新"<APIType>"列表返回的资源列表为"空对象"
    Then 更新"<APIType>"列表异步操作结果为失败
  Examples:
    | APIType             | ResType          | ResName | DeviceInfo |
    | updateNormalResList | h5               | 空对象     |            |
    | updateDeviceResList | h5               | resB    | 空对象        |
    | updateDeviceResList | config           | resC    | 空对象        |
    | updateDeviceResList | deviceCustomInfo | resD    | 空对象        |
    | updateDeviceResList | app-func-model   | resF    | 空对象        |


Feature: 资源加载上报
    功能范围:
    主要涉及资源加载时，对所加载资源的名称、类型、版本进行采集上报。为资源触达率计算提供原始数据。
    根据资源不同的触达方式，将资源分为如下三个类型：
    类型|触达定义|资源包
    -----|-------|-------
    App功能 | 打开新包| 插件包，H5资源包，Flutter动态包等
    App配置|加载新包|逻辑引擎配置文件，应用侧属性描述文件，配置文件等
    App资源|读取新包|图片，音频，视频等

    针对上述三种触达类型的资源，采集数据时机如下：
    - App功能：在功能类资源加载末端（容器）使用资源时，采集所使用资源的名称，类型，版本。
    - App配置与App资源：当获取资源的路径时，采集所使用资源的名称，类型，版本。
        - 资源未安装时，不采集信息。
        - 为防止频繁调用path属性，对相同资源，添加3s防抖机制。

    采集上报机制：
    采集数据时，触发上报数据操作。
    1. 上报数据：
    缓存数据到内存。
    若当前有上传任务进行或者无网络时，不进行上报。
    若当前无上传任务进行并且网络正常时，则由缓存数据中获取数据（最大50条），进行数据上报，并在缓存中移除上报数据。
    2. 上报完成：
    若缓存数据不为空，发起上报数据操作。
    若缓存数据为空，停止上报。

    外部依赖:
    - 上报功能代理
    - 网络状态代理
    - 时间日期代理

    接口说明:
    1.上报加载资源信息
    资源达到触达时机时，上报资源的名称、类型、版本信息。

    Background:
        Given 初始化资源管理器,清理器为"默认的",调度器为"异步的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
        Given 设置系统当前时间为"20200225 12:00:00"

    Scenario Outline:[16000]调用资源加载信息上报接口，参数异常时，不采集上报资源信息
        Given 调用上报加载资源信息接口结果为"成功"
        Given 设置系统网络状态为"可用"
        When 调用上报加载资源信息接口,参数如下:
            | Name      | Type      | Version      |
            | <ResName> | <ResType> | <ResVersion> |
        Then 资源数据源的上报加载资源信息接口被调用"0"次,上报数据如下:
            | Name | Type | Version | Timestamp |
        Examples:
            | ResName | ResType  | ResVersion |
            |         | mPaaS    | 1.0.0      |
            | resB    | mockType | 1.0.0      |
            | resC    | video    |            |
            | resD    |          | 1.0.0      |

    Scenario Outline:[16001]调用资源加载信息上报接口，且无上传任务、网络不可用，采集但不上报资源信息
        Given 设置系统网络状态为"不可用"
        Given 调用上报加载资源信息接口结果为"成功"
        When 调用上报加载资源信息接口,参数如下:
            | Name      | Type      | Version      |
            | <ResName> | <ResType> | <ResVersion> |
        Then 资源数据源的上报加载资源信息接口被调用"0"次,上报数据如下:
            | Name | Type | Version |
        Examples:
            | ResName | ResType | ResVersion |
            | resA    | mPaaS   | 1.0.0      |
            | resB    | config  | 1.0.0      |
            | resC    | video   | 1.0.0      |

    Scenario Outline:[16002]调用资源加载信息上报接口，且无上传任务、网络可用，采集并上报资源信息
        Given 调用上报加载资源信息接口结果为"成功"
        Given 设置系统网络状态为"可用"
        When 调用上报加载资源信息接口,参数如下:
            | Name      | Type      | Version      |
            | <ResName> | <ResType> | <ResVersion> |
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name      | Type      | Version      | Timestamp     |
            | <ResName> | <ResType> | <ResVersion> | 1582603200000 |
        Examples:
            | ResName | ResType | ResVersion |
            | resA    | mPaaS   | 1.0.0      |
            | resB    | config  | 1.0.0      |
            | resC    | video   | 1.0.0      |

    Scenario:[16003]调用资源加载信息上报接口，且有上传任务、网络可用，采集但不上报资源信息
        Given 调用上报加载资源信息接口等待"1"秒后返回结果为"成功"
        Given 设置系统网络状态为"可用"
        When 调用上报加载资源信息接口,参数如下:
            | Name | Type  | Version |
            | resA | mPaaS | 1.0.0   |
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resA | mPaaS | 1.0.0   | 1582603200000 |
        When 调用上报加载资源信息接口,参数如下:
            | Name | Type  | Version |
            | resB | mPaaS | 1.0.0   |
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resA | mPaaS | 1.0.0   | 1582603200000 |
        When 等待"2"秒
        Then 资源数据源的上报加载资源信息接口被调用"2"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resB | mPaaS | 1.0.0   | 1582603200000 |

    Scenario Outline:[16004]读取资源路径时，所读取资源为App资源类型或普通类配置类型资源时，且无上传任务、网络可用，采集并上报资源信息
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询普通资源列表方法,传入参数类型为"<TypeName>",名字为"<ResName>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"<Count>"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Examples:
            | Count | Query                                              | Condition                                          | ResId | TypeName   | ResName | ResVersion | ResLink         | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                                 | Latest |
            | 1     | appversion=6.9.0,fromFun=0,rt=video,name=resA      | appversion=6.9.0,fromFun=0,rt=video,name=resA      | 0     | video      | resA    | 1.0.0      | http://resA.zip | hashA   |          |           |           |             | /path/to/resource/video/resA@1.0.0      | true   |
            | 1     | appversion=6.9.0,fromFun=0,rt=configFile,name=resC | appversion=6.9.0,fromFun=0,rt=configFile,name=resC | 2     | configFile | resC    | 1.0.0      | http://resC.zip | hashC   |          |           |           |             | /path/to/resource/configFile/resC@1.0.0 | true   |

    Scenario Outline:[16005]读取资源路径时，所读取资源为设备类配置类型资源时，且无上传任务、网络可用，采集并上报资源信息
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询设备资源列表方法,传入参数条件为"<Condition>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"<Count>"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Examples:
            | Count | Query                                                                                                      | Condition                                                                                                  | ResId | TypeName | ResName | ResVersion | ResLink          | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                                               | Latest |
            | 1     | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 1     | config   | resB    | 1.0.0      | http://resB.json | hashB   | modelB   | typeIdB   | prodNoB   | typeCodeB   | /path/to/resource/DeviceConfig/<EMAIL> | true   |

    Scenario Outline:[16006]读取资源路径时，所读取资源非App资源类型或普通类配置类型资源、或者所读取App资源类型资源未安装时，且无上传任务、网络可用，不采集上报资源信息
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询普通资源列表方法,传入参数类型为"<TypeName>",名字为"<ResName>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"<Count>"次,上报数据如下:
            | Name | Type | Version | Timestamp |
        Examples:
            | Count | Query                                              | Condition                                          | ResId | TypeName   | ResName | ResVersion | ResLink         | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                            | Latest |
            | 0     | appversion=6.9.0,fromFun=0,rt=mPaaS,name=resB      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=resB      | 1     | mPaas      | resB    | 1.0.0      | http://resB.zip | hashB   |          |           |           |             | /path/to/resource/mPaas/resB@1.0.0 | true   |
            | 0     | appversion=6.9.0,fromFun=0,rt=video,name=resD      | appversion=6.9.0,fromFun=0,rt=video,name=resD      | 3     | video      | resD    | 1.0.0      | http://resD.zip | hashD   |          |           |           |             |                                    | true   |
            | 0     | appversion=6.9.0,fromFun=0,rt=configFile,name=resG | appversion=6.9.0,fromFun=0,rt=configFile,name=resF | 5     | configFile | resG    | 1.0.0      | http://resG.zip | hashG   |          |           |           |             |                                    | true   |

    Scenario Outline:[16007]读取资源路径时，所读取设备类配置类型资源未安装时，且无上传任务、网络可用，不采集上报资源信息
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"不存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询设备资源列表方法,传入参数条件为"<Condition>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"<Count>"次,上报数据如下:
            | Name | Type | Version | Timestamp |
        Examples:
            | Count | Query                                                                                                      | Condition                                                                                                  | ResId | TypeName | ResName | ResVersion | ResLink          | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                           | Latest |
            | 0     | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | 4     | config   | resF    | 1.0.0      | http://resF.json | hashF   | modelF   | typeIdF   | prodNoF   | typeCodeF   |                                   | true   |
            | 0     | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | 4     | config   | resF    | 1.0.0      | http://resF.json | hashF   | modelF   | typeIdF   | prodNoF   | typeCodeF   | /path/to/resource/config/ResPath  | true   |

    Scenario Outline:[16008]读取资源路径时，所读取资源为App资源类型或普通类配置类型资源时，且无上传任务、网络可用、资源已安装，相同资源信息多次采集时间间隔在3秒内，仅采集第一份数据。3秒过后，可继续采集。
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询普通资源列表方法,传入参数类型为"<TypeName>",名字为"<ResName>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Given 设置系统当前时间为"20200225 12:00:01"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Given 设置系统当前时间为"20200225 12:00:04"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"2"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603204000 |
        Examples:
            | Query                                              | Condition                                          | ResId | TypeName   | ResName | ResVersion | ResLink         | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                                 | Latest |
            | appversion=6.9.0,fromFun=0,rt=video,name=resD      | appversion=6.9.0,fromFun=0,rt=video,name=resD      | 3     | video      | resD    | 1.0.0      | http://resD.zip | hashD   |          |           |           |             | /path/to/resource/video/resD@1.0.0      | true   |
            | appversion=6.9.0,fromFun=0,rt=configFile,name=resG | appversion=6.9.0,fromFun=0,rt=configFile,name=resG | 5     | configFile | resG    | 1.0.0      | http://resG.zip | hashG   |          |           |           |             | /path/to/resource/configFill/resG@1.0.0 | true   |

    Scenario Outline: [16009]读取资源路径时，所读取资源为设备类配置类型资源时，且无上传任务、网络可用、资源已安装，相同资源信息多次采集时间间隔在3秒内，仅采集第一份数据。3秒过后，可继续采集。
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口结果为"成功"
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Given 文件系统中"存在"的"文件"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询设备资源列表方法,传入参数条件为"<Condition>"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Given 设置系统当前时间为"20200225 12:00:01"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603200000 |
        Given 设置系统当前时间为"20200225 12:00:04"
        When 调用资源"<ResName>"的路径属性
        When 等待"1"秒
        Then 资源数据源的上报加载资源信息接口被调用"2"次,上报数据如下:
            | Name      | Type       | Version      | Timestamp     |
            | <ResName> | <TypeName> | <ResVersion> | 1582603204000 |
        Examples:
            | Query                                                                                                      | Condition                                                                                                  | ResId | TypeName | ResName | ResVersion | ResLink          | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                                               | Latest |
            | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | appversion=6.9.0,fromFun=2,rt=config,name=,md=modelF,ti=typeIdF,pn=prodNoF,tc=typeCodeF,dnt=deviceNetTypeF | 4     | config   | resF    | 1.0.0      | http://resF.json | hashF   | modelF   | typeIdF   | prodNoF   |             | /path/to/resource/DeviceConfig/<EMAIL> | true   |

    Scenario Outline:[16010]上报资源信息。上报完成后，若依然存在未上报数据可继续上报资源信息。
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口等待"1"秒后返回结果为"<Success>"
        When 调用上报加载资源信息接口,参数如下:
            | Name | Type  | Version |
            | resA | mPaaS | 1.0.0   |
        When 调用上报加载资源信息接口,参数如下:
            | Name | Type  | Version |
            | resB | mPaaS | 1.0.0   |
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resA | mPaaS | 1.0.0   | 1582603200000 |
        When 等待"2"秒
        Then 资源数据源的上报加载资源信息接口被调用"2"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resB | mPaaS | 1.0.0   | 1582603200000 |
        Examples:
            | Success |
            | 成功    |
            | 失败    |

    Scenario Outline:[16011]上报资源加载信息，一次上报数据上限为50条。
        Given 设置系统网络状态为"可用"
        Given 调用上报加载资源信息接口等待"1"秒后返回结果为"成功"
        When 调用上报加载资源信息接口,参数如下:
            | Name | Type  | Version |
            | resA | mPaaS | 1.0.0   |
        When 上报"<RequestCount>"条资源加载信息
        Then 资源数据源的上报加载资源信息接口被调用"1"次,上报数据如下:
            | Name | Type  | Version | Timestamp     |
            | resA | mPaaS | 1.0.0   | 1582603200000 |
        When 等待"3"秒
        Then 资源数据源的上报加载资源信息接口被调用"<ApiCallCount>"次,上报数据条数为"<ResultCount>"
        Examples:
            | RequestCount | ResultCount | ApiCallCount|
            | 49          | 49          |    2        |
            | 50          | 50          |   2         |
            | 51          | 1          |    3         |

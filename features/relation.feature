Feature: 关联资源关系
    功能范围:
    主要用于测试组件中资源列表和查询关系的关联关系。主要提供关联资源列表和查询关系接口
    - 查询关系是由APP版本、方法来源、查询参数(资源名称、资源类型、资源类型、型号、类型ID、产品编号)组合的条件组成
    - 关联资源和查询关系时，本地数据存储中没有关联的资源时需要进行插入资源操作，否则进行更新操作，然后将资源和查询关系保存到本地数据存储中
    - 预置标识为是时不删除旧的关联关系
    外部依赖:
    - 系统时间代理
    - 本地数据存储代理

    接口说明:
    1.关联资源列表和查询关系
    如果查询关系为空对象时，关联失败，接口返回关联后的资源列表为空对象；
    如果删除旧的关联关系失败时，关联失败，接口返回关联后的资源列表为空对象；
    如果资源列表为空对象或空列表时，关联成功，接口返回关联后的资源列表为空列表；
    如果本地数据存储插入或者更新资源时失败，关联失败，接口返回关联后的资源列表为空对象；
    如果保存资源和查询关系到本地数据存储时失败，关联失败，接口返回关联后的资源列表为空对象；
    以上操作都为成功时，关联成功，接口返回关联后的资源列表

    Background:
        Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
        Given 设置系统当前时间为"20200229 12:00:00"
        Given 本地数据库存储删除关系接口结果为"成功"

    Scenario:[8000]查询关系为空对象时，执行关联资源列表和查询关系的操作，则关联失败，返回关联后的资源列表为空对象
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"空对象",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario Outline:[8001]执行关联资源列表和查询关系的操作，当预置标识为是，资源列表为空对象或者空列表时，直接返回空列表
        When 调用关联资源列表和查询关系方法,传入预置标识为"是",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表为"<ResList>"
        Then 关联资源列表和查询关系返回的资源列表为"空列表"
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次
        Examples:
            | ResList |
            | 空对象     |
            | 空列表     |

    Scenario: [8002]执行关联资源列表和查询关系的操作，当预置标识为否，删除关联关系失败，返回资源列表为空对象
        Given 本地数据库存储删除关系接口结果为"失败"
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次

        #插入资源失败用例
    Scenario:[8004]资源列表中的普通资源信息都不存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储插入资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储插入资源操作结果为"失败",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE"
        Then 本地数据存储插入资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储查询资源接口被调用"1"次,资源名称为"resE",资源类型为"h5",资源版本为"1.0.0"
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario Outline:[8005]资源列表中的设备资源信息都不存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储插入资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源第一次为空，第二次查询操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入资源操作结果为"失败",资源信息如下:
            | Id   | Type   | Name   | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path   | isServerLatest | CreateTime   | UpdateTime   |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> | <Path> | false          | <CreateTime> | <UpdateTime> |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=resF",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
            |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC"
        Then 本地数据存储插入资源接口被调用"<InsertCount>"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
            | 2     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
            | 2     |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储查询资源接口被调用"<SearchCount>"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resF | 1.0.0   |
            | 3     |    | h5   | resF | 1.0.0   |
            | 3     |    | h5   | resF | 1.0.0   |
            | 3     |    | h5   | resG | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"<InsertQueryCount>"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次
        Examples:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | CreateTime    | UpdateTime    | InsertCount | SearchCount | InsertQueryCount |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | 1582948800000 | 1582948800000 | 1           | 1           | 0                |
            |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | 1582948800000 | 1582948800000 | 2           | 3           | 1                |

        #插入资源成功后，第二次查询资源失败用例
    Scenario:[8006]资源列表中的普通资源信息都不存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储查询资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储插入资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE"
        Then 本地数据存储插入资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储查询资源接口被调用"2"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario:[8007]资源列表中的设备资源信息都不存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储查询资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源第一次为空，第二次查询操作结果为"失败"
        Given 本地数据存储插入资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
            |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC"
        Then 本地数据存储插入资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储查询资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 2     |    | h5   | resF | 1.0.0   |
            | 2     |    | h5   | resF | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | CreateTime    | UpdateTime    |
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario: [8007-1]资源列表中的设备资源信息都不存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储查询资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源第一次为空，第二次查询操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
            |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resF",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
            |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC"
        Then 本地数据存储插入资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
            | 2     |    | h5   | resG | 1.0.0   | http://resG.zip | hashG | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储查询资源接口被调用"4"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 4     |    | h5   | resF | 1.0.0   |
            | 4     |    | h5   | resF | 1.0.0   |
            | 4     |    | h5   | resG | 1.0.0   |
            | 4     |    | h5   | resG | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelC,ti=typeIdC,pn=prodNoC,tc=typeCodeC,dnt=deviceNetTypeC",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     |    | h5   | resF | 1.0.0   | http://resF.zip | hashF | modelC | typeIdC | prodNoC | typeCodeC |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次

        #预置标识为否，更新资源失败用例
    Scenario:[8008]资源列表中的普通资源信息都存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储更新资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"失败",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | false          | 1582948800000 | 1582948800000 | 
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源名称为"resA",资源类型为"h5",资源版本为"1.0.0"
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario: [8009]资源列表中的设备资源信息都存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储更新资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"失败",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |          
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            |    | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,,dnt=deviceNetTypeB"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resC | 1.0.1   |
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"0"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario:[8009-1]资源列表中的设备资源信息都存在数据库中，执行关联资源列表和查询关系的操作，本地数据存储更新资源列表中任意一个资源失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
            |    | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,,dnt=deviceNetTypeB"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 2     |    | h5   | resC | 1.0.1   |
            | 2     |    | h5   | resD | 2.0.1   |
        Then 本地数据存储更新资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 2     | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次

        #插入关联关系失败
    Scenario:[8010]资源列表中的为普通资源信息，执行关联资源列表和查询关系的操作，关联资源信息和查询关系失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 6  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 6  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE"
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     | 6  | h5   | resE | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 6  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"1"次
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario:[8011]资源列表中的为设备资源信息，执行关联资源列表和查询关系的操作，关联资源信息和查询关系失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"失败",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
            |    | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resC | 1.0.1   |
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario:[8011-1]资源列表中的为设备资源信息，执行关联资源列表和查询关系的操作，关联资源信息和查询关系失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
            |    | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 2     |    | h5   | resC | 1.0.1   |
            | 2     |    | h5   | resD | 2.0.1   |
        Then 本地数据存储更新资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 2     | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"2"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 2     | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"0"次

    Scenario:[8012]资源列表中的为普通资源信息，执行关联资源列表和查询关系的操作，关联资源信息和查询关系都成功时，则关联成功，返回关联后的资源列表不为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 9  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 9  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 9  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 9  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resE | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"1"次
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resE",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 9  | h5   | resE | 1.0.0   | http://resE.zip | hashE |       |        |        |          |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"1"次

    Scenario:[8013]资源列表中的为设备资源信息，执行关联资源列表和查询关系的操作，关联资源信息和查询关系都成功时，则关联成功，返回关联后的资源列表不为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
            |    | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储删除关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 2     |    | h5   | resC | 1.0.1   |
            | 2     |    | h5   | resD | 2.0.1   |
        Then 本地数据存储更新资源接口被调用"2"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 2     | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储插入关系接口被调用"2"次,查询关系为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB",资源信息如下:
            | Count | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 2     | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
            | 2     | 5  | h5   | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB |      | false          | 1582948800000 | 1582948800000 |
        Then 本地数据存储事务成功接口被调用"1"次

        #预置标识"是"，未安装进行资源更新
    Scenario:[8014]资源列表中的为普通资源信息，执行关联资源列表和查询关系的操作，当预置标识为是并且从本地数据存储查询到的资源信息是非预置资源且未安装时进行更新操作，关联资源信息和查询关系都成功时，则关联成功，返回关联后的资源列表不为空对象
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"是",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            |    | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Then 关联资源列表和查询关系返回的资源列表如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resA | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源信息如下:
            | Count | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Then 本地数据存储事务成功接口被调用"1"次

        #预置标识"是"，本地资源已安装
    Scenario:[8015]资源列表中的为普通资源信息，执行关联资源列表和查询关系的操作，当预置标识为是并且从本地数据存储查询到的资源信息是非预置资源且已安装时未进行更新操作，关联资源信息和查询关系都成功时，则关联成功，返回关联后的资源列表不为空对象
        Given 文件系统中"存在"的"文件"路径如下:
            | Path                                  |
            | /path/to/resource/H5ResPkg/resA@1.0.0 |
        Given 本地数据存储查询资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | false          | 1480802243000 | 1480802243000 |
        Given 本地数据存储更新资源操作结果为"成功",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | false          | 1480802243000 | 1480802243000 |
        Given 本地数据存储插入关系操作结果为"成功",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源信息如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        When 调用关联资源列表和查询关系方法,传入预置标识为"是",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime |
            |    | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          |      | false          |            |            |
        Then 关联资源列表和查询关系返回的资源列表如下:
            | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | false          | 1480802249000 | 1480802249000 |
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"1"次,资源信息如下:
            | Count | Id | Type | Name | Version |
            | 1     |    | h5   | resA | 1.0.0   |
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"1"次,查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源信息如下:
            | Count | Id | Type | Name | Version | Link          | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    |
            | 1     | 1  | h5   | resA | 1.0.0   | h5@resA@1.0.0 | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | false          | 1480802249000 | 1480802249000 |
        Then 本地数据存储事务成功接口被调用"1"次

    # 开始事务和结束事务的测试边界：
    # 一种情况是两个接口都不会调用，另一种情况是两个接口都会调用，因此添加两个用例进行测试，不存在调用其中之一的情况
    @ios_ignore
    Scenario:[8016]查询关系为空对象时，执行关联资源列表和查询关系的操作，则关联失败，返回关联后的资源列表为空对象
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"空对象",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime    | UpdateTime    |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | false          | 1480802243000 | 1480802243000 |
        Then 本地数据存储事务开始接口被调用"0"次
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次
        Then 本地数据存储事务结束接口被调用"0"次

    @ios_ignore
    Scenario:[8017]执行关联资源列表和查询关系的操作，当根据查询条件删除旧的关联关系失败时，则关联失败，返回关联后的资源列表为空对象
        Given 本地数据库存储删除关系接口结果为"失败"
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储事务开始接口被调用"1"次
        Then 本地数据存储删除关系接口被调用"1"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次
        Then 本地数据存储事务结束接口被调用"1"次

    @ios_ignore
    Scenario:[8018]执行关联资源列表和查询关系的操作，当获取查询条件为空时，则查询失败，提前结束
        Given 本地数据库存储删除关系接口结果为"失败"
        Given 获取查询条件为空
        When 调用关联资源列表和查询关系方法,传入预置标识为"否",查询关系为"appversion=6.9.0,fromFun=0,rt=h5,name=resA",资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
        Then 关联资源列表和查询关系返回的资源列表为"空对象"
        Then 本地数据存储事务开始接口被调用"1"次
        Then 本地数据存储删除关系接口被调用"0"次
        Then 本地数据存储插入资源接口被调用"0"次
        Then 本地数据存储查询资源接口被调用"0"次
        Then 本地数据存储更新资源接口被调用"0"次
        Then 本地数据存储插入关系接口被调用"0"次
        Then 本地数据存储事务成功接口被调用"0"次
        Then 本地数据存储事务结束接口被调用"1"次

各Feature中每条Scenario对应序号如下：
initResMgr.feature : 1000
presetResInfo.feature : 2000
localCacheClean.feature : 3000
searchResInfo.feature : 4000
requestResInfo.feature : 5000
taskResInfo.feture : 6000
combineResInfo.feature : 7000
relationResInfo.feature : 8000 
updateResInfo.feature : 9000
asynInsertAndInstallInfo.feature :10000
cleanUselessRes.feature :11000
threadTest.feature :12000
grey.feature :13000
```
Scenario: [3000]初始化方法中的所有参数均不为空，则初始化成功。
        Given xxxx
        When xxx
        Then xxx
```

### 各Feature文件分类的解释
1.initResMgr.feature描述初始化功能，主要提供初始化资源相关代理类。包括文件系统、网络、下载、时间、存储等功能的具体实现,初始化时所有状态均为默认状态
2.presetResInfo.feature描述预置资源功能，主要提供异步预置、同步预置
3.localCacheClean.feature描述清除缓存功能，主要是清除指定文件下的资源文件及清空资源表，以及清理缓存时保护指定预置资源的功能
4.searchResInfo.feature描述查询本地资源功能，主要提供查询本地资源、已安装最新资源、根据条件过滤查询资源等
5.updateResInfo.feature描述更新资源功能，主要是从运营平台将资源信息更新到本地数据库，提供更新普通资源和设备资源的方法
6.taskResInfo.feature描述资源安装和卸载的任务功能，主要提供单个资源安装资源和卸载资源，提供任务取消和获取任务id
7.combineResInfo.feature描述组合升级资源功能，结合taskResInfo中的安装和updateResInfo中的更新接口，组合的新功能，主要提供普通资源和设备资源自动升级，批量预加载、自动安装最新资源
8.relationResInfo.feature描述查询条件与资源列表的关联关系,将查询条件与资源关联起来。
9.requestResInfo.feature描述通过请求条件从运营平台请求数据
10.asynInsertAndInstallInfo.feature描述通过外界异步插入资源包且安装资源包信息
11.cleanUselessRes.feature描述清除无用资源，与当前版本无关的资源，有关但是不是最新安装资源
12.threadTest.feature描述多线程场景下对于安装、卸载进测试
13.grey.feature描述灰度下从运营平台请求资源列表，资源安装进行测试以及根据请求条件查询上次缓存在本地的设备资源列表的功能进行测试
### 通用术语
1.资源 - 指APP运行时需要用到的外部数据，是从运营平台获取的ZIP压缩包或者JSON文件，且不同的资源具有不同的类型；ZIP压缩包可使用的类型为h5、apicloud、mPaaS、deviceCustomInfo,JSON文件为config
2.资源类型 - 每一个资源都有对应的类型枚举值，分别为HTML("h5")，DEVICE_CONFIG("config")，API_CLOUD("apicloud")和MPAAS("mPaaS")，DEVICE_CUSTOM_INFO("deviceCustomInfo")当需要获取全部类型时，使用的枚举值为ALL_RES(""),ALL_RES不包括查询DEVICE_CONFIG("config")
3.设备资源 - 设备资源分为配置文件和非配置文件资源，更新时需要传入资源类型，型号，类型ID，类型代码，产品编号，网器标识
4.普通资源 - 更新时只需要传入资源类型和资源名称
5.预置资源加载器 - 实现了预置资源加载接口协议，该协议主要有两个接口，一个是预置资源文件扫描接口，该接口返回扫描到的预置资源文件名列表，另一个是，打开预置资源文件接口，该接口在Android平台下返回的是文件输入流，在iOS平台下则是该预置资源文件的文件路径
6.资源存储路径(resourceRootPath) - 用来保存资源信息相关文件的根目录
7.资源数据来源(dataSource) - 资源信息的数据源
8.本地数据存储代理(databaseDelegate) - 管理器通过该参数实现数据存储功能
9.文件系统代理(fileDelegate) - 管理器通过该代理完成文件系统的相关功能
10.系统时间代理(timeDelegate) - 管理器通过该代理实现系统时间的相关操作
11.网络状态代理(connectionDelegate) - 为管理器提供网络状态的查询、监听等功能
12.下载功能代理(downloadDelegate) - 管理器通过该参数实现对资源包的下载功能
13.资源清理器(cleaner) - 管理器通过该参数实现对资源包的清理功能，初始化时，若不传该参数，则会使用默认实现，清理器分为"模拟的"和"默认的",默认的清理器就是走组件真实清理功能，模拟的是为了模拟正在清理这个状态。
14.线程调度器(scheduler) - 避免在主线程执行耗时操作(该参数仅android版本需要)
15.过滤器 - 指用户可以按照自己的需求对资源信息进行过滤，例如按照资源信息属性：资源类型、资源名称、资源版本、资源安装路径、资源是否安装、资源链接等属性进行过滤
16.时间阈值 - 指当前系统时间与上一次查询记录的系统时间间隔值，默认10分钟，单位毫秒
17.资源版本 - 资源版本号格式分为三段式(x.y.z)、四段式(x.y.z_yyyyMMddNN)、五段式(x.y.z.E.yyyyMMddNN)，且各部分只能为数字
18.空对象 - iOS指nil,Android指null
19.空字符串 - 双端均指""长度为0的字符串对象
20.最新资源 - 从运营平台获取的资源
22.关联资源关系代理 - 用来关联资源列表与查询条件，每个资源都会对应一个查询条件
23.资源安装代理 - 为资源管器提供安装资源功能
24.资源请求代理 - 主要用于从运营平台请求数据
25.资源安装结果上报代理 - 在资源安装成功后,统计已安装资源的相关信息
26.无用资源信息 - 与当前APP版本没有关联的资源信息、APP版本关联但不是最新资源信息、数据存储路径下的文件没有关联到本地数据存储的资源信息
### Feature中可能用到的Background如下

```
   Given 初始化资源管理器,资源清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
   Given 初始化资源管理器,资源清理器为"默认的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
   Given 资源请求代理使用模拟的
   Given 资源安装代理使用模拟的
   Given 关联资源关系代理使用模拟的
   Given 资源安装结果上报代理使用模拟的


   Given 运营平台上使用APP版本"6.9.0"部署的普通资源列表如下:
            | Type     | Name | Version | Link            | MD5   |
            | apicloud | resB | 2.0.0   | http://resB.zip | hashB |
            | h5       | resA | 1.0.0   | http://resA.zip | hashA |
            | mPaaS    | resM | 1.0.0   | http://resM.zip | hashM |
   Given 运营平台上使用APP版本"6.9.0"部署的与型号"modelB",类型标识"typeIdB",产品编码"prodNoB",类型代码"typeCodeB"和网器标识"deviceNetTypeB"相关联的设备资源列表如下:
            | Type     | Name | Version | Link                    | MD5   |
            | apicloud | resD | 2.0.1   | http://resD.zip         | hashD |
            | h5       | resC | 1.0.1   | http://resC.zip         | hashC |
            | config   | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1 |
            | mPaaS    | resM1| 1.0.0   | http://resM1.zip        | hashM1|
   Given 用户已从运营平台更新数据且缓存到本地数据库,如下:
            | Id | Type     | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5       | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           |
            | 2  | apicloud | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | true           |
            | 3  | mPaaS    | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           |                                       | false          |
            | 4  | mPaaS    | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | true           |
            | 5  | h5       | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           |
            | 6  | apicloud | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB |                                       | true           |
            | 7  | config   | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           |
    Given 设置系统当前时间为"20200225 12:00:00"
    Given 设置系统网络连接为"wifi"
    Given 设置系统网络状态为"可用"
        
```

### Feature中的所有接口，中文名称整理如下：
1、初始化资源管理器
2、同步预置资源信息
3、异步预置资源列表
4、更新普通资源列表
5、更新设备资源列表
6、安装资源
7、批量预加载普通资源
8、卸载资源
9、取消任务
10、获取正在执行的任务id
11、清空缓存
12、自动升级普通资源
13、自动升级设备资源
14、查询本地全部资源列表
15、查询本地最新资源列表
16、查询本地最新资源
17、查询本地已安装的最新资源
18、查询本地指定资源
19、获取指定类型资源的安装路径
20、是否正在清除缓存
21、自动升级最新资源
22、异步插入且安装资源
23、查询普通资源列表
24、查询设备资源列表
25、请求普通资源列表
26、请求设备资源列表
27、清空缓存时保护指定预置资源

### 标识符解释

[Function]表示方法，方法名与组件Feature中接口名一致
[Operation]表示操作,可以为"安装"，"更新"，"获取"，等操作动词
[Bool]表示YES或者NO,可以对应"成功"或者"失败",也可以对应"存在"或者"不存在",也可以对应"可用"或者"不可用"
[Object]表示使用对象,可以为""等
[Argments]表示参数
[API]表示指外部依赖中用到的接口
[Number]表示数字
[Attribute]表示对象的某个属性
<###>表示泛指任意词
[Data]表示数据，也可以表示参数和返回值的关系表，参数列表等。可以为列表,JSON等任意数据,

```
//如下只是列表格式，可以为其他格式
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```

### 运营平台相关领域语言

* Given 用户已从运营平台更新数据且缓存到本地数据库,如下:[Data] 

```
  Given 用户已从运营平台更新数据且缓存到本地数据库,如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```
* Given 运营平台上使用APP版本<###>部署的普通资源列表如下:[Data]

```
  Given 运营平台上使用APP版本"6.9.0"部署的普通资源列表如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```

* Given 运营平台上使用APP版本<###>部署的与型号<###>,类型标识<###>,产品编码<###>,类型代码<###>和网器标识<###>相关联的设备资源列表如下:[Data]

```
  Given 运营平台上使用APP版本"6.9.0"部署的与型号"modelB",类型标识"typeIdB",产品编码"prodNoB",类型代码"typeCodeB"和网器标识"deviceNetTypeB"相关联的设备资源列表如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```

* Given 运营平台上使用APP版本<###>部署的与设备<###>相关联的设备资源列表如下:[Data]

``` 
  Given 运营平台上使用APP版本"6.9.0"部署的与设备"<DeviceInfo>"相关联的设备资源列表如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```

* Given 运营平台上查询<###>列表的接口返回[Bool]

```
    Given 运营平台上查询"普通资源"列表的接口返回"失败"
```
### 数据库相关领域语言
* Given 更新资源<###>到数据库的操作结果为[Bool]

```
 Given 更新资源"<ResInfo>"到数据库的操作结果为"成功"
```

* Then 清空数据库接口被调用[Number]次

```
  Then 清空数据库接口被调用"1"次
```

### 文件系统相关领域语言
* Then 文件系统中[Bool]一个路径为<###>的<###>

```
  Then 文件系统中"存在"一个路径为"/path/to/resource/resA@2.1.1"的"文件夹"
```
* Given 文件系统解压仅支持<###>格式的压缩文件

```
  Given 文件系统解压仅支持"zip"格式的压缩文件
```

* Given 预置资源加载器扫描的文件名列表如下:[Data]

```
  Given 预置资源加载器扫描的文件名列表如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 | 
```
* Given 设置文件<###>的MD5值为<###>

```
  Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashA"
```

* Then 文件<###>[Bool]

```
  Then 文件"/path/to/resource/H5ResPkg/resA@1.0.0""存在"
```
* Given 删除文件<###>结果为[Bool]

```
   Given 删除文件"/path/to/resource/H5ResPkg/resC@1.0.1"结果为"失败"
```

* Given 解压资源<###>操作结果为[Bool]
```
  Given 解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
```

* Given 移动资源包<###>至<###>操作结果为[Bool]
```
  Given 移动资源包"/path/to/resource/Temp/resA"至"/path/to/resource/H5ResPkg/resA@1.0.0"操作结果为"成功"
```
* Given 文件系统中[Bool]一个路径为<###>的文件夹
```
  Given 文件系统中"不存在"一个路径为"/path/to/resource/mPaaS"的文件夹
```

* Given 创建文件夹结果为[Bool]
```
   Given 创建文件夹结果为"成功"
```

### 网络相关领域语言
* Given 设置系统网络连接为<###>

```
  Given 设置系统网络连接为"wifi"
```

* Given 设置系统网络状态为[Bool]

```
  Given 设置系统网络状态为"可用"
```

### 下载相关领域语言
* Given 从网络下载资源包操作结果为[Bool]

```
  Given 从网络下载资源包操作结果为"失败"
```
*  Given 从网络下载资源链接<###>操作结果为[Bool]

```
  Given 从网络下载资源链接"http://resA.zip"操作结果为"成功"
```

### 初始化相关领域语言
* When 初始化资源管理器对象,参数如下:[Data]

```
  When 初始化资源管理器对象,参数如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 |
```
* Then 资源管理器初始化[Bool]

```
 Then 资源管理器初始化"失败"
```

* Given 初始化资源管理器,清理器为<###>,APP版本号为<###>,数据存储的根路径为<###>

```
Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
```

### 缓存相关领域语言
* When 清除本地缓存
* Then 本地缓存清除[Bool]

```
  Then 本地缓存清除"成功"
```
* Given 正在执行清除本地缓存操作

* Given 用户已从运营平台更新数据且缓存到本地数据库,如下:[Data]

```
  Given 用户已从运营平台更新数据且缓存到本地数据库,如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
    | a2 | b2 | c2 |
```

### 调用方法的相关领域语言
* When [Object][Operation][Function|<###>]

```
  When 使用者调用同步预置资源信息接口
  When 调用查询本地所有资源方法,过滤条件类型为"apicloud",名字为"res"
```
* When [Object][Operation][Function|<###>],传入资源信息如下:[Data]

```
  When 调用安装资源方法，传入资源信息如下:
    | A  | B  | C  |
    | a1 | b1 | c1 |
```
* Then [Object]收到[API]接口的回调结果为[Bool]

```
  Then 收到卸载资源接口的回调结果为"成功"
```

* Then [Object]收到[API]接口的回调结果为[Bool],资源包数组如下:[Data]

```
  Then 使用者收到批量安装资源接口的回调结果为“成功”,资源包数组如下:
   | A  | B  | C  |
   | a1 | b1 | c1 |
```

### 资源版本比较规则
需求地址：https://uh.haier.net:8444/confluence/pages/viewpage.action?pageId=186684311

资源版本格式：三段式(x.y.z)、四段式(x.y.z_yyyyMMddNN)、五段式(x.y.z.E.yyyyMMddNN)，且各部分都只能为数字
版本比较规则：
1.三种格式会进行两两比较
2.若前三位(x.y.z)不同，对前三位(x.y.z)进行逐个比较，高位越大，版本越大
3.若前三位(x.y.z)相同，三段式版本 > 五段式版本 > 四段式版本
3.1 四段式和四段式比较: 高位越大，版本越大
3.2 五段式和五段式比较: 若E不同，E越小，版本越大；若E相同，高位越大，版本越大
 

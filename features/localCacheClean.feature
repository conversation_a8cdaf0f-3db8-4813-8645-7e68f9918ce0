Feature: 清除本地缓存
  功能范围:
  主要用于测试本地资源缓存相关的功能和接口。主要功能接口有清空缓存和是否正在清除缓存。
  - 清空缓存，用户可通过清空缓存接口，来清除资源管理库在指定资源存储目录下的相关缓存文件。
  清除缓存成功后，数据库不会被删除，数据表会被清空，资源包相关文件也会被删除
  - 是否正在清除缓存，用户可通过该接口，来查询资源管理器当前是否正在清除缓存。
  - 清空缓存时保护指定预置资源，用户可以通过清理缓存时保护指定预置资源接口，清理资源缓存文件是保护指定的预置资源。

  外部依赖:
  - 本地数据存储代理
  - 文件系统代理

  接口说明
  1.清空缓存
  该接口为异步接口，清空缓存的结果会通过异步回调通知给调用方。
  当资源清理器处于清理中时，接口回调失败结果。
  当资源清理器处于非清理中时，先清空数据库的数据表，然后清除资源管理库在指定资源存储目录下的相关缓存文件。当数据库清空操作和缓存文件清除操作均成功时，接口回调成功结果，否则接口回调失败结果。
  2.是否正在清除缓存
  当资源清理器正在清除缓存时，接口返回是。否则返回否。
  3.清空缓存时保护指定预置资源
  该接口为异步接口，传入参数为等待保护资源列表和异步回调两个参数。清空缓存的结果会同步异步回调通知调用方。
  当资源清理器处于清理中时，接口回到失败。
  当资源清理器处于非清理中时，检查传入的等待保护资源列表，将不合法的资源信息从列表中移除。
  当资源信息的资源名称为空字符串或者空对象为空时资源信息不合法，但资源不在预置资源列表中时资源信息不合法。
  在检查资源，剔除不合法资源后，如果受保护资源列表为空则会清理所有数据，先清空数据库的数据表，然后清除资源管理库在指定资源存储目录下的相关缓存文件。当数据库清空操作和缓存文件清除操作均成功时，接口回调成功结果，否则接口回调失败结果。
  在检查资源，剔除不合法资源后，如果受保护资源列表不为空，则先删除数据库中非保护资源的信息、删除查询条件信息以及关联关系数据，然后清除非保护资源在存储目录下的相关缓存文件。当数据库和缓存文件清理成功是，接口回调成功，否则接口回调失败结果。

  Background:
    Given 初始化资源管理器,清理器为"默认的",调度器为"异步的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"

  Scenario:[3000]当资源管理器正在清理缓存时，再次清空缓存结果为失败。
    Given 本地数据存储代理的清空所有数据接口持续时长为"3"秒.
    When 清除本地缓存,参数为"CallbackA"
    When 清除本地缓存,参数为"CallbackB"
    When 等待"4"秒
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then "CallbackB"收到本地缓存清除"失败"的回调

  Scenario:[3001]当数据库清空数据操作失败时，清空缓存接口回调结果应为失败，无需再调用文件系统接口删除文件。
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/ResA@1.0.0 |
    Given 本地数据存储代理的清空所有数据接口结果为"失败"
    When 清除本地缓存,参数为"CallbackA"
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"0"次

  Scenario:[3002]当数据库清空数据操作成功，而文件系统删除文件接口操作失败时，清空缓存接口回调结果应为失败。
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                        | Type   |
      | /path/to/resource/H5ResPkg         | 文件夹 |
      | /path/to/resource/APICloud         | 文件夹 |
      | /path/to/resource/mPaaS            | 文件夹 |
      | /path/to/resource/ReactNative      | 文件夹 |
      | /path/to/resource/DeviceCustomInfo | 文件夹 |
      | /path/to/resource/configAPP        | 文件夹 |
    Given 本地数据存储代理的清空所有数据接口结果为"成功"
    Given 文件系统代理删除文件路径接口返回结果为"失败"
    When 清除本地缓存,参数为"CallbackA"
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"6"次,删除的路径为如下:
      | Path                               |
      | /path/to/resource/H5ResPkg         |
      | /path/to/resource/APICloud         |
      | /path/to/resource/mPaaS            |
      | /path/to/resource/ReactNative      |
      | /path/to/resource/DeviceCustomInfo |
      | /path/to/resource/configAPP        |


  Scenario:[3003]当数据库清空数据和本地清除操作均成功时，清空缓存接口回调结果为成功，且本地缓存的各类型资源包文件均应被删除。
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                        | Type   |
      | /path/to/resource/H5ResPkg         | 文件夹 |
      | /path/to/resource/APICloud         | 文件夹 |
      | /path/to/resource/mPaaS            | 文件夹 |
      | /path/to/resource/ReactNative      | 文件夹 |
      | /path/to/resource/DeviceCustomInfo | 文件夹 |
      | /path/to/resource/configAPP        | 文件夹 |
    Given 本地数据存储代理的清空所有数据接口结果为"成功"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    When 清除本地缓存,参数为"CallbackA"
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"6"次,删除的路径为如下:
      | Path                               |
      | /path/to/resource/H5ResPkg         |
      | /path/to/resource/APICloud         |
      | /path/to/resource/mPaaS            |
      | /path/to/resource/ReactNative      |
      | /path/to/resource/DeviceCustomInfo |
      | /path/to/resource/configAPP        |

  Scenario: [3004]正在清空缓存时，是否正在清除缓存接口结果为是。
    Given 本地数据存储代理的清空所有数据接口持续时长为"3"秒.
    When 清除本地缓存,参数为"CallbackA"
    When 查询是否正在清除缓存
    Then 是否正在清除缓存的查询结果为"是"

  Scenario: [3005]没有清空缓存时，是否正在清除缓存接口结果为否。
    When 查询是否正在清除缓存
    Then 是否正在清除缓存的查询结果为"否"

  # 测试清理缓存数据保护指定预置资源功能
  Scenario:[3010]当资源管理器执行清理缓存保护指定预置资源操作时，再次执行该操作结果为失败。
    Given 本地数据存储代理的清空所有数据接口持续时长为"3"秒.
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackB",受保护资源列表参数为:
      | type | name |
    When 等待"4"秒
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then "CallbackB"收到本地缓存清除"失败"的回调

  Scenario:[3011]当数据库清空数据操作失败时，清理缓存保护指定预置资源接口回调结果应为失败，无需再调用文件系统接口删除文件。
    Given 本地数据存储代理的清空所有数据接口结果为"失败"
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"0"次

  Scenario:[3012]调用清除本地缓存保护指定预置资源接口，当数据库清空数据操作成功，而文件系统删除文件接口操作失败时，清除本地缓存保护指定预置资源接口回调结果应为失败。
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                        | Type   |
      | /path/to/resource/H5ResPkg         | 文件夹 |
      | /path/to/resource/APICloud         | 文件夹 |
      | /path/to/resource/mPaaS            | 文件夹 |
      | /path/to/resource/ReactNative      | 文件夹 |
      | /path/to/resource/DeviceCustomInfo | 文件夹 |
      | /path/to/resource/configAPP        | 文件夹 |
    Given 本地数据存储代理的清空所有数据接口结果为"成功"
    Given 文件系统代理删除文件路径接口返回结果为"失败"
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"6"次,删除的路径为如下:
      | Path                               |
      | /path/to/resource/H5ResPkg         |
      | /path/to/resource/APICloud         |
      | /path/to/resource/mPaaS            |
      | /path/to/resource/ReactNative      |
      | /path/to/resource/DeviceCustomInfo |
      | /path/to/resource/configAPP        |

  Scenario:[3013]当数据库清空数据和本地清除操作均成功时，清理缓存保护指定预置资源接口回调结果为成功，且本地缓存的各类型资源包文件均应被删除。
    Given 设置文件"/path/to/resource"的子目录列表为:
      | Path                               |
      | /path/to/resource/H5ResPkg         |
      | /path/to/resource/DeviceConfig     |
      | /path/to/resource/APICloud         |
      | /path/to/resource/mPaaS            |
      | /path/to/resource/ReactNative      |
      | /path/to/resource/DeviceCustomInfo |
      | /path/to/resource/configAPP        |
    Given 本地数据存储代理的清空所有数据接口结果为"成功"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"7"次,删除的路径为如下:
      | Path                               |
      | /path/to/resource/H5ResPkg         |
      | /path/to/resource/DeviceConfig     |
      | /path/to/resource/APICloud         |
      | /path/to/resource/mPaaS            |
      | /path/to/resource/ReactNative      |
      | /path/to/resource/DeviceCustomInfo |
      | /path/to/resource/configAPP        |

  Scenario: [3014]数据库和文件系统中存在某个预置资源以及其他多个资源，调用清理缓存保护指定预置资源参数为该预置资源,其他资源被清理，该预置资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip | hashI |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | true           | 1480802249000 | 1480802249000 | 是       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.9.0      | 0       | h5       | resI |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
      | h5   | resI |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"2"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario: [3015]数据库和文件系统中存在某个预置资源和该资源的最新版本以及其他多个资源，但都未标识为最新资源，调用清理缓存保护指定预置资源参数为该资源，该资源的旧版本和其他资源被清理，该资源的最新版本被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type      | Name | Version | Link                    | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                               | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5        | resA | 1.0.0   | http://resA.zip         | hashA  |       |        |        |          |                                                    | false          | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud  | resB | 1.0.0   | http://resB.zip         | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0              | false          | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5        | resI | 1.0.0   | http://resI.zip         | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0              | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5        | resI | 1.1.0   | http://resI1.zip        | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0              | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud  | resB | 0.9.0   | http://resB2.zip        | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0              | false          | 1480802244000 | 1480802244000 | 是       |
      | 6  | configAPP | resE | 1.0.0   | http://resE.signed.json | hashE2 |       |        |        |          | /path/to/resource/configAPP/<EMAIL> | false          | 1480802244000 | 1480802244000 | 否       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                 | Type   |
      | /path/to/resource/H5ResPkg  | 文件夹 |
      | /path/to/resource/ApiCloud  | 文件夹 |
      | /path/to/resource/configAPP | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/configAPP           |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
      | h5   | resI |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | false          | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | false          | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario: [3016]数据库和文件系统中存在某个预置资源和该资源的最新版本以及其他多个资源，调用清理缓存保护指定预置资源参数为该资源，未被标识为最新资源的该资源和其他资源被清理，被标识为最新资源的该资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | true           | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.9.0      | 0       | h5       | resI |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name |
      | h5   | resI |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario Outline: [3017]数据库和文件系统中存在预置资源和其他非预置资源，调用清理缓存保护指定预置资源参数为空对象或空字符串,所有资源均被清理
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 本地数据存储代理的清空所有数据接口结果为"成功"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type        | name        |
      | <paramType> | <paramName> |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"0"次,参数列表如下:
      | Id | Type | Name | Version | Link | MD5 | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest | CreateTime | UpdateTime | isPreset |
    Then 数据库批量删除资源查询条件接口被调用"0"次,参数列表如下:
      | query |
    Then 数据库批量删除关联信息列表接口被调用"0"次
    Then 本地数据存储代理的清空所有数据接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"2"次,删除的路径为如下:
      | Path                       |
      | /path/to/resource/H5ResPkg |
      | /path/to/resource/ApiCloud |
    Examples:
      | paramType | paramName |
      | 空对象    | resI      |
      | h5        | 空字符串  |
      | h5        | 空对象    |


  Scenario: [3018]数据库和文件系统中存在预置资源和其他非预置资源，调用清理缓存保护指定预置资源参数中存在非预置资源,非预置资源被清理预置资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resC | 0.9.0   | http://resC.zip  | hashC  |       |        |        |          | /path/to/resource/ApiCloud/resC@0.9.0 | true           | 1480802244000 | 1480802244000 | 是       |
    Given 数据库代理根据名字"resC",类型"apicloud",查询资源结果为"匹配结果"
    Given 数据库代理根据名字"resB",类型"apicloud",查询资源结果为"匹配结果"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
      | 3           | 5     | 6.9.0      | 0       | apicloud | resC |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resC@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resC@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type     | name |
      | apicloud | resC |
      | apicloud | resB |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
      | appversion=6.9.0,fromFun=0,rt=h5,name=resI       |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"2"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg            |
      | /path/to/resource/ApiCloud/resB@1.0.0 |

  Scenario: [3019]数据库和文件系统中存在某预置资源和其他资源，但都未标识为最新资源，调用清理缓存保护指定预置资源参数中存在不合法数据和该资源，该资源的旧版本和其他资源被清理，该资源的最新版本被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | false          | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | false          | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type   | name     |
      | h5     | resI     |
      | 空对象 | resI     |
      | h5     | 空字符串 |
      | h5     | 空对象   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | false          | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | false          | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario: [3020]数据库和文件系统中存在某预置资源和其他资源，调用清理缓存保护指定预置资源参数中存在不合法数据和该资源,未被标识为最新资源的该资源和其他资源被清理，被标识为最新资源的该资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | true           | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 3     | 6.9.0      | 0       | h5       | resI |
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type   | name     |
      | h5     | resI     |
      | 空对象 | resI     |
      | h5     | 空字符串 |
      | h5     | 空对象   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | false          | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario: [3021]数据库和文件系统中存在某预置资源和其他资源，调用清理缓存保护指定预置资源参数中存在不合法数据和该资源,当数据库清空数据操作失败时，清理缓存保护指定预置资源接口回调结果应为失败该资源，无需再调用文件系统接口删除文件。
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 数据库批量删除资源列表接口返回结果为"失败"
    Given 数据库批量删除资源查询条件接口返回结果为"失败"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type   | name     |
      | h5     | resI     |
      | 空对象 | resI     |
      | h5     | 空字符串 |
      | h5     | 空对象   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"0"次

  Scenario: [3022]数据库和文件系统中存在某预置资源和其他资源，调用清理缓存保护指定预置资源参数中存在不合法数据和该资源,当数据库清空数据操作成功，而文件系统删除文件接口操作失败时，清除本地缓存保护指定预置资源接口回调结果应为失败。
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 4  | h5       | resI | 1.1.0   | http://resI1.zip | hashI1 |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.1.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB |
      | 2           | 4     | 6.9.0      | 0       | h5       | resI |
    Given 数据库代理根据名字"resI",类型"h5",查询资源结果为"匹配结果"
    Given 文件系统代理删除文件路径接口返回结果为"失败"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/H5ResPkg | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
      | /path/to/resource/ApiCloud/resB@0.9.0 | 文件夹 |
    Given 设置文件"/path/to/resource/H5ResPkg"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/H5ResPkg/resI@1.0.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 | 文件夹 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/ApiCloud/resB@1.0.0 |
      | /path/to/resource/ApiCloud/resB@0.9.0 |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resI@1.1.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type   | name     |
      | h5     | resI     |
      | 空对象 | resI     |
      | h5     | 空字符串 |
      | h5     | 空对象   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"失败"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA | 1.0.0   | http://resA.zip  | hashA  |       |        |        |          |                                       | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB | 1.0.0   | http://resB.zip  | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0 | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | h5       | resI | 1.0.0   | http://resI.zip  | hashI  |       |        |        |          | /path/to/resource/H5ResPkg/resI@1.0.0 | false          | 1480802249000 | 1480802249000 | 是       |
      | 5  | apicloud | resB | 0.9.0   | http://resB2.zip | hashB2 |       |        |        |          | /path/to/resource/ApiCloud/resB@0.9.0 | false          | 1480802244000 | 1480802244000 | 是       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resI@1.0.0 |
      | /path/to/resource/H5ResPkg/resD@1.0.2 |
      | /path/to/resource/ApiCloud            |

  Scenario: [3023]数据库和文件系统中存在某个预置资源以及其他多个资源，调用清理缓存保护指定版本的不同名称的主题资源时，其他资源被清理，受保护的主题资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.9.0      | 0       | mPaaS    | appComResI |
      | 3           | 5     | 6.9.0      | 0       | mPaaS    | appComResZ |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | mPaaS | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Given 数据库代理根据名字"appComResZ"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 5  | mPaaS | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/mPaaS    | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/mPaaS"的子目录列表为:
      | SubFilePath                              | Type   |
      | /path/to/resource/mPaaS/resD@1.0.2       | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@1.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type  | name       | version |
      | mPaaS | appComResI | 1.0.0   |
      | mPaaS | appComResZ | 1.0.0   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                     |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/ApiCloud               |

  Scenario: [3024]数据库和文件系统中存在某个预置资源以及其他多个资源，调用清理缓存保护指定版本的同名主题资源时，其他资源被清理，该主题资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.9.0      | 0       | mPaaS    | appComResI |
      | 3           | 5     | 6.9.0      | 0       | mPaaS    | appComResZ |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | mPaaS | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"2.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 4  | mPaaS | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/mPaaS    | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/mPaaS"的子目录列表为:
      | SubFilePath                              | Type   |
      | /path/to/resource/mPaaS/resD@1.0.2       | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@1.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type  | name       | version |
      | mPaaS | appComResI | 1.0.0   |
      | mPaaS | appComResI | 2.0.0   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                               |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB    |
      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=appComResZ |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                     |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
      | /path/to/resource/ApiCloud               |

  Scenario: [3025]数据库和文件系统中存在某个小优资源以及其他多个资源，调用清理缓存有受保护资源且受保护资源不包括小优资源时，其他资源被清理，受保护的资源以及小优资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 6  | video    | xiaoyou    | 1.0.0   | http://xiaoyou.zip    | hashX  |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0    | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.9.0      | 0       | mPaaS    | appComResI |
      | 3           | 5     | 6.9.0      | 0       | mPaaS    | appComResZ |
      | 4           | 6     | 6.9.0      | 0       | video    | xiaoyou    |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | mPaaS | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Given 数据库代理根据名字"appComResZ"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 5  | mPaaS | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库代理根据名字"xiaoyou"类型"video"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 6  | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/mPaaS    | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
      | /path/to/resource/video    | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/mPaaS"的子目录列表为:
      | SubFilePath                              | Type   |
      | /path/to/resource/mPaaS/resD@1.0.2       | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@1.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/video"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/video/xiaoyou@1.0.0 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
      | /path/to/resource/video/xiaoyou@1.0.0    |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type  | name       | version |
      | mPaaS | appComResI | 1.0.0   |
      | mPaaS | appComResZ | 1.0.0   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                     |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/ApiCloud               |

  Scenario: [3026]数据库和文件系统中存在某个小优资源以及其他多个资源，调用清理缓存有受保护资源且受保护资源包括小优资源时，其他资源被清理，受保护的资源以及小优资源被保留
    Given  数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 6  | video    | xiaoyou    | 1.0.0   | http://xiaoyou.zip    | hashX  |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0    | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.9.0      | 0       | mPaaS    | appComResI |
      | 3           | 5     | 6.9.0      | 0       | mPaaS    | appComResZ |
      | 4           | 6     | 6.9.0      | 0       | video    | xiaoyou    |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | mPaaS | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Given 数据库代理根据名字"appComResZ"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 5  | mPaaS | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库代理根据名字"xiaoyou"类型"video"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 6  | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/mPaaS    | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
      | /path/to/resource/video    | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/mPaaS"的子目录列表为:
      | SubFilePath                              | Type   |
      | /path/to/resource/mPaaS/resD@1.0.2       | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@1.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/video"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/video/xiaoyou@1.0.0 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
      | /path/to/resource/video/xiaoyou@1.0.0    |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type  | name       | version |
      | mPaaS | appComResI | 1.0.0   |
      | mPaaS | appComResZ | 1.0.0   |
      | video | xiaoyou    | 1.0.0   |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                            |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"3"次,删除的路径为如下:
      | Path                                     |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/ApiCloud               |

  Scenario: [3027]数据库和文件系统中存在某个小优资源以及其他多个资源，调用清理缓存无受保护资源，其他资源被清理，小优资源被保留
    Given 数据库代理根据名字和类型查询所有的资源列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 6  | video    | xiaoyou    | 1.0.0   | http://xiaoyou.zip    | hashX  |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0    | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库批量查询资源条件列表如下:
      | conditionId | resId | appversion | fromFun | rt       | name       |
      | 1           | 2     | 6.9.0      | 0       | apicloud | resB       |
      | 2           | 3     | 6.9.0      | 0       | mPaaS    | appComResI |
      | 3           | 5     | 6.9.0      | 0       | mPaaS    | appComResZ |
      | 4           | 6     | 6.9.0      | 0       | video    | xiaoyou    |
    Given 数据库代理根据名字"appComResI"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 3  | mPaaS | appComResI | 1.0.0   | http://appComResI.zip | hashI |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
    Given 数据库代理根据名字"appComResZ"类型"mPaaS"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 5  | mPaaS | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 数据库代理根据名字"xiaoyou"类型"video"版本"1.0.0"查询资源结果如下:
      | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 6  | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Given 文件系统代理删除文件路径接口返回结果为"成功"
    Given 数据库批量删除资源列表接口返回结果为"成功"
    Given 数据库批量删除资源查询条件接口返回结果为"成功"
    Given 设置文件"/path/to/resource"的子目录列表为:
      | SubFilePath                | Type   |
      | /path/to/resource/mPaaS    | 文件夹 |
      | /path/to/resource/ApiCloud | 文件夹 |
      | /path/to/resource/video    | 文件夹 |
    Given 设置文件"/path/to/resource/ApiCloud"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/ApiCloud/resB@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/mPaaS"的子目录列表为:
      | SubFilePath                              | Type   |
      | /path/to/resource/mPaaS/resD@1.0.2       | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@1.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResI@2.0.0 | 文件夹 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 | 文件夹 |
    Given 设置文件"/path/to/resource/video"的子目录列表为:
      | SubFilePath                           | Type   |
      | /path/to/resource/video/xiaoyou@1.0.0 | 文件夹 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/ApiCloud/resB@1.0.0    |
      | /path/to/resource/mPaaS/appComResI@1.0.0 |
      | /path/to/resource/mPaaS/resD@1.0.2       |
      | /path/to/resource/mPaaS/appComResI@2.0.0 |
      | /path/to/resource/mPaaS/appComResZ@1.0.0 |
      | /path/to/resource/video/xiaoyou@1.0.0    |
    When 调用清除本地缓存保护指定预置资源接口,回调为"CallbackA",受保护资源列表参数为:
      | type | name | version |
    When 等待"3"秒
    Then "CallbackA"收到本地缓存清除"成功"的回调
    Then 数据库批量删除资源列表接口被调用"1"次,参数列表如下:
      | Id | Type     | Name       | Version | Link                  | MD5    | Model | TypeId | ProdNo | TypeCode | Path                                     | isServerLatest | CreateTime    | UpdateTime    | isPreset |
      | 1  | h5       | resA       | 1.0.0   | http://resA.zip       | hashA  |       |        |        |          |                                          | true           | 1480802243000 | 1480802243000 | 否       |
      | 2  | apicloud | resB       | 1.0.0   | http://resB.zip       | hashB  |       |        |        |          | /path/to/resource/ApiCloud/resB@1.0.0    | true           | 1480802244000 | 1480802244000 | 否       |
      | 3  | mPaaS    | appComResI | 1.0.0   | http://appComResI.zip | hashI  |       |        |        |          | /path/to/resource/mPaaS/appComResI@1.0.0 | true           | 1480802249000 | 1480802249000 | 否       |
      | 4  | mPaaS    | appComResI | 2.0.0   | http://appComResI.zip | hashI1 |       |        |        |          | /path/to/resource/mPaaS/appComResI@2.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
      | 5  | mPaaS    | appComResZ | 1.0.0   | http://appComResZ.zip | hashZ  |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.0.0 | true           | 1480802250000 | 1480802250000 | 否       |
    Then 数据库批量删除资源查询条件接口被调用"1"次,参数列表如下:
      | query                                               |
      | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB    |
      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=appComResI |
      | appversion=6.9.0,fromFun=0,rt=mPaaS,name=appComResZ |
    Then 数据库批量删除关联信息列表接口被调用"1"次
    Then 文件系统代理的删除文件路径接口被调用"2"次,删除的路径为如下:
      | Path                       |
      | /path/to/resource/mPaaS    |
      | /path/to/resource/ApiCloud |

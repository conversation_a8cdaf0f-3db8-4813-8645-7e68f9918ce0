Feature:查询资源信息
    功能范围：
    主要提供查询资源信息功能，查询资源信息是指资源信息从运营平台更新后缓存到本地数据库，通过指定过滤器或者资源名称、类型和版本号查询本地数据库中的资源信息。
    主要包括查询所有资源信息、最新资源信息、最新已安装资源信息、指定资源信息、本地普通资源列表、设备资源列表等接口
    - 过滤器为空时查询本地所有资源列表结果为所有资源信息，查询本地最新资源列表结果为最新资源列表
    - 资源名字、类型、版本三者同时匹配具有唯一性，当名字或类型或版本为空对象、或者都不匹配时，查询结果为空，反之查询结果为匹配名字、类型、版本的资源信息
    - 执行清除缓存操作时，查询资源结果为空。
    - 本地数据库中没有资源信息时，查询结果为空。
    - 查询本地所有资源，查询结果是符合查询条件的所有资源信息
    - 查询本地最新资源，查询结果是在符合查询条件的所有资源中，去掉重复资源，保留标识是最新资源的资源信息
    - 获取指定类型资源的安装路径，查询结果是查询类型的资源路径
    - 查询普通资源列表，查询结果是上一次从运营平台请求普通资源条件所关联的查询结果
    - 查询设备资源列表，查询结果是上一次从运营平台请求设备资源条件所关联的查询结果

    外部依赖：
    1.本地数据存储代理

    接口说明：
    1、查询本地全部资源列表
    根据过滤器获取本地全部资源列表，当过滤器为空时，获取到的资源信息是所有资源信息，当过滤器不为空时，获取到的是符合过滤规则的资源信息
    2、查询本地最新资源列表
    根据过滤器获取本地最新资源列表，当过滤器为空时，获取到的资源信息是去掉重复资源后的所有最新资源列表，当过滤器不为空时，获取到的是符合过滤规则的所有资源中，去掉重复资源，保留标识最新资源的资源信息
    本地缓存的所有资源都未标识为最新资源时，获取到的资源信息是去掉重复资源后，保留版本号最大的资源信息
    3、查询本地最新资源
    根据资源名字和资源类型获取最新资源信息，查询结果是符合条件的标识为最新资源的资源信息，若符合条件的所有资源都未标识为最新资源时，返回版本号最大的资源信息
    4、查询本地已安装的最新资源
    根据资源名字和资源类型获取最新已安装资源信息，查询结果是符合条件且已经安装的标识为最新资源的资源信息，若符合条件且已安装的资源都未标识为最新资源时，返回版本号最大的资源信息
    5、查询本地指定资源
    根据资源名称和资源类型、资源版本号获取指定资源信息，三者同时匹配具有唯一性，当名字或类型或版本为空对象、或者都不匹配时，查询结果为空，反之查询结果为匹配名字、类型、版本的资源信息
    6、获取指定类型资源的安装路径
    根据资源类型获取资源类型路径，当类型为非指定类型时，获取结果为空对象，反之获取指定路径，若该路径不存在时，会自动创建指定类型的文件夹，并返回路径，否则直接返回指定路径
    7、查询普通资源列表
    根据资源名称和资源类型，组成查询条件，获取到的是关联查询条件的的资源列表
    8、查询设备资源列表
    根据资源类型，型号，类型ID，类型代码，产品编号，网器标识，组成查询条件，获取到的是关联查询条件的资源列表
    9、查询本地已安装最新资源列表
    根据资源名称列表和资源类型获取最新已安装资源列表，查询结果是符合条件且已经安装的标识为最新资源的资源信息，若符合条件且已安装的资源都未标识为最新资源时，返回版本号最大的资源信息

    Background:
      Given  初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
      Given  数据库代理中所有资源列表如下:
        | Id | Type             | Name | Version | Link                     | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest | ResStatus |
        | 1  | h5               | resA | 1.0.0   | http://resA.zip          | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           | 0         |
        | 2  | apicloud         | resB | 2.0.0   | http://resB.zip          | hashB  |        |         |         |           |                                       | true           | 0         |
        | 3  | mPaaS            | resM | 1.0.0   | http://resM.zip          | hashM  |        |         |         |           |                                       | true           | 0         |
        | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip         | hashM1 |        |         |         |           |                                       | true           | 0         |
        | 5  | h5               | resC | 1.0.1   | http://resC.zip          | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           | 0         |
        | 6  | apicloud         | resD | 2.0.1   | http://resD.zip          | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          | 0         |
        | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json  | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           | 0         |
        | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip          | hashZ  |        |         | prodNoB |           |                                       | true           | 0         |
        | 9  | apicloud         | resD | 2.0.0   | http://resD.zip          | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           | 0         |
        | 10 | configAPP        | resJ | 1.0.1   | http://resJ.json         | hashJ  |        |         |         |           |                                       | true           | 0         |
        
    Scenario:[4001]正在执行清除缓存操作时，查询本地所有资源，查询结果为空数组
        Given 正在执行清除本地缓存操作
        When 调用查询本地全部资源列表方法
        Then 数据库根据名字和类型查询接口被调用"0"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组

    Scenario:[4002]本地资源列表为空,查询本地所有资源，查询结果为空数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"空数组"
        When 调用查询本地全部资源列表方法
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组

    Scenario:[4003]本地已缓存资源，过滤器为空，查询本地所有资源，查询结果为全部资源信息
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地全部资源列表方法
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果数组如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | true           |
            | 3  | mPaaS            | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           |                                       | true           |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | true           |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           |
            | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | true           |
            | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           |
            | 10 | configAPP        | resJ | 1.0.1   | http://resJ.json        | hashJ  |        |         |         |           |                                       | true           |

    Scenario:[4004]本地已缓存资源，设置与本地资源类型和名字匹配的过滤器，查询本地所有资源，查询结果为与过滤器匹配的资源信息数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地全部资源列表方法,过滤条件类型为"h5",名字为"resC"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果数组如下:
            | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 5  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           |


    Scenario:[4005]本地已缓存资源，设置与本地资源类型和名字不匹配的过滤器，查询本地所有资源，查询结果为空数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地全部资源列表方法,过滤条件类型为"apicloud",名字为"res"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组


    Scenario:[4006]正在执行清除缓存操作时，查询本地最新资源，查询结果为空数组
        Given 正在执行清除本地缓存操作
        When 调用查询本地最新资源列表方法
        Then 数据库根据名字和类型查询接口被调用"0"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组

    Scenario:[4007]已经成功执行完清除缓存操作，过滤器为空，本地资源为空时，查询本地最新资源，查询结果为空数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"空数组"
        When 调用查询本地最新资源列表方法
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组

    Scenario:[4008]本地已缓存资源，过滤器为空，查询本地最新资源，查询结果为去掉重复资源，保留标识为最新的资源数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地最新资源列表方法
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果数组如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | true           |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | true           |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | true           |
            | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           |
            | 10 | configAPP        | resJ | 1.0.1   | http://resJ.json        | hashJ  |        |         |         |           |                                       | true           |

    Scenario:[4009]本地已缓存资源，设置与本地资源类型和名字匹配的过滤器，查询本地最新资源，查询结果为与过滤器匹配的资源信息数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地最新资源列表方法,过滤条件类型为"config",名字为"cfg1"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果数组如下:
            | Id | Type   | Name | Version | Link                    | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 7  | config | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1 | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | true           |

    Scenario:[4010]本地已缓存资源，设置与本地资源类型和名字不匹配的过滤器，查询本地最新资源，查询结果为空数组
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地最新资源列表方法,过滤条件类型为"h5",名字为"resB"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果为空数组


    Scenario:[4011]正在执行清除缓存操作时，查询最新资源，查询结果为空对象
        Given 正在执行清除本地缓存操作
        When 调用查询本地最新资源方法,传入参数类型为"h5",名字为"resA"
        Then 数据库根据名字和类型查询接口被调用"0"次
        Then 查询结果为空对象

    Scenario Outline:[4012]已经成功执行完清除缓存操作，本地数据库为空时，查询本地最新资源，无论名字和类型与本地资源是否匹配，查询结果均为空对象
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",查询资源结果为"空数组"
        When 调用查询本地最新资源方法,传入参数类型为"<TypeA>",名字为"<NameB>"
        Then 数据库根据名字和类型查询接口被调用"<Count>"次,参数名字为"<NameB>",类型为"<TypeA>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | Count |
            | h5     | resA   | 1     |
            | h5     | resB   | 1     |
            | 空对象 | resB   | 0     |
            | h5     | 空对象 | 0     |
            | 空对象 | 空对象 | 0     |

    Scenario:[4013]本地已缓存资源，通过名字和类型查询本地最新资源,当名字和类型同时匹配时,查询匹配条件的资源信息。
        Given 数据库代理根据名字"resA",类型"h5",查询资源结果为"匹配结果"
        When 调用查询本地最新资源方法,传入参数类型为"h5",名字为"resA"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resA",类型为"h5"
        Then 查询资源结果如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | true           |

    Scenario Outline:[4014]本地已缓存资源，通过名字和类型查询本地最新资源,当名字或类型为空对象、或者不匹配时,查询结果为空对象
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",查询资源结果为"匹配结果"
        When 调用查询本地最新资源方法,传入参数类型为"<TypeA>",名字为"<NameB>"
        Then 数据库根据名字和类型查询接口被调用"<Count>"次,参数名字为"<NameB>",类型为"<TypeA>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | Count |
            | h5     | resB   | 1     |
            | 空对象 | resB   | 0     |
            | h5     | 空对象 | 0     |
            | 空对象 | 空对象 | 0     |

    Scenario:[4015]正在执行清除缓存操作时，查询本地指定资源，查询结果为空对象
        Given 正在执行清除本地缓存操作
        When 调用查询本地指定资源方法,传入参数类型为"h5",名字为"resA",版本为"1.0.0"
        Then 数据库根据名字、类型和版本查询接口被调用"0"次,参数名字为"h5",类型为"resA",版本为"1.0.0"
        Then 查询结果为空对象

    Scenario Outline:[4016]已经成功执行完清除缓存操作，查询指定资源,无论名字、类型和版本与本地资源是否匹配，查询结果均为空对象
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",版本"<VersionC>",查询资源结果为"空对象"
        When 调用查询本地指定资源方法,传入参数类型为"<TypeA>",名字为"<NameB>",版本为"<VersionC>"
        Then 数据库根据名字、类型和版本查询接口被调用"<Count>"次,参数名字为"<NameB>",类型为"<TypeA>",版本为"<VersionC>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | VersionC | Count |
            | h5     | resA   | 1.0.0    | 1     |
            | h5     | resA   | 1.0.1    | 1     |
            | h5     | resA   | 空对象   | 0     |
            | h5     | 空对象 | 空对象   | 0     |
            | 空对象 | 空对象 | 空对象   | 0     |
            | h5     | 空对象 | 1.0.0    | 0     |
            | 空对象 | resA   | 1.0.0    | 0     |
            | 空对象 | 空对象 | 1.0.0    | 0     |

    Scenario:[4017]本地已缓存资源,查询指定资源,当名字、类型和版本同时匹配时,查询匹配条件的资源信息
        Given 数据库代理根据名字"resA",类型"h5",版本"1.0.0",查询资源结果为"匹配结果"
        When 调用查询本地指定资源方法,传入参数类型为"h5",名字为"resA",版本为"1.0.0"
        Then 数据库根据名字、类型和版本查询接口被调用"1"次,参数名字为"resA",类型为"h5",版本为"1.0.0"
        Then 查询资源结果如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 | true           |

    Scenario Outline:[4018]本地已缓存资源,查询指定资源,当名字或类型或版本为空对象、或者不匹配时,查询结果为空
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",版本"<VersionC>",查询资源结果为"匹配结果"
        When 调用查询本地指定资源方法,传入参数类型为"<TypeA>",名字为"<NameB>",版本为"<VersionC>"
        Then 数据库根据名字、类型和版本查询接口被调用"<Count>"次,参数名字为"<NameB>",类型为"<TypeA>",版本为"<VersionC>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | VersionC | Count |
            | h5     | resA   | 1.0.1    | 1     |
            | h5     | resA   | 空对象   | 0     |
            | h5     | 空对象 | 空对象   | 0     |
            | 空对象 | 空对象 | 空对象   | 0     |
            | h5     | 空对象 | 1.0.0    | 0     |
            | 空对象 | resA   | 1.0.0    | 0     |
            | 空对象 | 空对象 | 1.0.0    | 0     |
            | 空对象 | resA   | 空对象   | 0     |

    Scenario:[4019]正在执行清除缓存操作时，通过名字和类型查询已安装的最新资源，查询结果为空对象
        Given 正在执行清除本地缓存操作
        When 调用查询本地已安装的最新资源方法,传入参数类型为"h5",名字为"resA"
        Then 数据库根据名字和类型查询接口被调用"0"次,参数名字为"resA",类型为"h5"
        Then 查询结果为空对象


    Scenario Outline:[4020]已经成功执行完清除缓存操作，通过名字和类型查询已安装的最新资源，无论名字和类型与本地资源是否匹配，查询结果均为空对象
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",查询资源结果为"空数组"
        When 调用查询本地已安装的最新资源方法,传入参数类型为"<TypeA>",名字为"<NameB>"
        Then 数据库根据名字和类型查询接口被调用"<Count>"次,参数名字为"<NameA>",类型为"<TypeA>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | Count |NameA   |
            | h5     | resA   | 1     | resA    |
            | h5     | resB   | 1     | resB    |
            | 空对象 | resB   | 0     |resB    |
            | h5     | 空对象 | 0     |空对象     |
            | 空对象 | 空对象 | 0     |空对象     |
            | video | xiaoyou    | 1     | xiaoyou |
            | video | xiaoyou_01 | 1     | xiaoyou |

    Scenario Outline:[4021-1]本地已缓存资源，查询本地已安装的最新资源,当名字和类型同时匹配时,查询匹配条件的资源信息。
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest |
            | 11 | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           |
        Given 数据库代理根据名字"<ResName>",类型"<ResType>",查询资源结果为"匹配结果"
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path      |
            | <ResPath> |
        When 调用查询本地已安装的最新资源方法,传入参数类型为"<ResType>",名字为"<ResName>"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"<ResName>",类型为"<ResType>"
        Then 查询资源结果如下:
            | Id   | Type   | Name   | Version   | Link   | MD5   | Model   | TypeId   | ProdNo   | TypeCode   | Path   | isServerLatest   |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> | <Path> | <isServerLatest> |
        Examples:
            | ResPath                               | ResType | ResName | Id | Type  | Name    | Version | Link               | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | /path/to/resource/H5ResPkg/resC@1.0.1 | h5      | resC    | 5  | h5    | resC    | 1.0.1   | http://resC.zip    | hashC | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           |
            | /path/to/resource/video/xiaoyou@1.0.0 | video   | xiaoyou | 11 | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |        |         |         |           | /path/to/resource/video/xiaoyou@1.0.0 | true           |

    Scenario Outline:[4021-2]本地已缓存小优资源，查询本地已安装的最新小优资源,当类型为小优视频名字为小优资源源文件名字时,查询匹配条件的资源信息。
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest |
            | 11 | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           |
        Given 数据库代理根据名字"<ResName>",类型"<ResType>",查询资源结果为"匹配结果"
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path      |
            | <ResPath> |
        Given 设置文件"<ResPath>"的子目录列表为:
            | SubFilePath  | Type |
            | <ResSubPath> | 文件   |
        Given 文件系统通过文件路径获取文件名称列表如下:
            | Path                                                 | FileName       |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou.mp4    | xiaoyou.mp4    |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou_01.mp4 | xiaoyou_01.mp4 |
        When 调用查询本地已安装的最新资源方法,传入参数类型为"<Type>",名字为"<Name>"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"<ResName>",类型为"<ResType>"
        Then 查询资源结果如下:
            | Id | Type  | Name       | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                                 | isServerLatest |
            | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> | <Model> | <TypeId> | <ProdNo> | <TypeCode> | <Path> | <isServerLatest> |
        Examples:
            | ResSubPath                                           | ResPath                               | ResType | ResName | Id | Type  | Name       | Version | Link               | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                                 | isServerLatest |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou.mp4    | /path/to/resource/video/xiaoyou@1.0.0 | video   | xiaoyou | 11 | video | xiaoyou    | 1.0.0   | http://xiaoyou.zip | hashX |        |         |         |           | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou.mp4    | true           |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou_01.mp4 | /path/to/resource/video/xiaoyou@1.0.0 | video   | xiaoyou | 11 | video | xiaoyou_01 | 1.0.0   | http://xiaoyou.zip | hashX |        |         |         |           | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou_01.mp4 | true           |

    Scenario:[4021-3]本地已缓存小优资源，查询本地已安装的最新小优资源,当类型为小优视频名字为本地不存在的小优资源源文件名字时,返回空对象。
        Given 数据库代理根据名字和类型查询所有的资源列表如下:
            | Id | Type  | Name    | Version | Link               | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  | isServerLatest |
            | 11 | video | xiaoyou | 1.0.0   | http://xiaoyou.zip | hashX |       |        |        |          | /path/to/resource/video/xiaoyou@1.0.0 | true           |
        Given 数据库代理根据名字"xiaoyou",类型"video",查询资源结果为"匹配结果"
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path                                  |
            | /path/to/resource/video/xiaoyou@1.0.0 |
        Given 设置文件"/path/to/resource/video/xiaoyou@1.0.0"的子目录列表为:
            | SubFilePath                                          | Type |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou_01.mp4 | 文件   |
        Given 文件系统通过文件路径获取文件名称列表如下:
            | Path                                                 | FileName       |
            | /path/to/resource/video/xiaoyou@1.0.0/xiaoyou_01.mp4 | xiaoyou_01.mp4 |
        When 调用查询本地已安装的最新资源方法,传入参数类型为"video",名字为"xiaoyou_02"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"xiaoyou",类型为"video"
        Then 查询结果为空对象

    Scenario Outline:[4022]本地已缓存资源，查询本地已安装的最新资源,当名字或类型为空对象、或者不匹配时,查询结果为空对象
        Given 数据库代理根据名字"<NameB>",类型"<TypeA>",查询资源结果为"匹配结果"
        When 调用查询本地已安装的最新资源方法,传入参数类型为"<TypeA>",名字为"<NameB>"
        Then 数据库根据名字和类型查询接口被调用"<Count>"次,参数名字为"<NameB>",类型为"<TypeA>"
        Then 查询结果为空对象
        Examples:
            | TypeA  | NameB  | Count |
            | h5     | resB   | 1     |
            | 空对象 | resB   | 0     |
            | h5     | 空对象 | 0     |
            | 空对象 | 空对象 | 0     |

    Scenario:[4023]本地已缓存资源，查询本地已安装的最新资源,当名字和类型同时匹配时,但资源未安装，查询结果为空对象
        Given 数据库代理根据名字"resM",类型"mPaaS",查询资源结果为"匹配结果"
        When 调用查询本地已安装的最新资源方法,传入参数类型为"mPaaS",名字为"resM"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resM",类型为"mPaaS"
        Then 查询结果为空对象


    Scenario:[4024]获取指定类型存储路径,当类型为非指定类型时，获取结果为空对象
        When 调用获取指定类型资源的安装路径方法,传入参数类型"AllType"
        Then 获取文件路径结果为"空对象"

    Scenario:[4025]查询类型资源已安装，获取指定类型存储路径，返回对应资源存储路径
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path                       |
            | /path/to/resource/H5ResPkg |
        When 调用获取指定类型资源的安装路径方法,传入参数类型"h5"
        Then 获取文件路径结果为"/path/to/resource/H5ResPkg"


    Scenario Outline:[4026]查询类型资源未安装，获取指定类型存储路径，需创建指定文件夹后返回对应资源存储路径
        Given 创建文件夹结果为"<CreateResult>"
        When 调用获取指定类型资源的安装路径方法,传入参数类型"mPaaS"
        Then 创建文件夹接口被调用"1"次,参数为"/path/to/resource/mPaaS"
        Then 获取文件路径结果为"<ReturnResult>"
        Examples:
            | CreateResult | ReturnResult            |
            | 成功         | /path/to/resource/mPaaS |
            | 失败         | 空对象                  |


    Scenario:[4027]正在执行清除缓存操作时，查询本地普通资源列表，查询结果返回空对象
        Given 正在执行清除本地缓存操作
        When 调用查询普通资源列表方法,传入参数类型为"h5",名字为"resA"
        Then 数据库查询请求条件接口被调用"0"次,参数方法来源为"",App版本号为"",参数组合条件为""
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询普通资源结果为空对象

    Scenario Outline: [4028]查询本地普通资源列表，当名称为空或者资源类型config或者configAPP时，查询结果返回空对象
        When 调用查询普通资源列表方法,传入参数类型为"<TypeA>",名字为"<NameB>"
        Then 数据库查询请求条件接口被调用"0"次,参数方法来源为"",App版本号为"",参数组合条件为""
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询普通资源结果为空对象
        Examples:
            | TypeA  | NameB  |
            | config | resB   |
            | h5     | 空对象 |

    Scenario: [4029]查询普通资源列表，数据库查询请求条件失败时，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"空对象"
        When 调用查询普通资源列表方法,传入参数类型为"h5",名字为"resA"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"0",App版本号为"6.9.0",参数组合条件为"rt=h5,name=resA"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询普通资源结果为空对象

    Scenario Outline: [4030]查询普通资源列表，数据库查询关联资源列表失败时，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        When 调用查询普通资源列表方法,传入参数类型为"<TypeName>",名字为"<ResName>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 查询普通资源结果为空对象
        Examples:
            | Query                                            | TypeName | ResName | FromFun | AppVersion | ConditionStr          |
            | appversion=6.9.0,fromFun=0,rt=h5,name=resA       | h5       | resA    | 0       | 6.9.0      | rt=h5,name=resA       |
            | appversion=6.9.0,fromFun=0,rt=apicloud,name=resB | apicloud | resB    | 0       | 6.9.0      | rt=apicloud,name=resB |

    Scenario: [4031]已经成功执行完清除缓存操作,查询普通资源列表，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"空对象"
        When 调用查询普通资源列表方法,传入参数类型为"h5",名字为"resA"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"0",App版本号为"6.9.0",参数组合条件为"rt=h5,name=resA"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询普通资源结果为空对象

    Scenario: [4032]查询普通资源列表,当查询条件有关联资源列表时，查询结果不为空
        Given 数据库查询请求条件接口执行结果为"appversion=6.9.0,fromFun=0,rt=h5,name=resA"
        Given 查询条件为"appversion=6.9.0,fromFun=0,rt=h5,name=resA"相关联的资源列表如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | true           |
        When 调用查询普通资源列表方法,传入参数类型为"h5",名字为"resA"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"0",App版本号为"6.9.0",参数组合条件为"rt=h5,name=resA"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"appversion=6.9.0,fromFun=0,rt=h5,name=resA"
        Then 查询资源结果数组如下:
            | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
            | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      | true           |

    Scenario:[4033]正在执行清除缓存操作时，查询设备资源列表，查询结果返回空对象
        Given 正在执行清除本地缓存操作
        When 调用查询设备资源列表方法,传入参数条件为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 数据库查询请求条件接口被调用"0"次,参数方法来源为"",App版本号为"",参数组合条件为""
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询设备资源结果为空对象

    Scenario: [4034]查询设备资源列表，当查询条件对象为空时，查询结果返回空对象
        When 调用查询设备资源列表方法,传入参数条件为"空对象"
        Then 数据库查询请求条件接口被调用"0"次,参数方法来源为"",App版本号为"",参数组合条件为""
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询设备资源结果为空对象


    Scenario: [4035]查询设备资源列表，数据库查询请求条件失败时，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"空对象"
        When 调用查询设备资源列表方法,传入参数条件为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"2",App版本号为"6.9.0",参数组合条件为"rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次,参数查询条件为""
        Then 查询设备资源结果为空对象

    Scenario Outline: [4036]查询设备资源列表，数据库查询关联资源列表失败时，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 数据通过查询条件查询资源列表接口执行结果为"失败"
        When 调用查询设备资源列表方法,传入参数条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 查询设备资源结果为空对象
        Examples:
            | Query                                                                                                        | Condition                                                                                                    | ResId | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                               | Latest | FromFun | AppVersion | ConditionStr                                                                      |
            | appversion=6.9.0,fromFun=2,rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | appversion=6.9.0,fromFun=2,rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 6     | apicloud         | resD    | 2.0.1      | http://resD.zip         | hashD   | modelB   | typeIdB   | prodNoB   | typeCodeB   | /path/to/resource/ApiCloud/ResD@2.0.1 | false  | 2       | 6.9.0      | rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          | 7     | config           | cfg1    | 1.2.0      | http://cfg1.signed.json | hash1   | modelB   | typeIdB   | prodNoB   | typeCodeB   | /path/to/resource/ApiCloud/cfg1@1.2.0 | true   | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             | 8     | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   |          |           | prodNoB   |             |                                       | true   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             |

    Scenario:[4037]已经成功执行完清除缓存操作,查询设备资源列表，查询结果返回空对象
        Given 数据库查询请求条件接口执行结果为"空对象"
        When 调用查询设备资源列表方法,传入参数条件为"appversion=6.9.0,fromFun=2,rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"2",App版本号为"6.9.0",参数组合条件为"rt=h5,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB"
        Then 数据库通过查询条件查询资源列表接口被调用"0"次
        Then 查询设备资源结果为空对象

    Scenario Outline: [4038]查询设备资源列表,当查询条件有关联资源列表时，查询结果不为空
        Given 数据库查询请求条件接口执行结果为"<Query>"
        Given 查询条件为"<Query>"相关联的资源列表如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        When 调用查询设备资源列表方法,传入参数条件为"<Condition>"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"<FromFun>",App版本号为"<AppVersion>",参数组合条件为"<ConditionStr>"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"<Query>"
        Then 查询资源结果数组如下:
            | Id      | Type       | Name      | Version      | Link      | MD5       | Model      | TypeId      | ProdNo      | TypeCode      | Path      | isServerLatest |
            | <ResId> | <TypeName> | <ResName> | <ResVersion> | <ResLink> | <HashMD5> | <ResModel> | <ResTypeId> | <ResProdNo> | <ResTypeCode> | <ResPath> | <Latest>       |
        Examples:
            | Query                                                                                                        | Condition                                                                                                    | ResId | TypeName         | ResName | ResVersion | ResLink                 | HashMD5 | ResModel | ResTypeId | ResProdNo | ResTypeCode | ResPath                               | Latest | FromFun | AppVersion | ConditionStr                                                                      |
            | appversion=6.9.0,fromFun=2,rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | appversion=6.9.0,fromFun=2,rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB | 6     | apicloud         | resD    | 2.0.1      | http://resD.zip         | hashD   | modelB   | typeIdB   | prodNoB   | typeCodeB   | /path/to/resource/ApiCloud/ResD@2.0.1 | false  | 2       | 6.9.0      | rt=apicloud,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=typeCodeB,dnt=deviceNetTypeB |
            | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          | appversion=6.9.0,fromFun=1,rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          | 7     | config           | cfg1    | 1.2.0      | http://cfg1.signed.json | hash1   | modelB   | typeIdB   | prodNoB   | typeCodeB   | /path/to/resource/ApiCloud/cfg1@1.2.0 | true   | 1       | 6.9.0      | rt=config,name=,md=modelB,ti=typeIdB,pn=prodNoB,tc=,dnt=                          |
            | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             | appversion=6.9.0,fromFun=3,rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             | 8     | deviceCustomInfo | resZ    | 1.2.0      | http://resZ.zip         | hashZ   |          |           | prodNoB   |             |                                       | true   | 3       | 6.9.0      | rt=deviceCustomInfo,name=,md=,ti=,pn=prodNoB,tc=,dnt=                             |

    Scenario: [4039]本地已缓存资源的都未标识为最新资源，过滤器为空，查询本地最新资源时，查询结果为去掉重复资源，保留版本号最大的资源数组
        Given  数据库代理中所有资源列表如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | false          |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | false          |
            | 3  | mPaaS            | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           |                                       | false          |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | false          |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | false          |
            | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | false          |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | false          |
            | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | false          |
            | 10 | configAPP        | resJ | 1.0.1   | http://resJ.json        | hashJ  |        |         |         |           |                                       | false          |
        Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
        When 调用查询本地最新资源列表方法
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"空对象",类型为"AllType"
        Then 查询资源结果数组如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | false          |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | false          |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | false          |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | false          |
            | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | false          |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | false          |
            | 10 | configAPP        | resJ | 1.0.1   | http://resJ.json        | hashJ  |        |         |         |           |                                       | false          |

    Scenario: [4040]本地已缓存多个同类型同名称资源，通过名字和类型查询本地最新资源，当名字和类型同时匹配时，返回标识为最新的资源信息
        Given 数据库代理根据名字"resD",类型"apicloud",查询资源结果为"匹配结果"
        When 调用查询本地最新资源方法,传入参数类型为"apicloud",名字为"resD"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resD",类型为"apicloud"
        Then 查询资源结果如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 9  | apicloud | resD | 2.0.0   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           |

    Scenario: [4041]从运营平台获取并缓存多个同类型同名称资源，通过名字和类型查询本地最新资源，当名字和类型同时匹配时，返回标识为最新且版本号最大的资源信息
        Given 数据库代理根据名字"resM",类型"mPaaS",查询资源结果为"匹配结果"
        When 调用查询本地最新资源方法,传入参数类型为"mPaaS",名字为"resM"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resM",类型为"mPaaS"
        Then 查询资源结果如下:
            | Id | Type  | Name | Version | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
            | 4  | mPaaS | resM | 2.0.0   | http://resM1.zip | hashM1 |       |        |        |          |      | true           |

    Scenario: [4042]本地已缓存的资源都未标识为最新资源，通过名字和类型查询本地最新资源，当名字和类型同时匹配时，返回版本号最大的资源信息
        Given  数据库代理中所有资源列表如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | false          |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | false          |
            | 3  | mPaaS            | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           |                                       | false          |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | false          |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | false          |
            | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | false          |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | false          |
            | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | false          |
        Given 数据库代理根据名字"resD",类型"apicloud",查询资源结果为"匹配结果"
        When 调用查询本地最新资源方法,传入参数类型为"apicloud",名字为"resD"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resD",类型为"apicloud"
        Then 查询资源结果如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 6  | apicloud | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |

    Scenario: [4043]本地已缓存多个同类型同名称资源，查询本地已安装的最新资源，当名字和类型同时匹配时，返回匹配条件且标识为最新的资源信息
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path                                  |
            | /path/to/resource/ApiCloud/ResD@2.0.0 |
        Given 数据库代理根据名字"resD",类型"apicloud",查询资源结果为"匹配结果"
        When 调用查询本地已安装的最新资源方法,传入参数类型为"apicloud",名字为"resD"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resD",类型为"apicloud"
        Then 查询资源结果如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 9  | apicloud | resD | 2.0.0   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           |

    Scenario: [4044]本地已缓存的资源都未标识为最新资源，查询本地已安装的最新资源，当名字和类型同时匹配时，返回匹配条件且版本号最大的资源信息
        Given  数据库代理中所有资源列表如下:
            | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | false          |
            | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | false          |
            | 3  | mPaaS            | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           |                                       | false          |
            | 4  | mPaaS            | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           |                                       | false          |
            | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | false          |
            | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
            | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | false          |
            | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | false          |
            | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | false          |
        Given 文件系统中"存在"的"文件夹"路径如下:
            | Path                                  |
            | /path/to/resource/ApiCloud/ResD@2.0.1 |
        Given 数据库代理根据名字"resD",类型"apicloud",查询资源结果为"匹配结果"
        When 调用查询本地已安装的最新资源方法,传入参数类型为"apicloud",名字为"resD"
        Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resD",类型为"apicloud"
        Then 查询资源结果如下:
            | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
            | 6  | apicloud | resD | 2.0.1   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |

    Scenario: [4045]查询configAPP类型普通资源列表,当查询条件有关联资源列表时，查询结果不为空
        Given 数据库查询请求条件接口执行结果为"appversion=6.9.0,fromFun=0,rt=configAPP,name=resE"
        Given 查询条件为"appversion=6.9.0,fromFun=0,rt=configAPP,name=resE"相关联的资源列表如下:
            | Id | Type      | Name | Version | Link                    | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
            | 1  | configAPP | resE | 1.0.0   | http://resE.signed.json | hashE |       |        |        |          |      | true           |
        When 调用查询普通资源列表方法,传入参数类型为"configAPP",名字为"resE"
        Then 数据库查询请求条件接口被调用"1"次,参数方法来源为"0",App版本号为"6.9.0",参数组合条件为"rt=configAPP,name=resE"
        Then 数据库通过查询条件查询资源列表接口被调用"1"次,参数查询条件为"appversion=6.9.0,fromFun=0,rt=configAPP,name=resE"
        Then 查询资源结果数组如下:
            | Id | Type      | Name | Version | Link                    | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
            | 1  | configAPP | resE | 1.0.0   | http://resE.signed.json | hashE |       |        |        |          |      | true           |

    Scenario Outline: [4046]本地已缓存同名称同类型的多个版本的资源，查询该名称和类型的最新资源时，返回符合条件的版本号最大的资源信息，当版本格式存在不合法时，返回缓存列表中的第一个资源信息
      Given  数据库代理中所有资源列表如下:
        | Id | Type  | Name | Version       | Link             | MD5    | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
        | 1  | mPaaS | resL | <resVersion1> | http://resL.zip  | hashL  |       |        |        |          |      | true           |
        | 2  | mPaaS | resL | <resVersion2> | http://resL1.zip | hashL1 |       |        |        |          |      | true           |
      Given 数据库代理根据名字"resL",类型"mPaaS",查询资源结果为"匹配结果"
      When 调用查询本地最新资源方法,传入参数类型为"mPaaS",名字为"resL"
      Then 数据库根据名字和类型查询接口被调用"1"次,参数名字为"resL",类型为"mPaaS"
      Then 查询资源结果如下:
        | Id   | Type  | Name | Version   | Link   | MD5   | Model | TypeId | ProdNo | TypeCode | Path | isServerLatest |
        | <Id> | mPaaS | resL | <Version> | <Link> | <MD5> |       |        |        |          |      | true           |
      Examples:
        | resVersion1        | resVersion2        | Id | Version            | Link             | MD5    |
        | 1.2.1              | 1.2.1              | 1  | 1.2.1              | http://resL.zip  | hashL  |
        | 1.2.1              | 1.0.1              | 1  | 1.2.1              | http://resL.zip  | hashL  |
        | 1.0.1              | 1.2.1              | 2  | 1.2.1              | http://resL1.zip | hashL1 |
        | 1.2.1              | 1.2.1_2022033101   | 1  | 1.2.1              | http://resL.zip  | hashL  |
        | 1.2.1_2022033101   | 1.2.1              | 2  | 1.2.1              | http://resL1.zip | hashL1 |
        | 1.2.1_2022033101   | 1.0.1              | 1  | 1.2.1_2022033101   | http://resL.zip  | hashL1 |
        | 1.2.1              | *******.2022033101 | 1  | 1.2.1              | http://resL.zip  | hashL  |
        | *******.2022033101 | 1.2.1              | 2  | 1.2.1              | http://resL1.zip | hashL1 |
        | *******.2022033101 | 1.0.1              | 1  | *******.2022033101 | http://resL.zip  | hashL  |
        | 1.2.1_2022033101   | 1.2.1_2022033102   | 2  | 1.2.1_2022033102   | http://resL1.zip | hashL1 |
        | 1.2.1_2022033102   | 1.2.1_2022033101   | 1  | 1.2.1_2022033102   | http://resL.zip  | hashL  |
        | 1.2.1_2022033101   | *******.2022033101 | 2  | *******.2022033101 | http://resL1.zip | hashL1 |
        | 1.2.1_2022033101   | *******.2022033101 | 1  | 1.2.1_2022033101   | http://resL.zip  | hashL  |
        | *******.2022033101 | 1.2.1_2022033101   | 1  | *******.2022033101 | http://resL.zip  | hashL  |
        | *******.2022033102 | *******.2022033101 | 1  | *******.2022033102 | http://resL.zip  | hashL  |
        | *******.2022033102 | *******.2022033102 | 1  | *******.2022033102 | http://resL.zip  | hashL  |
        | *******.2022033102 | *******.2022033102 | 2  | *******.2022033102 | http://resL1.zip | hashL1 |
        | 0.0.1              | _1.0.0_            | 1  | 0.0.1              | http://resL.zip  | hashL  |
        | 0.0.1              | 1._.0              | 1  | 0.0.1              | http://resL.zip  | hashL  |
        | 0                  | *******.2022033102 | 1  | 0                  | http://resL.zip  | hashL  |
        | 1.0.0.2011         | *******.2022033102 | 1  | 1.0.0.2011         | http://resL.zip  | hashL  |
        | 1.0.0.2011         | 1.0.0.-99.100      | 1  | 1.0.0.2011         | http://resL.zip  | hashL  |
        | ....               | ..                 | 1  | ....               | http://resL.zip  | hashL  |
        | 1.2.               |                    | 1  | 1.2.               | http://resL.zip  | hashL  |
        | null               | 1.0.0              | 1  | null               | http://resL.zip  | hashL  |
        | a.b.c              | 1.0.0              | 1  | a.b.c              | http://resL.zip  | hashL  |
        | 10+1.0x123.-9      | 1.0.0              | 1  | 10+1.0x123.-9      | http://resL.zip  | hashL  |

    Scenario:[4047]正在执行清除缓存操作时，通过名字列表和类型查询已安装的最新资源列表，查询结果为空对象
      Given 正在执行清除本地缓存操作
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"h5",名字为"resA,resC"
      Then 数据库根据名字和类型查询接口被调用"0"次
      Then 查询结果为空对象

    Scenario Outline:[4048]已经成功执行完清除缓存操作，通过名字列表和类型查询已安装的最新资源，无论名字和类型与本地资源是否匹配，查询结果均为空对象
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"空数组"
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"<TypeA>",名字为"<NameB>"
      Then 数据库根据名字和类型查询接口被调用"<Count>"次
      Then 查询结果为空对象
      Examples:
        | TypeA | NameB     | Count |
        | h5    | resA,resC | 1     |
        | 空对象   | resA,resC | 1     |
        | h5    | 空对象       | 1     |
        | 空对象   | 空对象       | 1     |

    Scenario:[4049]本地已缓存资源，通过名字列表和类型查询已安装的最新资源,当名字和类型同时匹配时,查询匹配条件的资源信息。
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
      Given 文件系统中"存在"的"文件夹"路径如下:
        | Path                                  |
        | /path/to/resource/H5ResPkg/resC@1.0.1 |
        | /path/to/resource/H5ResPkg/resA@1.0.0 |
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"h5",名字为"resA,resC"
      Then 数据库根据名字和类型查询接口被调用"1"次
      Then 查询资源结果数组如下:
        | Id | Type | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest | ResStatus |
        | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | true           | 0         |
        | 5  | h5   | resC | 1.0.1   | http://resC.zip | hashC | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | true           | 0         |

    Scenario Outline:[4050]本地已缓存资源，查询本地已安装的最新资源,当名字列表或类型为空对象、或者不匹配时,查询结果为空对象
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"<TypeA>",名字为"<NameB>"
      Then 数据库根据名字和类型查询接口被调用"<Count>"次
      Then 查询结果为空对象
      Examples:
        | TypeA | NameB | Count |
        | h5    | resB  | 1     |
        | 空对象   | resB  | 1     |
        | h5    | 空对象   | 1     |
        | 空对象   | 空对象   | 1     |

    Scenario:[4051]本地已缓存资源，查询本地已安装的最新资源,当名字列表和类型同时匹配时,但资源未安装，查询结果为空对象
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"mPaaS",名字为"resM"
      Then 数据库根据名字和类型查询接口被调用"1"次
      Then 查询结果为空对象

    Scenario: [4052]本地已缓存多个同类型同名称资源，查询本地已安装的最新资源，当名字列表和类型同时匹配时，返回匹配条件且标识为最新的资源信息
      Given 文件系统中"存在"的"文件夹"路径如下:
        | Path                                  |
        | /path/to/resource/ApiCloud/ResD@2.0.0 |
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"apicloud",名字为"resD"
      Then 数据库根据名字和类型查询接口被调用"1"次
      Then 查询资源结果数组如下:
        | Id | Type     | Name | Version | Link            | MD5   | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
        | 9  | apicloud | resD | 2.0.0   | http://resD.zip | hashD | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | true           |

    Scenario: [4053]本地已缓存的资源都未标识为最新资源，查询本地已安装的最新资源，当名字列表和类型同时匹配时，返回匹配条件且版本号最大的资源信息
      Given  数据库代理中所有资源列表如下:
        | Id | Type             | Name | Version | Link                    | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
        | 1  | h5               | resA | 1.0.0   | http://resA.zip         | hashA  |        |         |         |           | /path/to/resource/H5ResPkg/resA@1.0.0 | false          |
        | 2  | apicloud         | resB | 2.0.0   | http://resB.zip         | hashB  |        |         |         |           |                                       | false          |
        | 3  | apicloud         | resM | 1.0.0   | http://resM.zip         | hashM  |        |         |         |           | /path/to/resource/ApiCloud/resM@1.0.0 | false          |
        | 4  | apicloud         | resM | 2.0.0   | http://resM1.zip        | hashM1 |        |         |         |           | /path/to/resource/ApiCloud/resM@2.0.0 | false          |
        | 5  | h5               | resC | 1.0.1   | http://resC.zip         | hashC  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/H5ResPkg/resC@1.0.1 | false          |
        | 6  | apicloud         | resD | 2.0.1   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
        | 7  | config           | cfg1 | 1.2.0   | http://cfg1.signed.json | hash1  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/cfg1@1.2.0 | false          |
        | 8  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip         | hashZ  |        |         | prodNoB |           |                                       | false          |
        | 9  | apicloud         | resD | 2.0.0   | http://resD.zip         | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.0 | false          |
      Given 文件系统中"存在"的"文件夹"路径如下:
        | Path                                  |
        | /path/to/resource/ApiCloud/ResD@2.0.1 |
        | /path/to/resource/ApiCloud/ResD@2.0.0 |
        | /path/to/resource/ApiCloud/resM@1.0.0 |
        | /path/to/resource/ApiCloud/resM@2.0.0 |
      Given 数据库代理根据名字"空对象",类型"AllType",查询资源结果为"全部资源"
      When 调用查询本地已安装的最新资源列表方法,传入参数类型为"apicloud",名字为"resD,resM,resB"
      Then 数据库根据名字和类型查询接口被调用"1"次
      Then 查询资源结果数组如下:
        | Id | Type     | Name | Version | Link             | MD5    | Model  | TypeId  | ProdNo  | TypeCode  | Path                                  | isServerLatest |
        | 6  | apicloud | resD | 2.0.1   | http://resD.zip  | hashD  | modelB | typeIdB | prodNoB | typeCodeB | /path/to/resource/ApiCloud/ResD@2.0.1 | false          |
        | 4  | apicloud | resM | 2.0.0   | http://resM1.zip | hashM1 |        |         |         |           | /path/to/resource/ApiCloud/resM@2.0.0 | false          |

Feature: 安装和卸载资源
  功能范围:
  主要用于测试组件中安装和卸载资源相关的接口和功能。主要提供单资源安装和卸载、取消任务、获取正在执行的任务id
  安装和卸载的资源指从运营平台更新后缓存到本地的资源信息,通过资源管理器安装和卸载指定的资源包，安装和卸载资源的操作会返回任务id，也可以通过资源管理器取消指定的任务
  - 安装资源操作，指从运营平台下载指定的资源包到APP本地，然后进行md5校验，解压，移动，扫描首页，更新数据库资源信息操作，安装结果会通知指定的回调对象
  - 安装成功后，可通过资源查询接口查询到被安装的资源包信息，且安装路径Path为"/path/to/resource/typeDir/name@version"
  "/path/to/resource"是初始化时指定的路径
  "typeDir"是根据资源type指定不同的目录（h5对应"H5ResPkg",apicloud对应"ApiCloud",config对应"DeviceConfig",mpaas对应"mPaaS",deviceCustomInfo对应"DeviceCustomInfo"）
  "name@version"是资源名称和资源版本使用"@"拼接而成
  - 资源安装过程的任意步骤失败就会导致安装失败，反之安装成功，安装成功会更新被安装资源信息的"path"属性，"path"值为当前资源安装路径
  资源安装成功会返回成功标志和成功信息("资源安装成功"，"资源已安装")及返回用户传入且更新过"path"属性的的资源信息
  - 执行清空操作时，不能执行任务
  - 安装一个已安装资源的新版本，新版本资源会被安装，且其旧版本资源依旧存在
  - 取消任务指安装资源过程中取消下载资源的操作，其他阶段的操作取消不了

  外部依赖:
  - 资源清理器
  - 下载功能代理
  - 网络状态代理
  - 文件系统代理
  - 本地数据存储代理
  - 资源数据来源
  - 资源存储路径
  - 线程调度器

  接口说明:
  1.安装资源
  如果传入的资源信息已安装，则接口回调成功结果，否则进行安装资源操作，接口回调安装结果。
  2.卸载资源
  如果传入的资源信息未安装，则接口回调成功结果，否则进行卸载资源操作，接口回调卸载结果。
  3.取消任务
  如果传入的任务id为空对象或者空字符串，则接口返回失败结果；
  如果根据传入的任务id获取的任务为空对象，则接口返回失败结果，否则取消任务，接口返回成功结果。
  4.获取正在执行的任务id
  如果传入的资源信息为空对象，则接口返回空对象；如果传入的资源信息中名称、类型、版本任意为空，则接口返回空对象;
  如果根据传入的资源信息获取的任务为空对象，则接口返回空对象，否则返回任务id。

  Background:
    Given 初始化资源管理器,清理器为"模拟的",APP版本号为"6.9.0",数据存储的根路径为"/path/to/resource"
    Given 设置系统网络连接为"wifi"
    Given 设置系统网络状态为"可用"
    Given 资源安装结果上报代理使用模拟的
    Given 设置系统当前时间为"20200225 12:00:00"
    Given 设置用户id为"fake_user_id"
    Given 设置客户端id为"fake_client_id"

  Scenario:[6000]处于执行清除本地缓存操作时，执行安装资源包的操作，则安装失败，任务id返回空对象，安装结果返回资源包为空对象
    Given 正在执行清除本地缓存操作
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 安装资源的任务id为"空对象"
    Then 安装资源的异步回调结果为"失败",资源包为"空对象"
    Then 从网络下载资源链接接口被调用"0"次
    Then 文件MD5校验接口被调用"0"次
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"0"次
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 文件系统代理的判断文件接口被调用"0"次
    Then 更新资源数据库接口被调用"0"次
    Then 资源安装结果上报接口被调用"0"次

  Scenario:[6001]被安装的资源为空对象，执行安装资源包的操作，则安装失败，任务id返回空对象，安装结果返回资源包为空对象
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link | MD5 |
    Then 安装资源的任务id为"空对象"
    Then 安装资源的异步回调结果为"失败",资源包为"空对象"
    Then 从网络下载资源链接接口被调用"0"次
    Then 文件MD5校验接口被调用"0"次
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"0"次
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 文件系统代理的判断文件接口被调用"0"次
    Then 更新资源数据库接口被调用"0"次
    Then 资源安装结果上报接口被调用"0"次

  Scenario:[6001-1]在其他非安装任务执行时，触发安装，则安装失败，任务id返回空对象，安装结果返回资源包为空对象
    When 重新启动，使用异步线程调度器
    Given 判断文件是否存在等待"1"秒返回
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Path                                  |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA | /path/to/resource/H5ResPkg/resA@1.0.0 |
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 安装资源的任务id为"空对象"
    Then 安装资源的异步回调结果为"失败",资源包为"空对象"
    Then 从网络下载资源链接接口被调用"0"次
    Then 文件MD5校验接口被调用"0"次
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"0"次
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 文件系统代理的判断文件接口被调用"0"次
    Then 更新资源数据库接口被调用"0"次
    Then 资源安装结果上报接口被调用"0"次

  Scenario:[6001-2]同时安装超过最大同时安装资源数(5个)时，后续任务会进入等待队列，并在有资源完成时执行安装
    When 重新启动，使用异步线程调度器
    Given 网络下载资源等待"0.2"秒
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Priority |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA | height   |
      | 2  | h5   | resB | 1.0.0   | http://resB.zip | hashB | height   |
      | 3  | h5   | resC | 1.0.0   | http://resC.zip | hashC | height   |
      | 4  | h5   | resD | 1.0.0   | http://resD.zip | hashD | height   |
      | 5  | h5   | resE | 1.0.0   | http://resE.zip | hashE | height   |
      | 6  | h5   | resF | 1.0.0   | http://resF.zip | hashF | height   |
      | 7  | h5   | resG | 1.0.0   | http://resG.zip | hashG | middle   |
      | 8  | h5   | resH | 1.0.0   | http://resH.zip | hashH | low      |
    When 等待"0.1"秒
    Then 正在下载任务的Link列表为:
      | Link            |
      | http://resA.zip |
      | http://resB.zip |
      | http://resC.zip |
      | http://resD.zip |
      | http://resE.zip |
    When 等待"1"秒
    Then 从网络下载资源链接接口被调用"8"次
    Then 安装资源的异步回调结果为"失败",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
      | 2  | h5   | resB | 1.0.0   | http://resB.zip | hashB |
      | 3  | h5   | resC | 1.0.0   | http://resC.zip | hashC |
      | 4  | h5   | resD | 1.0.0   | http://resD.zip | hashD |
      | 5  | h5   | resE | 1.0.0   | http://resE.zip | hashE |
      | 6  | h5   | resF | 1.0.0   | http://resF.zip | hashF |
      | 7  | h5   | resG | 1.0.0   | http://resG.zip | hashG |
      | 8  | h5   | resH | 1.0.0   | http://resH.zip | hashH |

  Scenario:[6002]资源包信息已经从运营平台更新完成，被安装的资源更新信息失败，执行安装资源包的操作，则安装失败，任务id返回有效的任务id，安装结果返回资源包为空对象
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashA"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg            |
      | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Given 从网络下载资源链接"http://resA.zip"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/resA@1.0.0"至"/path/to/resource/H5ResPkg/resA@1.0.0"操作结果为"成功"
    Given 更新资源"h5,resA,1.0.0"到数据库的操作结果为"失败"
    Given 设置文件"/path/to/resource/H5ResPkg/resA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/resA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/resA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/resA@1.0.0/index.html | 文件   |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/resA@1.0.0 | resA@1.0.0 |
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"失败",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashC |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resA.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/resA@1.0.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/resA@1.0.0",目标路径为"/path/to/resource/H5ResPkg/resA@1.0.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"1"次,参数路径信息如下:
      | FilePath                              |
      | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
    Then 资源安装结果上报接口被调用"0"次

  Scenario:[6003]资源包信息已经从运营平台更新完成，被安装的资源已安装，执行安装资源包的操作，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息且其安装路径下有已安装的资源包文件,可以查询到该资源已被安装
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resC@1.0.1 |
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          | /path/to/resource/H5ResPkg/resC@1.0.1 |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          | /path/to/resource/H5ResPkg/resC@1.0.1 |
    Then 从网络下载资源链接接口被调用"0"次
    Then 文件MD5校验接口被调用"0"次
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"0"次
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 文件系统代理的判断文件接口被调用"0"次
    Then 更新资源数据库接口被调用"0"次
    Then 资源安装结果上报接口被调用"0"次

  Scenario Outline:[6004]资源包信息已经从运营平台更新完成，被安装资源未安装，执行安装资源包的操作，安装过程中从网络下载、md5校验、解压、移动、扫描首页、更新数据库资源信息任意过程时失败，则安装失败，任务id返回有效的任务id，返回安装失败的资源包信息且path为空对象，其安装路径下没有已安装的资源包文件
    Given 从网络下载资源链接"<Link>"操作结果为"<DownloadResult>"
    Given 文件系统中"<TempFileExist>"的"文件"路径如下:
      | Path   |
      | <File> |
    Given 文件系统中"<TempFileExist>"的"文件夹"路径如下:
      | Path           |
      | <Path>         |
      | <ResourcePath> |
    Given 设置文件"<Path>"的子目录列表为"<SubFileInfo>"
    Given 设置文件"<File>"的MD5值为"<Md5Value>"
    Given 文件系统解压资源"<File>"操作结果为"<UnzipResult>"
    Given 移动资源包"<ResPkg>"至"<Path>"操作结果为"<MoveResult>"
    Given 更新资源"<ResInfo>"到数据库的操作结果为"<UpdateDatabaseResult>"
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path   | ParentPath             |
      | <File> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path   | FileName   |
      | <Path> | <FileName> |
    When 调用安装资源方法,传入资源包如下:
      | Id   | Type   | Name   | Version   | Link   | MD5   |
      | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"<InstallResult>",资源包如下:
      | Id   | Type   | Name   | Version   | Link   | MD5   |
      | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> |
    Then 从网络下载资源链接接口被调用"<DownloadCount>"次
    Then 文件MD5校验接口被调用"<Md5Count>"次
    Then 解压资源接口被调用"<UnzipCount>"次
    Then 移动资源包接口被调用"<MoveCount>"次
    Then 文件系统代理的获取文件子路径列表接口被调用"<ListSubPathsCount>"次
    Then 更新资源数据库接口被调用"<UpdateDatabaseCount>"次
    Then 资源安装结果上报接口被调用"<InstallRateCount>"次
    Then 资源下载结果上报接口被调用"<DownloadCount>"次
    Examples:
      | Id | Type             | Name | Version | Link            | MD5   | DownloadResult | DownloadCount | TempFileExist | File                                  | Md5Value | Md5Count | UnzipResult | UnzipCount | ResPkg                            | ResourcePath                       | Path                                          | FileName   | MoveResult | MoveCount | ResInfo                     | SubFileInfo                                                                                                                                             | ListSubPathsCount | UpdateDatabaseResult | UpdateDatabaseCount | InstallResult | InstallRateCount |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA | 失败           | 1             | 不存在        | /path/to/resource/Temp/<EMAIL> | hashB    | 0        | 失败        | 0          | /path/to/resource/Temp/resA@1.0.0 | /path/to/resource/H5ResPkg         | /path/to/resource/H5ResPkg/resA@1.0.0         | resA@1.0.0 | 失败       | 0         | h5,resA,1.0.0               | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件    | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashB    | 1        | 失败        | 0          | /path/to/resource/Temp/resA@1.0.0 | /path/to/resource/H5ResPkg         | /path/to/resource/H5ResPkg/resA@1.0.0         | resA@1.0.0 | 失败       | 0         | h5,resA,1.0.0               | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件    | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashA    | 1        | 失败        | 1          | /path/to/resource/Temp/resA@1.0.0 | /path/to/resource/H5ResPkg         | /path/to/resource/H5ResPkg/resA@1.0.0         | resA@1.0.0 | 失败       | 0         | h5,resA,1.0.0               | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件    | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashA    | 1        | 成功        | 1          | /path/to/resource/Temp/resA@1.0.0 | /path/to/resource/H5ResPkg         | /path/to/resource/H5ResPkg/resA@1.0.0         | resA@1.0.0 | 失败       | 1         | h5,resA,1.0.0               | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件    | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 1  | h5               | resA | 1.0.0   | http://resA.zip | hashA | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashA    | 1        | 成功        | 1          | /path/to/resource/Temp/resA@1.0.0 | /path/to/resource/H5ResPkg         | /path/to/resource/H5ResPkg/resA@1.0.0         | resA@1.0.0 | 成功       | 1         | h5,resA,1.0.0               | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件    | 1                 | 失败                 | 1                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 失败           | 1             | 不存在        | /path/to/resource/Temp/<EMAIL> | hashB    | 0        | 失败        | 0          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 失败       | 0         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashB    | 1        | 失败        | 0          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 失败       | 0         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashZ    | 1        | 失败        | 1          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 失败       | 0         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashZ    | 1        | 成功        | 1          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 失败       | 1         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashZ    | 1        | 成功        | 1          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 成功       | 1         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 1                   | 失败          | 0                |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashZ    | 1        | 成功        | 1          | /path/to/resource/Temp/resZ@1.2.0 | /path/to/resource/DeviceCustomInfo | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 | 成功       | 1         | deviceCustomInfo,resZ,1.2.0 | /path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceCustomInfo/resZ@1.2.0/functionDisplay.json,文件夹       | 0                 | 失败                 | 1                   | 失败          | 0                |
      | 7  | configAPP        | resC | 1.3.0   | http://resC.zip | hashC | 失败           | 1             | 不存在        | /path/to/resource/Temp/<EMAIL> | hashC    | 0        | 失败        | 0          | /path/to/resource/Temp/resC@1.3.0 | /path/to/resource/configAPP        | /path/to/resource/configAPP/resC@1.3.0        | resC@1.3.0 | 失败       | 0         | configAPP,resC,1.3.0        | /path/to/resource/configAPP/resC@1.3.0/js,文件;/path/to/resource/configAPP/resC@1.3.0/css,文件夹;/path/to/resource/configAPP/resC@1.3.0/index.html,文件 | 0                 | 失败                 | 0                   | 失败          | 0                |
      | 8  | config           | resB | 1.2.0   | http://resB.zip | hashB | 成功           | 1             | 存在          | /path/to/resource/Temp/<EMAIL> | hashA    | 1        | 失败        | 0          | /path/to/resource/Temp/resB@1.2.0 | /path/to/resource/DeviceConfig     | /path/to/resource/DeviceConfig/resB@1.2.0     | resB@1.2.0 | 失败       | 0         | DeviceConfig,resB,1.2.0     | /path/to/resource/DeviceConfig/resB@1.2.0/functionDisplay.json,文件;/path/to/resource/DeviceConfig/resB@1.2.0/functionDisplay.json,文件夹               | 0                 | 失败                 | 0                   | 失败          | 0                |


  Scenario Outline:[6005]资源包信息已经从运营平台更新完成，被安装资源为h5类型未安装，执行安装资源包的操作，从网络下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashA"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                      |
      | /path/to/resource/H5ResPkg                |
      | /path/to/resource/H5ResPkg/resA@1.0.0     |
      | /path/to/resource/H5ResPkg/resA@1.0.0/css |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/resA@1.0.0 | resA@1.0.0 |
    Given 从网络下载资源链接"http://resA.zip"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/resA@1.0.0"至"/path/to/resource/H5ResPkg/resA@1.0.0"操作结果为"成功"
    Given 更新资源"h5,resA,1.0.0"到数据库的操作结果为"成功"
    Given 设置文件"/path/to/resource/H5ResPkg/resA@1.0.0"的子目录列表为"<SubFileInfo1>"
    Given 设置文件"/path/to/resource/H5ResPkg/resA@1.0.0/css"的子目录列表为"<SubFileInfo2>"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resA.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/resA@1.0.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/resA@1.0.0",目标路径为"/path/to/resource/H5ResPkg/resA@1.0.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"<ListSubPathsCount>"次,参数路径信息如下:
      | FilePath       |
      | <SubFilePaths> |
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 资源安装结果上报接口被调用"1"次,参数资源名称为"resA",类型为"h5",版本为"1.0.0",是否预置为"NO",用户ID为"fake_user_id",客户端ID为"fake_client_id",app版本号为"6.9.0",安装时间为"1582603200000",灰度为"NO"
    Then 资源下载结果上报接口被调用"1"次
    Examples:
      | SubFileInfo1                                                                                                                                         | FilePath                                  | SubFileInfo2                                                                                                 | ListSubPathsCount | SubFilePaths                                                                    |
      | /path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件                                               | /path/to/resource/H5ResPkg/resA@1.0.0/css | /path/to/resource/H5ResPkg/resA@1.0.0/css/a,文件                                                             | 1                 | /path/to/resource/H5ResPkg/resA@1.0.0                                           |
      | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/index.html,文件 | /path/to/resource/H5ResPkg/resA@1.0.0/css | /path/to/resource/H5ResPkg/resA@1.0.0/css/a,文件                                                             | 1                 | /path/to/resource/H5ResPkg/resA@1.0.0                                           |
      | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹                                                       | /path/to/resource/H5ResPkg/resA@1.0.0/css | /path/to/resource/H5ResPkg/resA@1.0.0/css/a,文件夹;/path/to/resource/H5ResPkg/resA@1.0.0/css/index.html,文件 | 2                 | /path/to/resource/H5ResPkg/resA@1.0.0;/path/to/resource/H5ResPkg/resA@1.0.0/css |
      | /path/to/resource/H5ResPkg/resA@1.0.0/js,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css,文件夹                                                       | /path/to/resource/H5ResPkg/resA@1.0.0/css | /path/to/resource/H5ResPkg/resA@1.0.0/css/a,文件;/path/to/resource/H5ResPkg/resA@1.0.0/css/index.html,文件   | 2                 | /path/to/resource/H5ResPkg/resA@1.0.0;/path/to/resource/H5ResPkg/resA@1.0.0/css |

  Scenario:[6005-1]资源包信息已经从运营平台更新完成，被安装资源为deviceCustomInfo类型未安装，执行安装资源包的操作，从网络下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashZ"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                          |
      | /path/to/resource/DeviceCustomInfo            |
      | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                          | FileName   |
      | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 |
    Given 从网络下载资源链接"http://resZ.zip"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/resZ@1.2.0"至"/path/to/resource/DeviceCustomInfo/resZ@1.2.0"操作结果为"成功"
    Given 更新资源"deviceCustomInfo,resZ,1.2.0"到数据库的操作结果为"成功"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                          |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |       |        |        |          | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resZ.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/resZ@1.2.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/resZ@1.2.0",目标路径为"/path/to/resource/DeviceCustomInfo/resZ@1.2.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                          |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |       |        |        |          | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Then 资源安装结果上报接口被调用"1"次,参数资源名称为"resZ",类型为"deviceCustomInfo",版本为"1.2.0",是否预置为"NO",用户ID为"fake_user_id",客户端ID为"fake_client_id",app版本号为"6.9.0",安装时间为"1582603200000",灰度为"NO"
    Then 资源下载结果上报接口被调用"1"次

  Scenario:[6006]处于执行清除本地缓存操作时，执行卸载资源包的操作，则卸载失败，任务id返回为空对象，卸载结果返回资源包为空对象
    Given 正在执行清除本地缓存操作
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 卸载资源的任务id为"空对象"
    Then 卸载资源的异步回调结果为"失败",资源包为"空对象"
    Then 移除资源包接口被调用"0"次
    Then 更新资源数据库接口被调用"0"次

  Scenario:[6007]被卸载的资源为空对象，执行卸载资源包的操作，则卸载失败，任务id返回为空对象，卸载结果返回资源包为空对象
    When 调用卸载资源方法,传入资源包为"空对象"
    Then 卸载资源的任务id为"空对象"
    Then 卸载资源的异步回调结果为"失败",资源包为"空对象"
    Then 更新资源数据库接口被调用"0"次
    Then 移除资源包接口被调用"0"次

  Scenario:[6008]资源包信息已经从运营平台更新完成，被卸载的资源为非数据库存储的资源信息，执行卸载资源包的操作，则卸载失败，任务id返回为有效的任务id，卸载结果返回资源包为空对象
    Given 删除文件"/path/to/resource/H5ResPkg/resA@1.0.0"结果为"成功"
    Given 更新资源"h5,resA,1.0.0"到数据库的操作结果为"失败"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resA@1.0.0 |
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 卸载资源的任务id为"有效"
    Then 卸载资源的异步回调结果为"失败",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 7  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 移除资源包接口被调用"0"次

  Scenario:[6009]资源包信息已经从运营平台更新完成，被卸载的资源未安装，执行卸载资源包的操作，则卸载成功，任务id返回有效的任务id，返回卸载成功资源包信息且path为空对象，其安装路径下没有已安装的资源包文件,可以查询到该资源已被卸载
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
    Then 卸载资源的任务id为"有效"
    Then 卸载资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          |      |
    Then 更新资源数据库接口被调用"0"次
    Then 移除资源包接口被调用"0"次

  Scenario Outline:[6010]资源包信息已经从运营平台更新完成，被卸载的资源已安装，执行卸载资源包的操作，进行移除本地已安装的资源包或更新数据库信息过程时失败，则卸载失败，任务id返回有效的任务id，返回卸载失败资源包信息
    Given 删除文件"<Path>"结果为"<RemoveResult>"
    Given 更新资源"<ResInfo>"到数据库的操作结果为"<UpdateDatabaseResult>"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resC@1.0.1 |
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          | /path/to/resource/H5ResPkg/resC@1.0.1 |
    Then 卸载资源的任务id为"有效"
    Then 卸载资源的异步回调结果为"<UninstallResult>",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path         |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          | <ReturnPath> |
    Then 更新资源数据库接口被调用"<UpdateDatabaseCount>"次
    Then 移除资源包接口被调用"<RemoveCount>"次
    Examples:
      | Path                                  | UpdateDatabaseResult | UpdateDatabaseCount | RemoveResult | RemoveCount | ReturnPath                            | ResInfo       | UninstallResult | isExist |
      | /path/to/resource/H5ResPkg/resC@1.0.1 | 失败                 | 1                   | 失败         | 0           | /path/to/resource/H5ResPkg/resC@1.0.1 | h5,resC,1.0.1 | 失败            | 存在    |
      | /path/to/resource/H5ResPkg/resC@1.0.1 | 成功                 | 1                   | 失败         | 1           |                                       | h5,resC,1.0.1 | 失败            | 存在    |

  Scenario:[6011]资源包信息已经从运营平台更新完成，被卸载的资源已安装，执行卸载资源包的操作，进行移除本地已安装的资源包或更新数据库信息操作均成功，则卸载成功，任务id返回有效的任务id，返回卸载成功资源包信息且path为空对象
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/H5ResPkg/resC@1.0.1 |
    Given 删除文件"/path/to/resource/H5ResPkg/resC@1.0.1"结果为"成功"
    Given 更新资源"h5,resC,1.0.1"到数据库的操作结果为"成功"
    When 调用卸载资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          | /path/to/resource/H5ResPkg/resC@1.0.1 |
    Then 卸载资源的任务id为"有效"
    Then 卸载资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          |      |
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path |
      | 4  | h5   | resC | 1.0.1   | http://resC.zip | hashC |       |        |        |          |      |
    Then 移除资源包接口被调用"1"次,参数路径为"/path/to/resource/H5ResPkg/resC@1.0.1"

  Scenario Outline:[6012]取消任务，取消任务ID为空对象或者空字符串时，取消任务失败
    When 调用取消任务方法,传入参数任务id为"<TaskId>"
    Then 取消任务的结果为"失败"
    Examples:
      | TaskId   |
      | 空对象   |
      | 空字符串 |

  Scenario:[6013]取消任务，取消任务ID没有对应的任务时，取消任务失败
    When 重新启动，使用异步线程调度器
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashA"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                      |
      | /path/to/resource/H5ResPkg                |
      | /path/to/resource/H5ResPkg/resA@1.0.0     |
      | /path/to/resource/H5ResPkg/resA@1.0.0/css |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                  | FileName   |
      | /path/to/resource/H5ResPkg/resA@1.0.0 | resA@1.0.0 |
    Given 设置文件"/path/to/resource/H5ResPkg/resA@1.0.0"的子目录列表为:
      | SubFilePath                                      | Type   |
      | /path/to/resource/H5ResPkg/resA@1.0.0/js         | 文件   |
      | /path/to/resource/H5ResPkg/resA@1.0.0/css        | 文件夹 |
      | /path/to/resource/H5ResPkg/resA@1.0.0/index.html | 文件   |
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/resA@1.0.0"至"/path/to/resource/H5ResPkg/resA@1.0.0"操作结果为"成功"
    Given 更新资源"h5,resA,1.0.0"到数据库的操作结果为"成功"
    Given 网络下载资源等待"3"秒
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    When 调用取消任务方法,传入参数任务id为"FakeTaskId"
    Then 取消任务的结果为"失败"
    When 等待"5"秒
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resA.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/resA@1.0.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/resA@1.0.0",目标路径为"/path/to/resource/H5ResPkg/resA@1.0.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"1"次,参数路径信息如下:
      | FilePath                              |
      | /path/to/resource/H5ResPkg/resA@1.0.0 |
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                  |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |       |        |        |          | /path/to/resource/H5ResPkg/resA@1.0.0 |

  Scenario:[6014]资源包信息已经从运营平台更新完成，被安装资源未安装，执行安装资源包的操作，当网络下载资源包时，执行取消任务的操作，取消任务返回成功，安装资源返回安装失败的资源包信息且path为空对象，其安装路径下没有已安装的资源包文件
    When 重新启动，使用异步线程调度器
    Given 网络下载资源等待"3"秒
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    When 等待"1"秒
    When 调用取消任务方法,传入参数为已保存的任务id
    Then 取消任务的结果为"成功"
    When 等待"5"秒
    Then 安装资源的异步回调结果为"失败",资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resA.zip"
    Then 文件MD5校验接口被调用"0"次
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"0"次
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 文件系统代理的判断文件接口被调用"0"次

  Scenario:[6015]获取正在执行的任务id，资源信息为空对象时，返回的任务id为空对象
    When 调用获取正在执行的任务id方法,传入资源包为"空对象"
    Then 获取正在执行的任务id的结果为"空对象"

  Scenario Outline:[6016]获取正在执行的任务id，资源信息的名称、版本、类型任意为空对象或者空字符串时，返回的任务id为空对象
    When 调用获取正在执行的任务id方法,传入资源包如下:
      | Id | Type   | Name   | Version   | Link            | MD5   |
      | 1  | <Type> | <Name> | <Version> | http://resA.zip | hashA |
    Then 获取正在执行的任务id的结果为"<TaskId>"
    Examples:
      | Type     | Name     | Version  | TaskId |
      | 空对象   | resA     | 1.0.0    | 空对象 |
      | 空字符串 | resA     | 1.0.0    | 空对象 |
      | h5       | 空对象   | 1.0.0    | 空对象 |
      | h5       | 空字符串 | 1.0.0    | 空对象 |
      | h5       | resA     | 空对象   | 空对象 |
      | h5       | resA     | 空字符串 | 空对象 |

  Scenario Outline:[6017]获取正在执行的任务id，资源信息没有对应的任务时，返回的任务id为空对象
    When 重新启动，使用异步线程调度器
    Given 网络下载资源等待"3"秒
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    When 调用获取正在执行的任务id方法,传入资源包如下:
      | Id   | Type   | Name   | Version   | Link   | MD5   |
      | <Id> | <Type> | <Name> | <Version> | <Link> | <MD5> |
    Then 获取正在执行的任务id的结果为"<TaskId>"
    Examples:
      | Id | Type     | Name     | Version     | Link            | MD5   | TaskId |
      | 1  | fakeType | resA     | 1.0.0       | http://resA.zip | hashA | 空对象 |
      | 1  | h5       | fakeName | 1.0.0       | http://resA.zip | hashA | 空对象 |
      | 1  | h5       | resA     | fakeVersion | http://resA.zip | hashA | 空对象 |

  Scenario:[6018]资源包信息已经从运营平台更新完成，被安装资源未安装，执行安装资源包的操作，当网络下载资源包时，执行获取正在执行的任务id，返回有效的任务id
    When 重新启动，使用异步线程调度器
    Given 网络下载资源等待"3"秒
    When 调用安装资源方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    When 调用获取正在执行的任务id方法,传入资源包如下:
      | Id | Type | Name | Version | Link            | MD5   |
      | 1  | h5   | resA | 1.0.0   | http://resA.zip | hashA |
    Then 获取正在执行的任务id的结果为"有效"

  Scenario:[6019]资源包信息已经从运营平台更新完成，被安装资源deviceCustomInfo类型Path有值但是文件不存在，执行安装资源包的操作，从网络下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashZ"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                  |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"不存在"的"文件"路径如下:
      | Path                                          |
      | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                               |
      | /path/to/resource/DeviceCustomInfo |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                  | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                          | FileName   |
      | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 | resZ@1.2.0 |
    Given 从网络下载资源链接"http://resZ.zip"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/resZ@1.2.0"至"/path/to/resource/DeviceCustomInfo/resZ@1.2.0"操作结果为"成功"
    Given 更新资源"deviceCustomInfo,resZ,1.2.0"到数据库的操作结果为"成功"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                          |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |       |        |        |          | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                          |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |       |        |        |          | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resZ.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/resZ@1.2.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/resZ@1.2.0",目标路径为"/path/to/resource/DeviceCustomInfo/resZ@1.2.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type             | Name | Version | Link            | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                          |
      | 6  | deviceCustomInfo | resZ | 1.2.0   | http://resZ.zip | hashZ |       |        |        |          | /path/to/resource/DeviceCustomInfo/resZ@1.2.0 |
    Then 资源安装结果上报接口被调用"1"次,参数资源名称为"resZ",类型为"deviceCustomInfo",版本为"1.2.0",是否预置为"NO",用户ID为"fake_user_id",客户端ID为"fake_client_id",app版本号为"6.9.0",安装时间为"1582603200000",灰度为"NO"

  Scenario: [6020]资源包信息已经从运营平台更新完成，被安装资源为主题资源，执行安装资源包的操作，从网络下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息且其安装路径下有已安装的资源包文件,可以查询到该资源已被安装
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashZ"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                        |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                     |
      | /path/to/resource/mPaaS                  |
      | /path/to/resource/mPaaS/appComResZ@1.2.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                        | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                     | FileName         |
      | /path/to/resource/mPaaS/appComResZ@1.2.0 | appComResZ@1.2.0 |
    Given 从网络下载资源链接"http://appComResZ.zip"操作结果为"成功"
    Given 文件系统解压资源"/path/to/resource/Temp/<EMAIL>"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/appComResZ@1.2.0"至"/path/to/resource/mPaaS/appComResZ@1.2.0"操作结果为"成功"
    Given 更新资源"mPaaS,appComResZ,1.2.0"到数据库的操作结果为"成功"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type  | Name       | Version | Link                  | MD5   |
      | 6  | mPaaS | appComResZ | 1.2.0   | http://appComResZ.zip | hashZ |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     |
      | 6  | mPaaS | appComResZ | 1.2.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.2.0 |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://appComResZ.zip"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/Temp/appComResZ@1.2.0"
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/appComResZ@1.2.0",目标路径为"/path/to/resource/mPaaS/appComResZ@1.2.0"
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type  | Name       | Version | Link                  | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                     |
      | 6  | mPaaS | appComResZ | 1.2.0   | http://appComResZ.zip | hashZ |       |        |        |          | /path/to/resource/mPaaS/appComResZ@1.2.0 |
    Then 资源安装结果上报接口被调用"1"次,参数资源名称为"appComResZ",类型为"mPaaS",版本为"1.2.0",是否预置为"NO",用户ID为"fake_user_id",客户端ID为"fake_client_id",app版本号为"6.9.0",安装时间为"1582603200000",灰度为"NO"

  Scenario:[6021]资源包信息已经从运营平台更新完成，被安装资源为routes类型未安装,routes类型资源下载链接没有后缀，默认以json结尾，执行安装资源包的操作，从网络下载资源包、md5校验、解压、移动、更新数据库资源信息过程均成功，则安装成功，任务id返回有效的任务id，返回安装成功后的资源包信息
    Given 设置文件"/path/to/resource/Temp/<EMAIL>"的MD5值为"hashZ"
    Given 文件系统中"存在"的"文件"路径如下:
      | Path                                              |
      | /path/to/resource/Temp/<EMAIL> |
    Given 文件系统中"存在"的"文件夹"路径如下:
      | Path                                |
      | /path/to/resource/routes            |
      | /path/to/resource/routes/resZ@1.2.0 |
    Given 文件系统通过文件路径获取父文件路径列表如下:
      | Path                                              | ParentPath             |
      | /path/to/resource/Temp/<EMAIL> | /path/to/resource/Temp |
    Given 文件系统通过文件路径获取文件名称列表如下:
      | Path                                | FileName   |
      | /path/to/resource/routes/resZ@1.2.0 | resZ@1.2.0 |
    Given 从网络下载资源链接"http://resZ"操作结果为"成功"
    Given 移动资源包"/path/to/resource/Temp/<EMAIL>"至"/path/to/resource/routes/<EMAIL>"操作结果为"成功"
    Given 更新资源"routes,resZ,1.2.0"到数据库的操作结果为"成功"
    When 调用安装资源方法,传入资源包如下:
      | Id | Type   | Name | Version | Link        | MD5   |
      | 6  | routes | resZ | 1.2.0   | http://resZ | hashZ |
    Then 安装资源的任务id为"有效"
    Then 安装资源的异步回调结果为"成功",资源包如下:
      | Id | Type   | Name | Version | Link        | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                            |
      | 6  | routes | resZ | 1.2.0   | http://resZ | hashZ |       |        |        |          | /path/to/resource/routes/<EMAIL> |
    Then 从网络下载资源链接接口被调用"1"次,参数链接为"http://resZ"
    Then 文件MD5校验接口被调用"1"次,参数路径为"/path/to/resource/Temp/<EMAIL>"
    Then 解压资源接口被调用"0"次
    Then 移动资源包接口被调用"1"次,参数源路径为"/path/to/resource/Temp/<EMAIL>",目标路径为"/path/to/resource/routes/<EMAIL>"
    Then 文件系统代理的获取文件子路径列表接口被调用"0"次
    Then 更新资源数据库接口被调用"1"次,参数资源包如下:
      | Id | Type   | Name | Version | Link        | MD5   | Model | TypeId | ProdNo | TypeCode | Path                                            |
      | 6  | routes | resZ | 1.2.0   | http://resZ | hashZ |       |        |        |          | /path/to/resource/routes/<EMAIL> |
    Then 资源安装结果上报接口被调用"1"次,参数资源名称为"resZ",类型为"routes",版本为"1.2.0",是否预置为"NO",用户ID为"fake_user_id",客户端ID为"fake_client_id",app版本号为"6.9.0",安装时间为"1582603200000",灰度为"NO"
    Then 资源下载结果上报接口被调用"1"次